import { NextRequest, NextResponse } from "next/server"
import { getAuth } from "@clerk/nextjs/server"

export async function POST(req: NextRequest) {
    const { userId } = getAuth(req)

    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const body = await req.json()
        const { role } = body

        if (!role) {
            return NextResponse.json({ error: "Role is required" }, { status: 400 })
        }

        // For demo purposes, returning random true/false
        const hasRole = true
        //Math.random() < 0.5

        return NextResponse.json({
            userId,
            role,
            hasRole
        })

    } catch (error) {
        return NextResponse.json({ error: "Invalid request body" }, { status: 400 })
    }
}
