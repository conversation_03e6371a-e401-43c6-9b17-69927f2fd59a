import { createServer } from 'http';
import { Server } from 'socket.io';
import { NextResponse } from 'next/server';

// This is needed because Next.js API routes are serverless by default
// and we need to maintain a persistent Socket.IO server
let io;

// Check if we're in a build/prerender context or if the server is already running
const isServerContext = typeof window === 'undefined' &&
                        process.env.NODE_ENV !== 'production' &&
                        !process.env.NEXT_PHASE;

if (!global.io && isServerContext) {
  try {
    // Create a dummy HTTP server for Socket.IO
    const httpServer = createServer();

    // Initialize Socket.IO server
    global.io = new Server(httpServer, {
      cors: {
        origin: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true,
        allowedHeaders: ['Content-Type', 'Authorization']
      },
      path: '/api/socket/io',
      addTrailingSlash: false,
    });

    // Start listening on a port with error handling
    const socketPort = process.env.SOCKET_PORT || 3001;

    httpServer.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.warn(`Port ${socketPort} is already in use. Socket.IO server not started.`);
      } else {
        console.error('Socket.IO server error:', err);
      }
    });

    httpServer.listen(socketPort, () => {
      console.log(`Socket.IO server running on port ${socketPort}`);
    });

    // Set up event handlers
    global.io.on('connection', (socket) => {
      console.log('Client connected to Socket.IO', socket.id);

      socket.on('disconnect', () => {
        console.log('Client disconnected from Socket.IO', socket.id);
      });

      // Add your custom event handlers here
      // These would be similar to what you have in backend/src/lib/socketHandler.js
    });

    io = global.io;
  } catch (error) {
    console.error('Failed to initialize Socket.IO server:', error);
  }
} else {
  io = global.io || null;
}

// This is a dummy API route handler that won't actually be called for WebSocket connections
export async function GET() {
  return NextResponse.json({ message: 'Socket.IO server is running' });
}
