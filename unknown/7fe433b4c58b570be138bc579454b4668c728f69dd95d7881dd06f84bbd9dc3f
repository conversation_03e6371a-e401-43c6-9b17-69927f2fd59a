import { NextRequest, NextResponse } from "next/server"
import axiosInstance from "@/config/axios"

export async function GET(req: NextRequest) {
  const token = req.headers.get('authorization')?.split('Bearer ')[1]

  if (!token) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const response = await axiosInstance.get('/subscription/check', {
      headers: {
        'Authorization': `<PERSON><PERSON> ${token}`
      }
    })
    return NextResponse.json(response.data)
  } catch (error) {
    console.error('Failed to fetch subscription:', error)
    return NextResponse.json(
      { error: "Failed to fetch subscription status" },
      { status: 500 }
    )
  }
}


