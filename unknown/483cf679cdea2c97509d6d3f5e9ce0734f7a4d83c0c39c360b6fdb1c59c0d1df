import { getAuth } from "@clerk/nextjs/server"
import { NextRequest, NextResponse } from "next/server"

export async function GET(req: NextRequest) {
    const { userId, sessionId, getToken } = getAuth(req)

    if (!userId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const token = await getToken();
    return NextResponse.json({ userId, sessionId, token })
}