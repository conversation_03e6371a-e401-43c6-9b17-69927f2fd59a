name: Prod Next.js Frontend CI CD

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:
jobs:

  build:

    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Use Node.js 20.14.9
        uses: actions/setup-node@v2
        with:
          node-version: '20.0.0'

      - name: Install dependencies
        run: npm install

      # - name: Rename .env.production to .env
      #   run: mv .env.production .env

      # - name: Remove all other .env files
      #   run: rm -f .env.*
      

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKER_USERNAME }}/passisto-enterprise-backend-v2:prod
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # copy-file:
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Checkout repository
  #       uses: actions/checkout@v2

  #     - name: Copy docker-compose.test.yaml to test server
  #       uses: appleboy/scp-action@v0.1.3
  #       with:
  #         host: ${{ secrets.SERVER_HOST_PROD }}       
  #         username: ${{ secrets.SERVER_USERNAME }}
  #         key: ${{ secrets.SSH_PRIVATE_KEY }}     
  #         source: 'docker-compose.prod.yaml'          
  #         target: '/pe-prod'  
          
  #     - name: Rename file
  #       uses: appleboy/ssh-action@v1.0.3
  #       with:
  #         host: ${{ secrets.SERVER_HOST_PROD }}
  #         username: ${{ secrets.SERVER_USERNAME }}
  #         key: ${{ secrets.SSH_PRIVATE_KEY }}
  #         port: ${{ secrets.PORT }}
  #         script: |
  #           cd /pe-prod
  #           mv .env.produc

  deploy:
    needs: build
    runs-on: ubuntu-latest

    steps:

      - name: Deploy to Ubuntu server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_PROD }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-prod
            
            mkdir -p passisto-enterprise-backend-v2

            docker pull ${{ secrets.DOCKER_USERNAME }}/passisto-enterprise-backend-v2:prod
            
            docker compose up --build -d passisto-enterprise-backend-v2


            docker exec passisto-enterprise-backend-v2 npx prisma migrate deploy 
