FROM node:20
WORKDIR /app

# Copy package files 
COPY package*.json ./

# Install dependencies (including dev dependencies but omitting optional)
RUN npm install --omit=optional
RUN npm install prisma --save-dev

# Copy project files
COPY . .

COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh


# Expose the port from environment variable
EXPOSE ${PORT}

# Use entrypoint script and start the application with nodemon
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["npx", "nodemon", "src/index.js"]

# CMD ["bash"]
