#!/bin/sh
set -e


# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate

# Run Prisma migrations
# echo "Running Prisma migrations..."
# npx prisma migrate dev --name merge --skip-seed

# Seed the database (optional)
# echo "Seeding database..."
# prisma migrate reset
# npm run db:truncate
# npm run db:reset

# Create Views for dashvoard stats

# psql -d passisto-enterprise -f ./scripts/db/dashboard.sql

# Start the application
echo "Starting application..."
exec "$@"