/*
  Warnings:

  - You are about to drop the `File` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `NodeRun` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Workflow` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WorkflowRun` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "workflow";

-- CreateEnum
CREATE TYPE "workflow"."Status" AS ENUM ('draft', 'active', 'archived');

-- CreateEnum
CREATE TYPE "workflow"."WorkflowStatus" AS ENUM ('PENDING', 'RUNNING', 'WAITING_FOR_USER', 'SUCCESS', 'FAILED');

-- CreateEnum
CREATE TYPE "workflow"."NodeStatus" AS ENUM ('PENDING', 'RUNNING', 'WAITING_FOR_USER', 'SUCCESS', 'FAILED', 'SKIPPED');

-- DropForeignKey
ALTER TABLE "public"."File" DROP CONSTRAINT "File_nodeRunId_fkey";

-- DropForeignKey
ALTER TABLE "public"."NodeRun" DROP CONSTRAINT "NodeRun_assigneeId_fkey";

-- DropForeignKey
ALTER TABLE "public"."NodeRun" DROP CONSTRAINT "NodeRun_workflowRunId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Workflow" DROP CONSTRAINT "Workflow_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."WorkflowRun" DROP CONSTRAINT "WorkflowRun_workflowId_fkey";

-- DropTable
DROP TABLE "public"."File";

-- DropTable
DROP TABLE "public"."NodeRun";

-- DropTable
DROP TABLE "public"."Workflow";

-- DropTable
DROP TABLE "public"."WorkflowRun";

-- DropEnum
DROP TYPE "public"."NodeStatus";

-- DropEnum
DROP TYPE "public"."Status";

-- DropEnum
DROP TYPE "public"."WorkflowStatus";

-- CreateTable
CREATE TABLE "workflow"."Workflow" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "nodes" JSONB NOT NULL,
    "edges" JSONB NOT NULL,
    "viewport" JSONB NOT NULL DEFAULT '{"x": 0, "y": 0, "zoom": 1.0}',
    "status" "workflow"."Status" NOT NULL DEFAULT 'draft',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "Workflow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow"."WorkflowRun" (
    "id" TEXT NOT NULL,
    "workflowId" TEXT NOT NULL,
    "status" "workflow"."WorkflowStatus" NOT NULL DEFAULT 'PENDING',
    "startedAt" TIMESTAMP(3),
    "finishedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WorkflowRun_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow"."NodeRun" (
    "id" TEXT NOT NULL,
    "workflowRunId" TEXT NOT NULL,
    "nodeId" TEXT NOT NULL,
    "status" "workflow"."NodeStatus" NOT NULL DEFAULT 'PENDING',
    "startedAt" TIMESTAMP(3),
    "finishedAt" TIMESTAMP(3),
    "output" JSONB,
    "formData" JSONB,
    "assigneeId" TEXT,
    "assignedAt" TIMESTAMP(3),
    "completedBy" TEXT,

    CONSTRAINT "NodeRun_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workflow"."File" (
    "id" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "originalName" TEXT NOT NULL,
    "mimetype" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "path" TEXT NOT NULL,
    "url" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "nodeRunId" TEXT,
    "nodeId" TEXT,
    "uploadedBy" TEXT NOT NULL,

    CONSTRAINT "File_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "NodeRun_workflowRunId_nodeId_key" ON "workflow"."NodeRun"("workflowRunId", "nodeId");

-- AddForeignKey
ALTER TABLE "workflow"."Workflow" ADD CONSTRAINT "Workflow_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow"."WorkflowRun" ADD CONSTRAINT "WorkflowRun_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "workflow"."Workflow"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow"."NodeRun" ADD CONSTRAINT "NodeRun_workflowRunId_fkey" FOREIGN KEY ("workflowRunId") REFERENCES "workflow"."WorkflowRun"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow"."NodeRun" ADD CONSTRAINT "NodeRun_assigneeId_fkey" FOREIGN KEY ("assigneeId") REFERENCES "public"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workflow"."File" ADD CONSTRAINT "File_nodeRunId_fkey" FOREIGN KEY ("nodeRunId") REFERENCES "workflow"."NodeRun"("id") ON DELETE CASCADE ON UPDATE CASCADE;
