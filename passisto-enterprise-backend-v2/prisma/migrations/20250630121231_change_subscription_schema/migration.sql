/*
  Warnings:

  - You are about to drop the column `aiInterviewHoursUsed` on the `Company` table. All the data in the column will be lost.
  - You are about to drop the column `emailAgentsUsed` on the `Company` table. All the data in the column will be lost.
  - You are about to drop the column `maxAiInterviewHours` on the `Subscription` table. All the data in the column will be lost.
  - You are about to drop the column `maxEmailAgents` on the `Subscription` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Company" DROP COLUMN "aiInterviewHoursUsed",
DROP COLUMN "emailAgentsUsed";

-- AlterTable
ALTER TABLE "Subscription" DROP COLUMN "maxAiInterviewHours",
DROP COLUMN "maxEmailAgents";

-- CreateTable
CREATE TABLE "SubscriptionFeature" (
    "id" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SubscriptionFeature_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyUsage" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CompanyUsage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SubscriptionFeature_subscriptionId_key_key" ON "SubscriptionFeature"("subscriptionId", "key");

-- CreateIndex
CREATE UNIQUE INDEX "CompanyUsage_companyId_key_key" ON "CompanyUsage"("companyId", "key");

-- AddForeignKey
ALTER TABLE "SubscriptionFeature" ADD CONSTRAINT "SubscriptionFeature_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyUsage" ADD CONSTRAINT "CompanyUsage_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
