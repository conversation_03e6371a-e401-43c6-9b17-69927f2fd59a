{"name": "passisto-enterprise-backend-v2", "version": "1.0.0", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prisma:migrate": "npx prisma migrate dev --name init", "prisma:generate": "npx prisma generate", "db:truncate": "node src/seeders/truncate.js", "db:reset": "node src/seeders/local-seeder.js --truncate", "start": "nodemon index.js", "dev": "nodemon index.js", "logs": "docker logs passisto-enterprise-backend-v2 -f", "test-permissions": "node scripts/test-permissions.js", "clerk:clear": "node scripts/clerk/clear.js"}, "_moduleAliases": {"@": "./src"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@clerk/clerk-sdk-node": "^5.1.6", "@clerk/express": "^1.3.51", "@faker-js/faker": "^9.8.0", "@google/genai": "^0.8.0", "@google/generative-ai": "^0.24.0", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.3.1", "@langchain/mongodb": "^0.1.0", "@langchain/openai": "^0.5.11", "@opensearch-project/opensearch": "^3.5.1", "@prisma/client": "^6.10.0", "@radix-ui/react-progress": "^1.1.4", "axios": "^1.8.4", "basic-ftp": "^5.0.5", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "celery-node": "^0.5.9", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.1", "jira-client": "^8.2.2", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "moment": "^2.30.1", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "passisto-enterprise-backend-v2": "file:", "redis": "^4.7.0", "revai-node-sdk": "^3.8.5", "socket.io": "^4.8.1", "ssh2-sftp-client": "^12.0.0", "stripe": "^17.7.0", "svix": "^1.61.1", "uuid": "^11.1.0", "uuidv4": "^6.2.13", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.10.0", "prisma-erd-generator": "^2.0.4"}}