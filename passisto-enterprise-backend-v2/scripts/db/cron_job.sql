-- Parameters
\set db_name 'passisto-enterprise'
\set schema_name 'public'

-- Usage in cron job
SELECT cron.schedule(
  'refresh_views_every_10min',         -- Job name (optional if using pg_cron v1.5+)
  '*/10 * * * *',                      -- Cron expression: every 10 minutes
  $$SELECT :'schema_name'.refresh_dashboard_materialized_views();$$         -- SQL to execute (must return void or be DO block)
);

-- Note: You must run this script while connected to the :'db_name' database.
-- Example: psql -d :'db_name' -f cron_job.sql


-- psql -d your_db -v db_name='mydb' -v schema_name='myschema' -f cron_job.sql