const prisma = require("../config/db");

async function setupTestPermissions() {
  try {
    console.log("Setting up test permissions...");
    
    // 1. Find the Company and IT groups for the first company
    const company = await prisma.company.findFirst({
      where: {
        name: "Passisto"
      }
    });
    
    if (!company) {
      throw new Error("Company 'Passisto' not found");
    }
    
    const companyGroup = await prisma.group.findFirst({
      where: {
        name: "Company",
        companyId: company.id
      }
    });
    
    const itGroup = await prisma.group.findFirst({
      where: {
        name: "IT",
        companyId: company.id
      }
    });
    
    if (!companyGroup) {
      throw new Error("Company group not found");
    }
    
    if (!itGroup) {
      throw new Error("IT group not found");
    }
    
    // 2. Find the Manager role
    const managerRole = await prisma.role.findFirst({
      where: {
        name: "MANAGER"
      }
    });
    
    if (!managerRole) {
      throw new Error("MANAGER role not found");
    }
    
    // 3. Find permissions to assign
    const permissions = await prisma.permission.findMany({
      where: {
        action: {
          in: [
            "CAN_VIEW_USER",
            "CAN_VIEW_GROUP",
            "CAN_CREATE_USER",
            "CAN_UPDATE_USER",
            "CAN_ASSIGN_USER_TO_GROUP",
            "CAN_REMOVE_USER_FROM_GROUP"
          ]
        }
      }
    });
    
    if (permissions.length === 0) {
      throw new Error("No permissions found");
    }
    
    // Map permissions by action for easier access
    const permissionMap = {};
    permissions.forEach(p => {
      permissionMap[p.action] = p;
    });
    
    // 4. Assign 2 permissions to Company group
    console.log("Assigning permissions to Company group...");
    await prisma.groupPermission.deleteMany({
      where: {
        groupId: companyGroup.id
      }
    });
    
    await prisma.groupPermission.createMany({
      data: [
        {
          groupId: companyGroup.id,
          permissionId: permissionMap["CAN_VIEW_USER"].id,
          scopeType: "TEAM",
          scopeId: companyGroup.id
        },
        {
          groupId: companyGroup.id,
          permissionId: permissionMap["CAN_VIEW_GROUP"].id,
          scopeType: "TEAM",
          scopeId: companyGroup.id
        }
      ]
    });
    
    // 5. Assign 1 permission to IT group
    console.log("Assigning permission to IT group...");
    await prisma.groupPermission.deleteMany({
      where: {
        groupId: itGroup.id
      }
    });
    
    await prisma.groupPermission.create({
      data: {
        groupId: itGroup.id,
        permissionId: permissionMap["CAN_CREATE_USER"].id,
        scopeType: "TEAM",
        scopeId: itGroup.id
      }
    });
    
    // 6. Assign 3 permissions to Manager role
    console.log("Assigning permissions to Manager role...");
    await prisma.rolePermission.deleteMany({
      where: {
        roleId: managerRole.id
      }
    });
    
    await prisma.rolePermission.createMany({
      data: [
        {
          roleId: managerRole.id,
          permissionId: permissionMap["CAN_UPDATE_USER"].id,
          scopeType: "GLOBAL",
          scopeId: "global"
        },
        {
          roleId: managerRole.id,
          permissionId: permissionMap["CAN_ASSIGN_USER_TO_GROUP"].id,
          scopeType: "GLOBAL",
          scopeId: "global"
        },
        {
          roleId: managerRole.id,
          permissionId: permissionMap["CAN_REMOVE_USER_FROM_GROUP"].id,
          scopeType: "GLOBAL",
          scopeId: "global"
        }
      ]
    });
    
    console.log("Test permissions setup completed successfully!");
  } catch (error) {
    console.error("Error setting up test permissions:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  setupTestPermissions();
}

module.exports = { setupTestPermissions };