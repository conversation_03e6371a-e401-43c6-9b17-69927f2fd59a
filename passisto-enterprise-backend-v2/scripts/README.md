# Database Seeding Scripts

This directory contains scripts for seeding the database with sample data for different scenarios.

## Task Assignment Seed

The `task-assignment-seed.js` script creates a scenario where a task is assigned to a regular user. This is useful for testing the task assignment and completion workflow.

### What it creates:

1. **Users**:
   - An admin user (`<EMAIL>` / `admin123`)
   - A regular user with an assigned task (`<EMAIL>` / `john123`)
   - Another regular user for testing (`<EMAIL>` / `sarah123`)

2. **Workflow**:
   - A "Document Review Workflow" with the following nodes:
     - Start node
     - Task node (assigned to <PERSON>)
     - Ask AI node
     - End node

3. **Workflow Run**:
   - A running workflow instance
   - The Start node is completed
   - The Task node is waiting for user input
   - The workflow run status is set to WAITING_FOR_USER

### How to use:

1. Run the seed script:
   ```
   npm run seed:task
   ```

2. Start the backend server:
   ```
   npm run dev
   ```

3. Start the frontend:
   ```
   cd ../frontend
   npm run dev
   ```

4. Log in as the regular user (`<EMAIL>` / `john123`) to see and complete the assigned task.

5. You can also log in as the admin (`<EMAIL>` / `admin123`) to monitor the workflow progress.

### Testing the complete flow:

1. Log in as John and navigate to the "My Tasks" section
2. Complete the assigned document review task
3. The workflow should continue to the Ask AI node
4. The AI node should process the feedback
5. The workflow should complete successfully

## Resetting the database

If you want to reset the database to a clean state:

```
npm run prisma:reset
```

This will clear all data and run the default seed script.
