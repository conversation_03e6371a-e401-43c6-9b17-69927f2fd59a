const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

/**
 * This seed script creates a scenario where a task is assigned to a regular user.
 * It creates:
 * 1. An admin user who creates the workflow
 * 2. A regular user who will be assigned the task
 * 3. A workflow with a task node that requires user input
 * 4. A workflow run with the task node assigned to the regular user
 */
async function main() {
  try {
    console.log('Starting task assignment seed...');

    // Clean up existing data if needed
    console.log('Cleaning up existing data...');
    await prisma.nodeRun.deleteMany({});
    await prisma.workflowRun.deleteMany({});
    await prisma.workflow.deleteMany({});
    await prisma.user.deleteMany({});

    // Create admin user
    console.log('Creating admin user...');
    const adminUser = await prisma.user.create({
      data: {
        name: 'Admin Manager',
        email: '<EMAIL>',
        password: await bcrypt.hash('admin123', 10),
        role: 'admin'
      }
    });

    // Create regular user who will be assigned the task
    console.log('Creating regular user...');
    const regularUser = await prisma.user.create({
      data: {
        name: '<PERSON> Employee',
        email: '<EMAIL>',
        password: await bcrypt.hash('john123', 10),
        role: 'user'
      }
    });

    // Create another regular user for testing
    console.log('Creating another regular user...');
    const anotherUser = await prisma.user.create({
      data: {
        name: 'Sarah Teammate',
        email: '<EMAIL>',
        password: await bcrypt.hash('sarah123', 10),
        role: 'user'
      }
    });

    // Create a workflow with a task node that requires user input
    console.log('Creating workflow with task node...');
    const workflow = await prisma.workflow.create({
      data: {
        name: 'Document Review Workflow',
        description: 'Review and approve documents',
        status: 'active',
        userId: adminUser.id,
        nodes: [
          {
            id: 'start',
            type: 'start',
            position: { x: 250, y: 5 },
            data: { 
              label: 'Start',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'task-review',
            type: 'task',
            position: { x: 250, y: 100 },
            data: { 
              label: 'Review Document',
              description: 'Please review the attached document and provide your feedback',
              assignee: regularUser.id, // Pre-assign the task to John
              isActive: false,
              isCompleted: false,
              formFields: [
                {
                  id: 'feedback',
                  type: 'textarea',
                  label: 'Feedback',
                  placeholder: 'Enter your feedback here',
                  required: true
                },
                {
                  id: 'approved',
                  type: 'checkbox',
                  label: 'Approved',
                  required: true
                }
              ]
            }
          },
          {
            id: 'ask-ai',
            type: 'ask-ai',
            position: { x: 250, y: 200 },
            data: { 
              label: 'Analyze Feedback',
              model: 'gpt-4o',
              prompt: 'Analyze the feedback provided and suggest improvements',
              isActive: false,
              isCompleted: false
            }
          },
          {
            id: 'end',
            type: 'end',
            position: { x: 250, y: 300 },
            data: { 
              label: 'End',
              isActive: false,
              isCompleted: false
            }
          }
        ],
        edges: [
          {
            id: 'edge-start-task',
            source: 'start',
            target: 'task-review',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-task-ai',
            source: 'task-review',
            target: 'ask-ai',
            style: { strokeWidth: 2 }
          },
          {
            id: 'edge-ai-end',
            source: 'ask-ai',
            target: 'end',
            style: { strokeWidth: 2 }
          }
        ],
        viewport: {
          x: 0,
          y: 0,
          zoom: 1
        },
      }
    });

    // Create a workflow run
    console.log('Creating workflow run...');
    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId: workflow.id,
        status: 'RUNNING',
        startedAt: new Date(),
        createdAt: new Date()
      }
    });

    // Create node runs for each node in the workflow
    console.log('Creating node runs...');
    
    // Start node (completed)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'start',
        status: 'SUCCESS',
        startedAt: new Date(Date.now() - 60000), // 1 minute ago
        finishedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { result: 'Workflow started successfully' }
      }
    });

    // Task node (waiting for user)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'task-review',
        status: 'WAITING_FOR_USER',
        startedAt: new Date(Date.now() - 55000), // 55 seconds ago
        assigneeId: regularUser.id,
        assignedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { 
          taskDetails: {
            title: 'Review Document',
            description: 'Please review the attached document and provide your feedback',
            documentUrl: 'https://example.com/documents/sample.pdf',
            dueDate: new Date(Date.now() + 86400000) // Due in 24 hours
          }
        }
      }
    });

    // Update workflow run status to WAITING_FOR_USER
    await prisma.workflowRun.update({
      where: { id: workflowRun.id },
      data: { status: 'WAITING_FOR_USER' }
    });

    console.log('Task assignment seed completed successfully!');
    console.log('\nLogin credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User with assigned task: <EMAIL> / john123');
    console.log('Another user: <EMAIL> / sarah123');
    console.log('\nWorkflow run ID:', workflowRun.id);
    console.log('Workflow ID:', workflow.id);

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
