# Job Interview Workflow Seed

This seed script creates a collaborative job interview workflow scenario that demonstrates the use of speech-to-text, AI analysis, task assignments, decision nodes, and email notifications.

## Workflow Overview

The workflow simulates a real job interview process with the following steps:

1. **Start** - Begin the interview process
2. **Recruiter Task** - <PERSON><PERSON>ruiter uploads the interview recording and enters candidate information
3. **Speech-to-Text** - Transcribe the job interview audio recording
4. **Email Notification** - Notify HR that transcription is complete
5. **AI Analysis** - Generate an AI-powered analysis of the interview transcript
6. **HR Manager Review** - HR manager reviews the AI analysis and provides feedback
7. **Decision Node** - Evaluate candidate quality based on HR rating
   - Yes output: Proceed to hiring manager review (if rated excellent/good)
   - No output: Send rejection email (if rated average/below average/poor)
8. **Hiring Manager Review** - Hiring manager makes the final decision for promising candidates
9. **Decision Nodes** - Two separate decision nodes for clear yes/no paths:
   - "Is Candidate Hired?" decision:
     - Yes output: Send offer preparation email
     - No output: Send rejection email
   - "Need Additional Interview?" decision:
     - Yes output: Send additional interview request
     - No output: Send rejection email
10. **Email Notification** - Send the complete interview report to the team
11. **End** - Complete the workflow

## User Roles

The seed creates three users with different roles:

1. **HR Manager** (Admin)
   - Email: <EMAIL>
   - Password: hr123
   - Responsible for reviewing the AI analysis and providing HR feedback

2. **Hiring Manager** (Regular User)
   - Email: <EMAIL>
   - Password: hiring123
   - Makes the final hiring decision based on the interview analysis and HR feedback

3. **Recruiter** (Regular User)
   - Email: <EMAIL>
   - Password: recruiter123
   - Uploads interview recordings and enters candidate information

## Node Details

### Task Nodes
- **Recruiter Task**: Collects candidate information and interview recording
- **HR Manager Task**: Includes feedback form, candidate rating, and decision fields
- **Hiring Manager Task**: Includes technical assessment and final decision fields

### Speech-to-Text Node
- Configured to use Rev.ai for transcription
- Processes the uploaded interview recording
- AI-enhanced transcription enabled

### Ask AI Node
- Uses Gemini 2.0 Flash model
- Generates a comprehensive interview analysis report
- Includes candidate strengths/weaknesses, skills assessment, and recommendations

### Decision Nodes
- **Candidate Quality Decision**:
  - Yes output: Proceeds to hiring manager review if candidate is rated good/excellent
  - No output: Sends to rejection email if candidate is rated average/below average/poor
- **Is Candidate Hired?**:
  - Yes output: Proceeds to offer preparation if hiring manager decides to hire
  - No output: Sends to rejection email if not hired
- **Need Additional Interview?**:
  - Yes output: Sends to additional interview request if hiring manager requests it
  - No output: Sends to rejection email if not needed

### Email Nodes
- **Transcription Notification**: Notifies HR when transcription is complete
- **Offer Preparation**: Sent to HR when candidate is approved for hire
- **Additional Interview Request**: Sent to recruiter when additional interview is needed
- **Rejection Notification**: Sent to recruiter for candidates who don't meet requirements
- **Team Notification**: Sends the complete interview report to the entire team

## How to Use

1. Run the seed script:
   ```
   npm run seed:interview
   ```

2. Start the backend server:
   ```
   npm run dev
   ```

3. Start the frontend:
   ```
   cd ../frontend
   npm run dev
   ```

4. Log in as any of the created users to interact with the workflow

## Testing the Workflow

1. Log in as the HR Manager (<EMAIL> / hr123)
2. Navigate to the workflow dashboard
3. Execute the workflow
4. Log in as the Recruiter (<EMAIL> / recruiter123)
5. Complete the task to upload the interview recording and enter candidate information
6. The workflow will process the recording through speech-to-text
7. An email notification will be sent to HR
8. The AI will analyze the transcript
9. Log in as the HR Manager to review the analysis and rate the candidate
10. Based on the rating, the decision node will follow one of two paths:
    - Yes path (if rated excellent/good): The Hiring Manager will be assigned a task
    - No path (if rated average/below average/poor): A rejection email will be sent
11. If proceeding, log in as the Hiring Manager (<EMAIL> / hiring123) to complete their task
12. Based on the Hiring Manager's decision, two decision nodes will determine the path:
    - "Is Candidate Hired?" decision:
      - Yes path: An offer preparation email will be sent to HR
      - No path: A rejection email will be sent
    - "Need Additional Interview?" decision:
      - Yes path: A scheduling email will be sent to the Recruiter
      - No path: A rejection email will be sent
13. The workflow will send a final notification email to the team

This seed provides a realistic example of how to use workflows for collaborative business processes involving multiple stakeholders, conditional branching, and AI-powered analysis.
