// clerk script to remove all the users 
const { clerkClient } = require("@clerk/clerk-sdk-node");
const readline = require('readline');

// Function to ask for user confirmation
function askConfirmation(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

async function clearClerkUsers(confirmEach = false) {
  console.log("Clearing all users in Clerk...");
  try {
    // Check if Clerk API is accessible
    try {
      await clerkClient.users.getUserList({ limit: 1 });
    } catch (error) {
      console.error(`Clerk API not accessible: ${error.message}`);
      console.log("Skipping Clerk user truncation");
      return 0;
    }

    // Get all users from Clerk
    const responseUsers = await clerkClient.users.getUserList({
      limit: 100 // Adjust as needed
    });
    
    const allUsers = responseUsers.data;
    
    console.log(`Found ${allUsers.length} users in Clerk`);
    
    // Delete each user
    let deletedCount = 0;
    for (const user of allUsers) {
      try {
        const userInfo = `${user.id} (${user.emailAddresses[0]?.emailAddress || 'no email'})`;
        console.log(`Found user: ${userInfo}`);
        
        if (confirmEach) {
          const shouldDelete = await askConfirmation(`Do you want to delete user ${userInfo}? (yes/y to confirm): `);
          if (!shouldDelete) {
            console.log(`Skipped user: ${userInfo}`);
            continue;
          }
        }
        
        console.log(`Deleting Clerk user: ${userInfo}`);
        await clerkClient.users.deleteUser(user.id);
        deletedCount++;
      } catch (deleteError) {
        console.error(`Error deleting user ${user.id}: ${deleteError.message}`);
      }
    }
    
    console.log(`Successfully deleted ${deletedCount} users from Clerk`);
    return deletedCount;
  } catch (error) {
    console.error("Error clearing Clerk users:", error);
    return 0;
  }
}

// Run if executed directly
if (require.main === module) {
  // Check if --confirm-each flag is passed
  const confirmEach = process.argv.includes('--confirm-each');
  
  if (confirmEach) {
    console.log("Running in confirmation mode - you will be asked before each user deletion");
  }
  
  clearClerkUsers(confirmEach)
    .then(() => {
      console.log("Clerk users cleared.");
      process.exit(0);
    })
    .catch((err) => {
      console.error("Clearing failed:", err);
      process.exit(1);
    });
}

module.exports = {
  clearClerkUsers,
};