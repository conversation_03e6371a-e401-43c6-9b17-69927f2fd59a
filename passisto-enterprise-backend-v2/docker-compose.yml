version: '3.8'

services:
    postgres-local:
      image: postgres:15-alpine
      container_name: postgres-local-1
      environment:
        POSTGRES_USER: ${DB_USER:-postgres}     
        POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres} 
        POSTGRES_DB: ${DB_NAME:-passisto}    
      volumes:
        - postgres_data:/var/lib/postgresql/data-2
      ports:
        - "127.0.0.1:5036:5432"
        - "********:5036:5432"
      networks:
        - default-net


    mongo-local:
      image: mongo:latest
      container_name: mongo-local
      ports:
        - 127.0.0.1:27019:27017
      environment:
        - MONGO_INITDB_ROOT_USERNAME=admin
        - MONGO_INITDB_ROOT_PASSWORD=adminpassword
      networks:
        - default-net


    celery-client:
      build:
        context: .
        dockerfile: Dockerfile
      container_name: celery-client
      ports:
        # - "127.0.0.1:1026:1025"
        # - "********:1026:1025"
        - 1026:1025
      env_file:
        - .env.local
      depends_on:
        - postgres-local
        # - opensearch-local
        # - mongo-local
        # - rabbitmq-local  
        # - redis-local
      networks:
        - default-net
        # - pe-net
  

    rabbitmq-local:
      image: rabbitmq:3-management  
      container_name: passisto-rabbitmq
      environment:
        RABBITMQ_DEFAULT_USER: rabbit
        RABBITMQ_DEFAULT_PASS: Fqb3Ita2lsb
      ports:
        - "127.0.0.1:5682:5672"
        - "127.0.0.1:15682:15672"
        - "********:5682:5672"
        - "********:15682:15672"
      networks:
        - pe-net
        - default-net

    redis-local:
      image: redis:7-alpine
      container_name: passisto-redis
      ports:
        - "127.0.0.1:6172:6379"
        - "********:6172:6379"
      networks:
        - pe-net
        - default-net

    flower-local:
      image: mher/flower
      container_name: passisto-flower
      ports:
        - "127.0.0.1:5544:5555"
        - "********:5544:5555"
      environment:
        - FLOWER_BASIC_AUTH=${FLOWER_USER:-guest}:${FLOWER_PASS:-guest}
        - CELERY_BROKER_URL=${RABBITMQ_CELERY_BROKER}
        - CELERY_RESULT_BACKEND=${REDIS_CELERY_BACKEND}
      depends_on:
        - rabbitmq-local  
        - redis-local
        - celery-client
      networks:
        - default-net
    
    opensearch-local:
      image: opensearchproject/opensearch:latest
      container_name: opensearch-local
      restart: always
      environment:
        - cluster.name=opensearch-local
        - node.name=opensearch-local
        - discovery.seed_hosts=opensearch-local
        - cluster.initial_cluster_manager_nodes=opensearch-local
        - bootstrap.memory_lock=true
        - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
        - OPENSEARCH_USERNAME=${OPENSEARCH_USERNAME}
        - OPENSEARCH_INITIAL_ADMIN_PASSWORD=${OPENSEARCH_PASSWORD}
        - DISABLE_SECURITY_PLUGIN=true
      ulimits:
        memlock:
          soft: -1
          hard: -1
        nofile:
          soft: 65536
          hard: 65536
      volumes:
        - opensearch-data1:/usr/share/opensearch/data-1
      ports:
        - "127.0.0.1:9210:9200"
        - "127.0.0.1:9610:9600"
        - "********:9210:9200"
        - "********:9610:9600"
      networks:
        - pe-net
        - default-net

networks:
    pe-net:
      external: true
    default-net:
      driver: bridge

volumes:
  postgres_data:
  opensearch-data1:


