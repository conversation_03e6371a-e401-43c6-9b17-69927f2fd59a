const mongoose = require("mongoose");

const connectMongo = async (retryDelay = 5000) => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log("MongoDB connected");
    return mongoose.connection;
  } catch (err) {
    console.error("MongoDB connection error:", err);
    console.log(`Retrying connection in ${retryDelay / 1000} seconds...`);
    return new Promise(resolve => {
      setTimeout(async () => {
        resolve(await connectMongo(retryDelay));
      }, retryDelay);
    });
  }
};

module.exports = { mongoose, connectMongo };