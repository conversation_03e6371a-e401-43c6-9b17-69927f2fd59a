let redis = require("redis");

const redisClient = redis.createClient({
    url: process.env.REDIS_CELERY_BACKEND,
  });
redisClient.on('error', (err) => console.error('Redis Client Error', err));

const connectRedis = async () => {
    if (!redisClient.isOpen) {
        console.log('connecting to redis...')
        await redisClient.connect();
    }
};

module.exports = { redisClient, connectRedis };