const { Client } = require("@opensearch-project/opensearch");
const { OpenSearchVectorStore } = require("@langchain/community/vectorstores/opensearch");
const dotenv = require("dotenv");

dotenv.config();

let rejectUnauthorized = true;
if (process.env.ENVIRONMENT === 'DEV') {
  rejectUnauthorized = false;
}

let openSearchClientInstance;

function createOpenSearchClient() {
  return new Client({
    node: process.env.OPENSEARCH_HOST,
    auth: {
      username: process.env.OPENSEARCH_USERNAME,
      password: process.env.OPENSEARCH_PASSWORD,
    },
    ssl: {
      rejectUnauthorized,
    },
  });
}


async function getOpenSearchClient() {
  if (!openSearchClientInstance) {
    openSearchClientInstance = createOpenSearchClient();
    try {
      const info = await openSearchClientInstance.info();
      console.log("OpenSearch client connected:", info);
    } catch (error) {
      console.error("OpenSearch connection failed:", error.message);
      throw new Error("Failed to connect to OpenSearch");
    }
  }
  return openSearchClientInstance;
}


const vectorStore = async (alias) => {
  const client = await getOpenSearchClient();

  // Import and initialize Azure OpenAI embeddings
  const { AzureOpenAIEmbeddings } = await import("@langchain/openai");

  const embeddings = new AzureOpenAIEmbeddings({
    modelName: process.env.EMBEDDING_MODEL,
    dimensions: parseInt(process.env.EMBEDDING_DIMENSION),
    azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY,
    azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION,
    azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME,
    azureOpenAIApiDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
    azureOpenAI: true, // Enable Azure OpenAI mode
  });

  const vectorStore = new OpenSearchVectorStore(embeddings, {
    client,
    indexName: alias,
    vectorFieldName: "vector_field",
  });

  return vectorStore;
}


module.exports = vectorStore;
