"use strict";
require("dotenv").config();
const celery = require("celery-node");

const BROKER_URI = process.env.RABBITMQ_CELERY_BROKER;
const BACKEND_URI = process.env.REDIS_CELERY_BACKEND;

const JIRAceleryClient = celery.createClient(BROKER_URI, BACKEND_URI, 'jira');
const FTPceleryClient = celery.createClient(BROKER_URI, BACKEND_URI, 'ftp');

module.exports =  { JIRAceleryClient, FTPceleryClient };
