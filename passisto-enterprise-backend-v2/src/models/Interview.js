const mongoose = require("mongoose");

const interviewSchema = new mongoose.Schema({
  role: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
  level: {
    type: String,
    required: true,
  },
  techstack: [String],
  questions: [String],
  createdBy: {
    type: String,
    required: true,
  },
  candidates: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Candidate',
  }],
  candidateCount: {
    type: Number,
    required: false,
  },
  completedCount: {
    type: Number,
    required: false,
  },
  finalized: {
    type: Boolean,
    default: true,
  },
  company: {
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    }
  },
  createdAt: {
    type: String,
    default: new Date().toISOString(),
  },
});

const Interview = mongoose.model("Interview", interviewSchema);

module.exports = Interview;
