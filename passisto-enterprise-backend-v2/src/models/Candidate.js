const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  role: {
    type: String,
    enum: ['user', 'assistant'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: String,
    required: true
  }
});

const candidateSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true
  },
  password: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed'],
    required: false,
  },
  score: {
    type: Number
  },
  completedAt: {
    type: String
  },
  hasFeedback: {
    type: Boolean,
    required: false
  },
  transcript: {
    type: [messageSchema],
    default: []
  }
});

const Candidate = mongoose.model('Candidate', candidateSchema);

module.exports = Candidate;
