const mongoose = require("mongoose");

const categoryScoreSchema = new mongoose.Schema({
  name: { type: String, required: true },
  score: { type: Number, required: true },
  comment: { type: String, required: true },
});

const feedbackSchema = new mongoose.Schema({
  interviewId: { type: String, required: true },
  candidateId: { type: String, required: true },
  totalScore: { type: Number, required: true },
  categoryScores: [categoryScoreSchema],
  strengths: { type: [String], required: true },
  areasForImprovement: { type: [String], required: true },
  finalAssessment: { type: String, required: true },
});

const Feedback = mongoose.model("Feedback", feedbackSchema);

module.exports = Feedback;
