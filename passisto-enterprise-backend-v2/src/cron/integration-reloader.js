// src/services/integrationScheduler.js

const cron = require('node-cron');
const prisma  = require('@/config/db');
const { redisDeleteTaskKey } = require('@/utils/helpers')
const axios = require('axios');

// Import your Celery clients
const { JIRAceleryClient, FTPceleryClient } = require("@/config/celery"); 

/**
 * Check if an integration needs to be refreshed based on its update time
 * @param {Object} integration - The integration to check
 * @returns {boolean} - Whether the integration should be refreshed
 */
function shouldRefreshIntegration(integration) {
  const lastUpdated = new Date(integration.updatedAt);
  const now = new Date();
  const hoursSinceUpdate = Math.floor((now - lastUpdated) / (1000 * 60 * 60));
  const updateFrequencyHours = integration.updateTime * 24;
  return hoursSinceUpdate >= updateFrequencyHours && integration.status != 'failed' ;
}

/**
 * Refresh a Jira integration by sending a new Celery task
 */
async function refreshJiraIntegration(integration) {
  try {
    const jiraIntegration = await prisma.jiraIntegration.findUnique({
      where: { id: integration.id }
    });
    if (!jiraIntegration) {
      console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Jira integration details not found for integration ${integration.id}`);
      return null;
    }
    
    redisDeleteTaskKey(jiraIntegration);  //clean old task metadata in redis celery backend
    
    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Refreshing Jira integration ${integration.name}`);
    const taskResult = await JIRAceleryClient.sendTask(
      'celery.jira_loader',
      [
        jiraIntegration.domain,
        jiraIntegration.email,
        jiraIntegration.encryptedToken,
        jiraIntegration.project,
        integration.opensearchIndexId
      ],
      {}
    );
    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Celery did not return a task ID for Jira refresh`);
      return null;
    }
    return taskId;
  } catch (error) {
    console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Failed to refresh Jira integration ${integration.id}:`, error);
    return null;
  }
}

/**
 * Refresh an FTP integration by sending a new Celery task
 */
async function refreshFtpIntegration(integration) {
  try {
    // Fetch the FTP integration details
    const ftpIntegration = await prisma.ftpIntegration.findUnique({
      where: { id: integration.id }
    });
    if (!ftpIntegration) {
      console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] FTP integration details not found for integration ${integration.id}`);
      return null;
    }
 
    redisDeleteTaskKey(ftpIntegration);  //clean old task metadata in redis celery backend

    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Refreshing FTP integration ${integration.name}`);
    const taskResult = await FTPceleryClient.sendTask(
      'celery.ftp_loader',
      [
        ftpIntegration.isSecure,
        ftpIntegration.server,
        ftpIntegration.port,
        ftpIntegration.username,
        ftpIntegration.encryptedPassword,
        integration.opensearchIndexId
      ],
      {}
    );
    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Celery did not return a task ID for FTP refresh`);
      return null;
    }
    return taskId;
  } catch (error) {
    console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Failed to refresh FTP integration ${integration.id}:`, error);
    return null;
  }
}

/**
 * Refresh a Web integration by sending a new Scrapy task
 */
async function refreshWebIntegration(integration) {
  try {
    const webIntegration = await prisma.webIntegration.findUnique({
      where: { id: integration.id }
    });
    if (!webIntegration) {
      console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Web integration details not found for integration ${integration.id}`);
      return null;
    }

    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Refreshing Web integration ${integration.name} using Scrapy`);

    const scrapydResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/schedule.json`, null, {
      headers: { 'Content-Type': 'multipart/form-data' },
      auth: {
        username: process.env.SCRAPYD_USERNAME,
        password: process.env.SCRAPYD_PASSWORD,
      },
      params: {
        project: process.env.SCRAPYD_PROJECT,
        spider: process.env.SCRAPYD_SPIDER,
        index_name: integration.name,
        period: integration.updateTime,
        aliasName: integration.name,
        max_data_size: 200,
        website_url: webIntegration.url,
      }
    });

    const jobId = scrapydResponse?.data?.jobid;
    if (!jobId) {
      console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Scrapy did not return a job ID for Web refresh`);
      return null;
    }

    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Scrapy job triggered for Web integration ${integration.name}, job ID: ${jobId}`);
    return jobId;
  } catch (error) {
    console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Failed to refresh Web integration ${integration.id}:`, error);
    return null;
  }
}

/**
 * Update the integration with the new task ID and reset status
 */
async function updateIntegrationWithNewTask(integrationId, taskId) {
  try {
    await prisma.integration.update({
      where: { id: integrationId },
      data: {
        celeryTaskId: taskId,
        status: 'refreshing',
        firstLoad: false
      }
    });
    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Updated integration ${integrationId} with new task ID ${taskId}`);
    return true;
  } catch (error) {
    console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Failed to update integration ${integrationId}:`, error);
    return false;
  }
}

/**
 * Main function to check and refresh integrations
 */
async function checkAndRefreshIntegrations() {
  console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Running integration refresh check...`);
  try {
    const integrations = await prisma.integration.findMany({
      where: {
        status:{
          'in': ['completed', 'not_updated']
        }
      }
    });
    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}]Found ${integrations.length} integrations to check for refresh`);
    for (const integration of integrations) {
      if (!shouldRefreshIntegration(integration)) {
        continue;
      }
      console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Integration ${integration.name} (${integration.id}) needs refresh. Provider: ${integration.providerType}`);
      let taskId = null;
      // Handle different provider types
      console.log(integration.providerType);
      switch (integration.providerType) {
        case 'jira':
          taskId = await refreshJiraIntegration(integration);
          break;
        
        case 'ftp':
          taskId = await refreshFtpIntegration(integration);
          break;
        
        case 'web':
          taskId = await refreshWebIntegration(integration);
          break;
          
        default:
          console.error(`[SCHEDULE:ERROR] [${new Date().toISOString()}] Unknown provider type ${integration.providerType}`);
          continue;
      }
      
      // Update the integration with the new task ID if successful
      if (taskId) {
        await updateIntegrationWithNewTask(integration.id, taskId);
      }
    }
    console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Completed integration refresh check`);
  } catch (error) {
    console.error('[SCHEDULE:ERROR] [${new Date().toISOString()}] Error in checkAndRefreshIntegrations:', error);
  }
}

/**
 * Start the integration scheduler
 * @param {string} schedule - SCHEDULE schedule expression (default: once an hour)
 * @returns {object} - The SCHEDULE job
 */
function startScheduler(schedule = '0 * * * *') {
  const job = cron.schedule(schedule, checkAndRefreshIntegrations);
  console.log(`[SCHEDULE:INFO] [${new Date().toISOString()}] Integration scheduler started with schedule: ${schedule}`);
  checkAndRefreshIntegrations();
  return job;
}

module.exports = {
  startScheduler,
  checkAndRefreshIntegrations
};