const cron = require('node-cron');
const axios = require('axios');
const prisma = require('@/config/db');

const FLOWER_API_URL = process.env.FLOWER_URL;
const FLOWER_USER = process.env.FLOWER_USER;
const FLOWER_PASS = process.env.FLOWER_PASS;

/**
 * Fetch all Celery tasks from Flower API
 */
async function fetchCeleryTasks() {
  try {
    const response = await axios.get(FLOWER_API_URL, {
      auth: {
        username: FLOWER_USER,
        password: FLOWER_PASS,
      },
    });
    return response.data;
  } catch (error) {
    console.error(`[STATE:ERROR] [${new Date().toISOString()}] Error fetching Celery tasks:`, error.message);
    return {};
  }
}

/**
 * Fetch Scrapyd task state
 */
async function fetchScrapydStatus(jobId) {
  try {
    const response = await axios.get(`${process.env.SCRAPYD_SERVER}/status.json`,{
        params: {
          job: jobId
        },
        headers: { 'Content-Type': 'multipart/form-data' },
        auth: {
          username: process.env.SCRAPYD_USERNAME,
          password: process.env.SCRAPYD_PASSWORD,
        }
      }
    );
    return response.data.currstate; // "pending", "running", "finished"
  } catch (error) {
    console.error(`[STATE:ERROR] [${new Date().toISOString()}] Error fetching Scrapyd task status for job ${jobId}:`, error.message);
    return null;
  }
}

/**
 * Update status for Celery-based integration
 */
async function updateCeleryIntegrationStatus(taskId, taskInfo) {
  try {
    const integration = await prisma.integration.findFirst({
      where: { celeryTaskId: taskId }
    });

    if (!integration) return;

    let newStatus;
    switch (taskInfo.state) {
      case 'STARTED':
        newStatus = integration.firstLoad ? 'loading' : 'refreshing';
        break;
      case 'SUCCESS':
        newStatus = 'completed';
        break;
      case 'FAILURE':
        newStatus = integration.firstLoad ? 'failed' : 'not_updated';
        break;
      default:
        return; // Ignore other states
    }

    if (integration.status !== newStatus) {
      await prisma.integration.update({
        where: { id: integration.id },
        data: { status: newStatus, updatedAt: new Date() }
      });
      console.log(`[STATE:INFO] Updated Celery integration ${integration.name} to ${newStatus}`);
    }

  } catch (error) {
    console.error(`[STATE:ERROR] Error updating Celery integration ${taskId}:`, error.message);
  }
}

/**
 * Update status for Scrapyd-based Web integration
 */
async function updateScrapydIntegrationStatus(integration) {
  try {
    const scrapydStatus = await fetchScrapydStatus(integration.celeryTaskId); // using same taskId field
    if (!scrapydStatus) return;

    let newStatus;
    if (scrapydStatus === 'finished') {
      newStatus = 'completed'
    } else {
      newStatus = integration.firstLoad ? 'loading' : 'refreshing';
    }

    if (integration.status !== newStatus) {
      await prisma.integration.update({
        where: { id: integration.id },
        data: { status: newStatus, updatedAt: new Date() }
      });
      console.log(`[STATE:INFO] Updated Web integration ${integration.name} to ${newStatus}`);
    }

  } catch (error) {
    console.error(`[STATE:ERROR] Error updating Scrapyd integration ${integration.id}:`, error.message);
  }
}

/**
 * Main cron function
 */
async function checkAndUpdateIntegrations() {
  console.log(`[STATE:INFO][${new Date().toISOString()}] Running integration status check...`);

  try {
    const [tasks, integrations] = await Promise.all([
      fetchCeleryTasks(),
      prisma.integration.findMany({
        select: {
          id: true,
          name: true,
          celeryTaskId: true,
          status: true,
          firstLoad: true,
          web: { select: { id: true } } // Used to detect Scrapyd-based
        }
      })
    ]);

    const updates = integrations.map(integration => {
      const isWeb = !!integration.web;

      if (isWeb) {
        return updateScrapydIntegrationStatus(integration);
      } else if (tasks[integration.celeryTaskId]) {
        return updateCeleryIntegrationStatus(integration.celeryTaskId, tasks[integration.celeryTaskId]);
      }

      return Promise.resolve(); // no update needed
    });

    await Promise.all(updates);
    console.log(`[STATE:INFO] Completed integration status updates`);

  } catch (error) {
    console.error(`[STATE:ERROR] Error in checkAndUpdateIntegrations:`, error);
  }
}

/**
 * Start cron
 */
function startMonitoring(schedule = '* * * * *') {
  const job = cron.schedule(schedule, checkAndUpdateIntegrations);
  console.log(`[STATE:INFO] Celery/Scrapyd task monitor started with schedule: ${schedule}`);
  checkAndUpdateIntegrations(); // run on startup
  return job;
}

module.exports = {
  startMonitoring,
  checkAndUpdateIntegrations,
  fetchCeleryTasks,
};
