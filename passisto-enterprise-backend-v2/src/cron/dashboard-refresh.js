// src/cron/dashboard-refresh.js

const cron = require('node-cron');
const prisma = require('@/config/db');

/**
 * Refresh dashboard materialized views by calling the SQL function
 */
async function refreshDashboardMaterializedViews() {
  try {
    console.log(`[DASHBOARD:INFO] [${new Date().toISOString()}] Refreshing dashboard materialized views...`);
    
    // Execute the SQL function using Prisma's raw query
    await prisma.$executeRaw`SELECT refresh_dashboard_materialized_views();`;
    
    console.log(`[DASHBOARD:INFO] [${new Date().toISOString()}] Dashboard materialized views refreshed successfully`);
  } catch (error) {
    console.error(`[DASHBOARD:ERROR] [${new Date().toISOString()}] Failed to refresh dashboard materialized views:`, error);
  }
}

/**
 * Start the dashboard refresh scheduler
 * @param {string} schedule - Cron schedule expression (default: every 10 minutes)
 * @returns {object} - The cron job
 */
function startDashboardScheduler(schedule = '*/10 * * * *') {
  const dashboardJob = cron.schedule(schedule, refreshDashboardMaterializedViews);
  console.log(`[DASHBOARD:INFO] [${new Date().toISOString()}] Dashboard materialized views scheduler started with schedule: ${schedule}`);
  
  // Run once immediately on startup
  refreshDashboardMaterializedViews();
  
  return dashboardJob;
}

/**
 * Stop the dashboard refresh scheduler
 * @param {object} job - The cron job to stop
 */
function stopDashboardScheduler(job) {
  if (job) {
    job.stop();
    console.log(`[DASHBOARD:INFO] [${new Date().toISOString()}] Dashboard materialized views scheduler stopped`);
  }
}

module.exports = {
  startDashboardScheduler,
  stopDashboardScheduler,
  refreshDashboardMaterializedViews
};
