// const console = require('./console');

function setupSocketHandler(io, workflowExecutionService) {
  // Middleware to handle authentication and connection setup
  io.use((socket, next) => {
    console.info("New socket connection attempt", { socketId: socket.id });
    next();
  });

  io.on("connection", (socket) => {
    console.info("Client connected", { socketId: socket.id, transport: socket.conn.transport.name });

    // Handle client errors
    socket.on("error", (error) => {
      console.error(`Socket error`, { socketId: socket.id, error: error.message, stack: error.stack });
    });

    socket.on("disconnect", async (reason) => {
      console.info("Client disconnected", { socketId: socket.id, reason });
    });

    // Handle workflow run start
    socket.on("workflowRun:start", async ({ workflowId }) => {
      console.info("Starting workflow", { workflowId, socketId: socket.id });

      try {
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();

        // Check if workflow exists
        const workflow = await prisma.workflow.findUnique({
          where: { id: workflowId }
        });

        if (!workflow) {
          throw new Error('Workflow not found');
        }

        // Create a new workflow run
        const workflowRun = await prisma.workflowRun.create({
          data: {
            workflowId,
            status: 'PENDING'
          }
        });

        // Start workflow execution
        await workflowExecutionService.startWorkflowRun(workflowRun.id);

        // Send the workflowRun ID back to the client
        socket.emit("workflowRun:created", {
          workflowRunId: workflowRun.id,
          workflowId: workflowId
        });
      } catch (error) {
        console.error("Error starting workflow", { workflowId, socketId: socket.id, error: error.message, stack: error.stack });
        socket.emit("workflowRun:error", { message: error.message });
      }
    });

    // Handle workflow run stop
    socket.on("workflowRun:stop", async ({ workflowRunId }) => {
      console.info("Stopping workflow run", { workflowRunId, socketId: socket.id });
      try {
        await workflowExecutionService.stopWorkflowRun(workflowRunId);
      } catch (error) {
        console.error("Error stopping workflow run", { workflowRunId, socketId: socket.id, error: error.message, stack: error.stack });
        socket.emit("workflowRun:error", { message: error.message });
      }
    });

    // Handle user task completion
    socket.on("userTask:complete", async ({ workflowRunId, nodeId, userId, taskData }) => {
      console.info("User completing task", { userId, workflowRunId, nodeId, socketId: socket.id });
      try {
        // Validate required parameters
        if (!workflowRunId || !nodeId || !userId) {
          throw new Error('Missing required parameters');
        }

        // Complete the user task
        await workflowExecutionService.completeUserTask(workflowRunId, nodeId, userId, taskData || {});

        // Notify the client that the task was completed
        socket.emit("userTask:completed", {
          workflowRunId,
          nodeId,
          success: true,
          message: 'Task completed successfully'
        });
      } catch (error) {
        console.error("Error completing user task", { userId, workflowRunId, nodeId, socketId: socket.id, error: error.message, stack: error.stack });
        socket.emit("userTask:error", {
          workflowRunId,
          nodeId,
          message: error.message
        });
      }
    });
  });

  return io;
}

module.exports = setupSocketHandler;