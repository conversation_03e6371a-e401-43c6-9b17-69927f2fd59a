require('module-alias/register');
require("dotenv").config();
const taskMonitor = require('@/cron/integration-state-updater');
const integrationScheduler = require('@/cron/integration-reloader');
const dashboardRefresh = require('@/cron/dashboard-refresh');
const { connectRedis } = require('@/config/redis');
require('module-alias/register');

const express = require("express");
const cors = require("cors");
const { clerkMiddleware } = require("@clerk/express");
const router = require("./routes/router");
const { prisma, connectPostgres } = require("./config/postgres");
const { connectMongo } = require("./config/mongo");

const { createServer } = require('http');
const { Server } = require('socket.io');
const setupSocketHandler = require('./lib/socketHandler');
const WorkflowExecutionService = require('./services/workflows/workflowExecutionService');

const workflowRoutes = require('./routes/workflows/workflowRoutes');
const workflowRunRoutes = require('./routes/workflows/workflowRunRoutes');
const nodeRunRoutes = require('./routes/workflows/nodeRunRoutes');
const userRoutes = require('./routes/workflows/userRoutes');
// const authRoutes = require('./routes/workflows/auth');
const taskRoutes = require('./routes/workflows/taskRoutes');
const fileRoutes = require('./routes/workflows/fileRoutes');
const speechToTextRoutes = require('./routes/workflows/speechToTextRoutes');



const morgan = require('morgan');

const app = express();
// app.use(express.json());
app.use(morgan('dev'));

const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization']
  },
  transports: ['websocket', 'polling'],
  allowEIO3: true,
  pingTimeout: 60000,
  pingInterval: 25000,
  connectTimeout: 45000
});

// Initialize workflow execution service
const workflowExecutionService = new WorkflowExecutionService(io);

// Make io and execution service available to routes
app.set('io', io);
app.set('workflowExecutionService', workflowExecutionService);

app.use((req, res, next) => {
  if (req.path === '/api/v1/stripe/webhook' || req.path === '/api/v1/auth/clerk-webhook') {
    express.raw({type: 'application/json'})(req, res, next);
  } else {
    express.json()(req, res, next);
  }
});

// app.use(cors());

const corsOptions = {
  origin: [
    "http://*************:3000",
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:3000/",
    "http://localhost:8087",
    "https://www.passisto.com/",
    "https://passisto.com/",
    "www.passisto.com/",
    "https://www.passisto.com",
    "https://enterprise.passisto.com/",
  ],
  methods: ["GET", "POST", "PUT", "DELETE","PATCH", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
};

app.use(cors(corsOptions));

app.use(
  express.urlencoded({
    extended: true,
  })
);
app.use(clerkMiddleware());

app.use((req, res, next) => {
  console.log(`[DEBUG] ${req.method} ${req.path}`);
  next();
});

app.get("/api/hello", (req, res) => {
  console.log(req.user);
  res.send("Hello, World!");
});

setupSocketHandler(io, workflowExecutionService);

app.use("/api/v1", router);


const PORT = process.env.PORT || 5000;

// Connect to PostgreSQL first, then MongoDB, then start the server
connectPostgres()
  .then(async () => {
    try {
      // Connect to MongoDB
      await connectMongo();
      
      // Start the HTTP server
      httpServer.listen(PORT, async () => {
        // Connect to Redis
        await connectRedis();
        console.log(`Server running on port ${PORT}`);
        taskMonitor.startMonitoring('*/1 * * * *');
        integrationScheduler.startScheduler('0 * * * *');
        dashboardRefresh.startDashboardScheduler('*/10 * * * *');
      });
    } catch (error) {
      console.error("MongoDB connection error:", error);
      throw error;
    }
  })
  .catch((error) => {
    console.error("Database connection error:", error);
  });
