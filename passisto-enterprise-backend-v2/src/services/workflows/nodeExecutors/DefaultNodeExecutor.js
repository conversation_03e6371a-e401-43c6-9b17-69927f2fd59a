const BaseNodeExecutor = require('./BaseNodeExecutor');

/**
 * Default executor for nodes without a specific executor
 */
class DefaultNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute a node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output;
    let success = true;
    let error = null;

    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get selected outputs for input processing
      const selectedOutputs = node.data?.selectedOutputs || [];

      // Process selected inputs using the universal system
      let processedInputs = inputs;
      if (selectedOutputs.length > 0) {
        processedInputs = await this.processSelectedInputs(inputs, selectedOutputs, workflowRunId);
        console.log(`[Default Node] Processed ${selectedOutputs.length} selected inputs`);
      }

      // For other node types
      let resultText = `Executed ${node.type} node with id ${nodeId}`;

      if (Object.keys(processedInputs).length > 0 && !processedInputs.isStartNode) {
        if (selectedOutputs.length > 0) {
          resultText += ` with ${selectedOutputs.length} selected inputs from previous nodes`;
        } else {
          resultText += " with inputs from previous nodes";
        }
      }

      output = {
        result: resultText,
        processedInputs: processedInputs,
        selectedOutputsCount: selectedOutputs.length
      };
    } catch (err) {
      success = false;
      error = err;
      output = { error: err.message, inputs };
    }

    return { output, success, error };
  }
}

module.exports = DefaultNodeExecutor;
