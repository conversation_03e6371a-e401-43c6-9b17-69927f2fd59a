const BaseNodeExecutor = require('./BaseNodeExecutor');

/**
 * Default executor for nodes without a specific executor
 */
class DefaultNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute a node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output;
    let success = true;
    let error = null;

    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For other node types
      let resultText = `Executed ${node.type} node with id ${nodeId}`;

      if (Object.keys(inputs).length > 0 && !inputs.isStartNode) {
        resultText += " with inputs from previous nodes";
      }

      output = {
        result: resultText,
        processedInputs: inputs
      };
    } catch (err) {
      success = false;
      error = err;
      output = { error: err.message, inputs };
    }

    return { output, success, error };
  }
}

module.exports = DefaultNodeExecutor;
