/**
 * Base class for all node executors
 */
class BaseNodeExecutor {
  /**
   * Constructor for the base node executor
   * @param {Object} prisma - Prisma client instance
   * @param {Object} io - Socket.io instance
   */
  constructor(prisma, io) {
    this.prisma = prisma;
    this.io = io;
  }

  /**
   * Execute a node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    throw new Error('Method not implemented. Each node executor must implement this method.');
  }

  /**
   * Process inputs from previous nodes
   * @param {Object} inputs - The inputs from previous nodes
   * @returns {string} - Formatted string of previous results
   */
  formatPreviousResults(inputs) {
    if (!inputs || Object.keys(inputs).length === 0 || inputs.isStartNode) {
      return '';
    }

    return Object.entries(inputs)
      .map(([sourceNodeId, output]) => {
        // Format the output - prefer result property if available
        const outputContent = output.result || JSON.stringify(output, null, 2);
        return `Input from node: ${sourceNodeId}: \n${outputContent}`;
      })
      .join('\n');
  }

  /**
   * Log node execution
   * @param {string} nodeId - The ID of the node
   * @param {string} nodeType - The type of the node
   * @param {Object} inputs - The inputs to the node
   * @param {Object} output - The output from the node
   */
  logExecution(nodeId, nodeType, inputs, output) {
    console.log(`[Node Execution] Node ${nodeId} (${nodeType}) - OUTPUTS:`, JSON.stringify(output, null, 2));
  }

  /**
   * Create execution log
   * @param {string} nodeId - The ID of the node
   * @param {string} nodeType - The type of the node
   * @param {Object} inputs - The inputs to the node
   * @param {Object} output - The output from the node
   * @param {boolean} success - Whether the execution was successful
   * @param {Error|null} error - Any error that occurred
   * @returns {Object} - The execution log
   */
  createExecutionLog(nodeId, nodeType, inputs, output, success, error) {
    return {
      nodeId,
      nodeType,
      executionTime: new Date().toISOString(),
      inputs,
      outputs: output,
      success,
      error: error ? error.message : null
    };
  }

  /**
   * Emit execution details via WebSockets
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {Object} executionLog - The execution log
   */
  emitExecutionDetails(workflowRunId, nodeId, executionLog) {
    this.io.emit('nodeExecution', {
      workflowRunId,
      nodeId,
      executionDetails: executionLog
    });
  }

  /**
   * Update node run status in the database
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {boolean} success - Whether the execution was successful
   * @param {Object} output - The output from the node
   * @param {Error|null} error - Any error that occurred
   */
  async updateNodeRunStatus(workflowRunId, nodeId, success, output, error) {
    await this.prisma.nodeRun.update({
      where: {
        workflowRunId_nodeId: {
          workflowRunId: workflowRunId,
          nodeId: nodeId
        }
      },
      data: {
        status: success ? 'SUCCESS' : 'FAILED',
        finishedAt: new Date(),
        output: error ? { ...output, error: error.message } : output
      }
    });
  }

  /**
   * Emit node completion via WebSockets
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {boolean} success - Whether the execution was successful
   * @param {Object} output - The output from the node
   * @param {Error|null} error - Any error that occurred
   */
  emitNodeCompletion(workflowRunId, nodeId, success, output, error) {
    this.io.emit('nodeRunProgress', {
      workflowRunId,
      nodeId,
      status: success ? 'SUCCESS' : 'FAILED',
      message: success ? `Node ${nodeId} completed successfully` : `Node ${nodeId} failed: ${error?.message}`,
      output
    });
  }
}

module.exports = BaseNodeExecutor;
