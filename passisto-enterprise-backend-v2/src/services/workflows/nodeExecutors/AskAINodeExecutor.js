const BaseNodeExecutor = require('./BaseNodeExecutor');
const openRouterService = require('../openRouterService');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Executor for Ask AI nodes
 */
class AskAINodeExecutor extends BaseNodeExecutor {
  /**
   * Execute an Ask AI node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output;
    let success = true;
    let error = null;

    try {
      // Get the prompt from the node data
      let prompt = node.data?.prompt || 'No prompt provided';

      // Check if the node has selected outputs
      const selectedOutputs = node.data?.selectedOutputs || [];

      // Process variables in the prompt content
      if (selectedOutputs.length > 0) {
        // Get all available node results
        const allNodeResults = await this.getAllNodeResults(workflowRunId);

        // Replace variables in the prompt
        prompt = this.replaceVariables(prompt, selectedOutputs, allNodeResults, inputs);

        console.log(`[AskAI Node] Processed variables in prompt`);
      }

      // Process selected inputs using the universal system
      const enhancedInputs = await this.processSelectedInputs(inputs, selectedOutputs, workflowRunId);

      // Only append context if no variables were used in the prompt
      // (to avoid duplication since variables replace the need for appended context)
      const hasVariablesInPrompt = selectedOutputs.length > 0 && /\{[^}]+\}/.test(node.data?.prompt || '');

      // if (!hasVariablesInPrompt) {
      //   // Format the selected results and append as context
      //   const previousResults = this.formatPreviousResults(enhancedInputs);
      //   if (previousResults) {
      //     prompt = `${prompt}\n\nContext from previous nodes:\n${previousResults}`;
      //   }
      // }

      // Get AI model options from node data or use defaults
      const modelOptions = {
        model: node.data?.model || undefined,
        maxTokens: node.data?.maxTokens ? parseInt(node.data.maxTokens) : undefined,
        temperature: node.data?.temperature ? parseFloat(node.data.temperature) : undefined
      };

      // Log the request
      console.log(`[AskAI Node] Sending request to OpenRouter API with model: ${modelOptions.model || 'default'}`);
      console.log(`[AskAI Node] Prompt: ${prompt.substring(0, 100)}...`);

      // Update node run status to show we're processing
      await this.prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId: workflowRunId,
            nodeId: nodeId
          }
        },
        data: {
          status: 'RUNNING',
          output: { status: 'Generating AI response...' }
        }
      });

      // Emit progress update
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'RUNNING',
        message: 'Generating AI response...'
      });

      // Call the OpenRouter API
      let aiResponse;
      try {
        aiResponse = await openRouterService.generateResponse(prompt, modelOptions);
      } catch (apiError) {
        // Check if this is the Gemini experimental model error
        if (modelOptions.model?.includes('gemini-2.5-pro-exp-03-25:free') &&
            apiError.message?.includes('limited to OpenRouter users who have purchased')) {
          console.log('[AskAI Node] Falling back to default model due to Gemini experimental model restrictions');
          // Fall back to default model
          const fallbackOptions = {
            ...modelOptions,
            model: undefined // Use the default model from OpenRouterService
          };
          aiResponse = await openRouterService.generateResponse(prompt, fallbackOptions);
        } else {
          // Re-throw if it's not the specific error we're handling
          throw apiError;
        }
      }

      // Create the output with the AI response
      output = {
        result: aiResponse.content,
        enhancedPrompt: prompt,
        processedInputs: inputs,
        selectedInputs: enhancedInputs, // Include the selected inputs
        availableInputs: await this.getAllUpstreamNodeResults(workflowRunId, nodeId, true), // Include all available inputs for reference
        model: aiResponse.model,
        usage: aiResponse.usage
      };

      console.log(`[AskAI Node] Received response from OpenRouter API: ${aiResponse.content.substring(0, 100)}...`);

    } catch (err) {
      success = false;
      error = err;
      output = {
        error: err.message,
        inputs
      };

      // Only reference prompt if it was defined
      if (typeof prompt !== 'undefined') {
        output.enhancedPrompt = prompt;
      } else {
        output.enhancedPrompt = 'Error occurred before prompt was constructed';
      }

      console.error(`[AskAI Node] Error:`, err);
    }

    return { output, success, error };
  }
  /**
   * Get all upstream node results recursively
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the current node
   * @param {boolean} simplified - Whether to return a simplified version of the results
   * @returns {Promise<Object>} - All upstream node results
   */


  /**
   * Get all upstream node results recursively
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the current node
   * @param {boolean} simplified - Whether to return a simplified version of the results
   * @returns {Promise<Object>} - All upstream node results
   */
  async getAllUpstreamNodeResults(workflowRunId, nodeId, simplified = false) {
    try {
      // Get the workflow run to access edges
      const workflowRun = await this.prisma.workflowRun.findUnique({
        where: { id: workflowRunId },
        include: {
          workflow: {
            select: {
              edges: true
            }
          }
        }
      });

      if (!workflowRun) {
        console.error(`Workflow run ${workflowRunId} not found`);
        return {};
      }

      // Parse edges
      let edges;
      try {
        edges = typeof workflowRun.workflow.edges === 'string'
          ? JSON.parse(workflowRun.workflow.edges)
          : workflowRun.workflow.edges;
      } catch (error) {
        console.error('Error parsing edges:', error);
        return {};
      }

      // Get all node runs for this workflow
      const allNodeRuns = await this.prisma.nodeRun.findMany({
        where: {
          workflowRunId: workflowRunId,
          status: 'SUCCESS' // Only include successful nodes
        }
      });

      // Create a map of node IDs to their outputs
      const nodeOutputs = {};
      for (const nodeRun of allNodeRuns) {
        if (nodeRun.output) {
          try {
            const output = typeof nodeRun.output === 'string'
              ? JSON.parse(nodeRun.output)
              : nodeRun.output;

            // Skip the current node
            if (nodeRun.nodeId !== nodeId) {
              if (simplified) {
                // For simplified version, just include the result if available
                nodeOutputs[nodeRun.nodeId] = {
                  result: output.result || JSON.stringify(output).substring(0, 500) + '...'
                };
              } else {
                nodeOutputs[nodeRun.nodeId] = output;
              }
            }
          } catch (error) {
            console.error(`Error parsing output for node ${nodeRun.nodeId}:`, error);
          }
        }
      }

      return nodeOutputs;
    } catch (error) {
      console.error(`Error getting all upstream node results:`, error);
      return {};
    }
  }


}

module.exports = AskAINodeExecutor;
