const BaseNodeExecutor = require('./BaseNodeExecutor');

/**
 * Executor for Decision nodes
 */
class DecisionNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute a Decision node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output = {};
    let success = true;
    let error = null;

    try {
      // Get the conditions from the node data
      // Support both new format (conditions array) and legacy format (single condition string)
      let conditions = node.data?.conditions || [];
      const legacyCondition = node.data?.condition || '';
      const operator = node.data?.operator || 'AND';

      // Get selected outputs for variable replacement
      const selectedOutputs = node.data?.selectedOutputs || [];

      // Process variables in conditions if selectedOutputs are configured
      if (selectedOutputs.length > 0) {
        // Get all available node results
        const allNodeResults = await this.getAllNodeResults(workflowRunId);

        // Replace variables in condition expressions
        conditions = conditions.map(condition => {
          if (condition.type === 'condition' && condition.expression) {
            return {
              ...condition,
              expression: this.replaceVariables(condition.expression, selectedOutputs, allNodeResults, inputs)
            };
          }
          return condition;
        });

        console.log(`[Decision Node] Processed variables in conditions`);
      }

      // Update node run status to show we're processing
      await this.prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId: workflowRunId,
            nodeId: nodeId
          }
        },
        data: {
          status: 'RUNNING',
          output: { status: 'Evaluating condition...' }
        }
      });

      // Emit progress update
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'RUNNING',
        message: 'Evaluating condition...'
      });

      // Get the workflow run to access all previous node outputs
      const workflowRun = await this.prisma.workflowRun.findUnique({
        where: { id: workflowRunId },
        include: {
          nodeRuns: {
            where: {
              status: 'SUCCESS'
            }
          }
        }
      });

      if (!workflowRun) {
        throw new Error(`Workflow run ${workflowRunId} not found`);
      }

      // Collect all previous node outputs
      const allNodeOutputs = {};
      for (const nodeRun of workflowRun.nodeRuns) {
        if (nodeRun.output && typeof nodeRun.output === 'object') {
          allNodeOutputs[nodeRun.nodeId] = nodeRun.output;
        }
      }

      // Determine which path to take based on the conditions and inputs
      let path = 'no'; // Default path is 'no'

      // Get conditions to evaluate (either from new format or legacy format)
      let conditionsToEvaluate = [];
      let operatorsToUse = [];

      if (conditions.length > 0) {
        // Handle new format with separate condition and operator items
        const conditionItems = conditions.filter(c => c.type === 'condition' && c.expression);
        const operatorItems = conditions.filter(c => c.type === 'operator');

        // Extract expressions from condition items
        conditionsToEvaluate = conditionItems.map(c => c.expression);

        // Extract operators from operator items, defaulting to the global operator if not found
        if (operatorItems.length > 0) {
          // Build array of operators to use between conditions
          for (let i = 0; i < conditionItems.length - 1; i++) {
            // Find an operator that comes after this condition
            const nextIndex = conditions.findIndex((item, idx) => {
              const conditionIndex = conditions.findIndex(c => c.id === conditionItems[i].id);
              return idx > conditionIndex && item.type === 'operator';
            });

            if (nextIndex !== -1) {
              operatorsToUse.push(conditions[nextIndex].value);
            } else {
              // Fallback to global operator if no specific operator found
              operatorsToUse.push(operator);
            }
          }
        } else {
          // If no operator items found, use the global operator for all
          for (let i = 0; i < conditionItems.length - 1; i++) {
            operatorsToUse.push(operator);
          }
        }
      } else if (legacyCondition) {
        // Handle legacy format
        conditionsToEvaluate = [legacyCondition];
      }

      // Log the conditions and inputs for debugging
      console.log(`[Decision Node] Conditions to evaluate:`, JSON.stringify(conditionsToEvaluate, null, 2));
      console.log(`[Decision Node] Operator: ${operator}`);
      console.log(`[Decision Node] Inputs:`, JSON.stringify(inputs, null, 2));

      // If there are no conditions, default to 'no'
      if (conditionsToEvaluate.length === 0) {
        console.log(`[Decision Node] No conditions to evaluate, defaulting to 'no' path`);
      } else {
        // Evaluate each condition
        const results = [];

        for (const condition of conditionsToEvaluate) {
          try {
            // First try simple text matching (legacy support)
            let conditionResult = false;

            // Convert condition to lowercase for case-insensitive comparison
            const conditionLower = condition.toLowerCase();

            // Check if any input contains the condition text
            const inputsText = JSON.stringify(inputs).toLowerCase();
            if (inputsText.includes(conditionLower)) {
              conditionResult = true;
            }

            // If simple matching didn't work, try JavaScript evaluation
            if (!conditionResult && (condition.includes('inputs.') || condition.includes("inputs["))) {
              // Use Function constructor to create a sandboxed evaluation
              const evalFunc = new Function('inputs', `return ${condition}`);

              // Evaluate the condition
              conditionResult = !!evalFunc(inputs);
              console.log(`[Decision Node] Condition "${condition}" evaluated to: ${conditionResult}`);
            }

            // Add result to results array
            results.push(conditionResult);
          } catch (evalError) {
            console.warn(`[Decision Node] Error evaluating condition: ${evalError.message}`);
            console.warn(`[Decision Node] Condition: "${condition}"`);
            console.warn(`[Decision Node] Inputs: ${JSON.stringify(inputs, null, 2)}`);

            // On error, add false to results
            results.push(false);
          }
        }

        // Determine final result based on operators between conditions
        if (results.length === 1) {
          // If there's only one condition, use its result directly
          path = results[0] ? 'yes' : 'no';
          console.log(`[Decision Node] Single condition evaluated to: ${results[0]}`);
        } else if (results.length > 1) {
          // For multiple conditions, evaluate them with their respective operators
          let finalResult = results[0]; // Start with the first result

          // Log the operators for debugging
          console.log(`[Decision Node] Operators to use:`, JSON.stringify(operatorsToUse, null, 2));

          // Apply each operator in sequence
          for (let i = 1; i < results.length; i++) {
            const currentOperator = operatorsToUse[i-1] || operator;
            const currentResult = results[i];

            if (currentOperator === 'AND') {
              finalResult = finalResult && currentResult;
              console.log(`[Decision Node] ${finalResult} AND ${currentResult} = ${finalResult && currentResult}`);
            } else { // OR
              finalResult = finalResult || currentResult;
              console.log(`[Decision Node] ${finalResult} OR ${currentResult} = ${finalResult || currentResult}`);
            }
          }

          path = finalResult ? 'yes' : 'no';
          console.log(`[Decision Node] Final result: ${finalResult} => Path: ${path}`);
        } else {
          // No conditions to evaluate
          path = 'no';
          console.log(`[Decision Node] No conditions to evaluate, defaulting to 'no' path`);
        }
      }

      // Create the output with the decision result
      output = {
        result: `Decision: ${path}`,
        conditions: conditionsToEvaluate,
        operators: operatorsToUse,
        path: path,
        processedInputs: inputs,
        allNodeOutputs: allNodeOutputs
      };

      console.log(`[Decision Node] Evaluated ${conditionsToEvaluate.length} conditions with custom operators. Result: ${path}`);

      // Update the edges in the workflow run to reflect the decision
      // This is handled by the workflow execution service, which will follow the appropriate edge

      // Get all edges from the workflow associated with this run
      console.log(`[Decision Node] Getting edges for workflow run: ${workflowRunId}`);
      const workflowRunData = await this.prisma.workflowRun.findUnique({
        where: { id: workflowRunId },
        include: {
          workflow: {
            select: {
              edges: true
            }
          }
        }
      });

      console.log(`[Decision Node] Workflow run data retrieved:`, JSON.stringify(workflowRunData, null, 2));
      if (workflowRunData && workflowRunData.workflow && workflowRunData.workflow.edges) {
        let edges = typeof workflowRunData.workflow.edges === 'string'
          ? JSON.parse(workflowRunData.workflow.edges)
          : workflowRunData.workflow.edges;

        // Find all edges from this node
        const nodeEdges = edges.filter(edge => edge.source === nodeId);

        // Mark the chosen path in the output
        output.availablePaths = nodeEdges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.label || edge.id,
          chosen: edge.id === path || edge.label === path
        }));
      }

    } catch (err) {
      success = false;
      error = err;
      output = {
        error: err.message,
        inputs,
        errorDetails: {
          nodeId,
          conditions: node.data?.conditions || [],
          legacyCondition: node.data?.condition,
          operator: node.data?.operator || 'AND',
          errorType: 'decision_node_error'
        }
      };
      console.error('[Decision Node] Error:', err);

      // Update node run with error information
      try {
        await this.prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'FAILED',
            output: {
              error: err.message,
              errorType: 'decision_node_error',
              conditions: node.data?.conditions || [],
              legacyCondition: node.data?.condition,
              operator: node.data?.operator || 'AND'
            }
          }
        });
      } catch (updateErr) {
        console.error(`[Decision Node] Error updating node run:`, updateErr);
      }
    }

    return { output, success, error };
  }
}

module.exports = DecisionNodeExecutor;
