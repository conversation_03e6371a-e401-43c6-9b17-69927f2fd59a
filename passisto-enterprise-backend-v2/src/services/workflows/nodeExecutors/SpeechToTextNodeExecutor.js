const BaseNodeExecutor = require('./BaseNodeExecutor');
const { RevAiApiClient } = require('revai-node-sdk');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * Executor for Speech-to-Text nodes
 */
class SpeechToTextNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute a speech-to-text node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {Object} node - The node data
   * @param {Object} inputs - Inputs from previous nodes
   * @returns {Object} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output = {};
    let success = true;
    let error = null;
    let waiting = false;

    try {
      // Check if there are pre-uploaded files for this node
      const preUploadedFiles = await this.prisma.file.findMany({
        where: { nodeId: node.id }
      });

      // If we have a source URL or pre-uploaded files, we can process immediately
      if (node.data.source || preUploadedFiles.length > 0) {
        console.log(`[SpeechToText Node] Processing with ${preUploadedFiles.length} pre-uploaded files or URL`);

        // Update node run status to show we're processing
        await this.prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'RUNNING',
            output: { status: 'Processing transcription...' }
          }
        });

        // Emit progress update
        this.io.emit('nodeRunProgress', {
          workflowRunId,
          nodeId,
          status: 'RUNNING',
          message: 'Processing transcription...'
        });

        // Process the file or URL using the selected transcription service
        let transcription = '';
        const service = node.data.service || 'simulated';

        try {
          if (service === 'revai') {
            // Use Rev.ai for transcription
            transcription = await this.processWithRevAi(node.data, preUploadedFiles);
          } else if (service === 'azure-whisper') {
            // Use Azure Whisper for transcription
            transcription = await this.processWithAzureWhisper(node.data, preUploadedFiles);
          } else {
            // Fallback to simulated transcription
            transcription = "Speaker 2    00:00:07    Hello Maria. Thanks for coming in for the interview.  Speaker 1    00:00:10    It's my pleasure. Thanks for inviting me.  Speaker 2    00:00:12    Well, as you know, the company has been expanding and we have an opening on our HR department. We are creating a new role for someone to lead our training and development within the company.  Speaker 1    00:00:22    Yes, I very much think that my skills and experience are a good fit for what you're looking for.  Speaker 2    00:00:27    That sounds great. Uh, so your CV looks strong though. It would be good if you could give us an overview in your own words of what you've been doing over the past four years or so. Well,  Speaker 1    00:00:37    In my first job four years ago, I was working for a small HR services provider, which offered HR services, including l and d to corporate clients.  Speaker 2    00:00:47    Okay, so it was only B2B?  Speaker 1    00:00:50    Yes. We only offered services to other companies, not B2C.  Speaker 2    00:00:55    Right. And it says here, you then left that company about three years ago.  Speaker 1    00:01:01    Yes, that's right. I was looking for a little more stability and also to be part of a larger organization. So I joined a company with around 100 staff and a small HR team. As there are only a few of us, we each deal with a range of HR topics in addition to payroll. One of the areas I was responsible for was learning and development.  Speaker 2    00:01:24    I see. And so why do you want to change jobs now?  Speaker 1    00:01:29    Well, I very much like the l and d side of my role, and I've always had particularly good feedback for my work in this area. I believe I excel in that field, so I'm looking to specialize. And as your company has around 2000 people, right?  Speaker 2    00:01:46    Yes, that's right. Well,  Speaker 1    00:01:48    An organization of this size would give me this scope to specialize in l and d. I'm also a big follower of your brand and feel fully aligned with your image and values.  Speaker 2    00:02:00    Well, that all sounds good. And I can see you have an l and d qualification.  Speaker 1    00:02:05    Yes. I got a diploma two years ago. I am also currently working on a further diploma in psychology with a specific focus on learning and performance management.  Speaker 2    00:02:15    Very good. Well, it looks like you have the qualifications and experience we're looking for. What do you think will be the main challenges of coming to a much larger company?  Speaker 1    00:02:26    I can see that it might be perceived as a weakness to not have experience in an organization of this size. Though I see that it could also be a benefit. I won't be bringing too many preconceived and possibly inflexible ideas with me to the world.  Speaker 2    00:02:44    Yes, that would be a good thing.  Speaker 1    00:02:46    Also, I'm used to taking a very personal approach to employee development. I realized that such an approach with 2000 staff members will have to happen in a different way, but I bring many ideas with me that can be replicated on a larger scale.  Speaker 2    00:03:02    I see what you mean. Right. So do you have any questions for me?  Speaker 1    00:03:07    Um, I think we've covered many of the areas I had wanted to address. I have two quick questions though.  Speaker 2    00:03:14    Go on.  Speaker 1    00:03:15    Who would I mostly work with on a daily basis?  Speaker 2    00:03:19    Well, there's the HR manager who you would report to, and then the HR team, which currently has six people in it. There's usually an intern or two who you can get some support from also.  Speaker 1    00:03:31    Okay. Thanks. That's all really clear. And my other question is how performance in this role will be measured? What does success look like?  Speaker 2    00:03:40    That's a good question. As you know, we have a performance management system in place, and from that we have identified some learning and development needs within the organization, but we haven't devised a strategy. Your role would be to devise and then successfully implement this strategy.  Speaker 1    00:03:59    Thank you. That sounds interesting.  Speaker 2    00:04:01    Great. So, uh, thanks again for coming in today. We'll be discussing all candidates next week, and then I'll get back to you by the end of next week to let you know the outcome.  Speaker 1    00:04:10    Thank you for your time. I'd welcome the opportunity to continue discussing this role with you.";

            // Check if we're in demo mode
            if (node.data.demoMode) {
              transcription += ' (Running in demo mode)';
            }
          }
        } catch (transcriptionError) {
          console.error('[SpeechToText Node] Transcription error:', transcriptionError);

          // Update node run with warning status but mark as SUCCESS so the workflow continues
          await this.prisma.nodeRun.update({
            where: {
              workflowRunId_nodeId: {
                workflowRunId: workflowRunId,
                nodeId: nodeId
              }
            },
            data: {
              status: 'SUCCESS', // Mark as SUCCESS so the workflow continues
              output: {
                error: `Transcription failed: ${transcriptionError.message}`,
                errorType: 'transcription_error',
                transcription: `Error during transcription: ${transcriptionError.message}`,
                source: node.data.source || (preUploadedFiles.length > 0 ? preUploadedFiles[0].originalName : 'Unknown'),
                sourceType: node.data.sourceType || 'audio',
                demoMode: node.data.demoMode || false,
                fileCount: preUploadedFiles.length
              }
            }
          });

          // Emit progress event with warning but mark as SUCCESS
          this.io.emit('nodeRunProgress', {
            workflowRunId,
            nodeId,
            status: 'SUCCESS', // Mark as SUCCESS so the workflow continues
            message: `Transcription completed with warning: ${transcriptionError.message}`,
            output: {
              error: `Transcription failed: ${transcriptionError.message}`,
              errorType: 'transcription_error',
              transcription: `Error during transcription: ${transcriptionError.message}`,
              source: node.data.source || (preUploadedFiles.length > 0 ? preUploadedFiles[0].originalName : 'Unknown'),
              sourceType: node.data.sourceType || 'audio',
              demoMode: node.data.demoMode || false,
              fileCount: preUploadedFiles.length
            }
          });

          // Return with warning but mark as SUCCESS
          return {
            output: {
              error: `Transcription failed: ${transcriptionError.message}`,
              errorType: 'transcription_error',
              transcription: `Error during transcription: ${transcriptionError.message}`,
              source: node.data.source || (preUploadedFiles.length > 0 ? preUploadedFiles[0].originalName : 'Unknown'),
              sourceType: node.data.sourceType || 'audio',
              demoMode: node.data.demoMode || false,
              fileCount: preUploadedFiles.length,
              processedInputs: inputs
            },
            success: true, // Mark as SUCCESS so the workflow continues
            error: null,
            waiting: false
          };
        }

        // If we have pre-uploaded files, associate them with the node run
        if (preUploadedFiles.length > 0) {
          // First, get the actual NodeRun record to get its ID
          const nodeRun = await this.prisma.nodeRun.findUnique({
            where: {
              workflowRunId_nodeId: {
                workflowRunId,
                nodeId
              }
            }
          });

          if (nodeRun) {
            console.log(`[SpeechToText Node] Associating ${preUploadedFiles.length} files with node run ID: ${nodeRun.id}`);

            // Update the files to be associated with the node run
            for (const file of preUploadedFiles) {
              await this.prisma.file.update({
                where: { id: file.id },
                data: {
                  nodeRunId: nodeRun.id,
                  nodeId: null // Remove the nodeId association
                }
              });
            }
          } else {
            console.error(`[SpeechToText Node] Could not find node run for workflowRunId=${workflowRunId}, nodeId=${nodeId}`);
          }
        }

        // Update node run with success
        await this.prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'SUCCESS',
            output: {
              transcription,
              source: node.data.source || (preUploadedFiles.length > 0 ? preUploadedFiles[0].originalName : 'Unknown'),
              sourceType: node.data.sourceType || 'audio',
              demoMode: node.data.demoMode || false,
              fileCount: preUploadedFiles.length
            },
            finishedAt: new Date()
          }
        });

        // Emit completion event
        this.io.emit('nodeRunProgress', {
          workflowRunId,
          nodeId,
          status: 'SUCCESS',
          message: 'Transcription completed',
          output: {
            transcription,
            source: node.data.source || (preUploadedFiles.length > 0 ? preUploadedFiles[0].originalName : 'Unknown'),
            sourceType: node.data.sourceType || 'audio',
            demoMode: node.data.demoMode || false,
            fileCount: preUploadedFiles.length
          }
        });

        return {
          output: {
            transcription,
            source: node.data.source || (preUploadedFiles.length > 0 ? preUploadedFiles[0].originalName : 'Unknown'),
            sourceType: node.data.sourceType || 'audio',
            demoMode: node.data.demoMode || false,
            fileCount: preUploadedFiles.length,
            processedInputs: inputs
          },
          success: true,
          error: null,
          waiting: false
        };
      } else {
        // No source URL or pre-uploaded files
        // Check if we're in demo mode
        if (node.data.demoMode) {
          console.log(`[SpeechToText Node] Running in demo mode without files`);

          // Update node run status to show we're processing
          await this.prisma.nodeRun.update({
            where: {
              workflowRunId_nodeId: {
                workflowRunId: workflowRunId,
                nodeId: nodeId
              }
            },
            data: {
              status: 'RUNNING',
              output: { status: 'Processing transcription in demo mode...' }
            }
          });

          // Emit progress update
          this.io.emit('nodeRunProgress', {
            workflowRunId,
            nodeId,
            status: 'RUNNING',
            message: 'Processing transcription in demo mode...'
          });

          // Generate a demo transcription
          const transcription = "This is a demo transcription generated without requiring a file upload. In a real workflow, you would need to upload a file or provide a URL.";

          // Update node run with success
          await this.prisma.nodeRun.update({
            where: {
              workflowRunId_nodeId: {
                workflowRunId: workflowRunId,
                nodeId: nodeId
              }
            },
            data: {
              status: 'SUCCESS',
              output: {
                transcription,
                source: 'Demo mode (no file required)',
                sourceType: node.data.sourceType || 'audio',
                demoMode: true,
                fileCount: 0
              },
              finishedAt: new Date()
            }
          });

          // Emit completion event
          this.io.emit('nodeRunProgress', {
            workflowRunId,
            nodeId,
            status: 'SUCCESS',
            message: 'Demo transcription completed',
            output: {
              transcription,
              source: 'Demo mode (no file required)',
              sourceType: node.data.sourceType || 'audio',
              demoMode: true,
              fileCount: 0
            }
          });

          return {
            output: {
              transcription,
              source: 'Demo mode (no file required)',
              sourceType: node.data.sourceType || 'audio',
              demoMode: true,
              fileCount: 0,
              processedInputs: inputs
            },
            success: true,
            error: null,
            waiting: false
          };
        }

        // Not in demo mode and no files - return an error
        console.log(`[SpeechToText Node] No source URL or files provided and not in demo mode - returning error`);

        // Update node run status to show error
        await this.prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'FAILED',
            output: {
              error: 'No file uploaded. Please upload a file before running the workflow.',
              errorType: 'missing_file_error'
            },
            finishedAt: new Date()
          }
        });

        // Emit error event
        this.io.emit('nodeRunProgress', {
          workflowRunId,
          nodeId,
          status: 'FAILED',
          message: 'No file uploaded. Please upload a file before running the workflow.'
        });

        // Return with error status
        return {
          output: {
            error: 'No file uploaded. Please upload a file before running the workflow.',
            errorType: 'missing_file_error',
            processedInputs: inputs
          },
          success: false,
          error: 'No file uploaded. Please upload a file before running the workflow.',
          waiting: false
        };
      }
    } catch (err) {
      success = false;
      error = err;
      output = { error: err.message, inputs };
      console.error('[SpeechToText Node] Error:', err);
    }

    return { output, success, error, waiting };
  }

  /**
   * Process transcription with Rev.ai
   * @param {Object} nodeData - The node data
   * @param {Array} files - The uploaded files
   * @returns {string} - The transcription text
   */
  async processWithRevAi(nodeData, files) {
    try {
      // Get the Rev.ai API key from environment variables
      const revaiApiKey = process.env.REVAI_ACCESS_TOKEN;

      if (!revaiApiKey) {
        throw new Error('Rev.ai API key not configured');
      }

      // Initialize the Rev.ai client
      const client = new RevAiApiClient(revaiApiKey);

      // Determine the source (URL or file)
      let source = nodeData.source;
      let jobId;

      if (source) {
        // Submit a job with a URL
        console.log(`[Rev.ai] Submitting job with URL: ${source}`);
        // Submit job with URL - using the newer API method
        const job = await client.submitJobUrl(source, {
          metadata: `Workflow Node: ${nodeData.label || 'Speech to Text'}`,
          callback_url: process.env.REVAI_CALLBACK_URL || null,
          skip_diarization: false,
          skip_punctuation: false,
          remove_disfluencies: true
        });
        jobId = job.id;
      } else if (files && files.length > 0) {
        // Submit a job with a local file
        const file = files[0];
        const filePath = file.path;

        console.log(`[Rev.ai] Submitting job with file: ${filePath}`);
        // Submit job with local file - using the newer API method
        const job = await client.submitJobLocalFile(filePath, {
          metadata: `Workflow Node: ${nodeData.label || 'Speech to Text'}`,
          callback_url: process.env.REVAI_CALLBACK_URL || null,
          skip_diarization: false,
          skip_punctuation: false,
          remove_disfluencies: true
        });
        jobId = job.id;
      } else {
        throw new Error('No source URL or file provided');
      }

      console.log(`[Rev.ai] Created job with ID: ${jobId}`);

      // Poll for job completion (simplified for demo)
      // In a production environment, you would use webhooks or a more sophisticated polling mechanism
      let jobDetails;
      let attempts = 0;
      const maxAttempts = 5; // 5 minutes max (10 seconds * 30)

      do {
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
        jobDetails = await client.getJobDetails(jobId);
        attempts++;
        console.log(`[Rev.ai] Job ${jobId} status: ${jobDetails.status}, attempt ${attempts}/${maxAttempts}`);
      } while (jobDetails.status === 'in_progress' && attempts < maxAttempts);

      if (jobDetails.status !== 'transcribed') {
        throw new Error(`Rev.ai job did not complete successfully. Status: ${jobDetails.status}`);
      }

      // Get the transcript as plain text
      const transcriptText = await client.getTranscriptText(jobId);

      return transcriptText || 'No transcription available';
    } catch (error) {
      console.error('[Rev.ai] Error:', error);
      throw new Error(`Rev.ai transcription failed: ${error.message}`);
    }
  }

  /**
   * Process a speech-to-text file or URL
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {Object} data - The processing data
   * @returns {Object} - The processing result
   */
  async processTranscription(workflowRunId, nodeId, data) {
    try {
      // Get the node run
      const nodeRun = await this.prisma.nodeRun.findUnique({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        },
        include: {
          files: true
        }
      });

      if (!nodeRun) {
        throw new Error('Node run not found');
      }

      // Update node run status to show we're processing
      await this.prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        },
        data: {
          status: 'RUNNING',
          output: { status: 'Processing transcription...' }
        }
      });

      // Emit progress update
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'RUNNING',
        message: 'Processing transcription...'
      });

      // In a real implementation, we would:
      // 1. Process the file or URL using a transcription service
      // 2. Return the transcription text

      // For now, we'll simulate a transcription
      const transcription = data.transcription || 'This is a simulated transcription. In a real implementation, we would process the audio file and return the actual transcription.';

      // Update node run with success
      await this.prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        },
        data: {
          status: 'SUCCESS',
          output: {
            transcription,
            source: data.source || (nodeRun.files.length > 0 ? nodeRun.files[0].originalName : 'Unknown'),
            sourceType: data.sourceType || 'audio',
            demoMode: data.demoMode || false
          },
          finishedAt: new Date()
        }
      });

      // Emit completion event
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'SUCCESS',
        message: 'Transcription completed',
        output: {
          transcription,
          source: data.source || (nodeRun.files.length > 0 ? nodeRun.files[0].originalName : 'Unknown'),
          sourceType: data.sourceType || 'audio',
          demoMode: data.demoMode || false
        }
      });

      return {
        success: true,
        output: {
          transcription,
          source: data.source || (nodeRun.files.length > 0 ? nodeRun.files[0].originalName : 'Unknown'),
          sourceType: data.sourceType || 'audio',
          demoMode: data.demoMode || false
        }
      };
    } catch (error) {
      console.error('[SpeechToText Node] Processing error:', error);

      // Update node run with error
      await this.prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        },
        data: {
          status: 'FAILED',
          output: { error: error.message },
          finishedAt: new Date()
        }
      });

      // Emit error event
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'FAILED',
        message: `Error: ${error.message}`
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process transcription with Azure Whisper
   * @param {Object} nodeData - The node data
   * @param {Array} files - The uploaded files
   * @returns {string} - The transcription text
   */
  async processWithAzureWhisper(nodeData, files) {
    try {
      // Get the Azure Speech API key from environment variables
      const azureSpeechKey = process.env.AZURE_SPEECH_KEY;

      if (!azureSpeechKey) {
        throw new Error('Azure Speech API key not configured');
      }

      // Use the specific Azure Whisper endpoint provided
      const whisperEndpoint = 'https://seffa-m5m9g4ho-eastus2.cognitiveservices.azure.com/openai/deployments/whisper/audio/transcriptions?api-version=2024-02-15-preview';
      console.log(`[Azure Whisper] Using endpoint: ${whisperEndpoint}`);

      // Check if we have files or a source URL
      if (files && files.length > 0) {
        const file = files[0];
        console.log(`[Azure Whisper] Processing file: ${file.originalName}, size: ${file.size} bytes, path: ${file.path}`);

        // Read the file as binary data
        const fs = require('fs');
        const fileData = fs.readFileSync(file.path);

        try {
          // Send the file to Azure Whisper for transcription
          console.log('[Azure Whisper] Sending file to Azure Whisper API...');
          const axios = require('axios');

          // Create a FormData object to properly send the file
          const FormData = require('form-data');
          const form = new FormData();

          // Add the file to the form data
          // The API expects a file with the name 'file'
          form.append('file', fileData, {
            filename: file.originalName || 'audio.wav',
            contentType: file.mimetype || 'audio/wav'
          });

          // Add required parameters
          form.append('model', 'whisper');
          form.append('response_format', 'json');

          console.log(`[Azure Whisper] File prepared: ${file.originalName}, size: ${fileData.length} bytes`);

          // Log the request details for troubleshooting
          console.log('[Azure Whisper] Request details:', {
            url: whisperEndpoint,
            headers: {
              'api-key': '***REDACTED***',
              ...form.getHeaders()
            },
            formData: {
              file: `[Binary data: ${fileData.length} bytes]`,
              model: 'whisper',
              response_format: 'json'
            }
          });

          // Send the request with the form data
          const response = await axios.post(
            whisperEndpoint,
            form,
            {
              headers: {
                'api-key': azureSpeechKey,
                ...form.getHeaders()
              }
            }
          );

          console.log('[Azure Whisper] Received response:', response.data);

          // Extract the transcription from the response
          const transcription = response.data.text || response.data.transcript || '';

          // Format the transcription
          let formattedTranscription = '';

          // Check if we have segments with timestamps
          if (response.data.segments && response.data.segments.length > 0) {
            // Format with timestamps if segments are available
            for (const segment of response.data.segments) {
              const startTime = this.formatTimeFromSeconds(segment.start || 0);
              const speaker = segment.speaker ? `Speaker ${segment.speaker}` : 'Speaker';
              formattedTranscription += `${speaker}    ${startTime}    ${segment.text}\n`;
            }
          } else if (response.data.words && response.data.words.length > 0) {
            // If we have word-level timestamps but no segments, create our own segments
            let currentTime = 0;
            let currentText = '';
            const segmentDuration = 10; // Create a new segment every 10 seconds

            for (const word of response.data.words) {
              if (word.start && word.start >= currentTime + segmentDuration) {
                // Start a new segment
                const startTime = this.formatTimeFromSeconds(currentTime);
                formattedTranscription += `Speaker    ${startTime}    ${currentText.trim()}\n`;
                currentTime = Math.floor(word.start);
                currentText = word.word + ' ';
              } else {
                // Add to current segment
                currentText += word.word + ' ';
              }
            }

            // Add the last segment
            if (currentText.trim()) {
              const startTime = this.formatTimeFromSeconds(currentTime);
              formattedTranscription += `Speaker    ${startTime}    ${currentText.trim()}\n`;
            }
          } else {
            // Just use the plain transcription if no timing information
            formattedTranscription = transcription;
          }

          console.log('[Azure Whisper] Transcription completed successfully');
          return formattedTranscription;
        } catch (apiError) {
          console.error('[Azure Whisper] API Error:', apiError);
          throw apiError;
        }
      } else if (nodeData.source) {
        console.log(`[Azure Whisper] URL source detected: ${nodeData.source}`);
        throw new Error('URL sources are not currently supported with Azure Whisper. Please upload a file instead.');
      } else {
        throw new Error('No source URL or file provided');
      }
    } catch (error) {
      console.error('[Azure Whisper] Error:', error);

      // Provide more detailed error information
      let errorMessage = `Azure Whisper transcription failed: ${error.message}`;

      // If we have a response object with more details, include them
      if (error.response) {
        const status = error.response.status;
        const statusText = error.response.statusText;
        const data = error.response.data ? JSON.stringify(error.response.data) : 'No response data';

        errorMessage += ` (Status: ${status} ${statusText}, Data: ${data})`;
        console.error(`[Azure Whisper] Response error details: Status ${status}, Data:`, error.response.data);
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Format time from seconds to HH:MM:SS format
   * @param {number} seconds - Time in seconds
   * @returns {string} - Formatted time string
   */
  formatTimeFromSeconds(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    // Format as HH:MM:SS
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}

module.exports = SpeechToTextNodeExecutor;
