const BaseNodeExecutor = require('./BaseNodeExecutor');
const emailService = require('../emailService');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Executor for Email nodes
 */
class EmailNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute an Email node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output;
    let success = true;
    let error = null;

    try {
      // Get email details from node data
      let to = node.data?.to;
      let subject = node.data?.subject || 'No Subject';
      let body = node.data?.body || '';

      // Check if the node has selected outputs
      const selectedOutputs = node.data?.selectedOutputs || [];

      // Process variables in the email content
      if (selectedOutputs.length > 0) {
        // Get all available node results
        const allNodeResults = await this.getAllNodeResults(workflowRunId);

        // Replace variables in to, subject, and body
        to = this.replaceVariables(to, selectedOutputs, allNodeResults, inputs);
        subject = this.replaceVariables(subject, selectedOutputs, allNodeResults, inputs);
        body = this.replaceVariables(body, selectedOutputs, allNodeResults, inputs);

        console.log(`[Email Node] Processed variables in email content`);
      }

      // Validate required fields
      if (!to) {
        throw new Error('Email recipient (to) is required');
      }

      // If we have inputs from previous nodes and no specific outputs are selected,
      // incorporate all inputs into the email body as before
      if (Object.keys(inputs).length > 0 && selectedOutputs.length === 0) {
        const previousResults = this.formatPreviousResults(inputs);
        if (previousResults) {
          body = `${body}\n\nWorkflow Context:\n${previousResults}`;
        }
      }

      // Update node run status to show we're processing
      await this.prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId: workflowRunId,
            nodeId: nodeId
          }
        },
        data: {
          status: 'RUNNING',
          output: { status: 'Sending email...' }
        }
      });

      // Emit progress update
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'RUNNING',
        message: 'Sending email...'
      });

      // Log the email details
      console.log(`[Email Node] Sending email to: ${to}`);
      console.log(`[Email Node] Subject: ${subject}`);
      console.log(`[Email Node] Body: ${body.substring(0, 100)}${body.length > 100 ? '...' : ''}`);

      // Send the email
      const emailResult = await emailService.sendEmail({
        to,
        subject,
        body,
        isHtml: false // Use plain text for now
      });

      // Create the output with the email result
      // Also pass through any important data from previous nodes
      const speechToTextData = inputs['speech-to-text'];

      output = {
        result: `Email sent successfully to ${to}`,
        emailDetails: {
          to,
          subject,
          bodyPreview: body.substring(0, 100) + (body.length > 100 ? '...' : '')
        },
        messageId: emailResult.messageId,
        // Pass through the transcription data if it exists
        ...(speechToTextData && speechToTextData.transcription && {
          transcription: speechToTextData.transcription,
          transcriptionSource: speechToTextData.source
        }),
        // Include all processed inputs
        processedInputs: inputs
      };

      console.log(`[Email Node] Email sent successfully: ${emailResult.messageId}`);

    } catch (err) {
      success = false;
      error = err;
      output = {
        error: err.message,
        inputs
      };

      console.error(`[Email Node] Error:`, err);
    }

    return { output, success, error };
  }
  /**
   * Get all node results for the workflow run
   * @param {string} workflowRunId - The ID of the workflow run
   * @returns {Promise<Object>} - All node results
   */
  async getAllNodeResults(workflowRunId) {
    try {
      // Get all node runs for this workflow
      const allNodeRuns = await this.prisma.nodeRun.findMany({
        where: {
          workflowRunId: workflowRunId,
          status: 'SUCCESS' // Only include successful nodes
        }
      });

      // Create a map of node IDs to their outputs
      const nodeOutputs = {};
      for (const nodeRun of allNodeRuns) {
        if (nodeRun.output) {
          try {
            const output = typeof nodeRun.output === 'string'
              ? JSON.parse(nodeRun.output)
              : nodeRun.output;

            nodeOutputs[nodeRun.nodeId] = output;
          } catch (error) {
            console.error(`Error parsing output for node ${nodeRun.nodeId}:`, error);
          }
        }
      }

      return nodeOutputs;
    } catch (error) {
      console.error(`Error getting all node results:`, error);
      return {};
    }
  }

  /**
   * Replace variables in text with values from node outputs
   * @param {string} text - The text to process
   * @param {Array} selectedOutputs - The selected outputs
   * @param {Object} allNodeResults - All node results
   * @param {Object} directInputs - Direct inputs to the node
   * @returns {string} - The processed text
   */
  replaceVariables(text, selectedOutputs, allNodeResults, directInputs) {
    if (!text) return text;

    // Create a combined source of node outputs
    const nodeOutputs = { ...directInputs, ...allNodeResults };

    // Replace variables in the format {nodeId.key}
    return text.replace(/\{([^}]+)\}/g, (match, path) => {
      const [nodeId, key] = path.split('.');

      // Check if this variable is in the selected outputs
      const isSelected = selectedOutputs.some(
        selection => selection.nodeId === nodeId && selection.key === key
      );

      if (!isSelected) {
        return match; // Keep the original if not selected
      }

      // Get the node output
      const nodeOutput = nodeOutputs[nodeId];
      if (!nodeOutput) {
        return match; // Keep the original if node output not found
      }

      // Extract the value
      let value;
      if (key === 'result') {
        value = nodeOutput.result;
      } else if (key.includes('.')) {
        // Handle nested paths
        const parts = key.split('.');
        let current = nodeOutput;
        for (const part of parts) {
          if (current && typeof current === 'object' && part in current) {
            current = current[part];
          } else {
            return match; // Keep the original if path not found
          }
        }
        value = current;
      } else {
        value = nodeOutput[key];
      }

      // Return the value or the original if undefined
      return value !== undefined ? String(value) : match;
    });
  }
}

module.exports = EmailNodeExecutor;
