const BaseNodeExecutor = require('./BaseNodeExecutor');

/**
 * Executor for Task nodes
 */
class TaskNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute a Task node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output;
    let success = true;
    let error = null;

    try {
      // For task nodes, include any inputs from previous nodes in the task description
      let taskDescription = node.data?.label || 'Unnamed task';
      let taskDetails = node.data?.description || '';

      // Get selected outputs (new universal system) or contextual variables (legacy)
      const selectedOutputs = node.data?.selectedOutputs || [];
      const selectedContextVars = node.data?.selectedContextVars || [];

      // Process variables in the task description and details
      if (selectedOutputs.length > 0) {
        // Get all available node results
        const allNodeResults = await this.getAllNodeResults(workflowRunId);

        // Replace variables in task description and details
        taskDescription = this.replaceVariables(taskDescription, selectedOutputs, allNodeResults, inputs);
        taskDetails = this.replaceVariables(taskDetails, selectedOutputs, allNodeResults, inputs);

        console.log(`[Task Node] Processed variables in task content`);
      }

      // Process inputs based on selected outputs or context variables
      let contextualData = {};
      if (selectedOutputs.length > 0) {
        // Use new universal system
        const processedInputs = await this.processSelectedInputs(inputs, selectedOutputs, workflowRunId);
        contextualData = this.formatContextualData(processedInputs, selectedOutputs);
      } else if (selectedContextVars.length > 0) {
        // Use legacy system for backward compatibility
        contextualData = this.processContextualData(inputs, selectedContextVars);
      }

      // Check if this is a user task that requires waiting for user input
      const isUserTask = node.data?.requiresUserInput === true;
      // Get assignee ID from either assignee or assigneeId field in node data
      const assigneeId = node.data?.assignee || node.data?.assigneeId;
      const formFields = node.data?.formFields || []; // Get form fields if available

      if (isUserTask && assigneeId) {
        // Update node run to waiting status with form fields
        await this.prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'WAITING_FOR_USER',
            assigneeId: assigneeId, // This is the correct field name in the schema
            assignedAt: new Date(),
            formData: { fields: formFields }, // Store form fields in the formData field
            output: {
              taskDetails: {
                title: node.data?.label || 'Task',
                description: taskDetails,
                formFields: formFields,
                contextualData: contextualData // Add contextual data from previous nodes
              }
            }
          }
        });

        // Emit waiting for user event with form fields and contextual data
        this.io.emit('nodeRunProgress', {
          workflowRunId,
          nodeId,
          status: 'WAITING_FOR_USER',
          message: `Waiting for user ${assigneeId} to complete task: ${taskDescription}`,
          assigneeId: assigneeId,
          formFields: formFields,
          contextualData: contextualData
        });

        // Return early with waiting status
        return {
          output: {
            result: `Waiting for user to complete task: ${taskDescription}`,
            enhancedDescription: taskDescription,
            formFields: formFields,
            contextualData: contextualData,
            processedInputs: inputs
          },
          success: true,
          error: null,
          waiting: true
        };
      }

      // For non-user tasks or if no assignee, complete immediately
      output = {
        result: `Task completed for: ${taskDescription}`,
        enhancedDescription: taskDescription,
        contextualData: contextualData,
        processedInputs: inputs
      };

    } catch (err) {
      success = false;
      error = err;
      output = { error: err.message, inputs };
    }

    return { output, success, error };
  }
  /**
   * Process contextual data from inputs based on selected variables
   * @param {Object} inputs - The inputs from previous nodes
   * @param {Array} selectedContextVars - Array of selected context variables
   * @returns {Object} - Processed contextual data
   */
  processContextualData(inputs, selectedContextVars) {
    if (!inputs || Object.keys(inputs).length === 0 || inputs.isStartNode) {
      return {};
    }

    // If no specific variables are selected, return empty object
    if (!selectedContextVars || selectedContextVars.length === 0) {
      return {};
    }

    const contextualData = {};

    // Process each selected context variable
    selectedContextVars.forEach(contextVar => {
      const { nodeId, key, label } = contextVar;

      // Check if we have data from this node
      if (inputs[nodeId]) {
        // Extract the specific key from the node output
        // If key is 'result', get the result directly, otherwise navigate the output object
        let value;
        if (key === 'result') {
          value = inputs[nodeId].result;
        } else if (key.includes('.')) {
          // Handle nested paths like 'output.text'
          const parts = key.split('.');
          let current = inputs[nodeId];
          for (const part of parts) {
            if (current && typeof current === 'object') {
              current = current[part];
            } else {
              current = undefined;
              break;
            }
          }
          value = current;
        } else {
          value = inputs[nodeId][key];
        }

        // Add to contextual data with the provided label
        if (value !== undefined) {
          contextualData[label || key] = value;
        }
      }
    });

    return contextualData;
  }

  /**
   * Format contextual data from processed inputs for display in task UI
   * @param {Object} processedInputs - The processed inputs from selected outputs
   * @param {Array} selectedOutputs - Array of selected output configurations
   * @returns {Object} - Formatted contextual data
   */
  formatContextualData(processedInputs, selectedOutputs) {
    const contextualData = {};

    // Process each selected output to create labeled contextual data
    selectedOutputs.forEach(selection => {
      const { nodeId, key, label } = selection;

      if (processedInputs[nodeId]) {
        let value;

        if (key === 'result') {
          value = processedInputs[nodeId].result;
        } else if (key.includes('.')) {
          // Handle nested paths
          const parts = key.split('.');
          let current = processedInputs[nodeId];
          for (const part of parts) {
            if (current && typeof current === 'object' && part in current) {
              current = current[part];
            } else {
              current = undefined;
              break;
            }
          }
          value = current;
        } else {
          value = processedInputs[nodeId][key];
        }

        // Add to contextual data with the provided label or default to key
        if (value !== undefined) {
          contextualData[label || `${nodeId}.${key}`] = value;
        }
      }
    });

    return contextualData;
  }
}

module.exports = TaskNodeExecutor;
