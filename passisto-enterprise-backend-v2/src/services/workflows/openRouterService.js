const axios = require('axios');
require('dotenv').config();

/**
 * Service for interacting with the OpenRouter AI API
 */
class OpenRouterService {
  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY;
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.defaultModel = process.env.OPENROUTER_DEFAULT_MODEL || 'anthropic/claude-3-opus';
    this.defaultMaxTokens = parseInt(process.env.OPENROUTER_MAX_TOKENS || '4096');

    if (!this.apiKey) {
      console.warn('OpenRouter API key not found in environment variables. AI functionality will be limited.');
    }
  }

  /**
   * Generate a response from the AI model
   * @param {string} prompt - The prompt to send to the AI
   * @param {Object} options - Additional options for the API call
   * @returns {Promise<string>} - The AI response
   */
  async generateResponse(prompt, options = {}) {
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found. Using mock response.');
      return `[Mock AI Response] Response to: ${prompt}`;
    }

    try {
      const model = options.model || this.defaultModel;
      const maxTokens = options.maxTokens || this.defaultMaxTokens;
      const temperature = options.temperature || 0.7;

      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: model,
          messages: [
            { role: 'user', content: prompt }
          ],
          max_tokens: maxTokens,
          temperature: temperature,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            // 'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
            // 'X-Title': 'Workflow AI Node'
          }
        }
      );

      // Extract the response content
      if (!response.data.choices || !response.data.choices.length) {
        console.error('Invalid response from OpenRouter API:', response.data);
        throw new Error('Invalid response from OpenRouter API: No choices returned');
      }

      const content = response.data.choices[0]?.message?.content || '';

      // Return additional metadata for debugging and monitoring
      return {
        content,
        model: response.data.model,
        usage: response.data.usage,
        id: response.data.id
      };
    } catch (error) {
      console.error('Error calling OpenRouter API:', error.response?.data || error.message);

      // Provide detailed error information
      const errorDetails = error.response?.data?.error || { message: error.message };
      throw new Error(`OpenRouter API error: ${errorDetails.message}`);
    }
  }

  /**
   * Generate a response with streaming
   * @param {string} prompt - The prompt to send to the AI
   * @param {function} onChunk - Callback for each chunk of the response
   * @param {Object} options - Additional options for the API call
   * @returns {Promise<string>} - The complete AI response
   */
  async generateStreamingResponse(prompt, onChunk, options = {}) {
    if (!this.apiKey) {
      console.warn('OpenRouter API key not found. Using mock response.');
      const mockResponse = `[Mock AI Response] Response to: ${prompt}`;
      onChunk(mockResponse);
      return mockResponse;
    }

    try {
      const model = options.model || this.defaultModel;
      const maxTokens = options.maxTokens || this.defaultMaxTokens;
      const temperature = options.temperature || 0.7;

      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: model,
          messages: [
            { role: 'user', content: prompt }
          ],
          max_tokens: maxTokens,
          temperature: temperature,
          stream: true,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
            'X-Title': 'Workflow AI Node'
          },
          responseType: 'stream'
        }
      );

      let fullContent = '';

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk) => {
          try {
            const lines = chunk.toString().split('\n').filter(line => line.trim() !== '');

            for (const line of lines) {
              if (line.includes('[DONE]')) continue;

              if (line.startsWith('data: ')) {
                const jsonData = JSON.parse(line.substring(6));
                const content = jsonData.choices[0]?.delta?.content || '';

                if (content) {
                  fullContent += content;
                  onChunk(content);
                }
              }
            }
          } catch (err) {
            console.error('Error parsing streaming response:', err);
          }
        });

        response.data.on('end', () => {
          resolve({
            content: fullContent,
            model: model
          });
        });

        response.data.on('error', (err) => {
          reject(err);
        });
      });
    } catch (error) {
      console.error('Error with streaming OpenRouter API:', error.response?.data || error.message);
      throw new Error(`OpenRouter API streaming error: ${error.message}`);
    }
  }
}

// Create a singleton instance
const openRouterService = new OpenRouterService();

module.exports = openRouterService;
