const nodemailer = require('nodemailer');
require('dotenv').config();

/**
 * Service for sending emails
 */
class EmailService {
  constructor() {
    // Check if SMTP settings are configured
    this.isConfigured = !!(
      process.env.SMTP_HOST &&
      process.env.SMTP_PORT &&
      process.env.SMTP_USER &&
      process.env.SMTP_PASS
    );

    if (this.isConfigured) {
      // Create reusable transporter object using SMTP transport
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT, 10),
        secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });
    } else {
      console.warn('Email service not configured. Set SMTP_HOST, SMTP_PORT, SMTP_USER, and SMTP_PASS in .env file.');
    }
  }

  /**
   * Send an email
   * @param {Object} options - Email options
   * @param {string} options.to - Recipient email address
   * @param {string} options.subject - Email subject
   * @param {string} options.body - Email body (HTML or plain text)
   * @param {string} options.from - Sender email address (optional, defaults to SMTP_USER)
   * @param {boolean} options.isHtml - Whether the body is HTML (optional, defaults to true)
   * @returns {Promise<Object>} - The email sending result
   */
  async sendEmail({ to, subject, body, from = process.env.SMTP_FROM || process.env.SMTP_USER, isHtml = true }) {
    if (!this.isConfigured) {
      console.warn('Email service not configured. Using mock email service.');
      return this.mockSendEmail({ to, subject, body, from, isHtml });
    }

    try {
      // Validate email parameters
      if (!to || !subject || !body) {
        throw new Error('Missing required email parameters: to, subject, or body');
      }

      // Set up email options
      const mailOptions = {
        from,
        to,
        subject,
        ...(isHtml ? { html: body } : { text: body }),
      };

      // Send email
      const info = await this.transporter.sendMail(mailOptions);
      console.log(`Email sent: ${info.messageId}`);
      return {
        success: true,
        messageId: info.messageId,
        response: info.response,
      };
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  /**
   * Mock sending an email (for development/testing)
   * @param {Object} options - Email options
   * @returns {Promise<Object>} - Mock email sending result
   */
  async mockSendEmail({ to, subject, body, from, isHtml }) {
    console.log('MOCK EMAIL SERVICE');
    console.log('------------------');
    console.log(`From: ${from}`);
    console.log(`To: ${to}`);
    console.log(`Subject: ${subject}`);
    console.log(`Content-Type: ${isHtml ? 'text/html' : 'text/plain'}`);
    console.log('------------------');
    console.log(body);
    console.log('------------------');

    return {
      success: true,
      messageId: `mock-email-${Date.now()}@localhost`,
      response: 'Mock email sent successfully',
    };
  }
}

// Create a singleton instance
const emailService = new EmailService();

module.exports = emailService;
