const ftp = require('basic-ftp');
const SftpClient = require('ssh2-sftp-client');
const prisma = require('@/config/db');

async function checkFtpConnection(server, port, username, password, isSecure) {
  if (isSecure) {
    // Use ssh2-sftp-client for SFTP
    const sftp = new SftpClient();
    try {
      await sftp.connect({
        host: server,
        port: port || 22, // Default SFTP port is 22
        username,
        password,
      });
      console.log(`[INFO] SFTP connection successful to ${server}:${port}`);
      await sftp.end();
      return true;
    } catch (error) {
      console.error(`[ERROR] Failed SFTP connection to ${server}:${port} -`, error);
      return false;
    }
  } else {
    // Use basic-ftp for FTP
    const client = new ftp.Client();
    client.ftp.verbose = false;
    try {
      await client.access({
        host: server,
        user: username,
        password: password,
        secure: isSecure,
        port: port || 21, // Default FTP port is 21
      });
      console.log(`[INFO] FTP connection successful to ${server}:${port}`);
      await client.close();
      return true;
    } catch (error) {
      console.error(`[ERROR] Failed FTP connection to ${server}:${port} -`, error);
      return false;
    }
  }
}

async function checkFtpDuplicate(server, username, companyId) {
  const existing = await prisma.Integration.findFirst({
    where: {
      providerType: 'ftp',
      companyId,
      ftp: {
        server,
        username,
      },
    },
    include: { ftp: true },
  });

  return !!existing;
}

module.exports = {
  checkFtpConnection,
  checkFtpDuplicate,
};

