const prisma = require('@/config/db');
const axios = require('axios');

async function checkWebDuplicate({ url, companyId }) {
  const existing = await prisma.Integration.findFirst({
    where: {
      providerType: 'web',
      companyId,
      web: {
        url,
      },
    },
    include: { web: true },
  });

  return !!existing;
}

async function checkWebUrlReachable(url) {
  try {
    const response = await axios.get(url, { timeout: 5000 });
    return response.status >= 200 && response.status < 400;
  } catch (error) {
    console.error(`[ERROR] Web integration URL unreachable: ${url}`, error.message);
    return false;
  }
}

module.exports = {
  checkWebDuplicate,
  checkWebUrlReachable,
};
