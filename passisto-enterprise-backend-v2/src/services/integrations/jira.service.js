const prisma = require("@/config/db");
const Jira<PERSON>pi =  require('jira-client');

async function checkJiraDuplicate(domain, project, companyId) {
    const existing = await prisma.Integration.findFirst({
      where: {
        providerType: 'jira',
        companyId,
        jira: {
          domain,
          project,
        },
      },
      include: { jira: true },
    });
  
    return !!existing;
  }


async function checkJiraProjectExists(domain, email, token, project) {
  const jira = new JiraApi({
        protocol: 'https',
        host: domain.replace(/^https?:\/\//, ''), // Ensure clean host
        username: email,
        password: token,
        apiVersion: '2',
        strictSSL: true,
    });
    try {
        const result = await jira.getProject(project);
        return !!result;
    } catch (error) {
      console.error(`[ERROR] Failed to validate Jira project "${project}":`, error);
      return false;
    }
}
  
module.exports = { checkJiraDuplicate, checkJiraProjectExists };
  