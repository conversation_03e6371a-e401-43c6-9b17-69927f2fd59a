const fs = require("fs");
const multer = require("multer");
const csvParser = require("csv-parser");
const xlsx = require("xlsx");
const upload = multer({ dest: "uploads/" });

const parseFile = (file) => {
  return new Promise((resolve, reject) => {
    const candidates = [];

    const ext = file.originalname.split(".").pop();

    if (ext === "csv") {
      // CSV parsing
      fs.createReadStream(file.path)
        .pipe(csvParser())
        .on("data", (data) => {
          candidates.push({
            fullName: data["Full Name"],
            email: data["Email"],
          });
        })
        .on("end", () => resolve(candidates))
        .on("error", reject);
    } else if (ext === "xlsx") {
      // Excel parsing
      const workbook = xlsx.readFile(file.path);
      const sheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = xlsx.utils.sheet_to_json(sheet);

      data.forEach((row) => {
        candidates.push({ fullName: row["Full Name"], email: row["Email"] });
      });

      resolve(candidates);
    } else {
      reject("Unsupported file type");
    }
  });
};

module.exports = { upload, parseFile };
