const nodemailer = require("nodemailer");
const fs = require("fs").promises;
const path = require("path");
const translations = require("./otp/content");

const transport = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: true,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

const sendInviteMail = async (email, password) => {
  const platformLink = process.env.FRONTEND_URL || 'https://enterprise.passisto.com';
  console.log(`Sending invite email with platform link: ${platformLink}`);
  
  const filePath = path.join(__dirname, "../templates/inviteTemplate.html");
  const templateContent = await fs.readFile(filePath, "utf-8");
  const emailContent = templateContent
    .replace("{{platform_link}}", platformLink)
    .replace("{{platformLink}}", platformLink)
    .replace("{{email}}", email)
    .replace("{{password}}", password);
  const mailOptions = {
    from: process.env.DEFAULT_FROM_EMAIL,
    to: email,
    subject: "You're Invited to Passisto Platform!",
    html: emailContent,
  };
  
  try {
    const info = await transport.sendMail(mailOptions);
    console.log("Invite email sent: ", info.response);
    return true;
  } catch (error) {
    console.error("Error sending invite email: ", error);
    return false;
  }
};

const sendInterviewMail = async (
  candidateName,
  jobRole,
  companyName,
  interviewLink,
  email,       
  password,  
  companyEmail
) => {
  const filePath = path.join(
    __dirname,
    "../templates/CandidateInterviewConfirmation.html"
  );
  const templateContent = await fs.readFile(filePath, "utf-8");
  let emailContent = templateContent
    .replace(/{{CANDIDAT_NAME}}/g, candidateName)
    .replace(/{{JOB_ROLE}}/g, jobRole)
    .replace(/{{COMPANY_NAME}}/g, companyName)
    .replace(/{{INTERVIEW_LINK}}/g, interviewLink)
    .replace(/{{CANDIDAT_EMAIL}}/g, email)
    .replace(/{{CANDIDAT_PASSWORD}}/g, password)
    .replace(/{{COMPANY_EMAIL}}/g, companyEmail);
  const mailOptions = {
    from: process.env.DEFAULT_FROM_EMAIL,
    to: email,  // To: email (from function call)
    subject: `Please Join Your Interview for ${jobRole} ${companyName}`,
    html: emailContent,
  };
  try {
    const info = await transport.sendMail(mailOptions);
    console.log("Invite email sent: ", info.response);
    return true;
  } catch (error) {
    console.error("Error sending invite email: ", error);
    return false;
  }
};

const sendEmailBuilder = async (fromName, to, htmlBody, subject = "Email From Passisto") => {
    const mailOptions = {
        from: `${fromName} <${process.env.DEFAULT_FROM_EMAIL}>`,
        to: to,
        subject: subject,
        html: htmlBody
    };
    
    try {
        const info = await transport.sendMail(mailOptions);
        console.log("Email sent: ", info.response);
        return true;
    } catch (error) {
        console.error("Error sending email: ", error);
        return false;
    }
};

const sendEmail = async (fromName, to, htmlBody, subject = "Email From Passisto") => {
  const mailOptions = {
      from: `${fromName} <${process.env.DEFAULT_FROM_EMAIL}>`,
      to: to,
      subject: subject,
      html: htmlBody
  };
  
  try {
      const info = await transport.sendMail(mailOptions);
      console.log("Email sent: ", info.response);
      return true;
  } catch (error) {
      console.error("Error sending email: ", error);
      return false;
  }
};


const sendEmailNotification = async (data) => {
  const { firstName, lastName, company, position, email, message } = data;
  const emailContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>New Demo Request</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
      color: #333;
    }

    .container {
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .header {
      background-color: #00764c;
      color: white;
      text-align: center;
      padding: 30px 20px;
    }

    .header img {
      max-width: 140px;
      margin-bottom: 10px;
    }

    .header h1 {
      margin: 0;
      font-size: 22px;
      font-weight: 600;
    }

    .content {
      padding: 30px 25px;
    }

    .content h2 {
      margin-top: 0;
      color: #00764c;
    }

    .content p {
      margin: 10px 0;
    }

    .content .field-label {
      font-weight: bold;
      color: #555;
    }

    .product-info {
      margin-top: 25px;
      padding: 15px;
      background-color: #f1fdf5;
      border-left: 4px solid #00764c;
    }

    .product-info p {
      margin: 8px 0;
      font-size: 14.5px;
    }

    .footer {
      text-align: center;
      font-size: 13px;
      color: #777;
      padding: 20px;
      background-color: #f4f4f4;
    }

    .footer a {
      color: #00764c;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://www.passisto.com/_next/image?url=%2Fimages%2Flogo%2Flogo-white.png&w=256&q=75" alt="Passisto Logo">
      <h1>New Demo Request</h1>
    </div>

    <div class="content">
      <h2>You've received a new demo request for Passisto Enterprise</h2>
      <p><span class="field-label">First Name:</span> ${data.firstName}</p>
      <p><span class="field-label">Last Name:</span> ${data.lastName}</p>
      <p><span class="field-label">Company:</span> ${data.company}</p>
      <p><span class="field-label">Position:</span> ${data.position}</p>
      <p><span class="field-label">Email:</span> ${data.email}</p>
      <p><span class="field-label">Message:</span><br>${data.message}</p>
      <div class="product-info">
        <p><strong>Internal Note - Passisto Enterprise Demo Lead:</strong></p>
        <p>A new potential client has requested a demo of <strong>Passisto Enterprise</strong>.</p>
        <p>This lead may be seeking advanced features. Please review the message and details provided to determine the appropriate next steps for follow-up.</p>
     </div>
    </div>
    <div class="footer">
      &copy; ${new Date().getFullYear()} Passisto. All rights reserved.<br>
      <a href="https://www.passisto.com">www.passisto.com</a>
    </div>
  </div>
</body>
</html>`;

  const mailOptions = {
    from: process.env.DEFAULT_FROM_EMAIL,
    to: process.env.TEAM_NOTIFICATION_EMAIL,
    subject: "New Demo Request Notification",
    html: emailContent,
  };

  try {
    const info = await transport.sendMail(mailOptions);
    console.log("Notification email sent: ", info.response);
    return true;
  } catch (error) {
    console.error("Error sending notification email: ", error);
    return false;
  }
};


const sendEmailVerification = async (email, verificationCode, language) => {
  const t = translations[language] || translations.en;

  const emailContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>${t.title}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      background: #ffffff;
      padding: 0;
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background-color: #00764c;
      padding: 30px 20px;
      text-align: center;
    }
    .header img {
      max-width: 160px;
      height: auto;
      margin-bottom: 10px;
    }
    .header-title {
      color: #ffffff;
      font-size: 22px;
      font-weight: 600;
    }
    .content {
      padding: 30px 20px;
      text-align: center;
    }
    .content p {
      font-size: 16px;
      color: #333;
      margin: 16px 0;
    }
    .code-box {
      display: inline-block;
      background-color: #f9f9f9;
      border: 1px solid #ccc;
      border-radius: 6px;
      padding: 12px 20px;
      margin: 24px 0;
      font-size: 22px;
      font-weight: bold;
      color: #00764c;
      font-family: monospace;
      user-select: all;
    }
    .note {
      font-size: 14px;
      color: #666;
      margin-top: 8px;
    }
    .footer {
      background-color: #f4f4f4;
      text-align: center;
      font-size: 12px;
      color: #888;
      padding: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://www.passisto.com/_next/image?url=%2Fimages%2Flogo%2Flogo-white.png&w=256&q=75" alt="Passisto Logo" />
      <div class="header-title">${t.title}</div>
    </div>
    <div class="content">
      <p>${t.thankYou}</p>
      <div class="code-box">${verificationCode}</div>
      <div class="note">${t.tapToCopy}</div>
      <p>${t.disclaimer}</p>
    </div>
    <div class="footer">
      &copy; ${new Date().getFullYear()} Passisto. All rights reserved.
    </div>
  </div>
</body>
</html>`;

  const mailOptions = {
    from: process.env.DEFAULT_FROM_EMAIL,
    to: email,
    subject: "Verify Your Email Address",
    html: emailContent,
  };
  try {
    const info = await transport.sendMail(mailOptions);
    console.log("Verification email sent: ", info.response);
    return true;
  } catch (error) {
    console.error("Error sending verification email: ", error);
    return false;
  }
};

module.exports = { sendInviteMail, sendEmailBuilder, sendInterviewMail, sendEmail, sendEmailNotification, sendEmailVerification };
