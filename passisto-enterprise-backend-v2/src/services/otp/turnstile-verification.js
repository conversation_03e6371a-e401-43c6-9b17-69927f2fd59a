const axios = require('axios');

const SECRET_KEY = process.env.TURNSTILE_SECRET_KEY || '1x0000000000000000000000000000000AA';
const processedRequests = new Map(); // Store processed idempotency keys

const verifyTurnstileChallenge = async (turnstileToken, remoteIp, idempotency_key) => {
  try {
    // Check if the idempotency key has already been processed
    if (processedRequests.has(idempotency_key)) {
      console.warn('Duplicate request detected with idempotency key:', idempotency_key);
      throw new Error('Duplicate request detected');
    }

    const verificationResponse = await axios.post(
      'https://challenges.cloudflare.com/turnstile/v0/siteverify',
      new URLSearchParams({
        secret: SECRET_KEY,
        response: turnstileToken,
        remoteip: remoteIp || '',
      }).toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const { success } = verificationResponse.data;

    if (!success) {
      console.warn('Turnstile token verification failed');
      throw new Error('Invalid Turnstile token');
    }

    // Mark the idempotency key as processed
    processedRequests.set(idempotency_key, true);

    return success;
  } catch (error) {
    console.error('Error verifying Turnstile challenge:', error);
    throw new Error('Failed to verify Turnstile challenge');
  }
};

module.exports = { verifyTurnstileChallenge };