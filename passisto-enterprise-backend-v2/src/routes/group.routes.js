const express = require("express");
const {
  getAllGroups,
  getGroupById,
  createGroup,
  updateGroup,
  addMembersToGroup,
  removeMemberFromGroup,
  deleteGroup,
  getGroupIntegrations,
} = require("../controllers/group.controller.js");
const { authMiddleware } = require("../middlewares/authMiddleware.js");
const checkPermission = require("../middlewares/permMiddleware.js");

const groupRouter = express.Router();

// Get all groups for the company
groupRouter.get(
  "/all",
  authMiddleware,
  checkPermission([
    "CAN_MANAGE_GROUPS",
    "CAN_CREATE_GROUP",
    "CAN_UPDATE_GROUP",
    "CAN_DELETE_GROUP",
    "CAN_VIEW_GROUP",
    "CAN_ASSIGN_USER_TO_GROUP",
    "CAN_REMOVE_USER_FROM_GROUP",
  ]),
  getAllGroups
);

// Get a group by ID with its members
groupRouter.get(
  "/:groupId",
  authMiddleware,
  checkPermission([
    "CAN_MANAGE_GROUPS",
    "CAN_CREATE_GROUP",
    "CAN_UPDATE_GROUP",
    "CAN_DELETE_GROUP",
    "CAN_VIEW_GROUP",
    "CAN_ASSIGN_USER_TO_GROUP",
    "CAN_REMOVE_USER_FROM_GROUP",
  ]),
  getGroupById
);

// Create a new group
groupRouter.post(
  "/",
  authMiddleware,
  checkPermission(["CAN_MANAGE_GROUPS", "CAN_CREATE_GROUP"]),
  createGroup
);

// Update an existing group
groupRouter.put(
  "/:groupId",
  authMiddleware,
  checkPermission(["CAN_MANAGE_GROUPS", "CAN_UPDATE_GROUP"]),
  updateGroup
);

// Delete a group
groupRouter.delete(
  "/:groupId",
  authMiddleware,
  checkPermission(["CAN_MANAGE_GROUPS", "CAN_DELETE_GROUP"]),
  deleteGroup
);

// Add members to a group
groupRouter.post(
  "/:groupId/members",
  authMiddleware,
  checkPermission(["CAN_MANAGE_GROUPS", "CAN_ASSIGN_USER_TO_GROUP"]),
  addMembersToGroup
);

// Remove a member from a group
groupRouter.delete(
  "/:groupId/members/:userId",
  authMiddleware,
  checkPermission(["CAN_MANAGE_GROUPS", "CAN_REMOVE_USER_FROM_GROUP"]),
  removeMemberFromGroup
);

// Get all integrations for a group
groupRouter.get(
  "/:groupId/integrations",
  authMiddleware,
  checkPermission([
    "CAN_MANAGE_GROUPS",
    "CAN_VIEW_GROUP",
    "CAN_MANAGE_INTEGRATIONS",
    "CAN_READ_ALL_INTEGRATIONS",
    "CAN_ASSIGN_INTEGRATION_TO_GROUP",
    "CAN_REMOVE_INTEGRATION_FROM_GROUP",
  ]),
  getGroupIntegrations
);

module.exports = groupRouter;
