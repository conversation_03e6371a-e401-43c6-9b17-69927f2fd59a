const express = require('express');
const router = express.Router();
const multer = require('multer');
const fileController = require('../../controllers/workflows/fileController');
const { authMiddleware } = require('../../middlewares/authMiddleware');


// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  }
});

// Apply authentication middleware to all routes
router.use(authMiddleware)

// Upload a file
router.post('/upload', upload.single('file'), fileController.uploadFile);

// Get file by ID
router.get('/:id', fileController.getFileById);

// Get files by node run ID
router.get('/node-run/:nodeRunId', fileController.getFilesByNodeRun);

// Get files by node ID
router.get('/node/:nodeId', fileController.getFilesByNodeId);

// Download file
router.get('/download/:filename', fileController.downloadFile);

// Delete file
router.delete('/:id', fileController.deleteFile);

module.exports = router;
