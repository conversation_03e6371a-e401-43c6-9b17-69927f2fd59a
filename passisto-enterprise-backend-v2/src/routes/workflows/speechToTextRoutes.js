const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const speechToTextController = require('../../controllers/workflows/speechToTextController');
const { authMiddleware } = require('../../middlewares/authMiddleware');

// Apply authentication middleware to all routes
router.use(authMiddleware)

// Process a speech-to-text transcription
router.post('/process', [
  check('workflowRunId', 'Workflow run ID is required').not().isEmpty(),
  check('nodeId', 'Node ID is required').not().isEmpty(),
  check('sourceType').optional().isIn(['audio', 'video']),
  check('demoMode').optional().isBoolean(),
  check('service').optional().isIn(['simulated', 'revai', 'azure-whisper'])
], speechToTextController.processTranscription);

module.exports = router;
