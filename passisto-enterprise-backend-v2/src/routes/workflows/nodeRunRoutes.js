const express = require('express');
const router = express.Router();
const nodeRunController = require('../../controllers/workflows/nodeRunController');
const { check } = require('express-validator');
const { authMiddleware } = require('../../middlewares/authMiddleware');

// Apply auth middleware to all routes
router.use(authMiddleware)

// GET node runs by workflow run ID
router.get('/workflow-run/:workflowRunId', nodeRunController.getNodeRunsByWorkflowRun);

// GET single node run
router.get('/:id', nodeRunController.getNodeRun);

// POST create node run
router.post('/', [
  check('workflowRunId', 'Workflow run ID is required').not().isEmpty(),
  check('nodeId', 'Node ID is required').not().isEmpty()
], nodeRunController.createNodeRun);

// PUT update node run
router.put('/:id', [
  check('status', 'Status is required').isIn(['PENDING', 'RUNNING', 'SUCCESS', 'FAILED'])
], nodeRunController.updateNodeRun);

// DELETE node run
router.delete('/:id', nodeRunController.deleteNodeRun);

module.exports = router; 