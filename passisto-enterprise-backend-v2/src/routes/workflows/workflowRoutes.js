const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');
const workflowController = require('../../controllers/workflows/workflowController');
const { authMiddleware } = require('../../middlewares/authMiddleware');

// Apply authentication middleware to all routes
router.use(authMiddleware)

// Validation middleware
const validateWorkflow = [
  body('name').trim().notEmpty().withMessage('Name is required'),
  body('description').optional().trim(),
  body('nodes').isArray().withMessage('Nodes must be an array'),
  body('edges').isArray().withMessage('Edges must be an array'),
  body('status').isIn(['draft', 'active', 'archived']).withMessage('Invalid status')
];

const validateId = [
  // Using a more flexible validation for IDs that could be UUID or CUID
  param('id').matches(/^[a-zA-Z0-9_-]+$/).withMessage('Invalid workflow ID format')
];

// Get all workflows
router.get('/', workflowController.getWorkflows);

// Get single workflow
router.get('/:id', validateId, workflowController.getWorkflow);

// Create workflow
router.post('/', validateWorkflow, workflowController.createWorkflow);

// Update workflow
router.put('/:id', validateId, validateWorkflow, workflowController.updateWorkflow);

// Delete workflow
router.delete('/:id', validateId, workflowController.deleteWorkflow);

// Execute workflow
router.post('/:id/execute', validateId, workflowController.executeWorkflow);

module.exports = router;