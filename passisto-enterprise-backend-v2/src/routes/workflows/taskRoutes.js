const express = require('express');
const router = express.Router();
const taskController = require('../../controllers/workflows/taskController');
const { authMiddleware } = require('../../middlewares/authMiddleware');

// Apply authentication middleware to all routes
router.use(authMiddleware)

// Get all tasks assigned to the current user
router.get('/assigned', taskController.getAssignedTasks);

// Mark a task as completed
router.post('/:taskId/complete', taskController.completeTask);

module.exports = router; 