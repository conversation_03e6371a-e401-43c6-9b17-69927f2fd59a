const express = require('express');
const router = express.Router();
const workflowRunController = require('../../controllers/workflows/workflowRunController');
const { check } = require('express-validator');
const { authMiddleware } = require('../../middlewares/authMiddleware');

// Apply auth middleware to all routes
router.use(authMiddleware)

// GET all workflow runs
router.get('/', workflowRunController.getWorkflowRuns);

// GET workflow runs by workflow ID
router.get('/workflow/:workflowId', workflowRunController.getWorkflowRunsByWorkflowId);

// GET assigned tasks for the current user
router.get('/assigned-tasks', workflowRunController.getUserAssignedTasks);

// GET single workflow run with detailed node status
router.get('/:id/details', workflowRunController.getWorkflowRunWithNodeDetails);

// GET single workflow run
router.get('/:id', workflowRunController.getWorkflowRun);

// POST create workflow run
router.post('/', [
  check('workflowId', 'Workflow ID is required').not().isEmpty()
], workflowRunController.createWorkflowRun);

// POST complete a user task
router.post('/:workflowRunId/nodes/:nodeId/complete', [
  check('taskData').optional().isObject()
], workflowRunController.completeUserTask);

// PUT update workflow run status
router.put('/:id/status', [
  check('status', 'Status is required').isIn(['PENDING', 'RUNNING', 'WAITING_FOR_USER', 'SUCCESS', 'FAILED'])
], workflowRunController.updateWorkflowRunStatus);

// POST stop workflow run
router.post('/:id/stop', workflowRunController.stopWorkflowRun);

// DELETE workflow run
router.delete('/:id', workflowRunController.deleteWorkflowRun);

module.exports = router;