
const express = require('express');

const workflowRoutes = require('./workflowRoutes');
const workflowRunRoutes = require('./workflowRunRoutes');
const nodeRunRoutes = require('./nodeRunRoutes');
const userRoutes = require('./userRoutes');
// const authRoutes = require('./routes/auth');
const taskRoutes = require('./taskRoutes');
const fileRoutes = require('./fileRoutes');
const speechToTextRoutes = require('./speechToTextRoutes');

const workflowsRouter = express.Router();

// app.use('/api/auth', authRoutes);
workflowsRouter.use('/workflows', workflowRoutes);
workflowsRouter.use('/workflow-runs', workflowRunRoutes);
workflowsRouter.use('/node-runs', nodeRunRoutes);
workflowsRouter.use('/users', userRoutes);
workflowsRouter.use('/tasks', taskRoutes);
workflowsRouter.use('/files', fileRoutes);
workflowsRouter.use('/speech-to-text', speechToTextRoutes);

module.exports = workflowsRouter;