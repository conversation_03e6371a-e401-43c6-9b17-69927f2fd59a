const express = require('express');
const router = express.Router();
const { body, param } = require('express-validator');
const userController = require('../../controllers/workflows/userController');
const { authMiddleware } = require('../../middlewares/authMiddleware');

// Apply authentication middleware to all routes
router.use(authMiddleware);

// Validation middleware
const validateUser = [
  body('name').trim().notEmpty().withMessage('Name is required'),
  body('email').isEmail().withMessage('Valid email is required'),
  body('avatar').optional().trim()
];

const validateId = [
  param('id').isUUID(4).withMessage('Invalid user ID')
];

// Get all users - admin only
router.get('/', userController.getUsers);

// Get single user
router.get('/:id', validateId, userController.getUser);


module.exports = router; 