// routes/clerkWebhookRouter.js
const express = require("express");
const {
  onboarding,
  checkOnboarding,
  currentUser,
  createAndInviteUser,
  getAllUsers,
  getUserById,
  updateUser,
  toggleUserStatus,
  deleteUser,
  // testGetUsers
} = require("../controllers/user.controller.js");
const { authMiddleware } = require("../middlewares/authMiddleware.js");
const checkPermission = require("../middlewares/permMiddleware.js");

const userRouter = express.Router();

userRouter.post("/onboard", authMiddleware, onboarding);
userRouter.post("/check-onboarding", authMiddleware, checkOnboarding);

userRouter.get("/current", authMiddleware, currentUser);

userRouter.post(
  "/create",
  authMiddleware,
  checkPermission(["CAN_CREATE_USER", "CAN_MANAGE_USERS"]),
  createAndInviteUser
);

userRouter.get(
  "/all",
  authMiddleware,
  checkPermission(["CAN_VIEW_USER", "CAN_MANAGE_USERS", "CAN_CREATE_USER", "CAN_UPDATE_USER", "CAN_DELETE_USER", "CAN_TOGGLE_USER_STATUS"]),
  getAllUsers
);

userRouter.get(
  "/:userId",
  authMiddleware,
  checkPermission(["CAN_VIEW_USER", "CAN_MANAGE_USERS", "CAN_CREATE_USER", "CAN_UPDATE_USER", "CAN_DELETE_USER", "CAN_TOGGLE_USER_STATUS"]),
  getUserById
);

userRouter.put(
  "/:userId",
  authMiddleware,
  checkPermission(["CAN_UPDATE_USER", "CAN_MANAGE_USERS"]),
  updateUser
);

userRouter.patch(
  "/:userId/toggle-status",
  authMiddleware,
  checkPermission(["CAN_TOGGLE_USER_STATUS", "CAN_MANAGE_USERS"]),
  toggleUserStatus
);

userRouter.delete(
  "/:userId",
  authMiddleware,
  checkPermission(["CAN_DELETE_USER", "CAN_MANAGE_USERS"]),
  deleteUser
);

// userRouter.get('/test/users/:companyId', testGetUsers);

module.exports = userRouter;
