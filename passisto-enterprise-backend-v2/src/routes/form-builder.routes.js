// routes/clerkWebhookRouter.js
const express = require("express");
const {
  enhanceDescription,
  generateForm,
  getAllForms,
  getFormById,
  updateForm,
  deleteResponse,
  deleteForm,
  exportForm,
  submitForm,
  getFormResponses,
  downloadResponseFile,
} = require("../controllers/form-builder.controller");
const { authMiddleware } = require("../middlewares/authMiddleware");
const upload = require("../middlewares/uploadFile");
const checkPermission = require("../middlewares/permMiddleware.js");

const formBuilderRouter = express.Router();

formBuilderRouter.post(
  "/enhance-description",
  authMiddleware,
  checkPermission(["CAN_CREATE_FORM"]),
  enhanceDescription
);
formBuilderRouter.post(
  "/generate-form",
  authMiddleware,
  checkPermission(["CAN_CREATE_FORM"]),
  generateForm
);
formBuilderRouter.get(
  "/forms",
  authMiddleware,
  checkPermission(["CAN_VIEW_FORM", "CAN_UPDATE_FORM", "CAN_DELETE_FORM"]),
  getAllForms
);

formBuilderRouter.get(
  "/forms/:formId/export",
  authMiddleware,
  checkPermission(["CAN_EXPORT_FORM_DATA", "CAN_EXPORT_FORM_DATA"]),
  exportForm
);

formBuilderRouter.post("/:companyId/forms/:formId/submit", upload.any(), submitForm);
formBuilderRouter.get(
  "/forms/:formId/responses",
  authMiddleware,
  getFormResponses
);
formBuilderRouter.get("/forms/:companyId/:formId", getFormById);
formBuilderRouter.put(
  "/forms/:formId",
  authMiddleware,
  checkPermission([
    "CAN_UPDATE_FORM",
    "CAN_CREATE_FORM",
    "CAN_EXPORT_FORM_DATA",
    "CAN_EXPORT_FORM_DATA",
  ]),
  updateForm
);
formBuilderRouter.delete(
  "/forms/:formId/response",
  authMiddleware,
  deleteResponse
);
formBuilderRouter.delete(
  "/forms/:formId",
  authMiddleware,
  checkPermission(["CAN_DELETE_FORM"]),
  deleteForm
);
formBuilderRouter.get(
  "/forms/:formId/download/file/:fileName",
  authMiddleware,
  downloadResponseFile
);

module.exports = formBuilderRouter;
