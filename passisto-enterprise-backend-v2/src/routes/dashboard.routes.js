const { getDashboardStats, refreshDashboardViews } = require("../controllers/dashboard.controller");
const express = require("express");
const { authMiddleware } = require("../middlewares/authMiddleware");

const dashboardRouter = express.Router();

dashboardRouter.use(authMiddleware);

dashboardRouter.get('/stats', getDashboardStats);
dashboardRouter.post('/refresh', refreshDashboardViews);

module.exports = dashboardRouter;