const express = require("express");
const {
  createInterview,
  getInterviewsTemplate,
  sendInterviewToCandidat,
  createFeedback,
  seedData,
  getInterviewById,
  getCandidateByInterviewAndCandidateId,
  getFeedbackByInterviewAndCandidate,
  updateInterview,
  deleteInterviewById,
  checkCandidateAccess,
} = require("../controllers/ai-interview-agent.controller");
const { upload } = require("../services/multer.service");
const { authMiddleware } = require("../middlewares/authMiddleware");
const checkPermission = require("../middlewares/permMiddleware.js");

const aiInterviewAgent = express.Router();

aiInterviewAgent.post(
  "/interviews",
  // authMiddleware,
  createInterview
);
aiInterviewAgent.get(
  "/interviews",
  authMiddleware,
  checkPermission(["CAN_VIEW_INTERVIEW", "CAN_SEND_INTERVIEW_TO_CANDIDATE", "CAN_DELETE_INTERVIEW", "CAN_MANAGE_INTERVIEW_AGENT"]),
  getInterviewsTemplate
);
aiInterviewAgent.post(
  "/interviews/:interviewId/candidates",
  authMiddleware,
  checkPermission(["CAN_SEND_INTERVIEW_TO_CANDIDATE", "CAN_MANAGE_INTERVIEW_AGENT"]),
  upload.single("file"),
  sendInterviewToCandidat
);
aiInterviewAgent.post(
  "/interviews/:interviewId/feedback/:candidateId",
  authMiddleware,
  checkPermission(["CAN_VIEW_FEEDBACK", "CAN_MANAGE_INTERVIEW_AGENT"]),
  createFeedback
);
aiInterviewAgent.get(
  "/interviews/db/seed",
  authMiddleware,
  checkPermission(["CAN_MANAGE_INTERVIEW_AGENT"]),
  seedData
);
aiInterviewAgent.get(
  "/interviews/:interviewId",
  authMiddleware,
  checkPermission(["CAN_VIEW_INTERVIEW", "CAN_MANAGE_INTERVIEW_AGENT"]),
  getInterviewById
);
aiInterviewAgent.put(
  "/interviews/:interviewId",
  authMiddleware,
  checkPermission(["CAN_UPDATE_INTERVIEW", "CAN_MANAGE_INTERVIEW_AGENT"]),
  updateInterview
);
aiInterviewAgent.delete(
  "/interviews/:interviewId",
  authMiddleware,
  checkPermission(["CAN_DELETE_INTERVIEW", "CAN_MANAGE_INTERVIEW_AGENT"]),
  deleteInterviewById
);
aiInterviewAgent.get(
  "/interviews/:interviewId/candidates/:candidateId",
  authMiddleware,
  checkPermission(["CAN_VIEW_INTERVIEW", "CAN_MANAGE_INTERVIEW_AGENT"]),
  getCandidateByInterviewAndCandidateId
);
aiInterviewAgent.get(
  "/interviews/:interviewId/feedback/:candidateId",
  authMiddleware,
  checkPermission(["CAN_VIEW_FEEDBACK", "CAN_MANAGE_INTERVIEW_AGENT"]),
  getFeedbackByInterviewAndCandidate
);
aiInterviewAgent.post(
  "/interviews/:interviewId/candidates/:candidateId/access",
  authMiddleware,
  checkPermission(["CAN_VIEW_INTERVIEW", "CAN_MANAGE_INTERVIEW_AGENT"]),
  checkCandidateAccess
);

module.exports = aiInterviewAgent;
