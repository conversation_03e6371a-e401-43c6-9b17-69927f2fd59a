// routes/clerkWebhookRouter.js
const express = require('express');
const { handleClerkWebhook, createAndInviteUser } = require('../controllers/clerk.controller');
const {authMiddleware} = require('../middlewares/authMiddleware.js')

const authRouter = express.Router();

authRouter.post('/clerk-webhook', handleClerkWebhook);
authRouter.post('/invite-user', authMiddleware, createAndInviteUser);

module.exports = authRouter;
