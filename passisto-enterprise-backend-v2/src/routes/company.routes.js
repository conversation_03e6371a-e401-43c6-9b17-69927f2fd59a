const express = require('express');
const { 
  updateCompanyUsage, 
  checkUsageLimit,
  getCompanySubscriptionName,
  getCompanyUsageInfo,
  checkUsageLimitByCompanyId
} = require('../controllers/company.controller');
const { authMiddleware } = require('../middlewares/authMiddleware');

const companyRouter = express.Router();

companyRouter.post('/check-usage', authMiddleware, checkUsageLimit);
companyRouter.post('/check-usage-by-company-id', checkUsageLimitByCompanyId);
companyRouter.post('/update-usage', authMiddleware, updateCompanyUsage);
companyRouter.get('/subscription-name', authMiddleware, getCompanySubscriptionName);
companyRouter.get('/usage-info', authMiddleware, getCompanyUsageInfo);
module.exports = companyRouter;
