// routes/clerkWebhookRouter.js
const express = require('express');
const { enhanceDescription, generateEmail, getEmailById, getAllEmails, updateEmail, sendEmail, deleteEmail } = require('../controllers/email-builder.controller');
const { authMiddleware } = require('../middlewares/authMiddleware');
const checkPermission = require("../middlewares/permMiddleware.js");


const emailBuilderRouter = express.Router();

emailBuilderRouter.post(
  '/enhance-description',
  authMiddleware,
  checkPermission(['CAN_ENHANCE_EMAIL_BUILDER_DESCRIPTION']),
  enhanceDescription
);
emailBuilderRouter.post(
  '/generate-email',
  authMiddleware,
  checkPermission(['CAN_GENERATE_EMAIL_BUILDER_TEMPLATE']),
  generateEmail
);
emailBuilderRouter.get(
  '/emails/:emailId',
  authMiddleware,
  checkPermission(['CAN_VIEW_EMAIL_BUILDER_TEMPLATE', 'CAN_GENERATE_EMAIL_BUILDER_TEMPLATE', 'CAN_EDIT_EMAIL_BUILDER', 'CAN_SEND_EMAIL_BUILDER']),
  getEmailById
);
emailBuilderRouter.get(
  '/emails',
  authMiddleware,
  checkPermission(['CAN_VIEW_EMAIL_BUILDER_TEMPLATE', 'CAN_GENERATE_EMAIL_BUILDER_TEMPLATE', 'CAN_EDIT_EMAIL_BUILDER', 'CAN_SEND_EMAIL_BUILDER']),
  getAllEmails
);
emailBuilderRouter.put(
  '/emails/:emailId',
  authMiddleware,
  checkPermission(['CAN_EDIT_EMAIL_BUILDER']),
  updateEmail
);
emailBuilderRouter.post(
  '/emails/send',
  authMiddleware,
  checkPermission(['CAN_SEND_EMAIL_BUILDER']),
  sendEmail
);
emailBuilderRouter.delete(
  '/emails/:emailId',
  authMiddleware,
  checkPermission(['CAN_DELETE_EMAIL_BUILDER']),
  deleteEmail
);

module.exports = emailBuilderRouter;
