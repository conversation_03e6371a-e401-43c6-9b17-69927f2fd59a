const express = require("express");
const {
  getPlans,
  createCheckoutSession,
  handleStripeWebhook,
  checkSubscriptionStatus,
  getSubscriptionDetails,
  createPortalSession,
} = require("../controllers/stripe.controller.js");
const { authMiddleware } = require("../middlewares/authMiddleware.js");

const stripRouter = express.Router();

stripRouter.get("/plans", getPlans);
stripRouter.get(
  "/subscription-status",
  authMiddleware,
  checkSubscriptionStatus
);
stripRouter.get(
  "/subscription-details",
  authMiddleware,
  getSubscriptionDetails
);
stripRouter.post(
  "/create-checkout-session",
  authMiddleware,
  createCheckoutSession
);
stripRouter.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  handleStripeWebhook
);
stripRouter.post("/create-portal-session", authMiddleware, createPortalSession);
module.exports = stripRouter;
