const prisma = require("@/config/db");

const hasAuthority = (requireRoles = []) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.userId;
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          roles: true,
        },
      });
      const hasRole = user.roles.some((role) =>
        requireRoles.includes(role.name)
      );
      if (hasRole) {
        next();
      } else {
        res
          .status(403)
          .json({
            success: false,
            message: "Access denied: insufficient roles",
          });
      }
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: "Internal Server Error" });
    }
  };
};

const hasPermission = (permissionName) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.userId;
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          permissions: true,
        },
      });
      const hasPermission = user.permissions.some(
        (permission) => permission.action === permissionName
      );
      if (hasPermission) {
        next();
      } else {
        res.status(403).json({
          success: false,
          message: "Access denied: insufficient permissions",
        });
      }
    } catch (error) {
      console.error(error);
      return res.status(500).json({ error: "Internal Server Error" });
    }
  };
};


module.exports = { hasAuthority, hasPermission };