const { z } = require("zod");
const { IntegrationBaseSchema } = require("./integration.schema");

const FtpIntegrationSchema = IntegrationBaseSchema.extend({
  providerType: z.literal("ftp"),
  server: z.string().min(1).max(300),
  port: z.number().min(1).max(65535),
  username: z.string().min(1).max(100),
  password: z.string().min(1).max(100).optional(),
  isSecure: z.boolean(),
});

module.exports = { FtpIntegrationSchema };