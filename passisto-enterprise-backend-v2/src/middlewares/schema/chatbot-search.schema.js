const { z } = require('zod');

// Define validation schema for chatbot requests
const chatbotSchema = z.object({
  message: z.string().max(500),
  indices: z.array(z.string().uuid()).min(1),
  session_id: z.union([z.string().uuid(), z.literal('')]).nullable().optional(),
  user_id: z.string().max(100).nullable().optional(),
  chatbot_name: z.string().max(100).optional(),
  user_location: z.object({
    extra_id: z.string().max(100).optional(),
    _extra_ci: z.string().max(100).optional(),
    _extra_rg: z.string().max(100).optional(),
    _extra_co: z.string().max(100).optional(),
  }).optional(),
});

// Define validation schema for search requests
const searchSchema = z.object({
  query: z.string().max(500),
  indices: z.array(z.string().uuid()).min(1),
  user_id: z.string().max(100).nullable().optional(),
});

module.exports = { searchSchema, chatbotSchema };