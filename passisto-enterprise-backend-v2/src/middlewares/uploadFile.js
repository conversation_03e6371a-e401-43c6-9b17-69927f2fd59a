const multer = require('multer');
const path = require('path');
const fs = require('fs');

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Récupère companyId depuis req.user (middleware d'auth) et formId depuis req.params
    const companyId = req.params.companyId;
    const formId = req.params.formId;
    console.log(`Received file for field: ${file.fieldname}`);
    console.log(`Original filename: ${file.originalname}`);
    if (!companyId || !formId) {
      return cb(new Error('companyId et formId sont requis'));
    }

    const uploadPath = path.join(__dirname, '../../uploads', companyId,'form-builder', formId);
    fs.mkdirSync(uploadPath, { recursive: true });
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

module.exports = upload;