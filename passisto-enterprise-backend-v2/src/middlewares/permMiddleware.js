const prisma  = require('@/config/db');

function checkPermission(requiredPermissions) {
    // Return a regular middleware function (not async)
    return function(req, res, next) {
        const userId = req.user?.userId;
        console.log('userId', userId);
        // Move the async work inside
        prisma.user.findUnique({
            where: { id: userId },
            include: {
                roles: {
                    include: {
                        permissions: {
                            include: { permission: true }
                        }
                    }
                },
                groups: {
                    include: {
                        group: {
                            include: {
                                permissions: {
                                    include: { permission: true }
                            }
                            }
                        }
                    }
                },
                overrides: {
                    include: {
                        extraPermissions: { include: { permission: true } },
                        revokedPermissions: { include: { permission: true } }
                    }
                }
            }
        })
        .then(user => {
            const rolePerms = user.roles.flatMap(role =>
                role.permissions.map(p => p.permission.action)
            );

            const groupPerms = user.groups.flatMap(ug =>
                ug.group.permissions.map(p => p.permission.action)
            );

            const extraPerms = user.overrides?.extraPermissions.map(p => p.permission.action) || [];
            const revokedPerms = user.overrides?.revokedPermissions.map(p => p.permission.action) || [];

            const effectivePerms = new Set([
                ...rolePerms,
                ...groupPerms,
                ...extraPerms
            ].filter(p => !revokedPerms.includes(p)));
            
            // Check if user has the required permissions
            if (effectivePerms.size === 0 || !requiredPermissions?.some(rp => effectivePerms.has(rp))) {
                return res.status(403).json({ error: 'You are Forbidden to do this action' });
            }
            next();
        })
        .catch(error => {
            console.error('Error in check Permission middleware:', error);
            res.status(500).json({ error: 'Internal Server Error' });
        });
    };
}
    
module.exports = checkPermission;