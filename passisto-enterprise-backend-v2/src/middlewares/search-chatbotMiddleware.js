
/**
 * Middleware to validate request body against a Zod schema
 * @param {z.ZodSchema} schema - Zod schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schema) => {
    return (req, res, next) => {
      try {
        schema.parse(req.body);
        next();
      } catch (error) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({
          message: 'Invalid request data',
          errors: error.errors,
        });
      }
    };
  };

module.exports = validateRequest;