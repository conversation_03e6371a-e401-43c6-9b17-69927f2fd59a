const { JiraIntegrationSchema } = require("./schema/jira.schema");
const { FtpIntegrationSchema } = require("./schema/ftp.schema");
const { WebIntegrationSchema } = require("./schema/web.schema");

const SCHEMAS = {
  jira: JiraIntegrationSchema,
  ftp: FtpIntegrationSchema,
  web: WebIntegrationSchema,
};

async function validateIntegration(req, res, next){
  const { providerType } = req.body;

  if (!providerType || !(providerType in SCHEMAS)) {
    return res.status(400).json({ error: "Invalid or missing providerType" });
  }

  const schema = SCHEMAS[providerType];

  try {
    req.body = schema.parse(req.body);
    next();
  } catch (err) {
    return res.status(400).json({ error: err.errors });
  }
};

module.exports = validateIntegration;