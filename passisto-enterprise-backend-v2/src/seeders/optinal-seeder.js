const prisma = require("../config/db");
const bcrypt = require("bcrypt");

async function seedTestScenario() {
  console.log("Seeding test scenario...");
  try {
    // Create a company
    const company = await prisma.company.create({
      data: {
        name: "Test Company",
        description: "Company for testing purposes",
      },
    });

    // Create roles
    const adminRole = await prisma.role.create({ data: { name: "ADMIN" } });
    const userRole = await prisma.role.create({ data: { name: "USER" } });

    // Create an admin user
    const adminUser = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        firstName: "Admin",
        lastName: "User",
        password: await bcrypt.hash("Admin@123", 10),
        companyId: company.id,
        clerkId: 'admin',
        roles: {
          connect: { id: adminRole.id },
        },
      },
    });

    // Create two regular users
    const user1 = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        firstName: "User",
        lastName: "One",
        password: await bcrypt.hash("User1@123", 10),
        clerkId: 'user1',
        companyId: company.id,
        roles: {
          connect: { id: userRole.id },
        },
      },
    });

    const user2 = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        firstName: "User",
        lastName: "Two",
        password: await bcrypt.hash("User2@123", 10),
        clerkId: 'user2',
        companyId: company.id,
        roles: {
          connect: { id: userRole.id },
        },
      },
    });

    // Create two groups
    const group1 = await prisma.group.create({
      data: {
        name: "Group 1",
        description: "First test group",
        companyId: company.id,
      },
    });

    const group2 = await prisma.group.create({
      data: {
        name: "Group 2",
        description: "Second test group",
        companyId: company.id,
      },
    });

    // Add users to groups
    await prisma.userGroup.create({
      data: {
        userId: user1.id,
        groupId: group1.id,
      },
    });

    await prisma.userGroup.create({
      data: {
        userId: user2.id,
        groupId: group2.id,
      },
    });

    // Create integrations
    const integration1 = await prisma.integration.create({
      data: {
        name: "Integration 1",
        companyId: company.id,
        providerType: "web",
        opensearchIndexId: "integration1-index",
        celeryTaskId: "task1",
        createdBy: "Admin"
      },
    });

    const integration2 = await prisma.integration.create({
      data: {
        name: "Integration 2",
        companyId: company.id,
        providerType: "jira",
        opensearchIndexId: "integration2-index",
        celeryTaskId: "task12",
        createdBy: "Admin"
      },
    });

    // Attach integrations to groups
    await prisma.groupIntegration.create({
      data: {
        groupId: group1.id,
        integrationId: integration1.id,
      },
    });

    await prisma.groupIntegration.create({
      data: {
        groupId: group2.id,
        integrationId: integration2.id,
      },
    });

    console.log("Test scenario seeded successfully");
  } catch (error) {
    console.error("Error seeding test scenario:", error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedTestScenario()
    .then(() => {
      console.log("Test scenario seeded successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding test scenario failed:", error);
      process.exit(1);
    });
}

module.exports = { seedTestScenario };