const { v4: uuidv4 } = require("uuid");
const prisma = require("../config/db");

// Permission categories (reuse from your main seeder)
const { permissionCategories } = require("./companies-users.seeder");

// The static ADMIN role id you provided
const ADMIN_ROLE_ID = "97b160f6-3213-41d0-b909-5cfd45940c71";

async function seedPermissionsAndCompanyGroups() {
  console.log("Seeding permissions and Company groups for all companies...");

  // 1. Seed all permissions (if not exist)
  const createdPermissions = [];
  for (const category of permissionCategories) {
    for (const permissionName of category.permissions) {
      let permission = await prisma.permission.findUnique({
        where: { action: permissionName },
      });
      if (!permission) {
        permission = await prisma.permission.create({
          data: {
            action: permissionName,
            category: category.name,
          },
        });
        console.log(`Created permission: ${permission.action}`);
      }
      createdPermissions.push(permission);
    }
  }

  // 2. Assign all permissions to ADMIN role (if not already assigned)
  for (const permission of createdPermissions) {
    const exists = await prisma.rolePermission.findFirst({
      where: {
        roleId: ADMIN_ROLE_ID,
        permissionId: permission.id,
      },
    });
    if (!exists) {
      await prisma.rolePermission.create({
        data: {
          roleId: ADMIN_ROLE_ID,
          permissionId: permission.id,
          scopeType: "GLOBAL",
          scopeId: "global",
        },
      });
      console.log(`Assigned permission ${permission.action} to ADMIN role`);
    }
  }

  // 3. For each company, create "Company" group if not exists
  const companies = await prisma.company.findMany();
  for (const company of companies) {
    let group = await prisma.group.findFirst({
      where: {
        name: "Company",
        companyId: company.id,
      },
    });
    if (!group) {
      group = await prisma.group.create({
        data: {
          name: "Company",
          description: "Default company-wide group for all employees",
          companyId: company.id
        },
      });
      console.log(`Created "Company" group for company ${company.name}`);
    } else {
      console.log(`"Company" group already exists for company ${company.name}`);
    }

    // 4. Add all admin users of this company to the "Company" group
    const adminUsers = await prisma.user.findMany({
      where: {
        companyId: company.id,
        roles: {
          some: {
            id: ADMIN_ROLE_ID,
          },
        },
      },
    });

    for (const admin of adminUsers) {
      const exists = await prisma.userGroup.findFirst({
        where: {
          userId: admin.id,
          groupId: group.id,
        },
      });
      if (!exists) {
        await prisma.userGroup.create({
          data: {
            userId: admin.id,
            groupId: group.id,
          },
        });
        console.log(`Added admin user ${admin.email || admin.id} to "Company" group for company ${company.name}`);
      }
    }
  }

  console.log("Seeding complete.");
}

// Run if executed directly
if (require.main === module) {
  seedPermissionsAndCompanyGroups()
    .then(() => {
      console.log("Production permissions and groups seeding done.");
      process.exit(0);
    })
    .catch((err) => {
      console.error("Seeding failed:", err);
      process.exit(1);
    });
}

module.exports = {
  seedPermissionsAndCompanyGroups,
};