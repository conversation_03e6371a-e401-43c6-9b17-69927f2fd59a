const prisma = require("../config/db");
const bcrypt = require("bcrypt");
const { v4: uuidv4 } = require("uuid");
const { clerkClient } = require("@clerk/express");
const { faker } = require("@faker-js/faker");
const { seedFakeIntegrations } = require("./integration.seeder");

// Sample company data - just one company


// Define roles
const roles = ["ADMIN", "MANAGER", "MEMBER", "GUEST", "PUBLIC"];

// Define permission categories
const permissionCategories = [
  {
    name: "User & Group Management",
    permissions: [
      "CAN_MANAGE_USERS",
      "CAN_CREATE_USER",
      "CAN_UPDATE_USER",
      "CAN_DELETE_USER",
      "CAN_VIEW_USER",
      "CAN_TOGGLE_USER_STATUS",
      "CAN_MANAGE_GROUPS",
      "CAN_CREATE_GROUP",
      "CAN_UPDATE_GROUP",
      "CAN_DELETE_GROUP",
      "CAN_VIEW_GROUP",
      "CAN_ASSIGN_USER_TO_GROUP",
      "CAN_REMOVE_USER_FROM_GROUP",
    ],
  },
  {
    name: "Form Builder",
    permissions: [
      "CAN_CREATE_FORM",
      "CAN_UPDATE_FORM",
      "CAN_DELETE_FORM",
      "CAN_VIEW_FORM",
      "CAN_PUBLISH_FORM",
      "CAN_EXPORT_FORM_DATA",
      "CAN_VIEW_RESPONSES"
    ],
  },
  {
    name: "Data Provider Management",
    permissions: [
      // ALL 
      "CAN_MANAGE_INTEGRATIONS",

      // General
      "CAN_VIEW_INTEGRATION",
      "CAN_ASSIGN_INTEGRATION_TO_GROUP",
      "CAN_REMOVE_INTEGRATION_FROM_GROUP",

      // FTP
      "CAN_CREATE_FTP",
      "CAN_UPDATE_FTP",
      "CAN_DELETE_FTP",

      // Jira
      "CAN_CREATE_JIRA",
      "CAN_UPDATE_JIRA",
      "CAN_DELETE_JIRA",

      // Web
      "CAN_CREATE_WEB",
      "CAN_UPDATE_WEB",
      "CAN_DELETE_WEB",
    ],
  },
  {
    name: "Email Builder",
    permissions: [
      "CAN_ENHANCE_EMAIL_BUILDER_DESCRIPTION",
      "CAN_GENERATE_EMAIL_BUILDER_TEMPLATE",
      "CAN_VIEW_EMAIL_BUILDER_TEMPLATE",
      "CAN_EDIT_EMAIL_BUILDER",
      "CAN_DELETE_EMAIL_BUILDER",
      "CAN_SEND_EMAIL_BUILDER",
      "CAN_MANAGE_EMAIL_BUILDER",
    ],
  },
  {
    name: "AI Interview Agent",
    permissions: [
      "CAN_CREATE_INTERVIEW",
      "CAN_UPDATE_INTERVIEW",
      "CAN_DELETE_INTERVIEW",
      "CAN_VIEW_INTERVIEW",
      "CAN_SEND_INTERVIEW_TO_CANDIDATE",
      // "CAN_VIEW_INTERVIEW_RESULTS",
      // "CAN_EXPORT_INTERVIEW_DATA",
      // "CAN_CREATE_FEEDBACK",
      "CAN_VIEW_FEEDBACK",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ],
  },
  {
    name: "Billing & Subscription",
    permissions: [
      "CAN_VIEW_PLANS",
      "CAN_VIEW_SUBSCRIPTION_STATUS",
      "CAN_VIEW_SUBSCRIPTION_DETAILS",
      "CAN_CREATE_CHECKOUT_SESSION",
      "CAN_CREATE_PORTAL_SESSION",
      // "CAN_MANAGE_BILLING",
    ],
  },
  {
    name: "Chatbot & Search",
    permissions: ["CAN_ASK_CHATBOT", "CAN_SEARCH"],
  },
];



async function seedRolesAndPermissions() {
  console.log("Seeding roles and permissions...");
  try {
    // Create roles
    const createdRoles = [];
    for (const roleName of roles) {
      const role = await prisma.role.create({
        data: { name: roleName },
      });
      console.log(`Created role: ${role.name} (${role.id})`);
      createdRoles.push(role);
    }

    // Create permissions
    const createdPermissions = [];
    for (const category of permissionCategories) {
      for (const permissionName of category.permissions) {
        const permission = await prisma.permission.create({
          data: {
            action: permissionName,
            category: category.name,
          },
        });
        console.log(
          `Created permission: ${permission.action} (${permission.id})`
        );
        createdPermissions.push(permission);
      }
    }

    // Assign all permissions to ADMIN role
    const adminRole = createdRoles.find((role) => role.name === "ADMIN");
    if (adminRole) {
      for (const permission of createdPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id,
            scopeType: "GLOBAL",
            scopeId: "global",
          },
        });
      }
      console.log(`Assigned all permissions to ADMIN role`);
    }

    return { roles: createdRoles, permissions: createdPermissions };
  } catch (error) {
    console.error("Error seeding roles and permissions:", error);
    throw error;
  }
}





async function seedCompaniesAndUsers() {
  try {
    // Seed roles and permissions first
    const { roles: createdRoles, permissions: createdPermissions } =
      await seedRolesAndPermissions();

    // Seed company
    // const company = await seedCompanies();

    // Seed users for the company
    // const users = await seedUsers(company);

    // Create groups for the company and add users to groups
    // const groups = await seedGroups(company, users, createdPermissions);

    // Seed Integrations
    // await seedFakeIntegrations();
    console.log("Fake integrations seeded successfully");

    // return { company, users, groups };
  } catch (error) {
    console.error("Error seeding companies and users:", error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedCompaniesAndUsers()
    .then(() => {
      console.log("Companies and users seeded successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = {
  seedCompaniesAndUsers,
  seedRolesAndPermissions,
  roles,
  permissionCategories,
};
