const prisma = require("../config/db");

async function addPermissionToAdmin() {
  const permissionAction = "CAN_VIEW_RESPONSES";
  const permissionCategory = "Form Builder";
  const adminRoleName = "ADMIN";

  // 1. Upsert the permission
  const permission = await prisma.permission.upsert({
    where: { action: permissionAction },
    update: { category: permissionCategory },
    create: {
      action: permissionAction,
      category: permissionCategory,
    },
  });

  // 2. Find the ADMIN role
  const adminRole = await prisma.role.findUnique({
    where: { name: adminRoleName },
  });

  if (!adminRole) {
    throw new Error("ADMIN role not found");
  }

  // 3. Check if the permission is already assigned
  const alreadyAssigned = await prisma.rolePermission.findFirst({
    where: {
      roleId: adminRole.id,
      permissionId: permission.id,
      scopeType: "GLOBAL",
      scopeId: "global",
    },
  });

  // 4. Assign the permission if not already assigned
  if (!alreadyAssigned) {
    await prisma.rolePermission.create({
      data: {
        roleId: adminRole.id,
        permissionId: permission.id,
        scopeType: "GLOBAL",
        scopeId: "global",
      },
    });
    console.log(`Permission "${permissionAction}" assigned to ADMIN role.`);
  } else {
    console.log(`Permission "${permissionAction}" already assigned to ADMIN role.`);
  }
}

if (require.main === module) {
  addPermissionToAdmin()
    .then(() => {
      console.log("Done.");
      process.exit(0);
    })
    .catch((err) => {
      console.error(err);
      process.exit(1);
    });
}

module.exports = { addPermissionToAdmin };