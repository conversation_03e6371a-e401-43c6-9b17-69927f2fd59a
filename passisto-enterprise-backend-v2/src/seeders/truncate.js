const prisma = require("../config/db");
const { clerkClient } = require("@clerk/express");
const { deleteIntegrationIndices } = require("../utils/integration-helper"); // New addition

/**
 * Truncates all tables in the database
 * This function will delete all data while preserving the schema
 */
async function truncateAllTables() {
  console.log("Starting database truncation...");
  try {
    // First, delete all users from Clerk
    console.log("Deleting users from Clerk...");
    try {
      // Get all users from our database to find their Clerk IDs
      const users = await prisma.user.findMany({
        select: { clerkId: true }
      });
      
      // Delete each user from Clerk
      for (const user of users) {
        if (user.clerkId && !user.clerkId.startsWith('clerk_mock_')) {
          try {
            await clerkClient.users.deleteUser(user.clerkId);
            console.log(`Deleted user with Clerk ID: ${user.clerkId}`);
          } catch (clerkError) {
            console.error(`Error deleting user from <PERSON> (ID: ${user.clerkId}):`, clerkError.message);
            // Continue with other users even if one fails
          }
        }
      }
      console.log("Finished deleting users from Clerk");
    } catch (clerkError) {
      console.error("Error during Clerk user deletion:", clerkError);
      // Continue with database truncation even if Clerk deletion fails
    }

    // Disable foreign key checks to allow truncating tables with dependencies
    await prisma.$executeRaw`SET session_replication_role = 'replica';`;
    
    // Delete integration indices from OpenSearch before truncating integration-related tables
    console.log("Deleting integration indices...");
    await deleteIntegrationIndices();

    // Truncate integration-related tables first
    console.log("Truncating JiraIntegration table...");
    await prisma.jiraIntegration.deleteMany({});
    
    console.log("Truncating FtpIntegration table...");
    await prisma.ftpIntegration.deleteMany({});
    
    console.log("Truncating WebIntegration table...");
    await prisma.webIntegration.deleteMany({});
    
    console.log("Truncating GroupIntegration table...");
    await prisma.groupIntegration.deleteMany({});
    
    console.log("Truncating Integration table...");
    await prisma.integration.deleteMany({});
    
    // Truncate tables in reverse order of dependencies
    console.log("Truncating PermissionOverride table...");
    await prisma.permissionOverride.deleteMany({});
    
    console.log("Truncating UserOverride table...");
    await prisma.userOverride.deleteMany({});
    
    console.log("Truncating UserGroup table...");
    await prisma.userGroup.deleteMany({});
    
    console.log("Truncating GroupPermission table...");
    await prisma.groupPermission.deleteMany({});
    
    console.log("Truncating RolePermission table...");
    await prisma.rolePermission.deleteMany({});
    
    console.log("Truncating GroupIntegration table...");
    await prisma.groupIntegration.deleteMany({});
    
    console.log("Truncating JiraIntegration table...");
    await prisma.jiraIntegration.deleteMany({});
    
    console.log("Truncating WebIntegration table...");
    await prisma.webIntegration.deleteMany({});
    
    console.log("Truncating FtpIntegration table...");
    await prisma.ftpIntegration.deleteMany({});
    
    console.log("Truncating Integration table...");
    await prisma.integration.deleteMany({});
    
    console.log("Truncating Group table...");
    await prisma.group.deleteMany({});
    
    console.log("Truncating Email table...");
    await prisma.email.deleteMany({});
    
    console.log("Truncating Form table...");
    await prisma.form.deleteMany({});
    
    console.log("Truncating Invitation table...");
    await prisma.invitation.deleteMany({});
    
    console.log("Truncating User table...");
    await prisma.user.deleteMany({});
    
    console.log("Truncating Permission table...");
    await prisma.permission.deleteMany({});
    
    console.log("Truncating Role table...");
    await prisma.role.deleteMany({});
    
    console.log("Truncating Company table...");
    await prisma.company.deleteMany({});
    
    console.log("Truncating Subscription table...");
    await prisma.subscription.deleteMany({});
    
    // Re-enable foreign key checks
    await prisma.$executeRaw`SET session_replication_role = 'origin';`;
    
    console.log("All tables have been truncated successfully!");
    return true;
  } catch (error) {
    console.error("Error during database truncation:", error);
    
    // Make sure to re-enable foreign key checks even if there's an error
    try {
      await prisma.$executeRaw`SET session_replication_role = 'origin';`;
    } catch (innerError) {
      console.error("Error resetting session_replication_role:", innerError);
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the truncate function if this file is executed directly
if (require.main === module) {
  truncateAllTables()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("Truncation failed:", error);
      process.exit(1);
    });
}

module.exports = truncateAllTables;
