<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid #ff4444;
        }
        .content {
            padding: 20px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .usage-alert {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Usage Limit Warning</h2>
        </div>
        <div class="content">
            <p>Dear {{companyName}} Admin,</p>
            
            <div class="usage-alert">
                <h3>{{serviceName}} Usage Alert</h3>
                <p>You have used {{usedAmount}} out of {{totalLimit}} {{unit}}.</p>
                <p>Remaining: {{remaining}} {{unit}}</p>
            </div>

            <p>To avoid service interruption, consider upgrading your plan or managing your usage.</p>

            <p>
                <a href="{{portalUrl}}" class="button">Manage Subscription</a>
            </p>

            <p>If you have any questions, please don't hesitate to contact our support team.</p>

            <p>Best regards,<br>The Passisto Team</p>
        </div>
    </div>
</body>
</html>