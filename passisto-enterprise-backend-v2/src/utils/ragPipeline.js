const { Document } = require("@langchain/core/documents");
const { AzureOpenAIEmbeddings } = require("@langchain/openai");
const { OpenSearchVectorStore } = require("@langchain/community/vectorstores/opensearch");
const opensearchClient = require("@/config/opensearch"); // Your configured OpenSearch client
const dotenv = require("dotenv");

dotenv.config();

// Initialize Azure OpenAI embeddings.
const embeddings = new AzureOpenAIEmbeddings({
  modelName: process.env.EMBEDDING_MODEL,
  dimensions: parseInt(process.env.EMBEDDING_DIMENSION),
  azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY,
  azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION,
  azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME,
  azureOpenAIApiDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
  azureOpenAI: true, // Enable Azure OpenAI mode
});

/**
 * Loads documents into the vector store.
 * @param {Array<Object>} rawDocuments - Array of plain objects with `text` and `metadata`.
 * @param {string} indexName - Name of the OpenSearch index.
 * @returns {Promise<OpenSearchVectorStore>}
 */
async function loadDocumentsToVectorStore(rawDocuments, indexName) {
  // Convert raw documents into LangChain Document objects.
  const documents = rawDocuments.map(doc => new Document({
    pageContent: doc.text,
    metadata: doc.metadata
  })); 

  vectorStore = new OpenSearchVectorStore(embeddings,
     { 
      client: opensearchClient, 
      indexName: indexName,
      vectorFieldName: "vector_field"
    }
  );
  await vectorStore.addDocuments(documents);
  
  console.log(`Loaded ${documents.length} documents into index "${indexName}"`);
  return vectorStore;
}

module.exports = { loadDocumentsToVectorStore };