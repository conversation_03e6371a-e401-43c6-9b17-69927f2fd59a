require("dotenv").config();
require("module-alias/register");

const opensearch = require("@/config/opensearch");
const prisma  = require("@/config/db");
// const { v4: uuidv4 } = require("uuid");
const { loadDocumentsToVectorStore } = require("./ragPipeline");

/**
 * Creates an OpenSearch index if it does not exist and loads documents using the RAG pipeline.
 * @param {string} indexName - The name of the OpenSearch index.
 * @param {Array} documents - An array of documents to be loaded (each with text and metadata).
 * @returns {Promise<void>}
 */
async function createIndexAndFeedDocuments(indexName, documents) {
  console.log(`Verifying/OpenSearch index via RAG pipeline: ${indexName}`);
  try {
    // Before loading documents, verify if the index exists.
    const exists = await opensearch.indices.exists({ index: indexName });
    if (exists.statusCode === 404) {
      console.log(`Index ${indexName} does not exist. Creating new index with mapping...`);
      await opensearch.indices.create({
        index: indexName,
        body: {
          settings: {
            number_of_shards: 1,
            number_of_replicas: 0,
            index: {
              knn: true,
            },
          },
          mappings: {
            properties: {
              vector_field: {
                type: "knn_vector",
                dimension: 1024,
              },
              text: {
                type: "text",
              },
            },
          },
        },
      });
    } else {
      console.log(`Index ${indexName} already exists. Feeding documents...`);
    }
    // Use the RAG pipeline method to load documents (embeddings and indexing)
    await loadDocumentsToVectorStore(documents, indexName);
    console.log(`Documents successfully loaded into vector store index "${indexName}"`);
  } catch (error) {
    console.error(`Error creating or feeding index ${indexName}:`, error);
    throw error;
  }
}

/**
 * Deletes all indices defined in the integrations table.
 * @returns {Promise<void>}
 */
async function deleteIntegrationIndices() {
  console.log(`Querying integrations table for indices to delete...`);
  try {
    const integrations = await prisma.Integration.findMany({
      select: { opensearchIndexId: true }
    });
    for (const { opensearchIndexId: indexName } of integrations) {
      console.log(`Deleting OpenSearch index: ${indexName}`);
      try {
        const exists = await opensearch.indices.exists({ index: indexName });
        if (exists) {
          await opensearch.indices.delete({ index: indexName });
          console.log(`Index ${indexName} deleted successfully`);
        } else {
          console.log(`Index ${indexName} does not exist`);
        }
      } catch (error) {
        console.error(`Error deleting index ${indexName}:`, error);
      }
    }
  } catch (error) {
    console.error("Error retrieving integrations for index deletion:", error);
    throw error;
  }
}

/**
 * Generates sample documents for a given integration type.
 * @param {string} integrationType - The type of integration (e.g., "jira", "ftp", "web").
 * @returns {Array} - An array of sample documents.
 */
function generateSampleDocuments(integrationType) {
  const documents = [];
  
  const sampleTexts = {
    jira: [
      {
        title: "User Login Failure",
        source: "https://jira.company.com/browse/PROJ-101",
        text:
          "As a user, I want to be notified when my login attempt fails so that I can retry with correct credentials. Includes error logs and steps to reproduce the issue."
      },
      {
        title: "Improve Dashboard Load Time",
        source: "https://jira.company.com/browse/PROJ-102",
        text:
          "User story: The dashboard should load within 2 seconds under normal usage. Contains performance benchmarks and UI optimization tasks."
      },
      {
        title: "Implement Password Reset Feature",
        source: "https://jira.company.com/browse/PROJ-103",
        text:
          "Feature ticket: Create a secure password reset flow including email verification, rate limiting, and error handling."
      },
      {
        title: "Data Export Timeout Fix",
        source: "https://jira.company.com/browse/PROJ-104",
        text:
          "Bug report: Data export process times out with larger datasets. This ticket includes detailed logs and proposed refactoring solutions."
      },
      {
        title: "Notification System Enhancement",
        source: "https://jira.company.com/browse/PROJ-105",
        text:
          "Enhancement: Improve the notification delivery system to ensure reliable message dispatch under high load conditions. Acceptance criteria and retry logic are defined."
      },
      {
        title: "Mobile Interface Redesign",
        source: "https://jira.company.com/browse/PROJ-106",
        text:
          "User story: Redesign the mobile interface to enhance user experience and responsive design. Includes design mockups and user feedback."
      }
    ],
    ftp: [
      {
        title: "FTP Server Performance Metrics Q1",
        source: "https://docs.company.com/ftp/performance-q1.pdf",
        text:
          "This document provides an overview of FTP server performance in Q1, including transfer speeds, downtime statistics, and error rate analysis."
      },
      {
        title: "FTP User Guide",
        source: "https://docs.company.com/ftp/user-guide.pdf",
        text:
          "A comprehensive guide describing how to securely connect to the FTP server, with step-by-step instructions, configuration examples, and troubleshooting tips."
      },
      {
        title: "FTP Security Audit Report",
        source: "https://docs.company.com/ftp/security-audit.pdf",
        text:
          "This security audit report reviews vulnerability assessments and penetration testing results for the FTP server, along with recommended security enhancements."
      },
      {
        title: "FTP Protocol Documentation",
        source: "https://docs.company.com/ftp/protocol-documentation.pdf",
        text:
          "Detailed technical documentation for the FTP protocol implementation, including performance tuning guidelines and protocol optimizations."
      },
      {
        title: "FTP Downtime Analysis",
        source: "https://docs.company.com/ftp/downtime-analysis.pdf",
        text:
          "A thorough analysis of recent FTP downtime incidents, presenting statistical data, root cause analysis, and corrective measures."
      },
      {
        title: "Scalability & Future Performance Report",
        source: "https://docs.company.com/ftp/scalability-report.pdf",
        text:
          "An in-depth report discussing the scalability of the FTP server, future capacity planning, and infrastructure recommendations to improve performance."
      }
    ],
    web: [
      {
        title: "Internal Help Desk Portal - Overview",
        source: "https://intranet.company.com/helpdesk",
        text:
          "An overview of the internal help desk portal describing features, navigation, and guidelines for employees to submit support tickets."
      },
      {
        title: "Competitor Website Analysis",
        source: "https://intranet.company.com/competitor-analysis",
        text:
          "A detailed analysis of competitor websites focusing on design, usability, and key functionalities used for strategic benchmarking."
      },
      {
        title: "Employee Onboarding Portal",
        source: "https://intranet.company.com/onboarding",
        text:
          "This section of the internal website assists new hires with onboarding procedures, company policies, and training schedules."
      },
      {
        title: "Corporate Blog Archive",
        source: "https://intranet.company.com/blog",
        text:
          "An archive of corporate blog posts covering industry news, internal updates, and thought leadership articles written by company experts."
      },
      {
        title: "IT Support Portal",
        source: "https://intranet.company.com/it-support",
        text:
          "A portal dedicated to IT support, offering troubleshooting guides, FAQ sections, and an interface for submitting technical issues."
      },
      {
        title: "Marketing Campaign Dashboard",
        source: "https://intranet.company.com/marketing-campaign",
        text:
          "An internal landing page for marketing campaign updates, featuring performance analytics, promotional assets, and strategic overviews."
      },
      {
        title: "HR Resources & Benefits",
        source: "https://intranet.company.com/hr",
        text:
          "This internal website portal provides access to HR resources, employee benefits information, and corporate policy documents."
      },
      {
        title: "Project Management Dashboard",
        source: "https://intranet.company.com/projects",
        text:
          "A dynamic dashboard displaying detailed project statuses, deadlines, and collaboration tools to streamline cross-departmental efforts."
      },
      {
        title: "Annual Financial Report",
        source: "https://intranet.company.com/annual-report",
        text:
          "A section of the internal portal dedicated to the annual financial report, summarizing key performance indicators, revenue statistics, and strategic insights."
      },
      {
        title: "Client Access & Support Portal",
        source: "https://intranet.company.com/client-portal",
        text:
          "A secure portal that provides external clients with access to project updates, billing information, and tailored support resources."
      }
    ]
  };
  
  const items = sampleTexts[integrationType];
  if (!items) return [];
  
  for (let i = 0; i < items.length; i++) {
    const entry = items[i];
    const doc = {
      text: entry.text,
      metadata: {
        source: entry.source,
        title: entry.title
      }
    };
    if (integrationType === "ftp" && entry.page) {
      doc.metadata.page = entry.page;
    }
    documents.push(doc);
  }
  return documents;
}


/**
 * Main function: deletes and reloads indices with sample data.
 */
async function run() {
  try {
    await deleteIntegrationIndices();

    const integrations = await prisma.Integration.findMany({
      select: {
        opensearchIndexId: true,
        providerType: true,
      },
    });

    for (const { opensearchIndexId: indexName, providerType: type } of integrations) {
      const documents = generateSampleDocuments(type);
      if (documents.length === 0) {
        console.warn(`No documents found for integration type "${type}"`);
        continue;
      }
      await createIndexAndFeedDocuments(indexName, documents);
    }

    console.log("✅ All indices have been deleted and reloaded.");
    process.exit(0);
  } catch (err) {
    console.error("❌ Error during index reload:", err);
    process.exit(1);
  }
}

if (require.main === module) {
  run();
}


module.exports = { createIndexAndFeedDocuments, deleteIntegrationIndices, generateSampleDocuments };