const { clerkClient } = require("@clerk/express");
const { v4: uuidv4 } = require("uuid");

/**
 * Creates a user in Clerk or retrieves an existing one
 * @param {Object} userData - User data for creation
 * @param {string} userData.email - User's email
 * @param {string} userData.password - User's password
 * @param {string} userData.firstName - User's first name
 * @param {string} userData.lastName - User's last name
 * @returns {Promise<Object|null>} - Clerk user object or null if creation failed
 */
async function createOrGetClerkUser(userData) {
  try {
    // Ensure password meets Clerk's requirements (min 8 chars)
    const securePassword = userData.password.length >= 8 
      ? userData.password 
      : `${userData.password}${uuidv4().substring(0, 4)}`;
    
    console.log(`Attempting to create Clerk user with email: ${userData.email}`);
    
    // Try to create the user with the correct format for emailAddress
    const clerkUser = await clerkClient.users.createUser({
      emailAddress: [userData.email],
      password: securePassword,
      firstName: userData.firstName || "User",
      lastName: userData.lastName || "Name"
    });
    
    console.log(`Successfully created Clerk user with ID: ${clerkUser.id}`);
    return clerkUser;
  } catch (error) {
    console.error(`Error creating user in Clerk: ${error.message}`);
    console.error(`Error details: ${JSON.stringify(error, null, 2)}`);
    
    // If user already exists, try to fetch them
    if (error.message && error.message.includes("already exists")) {
      try {
        console.log(`Attempting to fetch existing user with email: ${userData.email}`);
        const users = await clerkClient.users.getUserList({
          emailAddress: [userData.email]
        });
        
        if (users && users.length > 0) {
          console.log(`Found existing Clerk user with ID: ${users[0].id}`);
          return users[0];
        }
      } catch (fetchError) {
        console.error(`Error fetching existing Clerk user: ${fetchError.message}`);
      }
    }
    
    return null;
  }
}

/**
 * Updates a Clerk user's metadata
 * @param {string} clerkUserId - Clerk user ID
 * @param {Object} metadata - Metadata to update
 * @returns {Promise<Object|null>} - Updated user or null if update failed
 */
async function updateClerkUserMetadata(clerkUserId, metadata) {
  try {
    console.log(`Updating metadata for Clerk user ${clerkUserId}`);
    console.log(metadata);
    const updatedUser = await clerkClient.users.updateUserMetadata(
      clerkUserId,
      { publicMetadata: metadata }
    );
    console.log(`Successfully updated metadata for Clerk user ${clerkUserId}`);
    return updatedUser;
  } catch (error) {
    console.error(`Error updating Clerk user metadata: ${error.message}`);
    console.error(`Error details: ${JSON.stringify(error, null, 2)}`);
    return null;
  }
}

module.exports = {
  createOrGetClerkUser,
  updateClerkUserMetadata
};

