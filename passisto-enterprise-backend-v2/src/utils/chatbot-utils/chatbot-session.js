const { BufferWindowMemory } = require("langchain/memory");
const { config : dotenvConfig } = require('dotenv');
const { MongoClient } = require('mongodb');
const { MongoDBChatMessageHistory } = require("@langchain/mongodb");

dotenvConfig();

let mongoclient;
let historyCollection;


// MongoDB Singleton
const getMongoCollections = async () => {
  try {
    if (!mongoclient) {
      console.log("Initializing MongoDB connection...");
      mongoclient = new MongoClient(process.env.MONGO_URI, {
        maxPoolSize: 50, // Optimize connection pool size
        minPoolSize: 5,  // Minimum number of connections
      });
      await mongoclient.connect(); // Establish the connection
      console.log("Connected to MongoDB");
      // Get the collection once after connecting
      historyCollection = mongoclient.db('langchain').collection('memory');
    } else if (!historyCollection) {
      // If client exists but collection doesn't, initialize the collection
      console.log("Initializing MongoDB collection...");
      historyCollection = mongoclient.db('langchain').collection('memory');
    }
    // Verify connection is still alive with a ping
    await mongoclient.db("admin").command({ ping: 1 });
    return [mongoclient, historyCollection];
  } catch (error) {
    console.error("MongoDB connection error:", error);
    // Try to reconnect if there was an error
    try {
      if (mongoclient) {
        await mongoclient.close();
      }
      mongoclient = new MongoClient(process.env.MONGO_URI, {
        maxPoolSize: 50,
        minPoolSize: 5,
      });
      await mongoclient.connect();
      console.log("Reconnected to MongoDB");

      historyCollection = mongoclient.db('langchain').collection('memory');
      return [mongoclient, historyCollection];
    } catch (reconnectError) {
      console.error("MongoDB reconnection failed:", reconnectError);
      throw new Error("Failed to connect to MongoDB");
    }
  }
}


const getMongoSessionHistory = async (historyCollection, sessionId, userId = null) => {
  // First, check if a session already exists and update it with userId if needed
  if (userId) {
    try {
      // Try to update an existing session with the user ID only in the document root
      await historyCollection.updateOne(
        { sessionId: sessionId },
        {
          $set: {
            userId: userId  // Store userId directly in the document root only
          },
        },
        { upsert: false }
      );
      console.log(`Updated session ${sessionId} with userId ${userId} in document root`);
    } catch (error) {
      console.error('Error updating session with userId:', error);
    }
  }
  const chatHistory = new BufferWindowMemory({
    memoryKey: 'chat_history',
    k: 10,
    returnMessages: true,
    chatHistory: new MongoDBChatMessageHistory({
      collection: historyCollection,
      sessionId: sessionId
      // No longer using sessionMetadata or metadata for userId
    }),
  });
  return chatHistory.chatHistory;
}

module.exports = { getMongoCollections, getMongoSessionHistory }