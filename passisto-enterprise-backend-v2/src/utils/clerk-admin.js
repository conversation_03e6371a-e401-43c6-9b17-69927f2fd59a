const { clerkClient } = require("@clerk/clerk-sdk-node");

/**
 * Truncates seeded users in Clerk
 * This function will delete all users that have the seedUser flag in their metadata
 * @returns {Promise<number>} - Number of deleted users
 */
async function truncateClerkUsers() {
  console.log("Truncating seeded users in Clerk...");
  try {
    // Check if Clerk API is accessible
    try {
      await clerkClient.users.getUserList({ limit: 1 });
    } catch (error) {
      console.error(`Clerk API not accessible: ${error.message}`);
      console.log("Skipping Clerk user truncation");
      return 0;
    }

    // Get all users from Clerk
    const responseUsers = await clerkClient.users.getUserList({
      limit: 100 // Adjust as needed
    });

    const allUsers = responseUsers.data;
    
    console.log(`Found ${allUsers.length} users in Clerk`);
    
    // Filter for seeded users (those with seedUser: true in publicMetadata)
    const seededUsers = allUsers.filter(user => 
      user.publicMetadata && user.publicMetadata.seedUser === true
    );
    
    console.log(`Found ${seededUsers.length} seeded users to delete`);
    
    // Delete each seeded user
    let deletedCount = 0;
    for (const user of seededUsers) {
      try {
        console.log(`Deleting Clerk user: ${user.id} (${user.emailAddresses[0]?.emailAddress || 'no email'})`);
        await clerkClient.users.deleteUser(user.id);
        deletedCount++;
      } catch (deleteError) {
        console.error(`Error deleting user ${user.id}: ${deleteError.message}`);
      }
    }
    
    console.log(`Successfully deleted ${deletedCount} seeded users from Clerk`);
    return deletedCount;
  } catch (error) {
    console.error("Error truncating Clerk users:", error);
    return 0;
  }
}

/**
 * Lists all users in Clerk with their metadata
 * @returns {Promise<Array>} - Array of Clerk users
 */
async function listClerkUsers() {
  try {
    const users = await clerkClient.users.getUserList({
      limit: 100 // Adjust as needed
    });
    
    return users.map(user => ({
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress || 'No email',
      firstName: user.firstName,
      lastName: user.lastName,
      metadata: user.publicMetadata,
      createdAt: user.createdAt
    }));
  } catch (error) {
    console.error("Error listing Clerk users:", error);
    return [];
  }
}

module.exports = {
  truncateClerkUsers,
  listClerkUsers
};