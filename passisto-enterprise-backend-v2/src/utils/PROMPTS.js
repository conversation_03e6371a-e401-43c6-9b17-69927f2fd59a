const FORM_BUILDER_ENHANCE_DESCRIPTION_PROMPT = (userInput) => `
I need to create a form for: "${userInput}".
        
Please enhance this description by adding more details about what fields might be needed,
what validations would be appropriate, and any conditional logic that might be useful.

Make the description more specific and detailed, but keep it concise (max 3-4 sentences).
     
`;

const FORM_BUILDER_GENERATE_TEMPLATE_PROMPT = (userInput) => `
Generate a form template for: "${userInput}".
        
        Return a JSON object with the following structure:
        {
          "title": "Form title based on the intent",
          "description": "Brief description of the form purpose",
          "fields": [
            {
              "id": "field-1",
              "type": "text|textarea|email|select|checkbox|radio|file",
              "label": "Field label",
              "required": true|false,
              "validation": "email" (only for email type),
              "options": ["Option 1", "Option 2"] (for select and radio types),
              "checkboxLabel": "Label for checkbox" (for checkbox type)
            }
          ]
        }
        
        Include appropriate fields based on the form intent. For example, if it's a contact form, include name, email, and message fields. If it's a job application, include fields for personal information, experience, and file upload for resume.
        
        IMPORTANT: Return ONLY the JSON object, nothing else.
`;

const EMAIL_BUILDER_ENHANCE_DESCRIPTION_PROMPT = (userInput) => `
You are an expert email marketing specialist. Your task is to enhance the user's email description by adding specific details that will help create a more effective and professional email template.

USER'S ORIGINAL DESCRIPTION:
${userInput}

Please enhance this description by:
1. Identifying the type of email (welcome, newsletter, promotional, etc.) if not specified
2. Suggesting a color scheme that matches the brand or purpose if not specified
3. Adding specific sections that would be appropriate for this type of email
4. Suggesting appropriate imagery or visual elements
5. Recommending call-to-action elements
6. Specifying tone and style details (formal, casual, friendly, professional)
7. Suggesting where lists or tables might be useful for organizing information
8. Recommending places where social icons would enhance engagement
9. Adding any other relevant details that would help create a better email template

Your enhanced description should be comprehensive but concise, focusing on practical details that will improve the email design. Do not include explanations or justifications - just provide the enhanced description itself.

`;
const EMAIL_BUILDER_GENERATE_TEMPLATE_PROMPT = (userInput) => `

You are an email template generator. Your goal is to create a professional and visually appealing email template using ONLY the components and settings specified below.

AVAILABLE COMPONENTS:
1. header - A heading for the email
2. text - A paragraph or block of text
3. image - An image with optional link
4. button - A clickable button with link
5. divider - A horizontal line separator
6. footer - Footer text, typically for copyright and unsubscribe info
7. list - A bulleted or numbered list to organize information clearly
8. table - A table layout for displaying structured data or product comparison
9. social - Social media icons linking to platforms like Facebook, Twitter, LinkedIn, etc.

AVAILABLE SETTINGS FOR EACH COMPONENT:
- All components: padding, margin, alignment (left, center, right), backgroundColor
- Text-based components (header, text, button, footer): fontSize, color, fontWeight, lineHeight
- Image: url, altText, width, height, borderRadius, linkImage (true/false), linkUrl
- Button: url, borderRadius, buttonWidth (auto or full), borderColor, borderWidth
- Divider: dividerColor, dividerWidth, dividerStyle (solid, dashed, dotted)
- List: listType (bullet, number), items (array of strings), fontSize, color, fontWeight
- Table: headers (array of strings), rows (array of arrays), fontSize, color, headerBackgroundColor, borderColor
- Social: iconSize, iconSpacing, iconColor, alignment, socialLinks (array of platform objects)

IMPORTANT GUIDELINES:
1. Only use the component types listed above.
2. You may use each component multiple times as necessary.
3. Ensure that each component includes the appropriate settings for professional, consistent design.
4. For images, use the URL "/placeholder.svg?height=200&width=600" for the image source.
5. The email should have a clean, organized layout with sufficient white space, good typography, and attention to detail.
6. Use proper formatting for each component and maintain a balance between text and visual elements. The tone of the email should be professional and engaging, while ensuring accessibility.
7. DO NOT include HTML tags like <a href> directly in the content. Use the appropriate component (like button) instead.
8. For links, use the button component or the linkImage setting for images.
9. Include social media icons at the bottom of the email for better engagement.
10. Make sure all links (buttons, images, social icons) have target="_blank" attribute to open in a new tab.

OUTPUT FORMAT:
Return a JSON array of components where each component includes the following properties:
- "title": A concise title for the email based on its intent.
- "description": A brief description of the email’s purpose.
- "fields": An array of components where each component includes:
  . "id": A unique identifier for each component (use UUID or a simple numeric identifier).
  . "type": The component type (header, text, image, button, divider, footer, list, table, social).
  . "content": The actual content (e.g., text or image URL), if applicable.
  . "settings": An object containing the settings for that component (e.g., padding, font size, alignment, etc.).

For list components, use this format:
{
  "id": "list-1",
  "type": "list",
  "settings": {
    "padding": "16px",
    "margin": "0",
    "listType": "bullet", // or "number"
    "fontSize": "16px",
    "color": "#333333"
  },
  "items": ["Item 1", "Item 2", "Item 3"] // Array of list items
}

For table components, use this format:
{
  "id": "table-1",
  "type": "table",
  "settings": {
    "padding": "16px",
    "margin": "0",
    "fontSize": "14px",
    "color": "#333333",
    "headerBackgroundColor": "#f3f4f6",
    "borderColor": "#e5e7eb"
  },
  "headers": ["Column 1", "Column 2", "Column 3"], // Array of header texts
  "rows": [
    ["Row 1, Cell 1", "Row 1, Cell 2", "Row 1, Cell 3"],
    ["Row 2, Cell 1", "Row 2, Cell 2", "Row 2, Cell 3"]
  ] // Array of arrays, each representing a row
}

For social components, use this format:
{
  "id": "social-1",
  "type": "social",
  "settings": {
    "padding": "16px",
    "margin": "0",
    "alignment": "center",
    "iconSize": "32px",
    "iconSpacing": "10px",
    "iconColor": "#333333"
  },
  "socialLinks": [
    { "platform": "facebook", "url": "https://facebook.com", "enabled": true },
    { "platform": "twitter", "url": "https://twitter.com", "enabled": true },
    { "platform": "instagram", "url": "https://instagram.com", "enabled": true },
    { "platform": "linkedin", "url": "https://linkedin.com", "enabled": true }
  ]
}

Example component:
{
  "id": "1",
  "type": "header",
  "content": "Welcome to Our Newsletter",
  "settings": {
    "padding": "20px",
    "margin": "0",
    "backgroundColor": "#f8fafc",
    "fontSize": "28px",
    "color": "#1e293b",
    "alignment": "center",
    "fontWeight": "bold"
  }
}

USER REQUEST:
${userInput}
`;
const PREPARE_INTERVIEW_PROMPT = `
        Prepare questions for a job interview.
        The job role is {role}.
        The job experience level is {level}.
        The tech stack used in the job is: {techstack}.
        The focus between behavioural and technical questions should lean towards: {type}.
        The amount of questions required is: {amount}.
        Please return only the questions, without any additional text.
        The questions are going to be read by a voice assistant so do not use "/" or "*" or any other special characters which might break the voice assistant.
        Return the questions formatted like this:
        ["Question 1", "Question 2", "Question 3"]
        
        Thank you! <3
`;
const GENERATE_INTERVIEW_FEEDBACK_PROMPT = (formattedTranscript) => `
        You are an professional AI interviewer analyzing a mock interview. Your task is to evaluate the candidate based on structured categories. Be thorough and detailed in your analysis. Don't be lenient with the candidate. If there are mistakes or areas for improvement, point them out.
        Transcript:
        ${formattedTranscript}

        Please score the candidate from 0 to 100 in the following areas. Do not add categories other than the ones provided:
        - **Communication Skills**: Clarity, articulation, structured responses.
        - **Technical Knowledge**: Understanding of key concepts for the role.
        - **Problem-Solving**: Ability to analyze problems and propose solutions.
        - **Cultural & Role Fit**: Alignment with company values and job role.
        - **Confidence & Clarity**: Confidence in responses, engagement, and clarity.
        
        Return the result in the following JSON format:
      {
        "totalScore": number,
        "categoryScores": [
          { "name": "Communication Skills", "score": number, "comment": string },
          { "name": "Technical Knowledge", "score": number, "comment": string },
          { "name": "Problem Solving", "score": number, "comment": string },
          { "name": "Cultural Fit", "score": number, "comment": string },
          { "name": "Confidence and Clarity", "score": number, "comment": string }
        ],
        "strengths": [string],
        "areasForImprovement": [string],
        "finalAssessment": string
      }
`;
module.exports = {
  FORM_BUILDER_GENERATE_TEMPLATE_PROMPT,
  FORM_BUILDER_ENHANCE_DESCRIPTION_PROMPT,
  EMAIL_BUILDER_ENHANCE_DESCRIPTION_PROMPT,
  EMAIL_BUILDER_GENERATE_TEMPLATE_PROMPT,
  PREPARE_INTERVIEW_PROMPT,
  GENERATE_INTERVIEW_FEEDBACK_PROMPT
};
