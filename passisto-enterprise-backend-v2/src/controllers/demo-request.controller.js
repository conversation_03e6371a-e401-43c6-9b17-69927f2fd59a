const prisma = require('@/config/db');
const { sendEmailVerification, sendEmailNotification } = require('@/services/mail.service');
const moment = require('moment');
const { verifyTurnstileChallenge } = require('@/services/otp/turnstile-verification');

// Helpers 
const OTP_LENGTH = 6;
const OTP_EXPIRATION_MINUTES = 3 //expired after 3 minutes

const generateOTP = (length = OTP_LENGTH) => {
  return Math.floor(Math.pow(10, length - 1) + Math.random() * 9 * Math.pow(10, length - 1));
};


// Controllers
const createContactSubmission = async (req, res) => {
  const { email, turnstileToken, idempotency_key, language = 'en' } = req.body;

  try {
    if (!turnstileToken) {
      console.log(`[${new Date().toISOString()}] Missing Turnstile token for email: ${email}`);
      return res.status(400).json({ error: 'Turnstile token is required.' });
    }

    const remoteIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const isValid = await verifyTurnstileChallenge(turnstileToken, remoteIp, idempotency_key);

    if (!isValid) {
      console.log(`[${new Date().toISOString()}] Invalid Turnstile token for email: ${email}`);
      return res.status(400).json({ error: 'Invalid Turnstile token.' });
    }

    // Generate a new OTP if no valid OTP exists
    const otp = generateOTP();
    const expiresAt = moment().add(OTP_EXPIRATION_MINUTES, "minutes").toDate();

    await prisma.oTPVerification.create({
      data: { email, otp, expiresAt },
    });

    console.log(`[${new Date().toISOString()}] Generated new OTP for email: ${email}`);

    const emailSent = await sendEmailVerification(email, otp, language);
    if (!emailSent) {
      console.error(`[${new Date().toISOString()}] Failed to send verification email for email: ${email}`);
      return res.status(500).json({ error: "Failed to send verification email." });
    }

    console.log(`[${new Date().toISOString()}] Verification email sent successfully for email: ${email}`);
    res.status(201).json({ success: true, message: "Verification email sent successfully." });
  } catch (err) {
    console.error(`[${new Date().toISOString()}] Error processing contact submission for email: ${email}`, err);
    res.status(500).json({ error: "Failed to process contact submission." });
  }
};



const resendOtp = async (req, res) => {
  const { email, language = 'en' } = req.body;

  try {
    if (!email) {
      return res.status(400).json({ error: 'Email is required.' });
    }

    const newExpiresAt = moment().add(OTP_EXPIRATION_MINUTES, "minutes").toDate();
    const existingOtpRecord = await prisma.oTPVerification.findFirst({
      where: { email },
      orderBy: { createdAt: 'desc' },
    });

    if (existingOtpRecord) {
      await prisma.oTPVerification.update({
      where: { id: existingOtpRecord.id },
      data: { expiresAt: newExpiresAt }
      });
    }

    if (!existingOtpRecord ) {
      return res.status(400).json({ error: 'No active OTP to resend.' });
    }

    const emailSent = await sendEmailVerification(email, existingOtpRecord.otp, language);
    if (!emailSent) {
      return res.status(500).json({ error: 'Failed to resend verification email.' });
    }

    console.log(`[${new Date().toISOString()}] Resent OTP for email: ${email}`);
    res.status(200).json({ success: true, message: 'Verification email resent.' });
  } catch (err) {
    console.error(`[${new Date().toISOString()}] Error resending OTP for email: ${email}`, err);
    res.status(500).json({ error: 'Failed to resend OTP.' });
  }
};





const verifyOtp = async (req, res) => {
  const { email, otp, demoRequestData } = req.body; // Added demoRequestData to destructure from req.body
  try {
    const record = await prisma.oTPVerification.findFirst({
      where: { email },
      orderBy: { createdAt: "desc" },
    });
    
    if (!record) return res.status(400).json({ error: "OTP not found." });
    if (record.otp !== parseInt(otp)) return res.status(400).json({ error: "Invalid OTP." });
    if (moment().isAfter(moment(record.expiresAt))) {
      return res.status(400).json({ error: "OTP has expired." });
    }
    if (record.isVerified) {
      return res.status(400).json({ error: "OTP has already been used." });
    }

    await prisma.oTPVerification.update({
      where: { id: record.id },
      data: { isVerified: true },
    });

    const request = await prisma.demoRequest.create({
      data: demoRequestData, // Using demoRequestData to create demo request
    });

    await sendEmailNotification(demoRequestData);

    res.status(200).json({ message: "OTP verified and demo request created successfully." });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to verify OTP." });
  }
};


module.exports = {
  createContactSubmission,
  verifyOtp,
  resendOtp
};
