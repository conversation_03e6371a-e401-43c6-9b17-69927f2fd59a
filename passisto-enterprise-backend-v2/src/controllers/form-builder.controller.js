const prisma = require("@/config/db");
const { v4: uuidv4 } = require("uuid");
const {
  FORM_BUILDER_ENHANCE_DESCRIPTION_PROMPT,
  FORM_BUILDER_GENERATE_TEMPLATE_PROMPT,
} = require("../utils/PROMPTS");
const fs = require("fs");
const path = require("path");

const { GoogleGenerativeAI } = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI({
  apiKey: "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4",
});

const GEMINI_API_URL =
  "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
const GEMINI_API_KEY = "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4";

const generateWithAI = async (prompt) => {
  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    throw error;
  }
};

const enhanceDescription = async (req, res) => {
  const { userInput } = req.body;

  if (!userInput || !userInput.trim()) {
    return res.status(400).json({ error: "Description is required" });
  }

  try {
    const prompt = FORM_BUILDER_ENHANCE_DESCRIPTION_PROMPT(userInput);
    const enhancedDescription = await generateWithAI(prompt);
    res.json({ enhancedDescription });
  } catch (error) {
    res.status(500).json({ error: "Failed to enhance description" });
  }
};

const generateForm = async (req, res) => {
  const { userInput } = req.body;
  const { companyId } = req.user;

  if (!userInput || !userInput.trim()) {
    return res.status(400).json({ error: "Form intent is required" });
  }

  try {
    const prompt = FORM_BUILDER_GENERATE_TEMPLATE_PROMPT(userInput);
    const text = await generateWithAI(prompt);

    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsedTemplate = JSON.parse(jsonMatch[0]);

      // Ajouter des IDs uniques aux champs si nécessaire
      if (parsedTemplate.fields) {
        parsedTemplate.fields = parsedTemplate.fields.map((field) => ({
          ...field,
          id: field.id || uuidv4(),
        }));
      }

      // Créer l'objet de données pour la base de données
      const formData = {
        title: parsedTemplate.title || "Untitled Form",
        description: parsedTemplate.description || "",
        fields: parsedTemplate.fields || [],
        companyId,
      };

      // Sauvegarder dans la base de données
      const savedForm = await prisma.form.create({
        data: formData,
      });

      res.status(200).json({ formTemplate: savedForm });
    } else {
      res
        .status(500)
        .json({ error: "Failed to generate a valid form template" });
    }
  } catch (error) {
    console.error("Error:", error);
    res.status(500).json({ error: "Failed to generate form and save form" });
  }
};

const getAllForms = async (req, res) => {
  console.log("req.user =", req.user);
  const { companyId } = req.user;

  try {
    const forms = await prisma.form.findMany({
      where: { companyId },
      orderBy: {
        createdAt: "desc",
      },
    });
    res.status(200).json(forms);
  } catch (error) {
    console.error("Error retrieving forms:", error);
    res.status(500).json({ error: "Failed to retrieve form list" });
  }
};

const getFormById = async (req, res) => {
  const { formId } = req.params;
  // const { companyId } = req?.user;

  try {
    const form = await prisma.form.findUnique({
      where: { id: formId },
    });

    if (!form) {
      return res.status(404).json({ error: "Form Not Found Test" });
    }
    console.log("abcd", form);

    // if (form.companyId !== companyId) {
    //   return res
    //     .status(403)
    //     .json({ error: "Forbidden: Form does not belong to the company" });
    // }
    res.status(200).json(form);
  } catch (error) {
    console.error("Error retrieving form:", error);
    res.status(500).json({ error: "Failed to retrieve form" });
  }
};
const updateForm = async (req, res) => {
  const { formId } = req.params;
  const { companyId } = req.user;
  const { title, description, fields, brandColor, logo } = req.body;

  try {
    const form = await prisma.form.findUnique({
      where: { id: formId },
    });

    if (!form) {
      return res.status(404).json({ error: "Form Not Found Test" });
    }

    if (form.companyId !== companyId) {
      return res
        .status(403)
        .json({ error: "Forbidden: Form does not belong to the company" });
    }
    // add other feilds: brandColor, logo
    const updatedForm = await prisma.form.update({
      where: { id: formId },
      data: {
        title,
        description,
        fields,
        brandColor,
        logo,
      },
    });

    res.status(200).json(updatedForm);
  } catch (error) {
    console.error("Error updating form:", error);
    res.status(500).json({ error: "Failed to update form" });
  }
};

const deleteForm = async (req, res) => {
  const { formId } = req.params;
  const { companyId } = req.user;

  try {
    const form = await prisma.form.findUnique({
      where: { id: formId },
    });

    if (!form) {
      return res.status(404).json({ error: "Form Not Found " });
    }

    if (form.companyId !== companyId) {
      return res
        .status(403)
        .json({ error: "Forbidden: Form does not belong to the company" });
    }

    await prisma.form.delete({
      where: { id: formId },
    });

    res.status(200).json({ message: "Form deleted successfully" });
  } catch (error) {
    console.error("Error deleting form:", error);
    res.status(500).json({ error: "Failed to delete form" });
  }
};

const submitForm = async (req, res) => {
  const { formId } = req.params;
  const newResponse = req.body;
  const uploadedFiles = req.files || [];

  try {
    const form = await prisma.form.findUnique({ where: { id: formId } });
    if (!form) {
      return res.status(404).json({ error: "Form Not Found" });
    }
    newResponse.id = uuidv4();

    newResponse.files = uploadedFiles.map((file) => ({
      filename: file.filename,
      originalname: file.originalname,
      path: file.path,
      mimetype: file.mimetype,
      size: file.size,
      label: file.fieldname
    }));
    console.log('nexResponse',newResponse)

    const existingResponse = await prisma.formResponse.findUnique({
      where: { formId },
    });

    if (existingResponse) {
      // Ajoute la nouvelle réponse au tableau
      const previousResponses = Array.isArray(existingResponse.responses)
        ? existingResponse.responses
        : [existingResponse.responses];

      const updatedResponses = [...previousResponses, newResponse];

      const updated = await prisma.formResponse.update({
        where: { id: existingResponse.id },
        data: {
          responses: updatedResponses,
          submittedAt: new Date(),
        },
      });

      return res.status(200).json(updated);
    } else {
      // Crée une nouvelle entrée avec un tableau contenant la première réponse
      const created = await prisma.formResponse.create({
        data: {
          formId,
          responses: [newResponse],
        },
      });

      return res.status(201).json(created);
    }
  } catch (error) {
    console.error("Erreur lors de la soumission :", error);
    return res.status(500).json({ error: "Erreur interne du serveur" });
  }
};

const getFormResponses = async (req, res) => {
  const { formId } = req.params;
  const { companyId } = req.user;

  console.log("Recherche du formulaire avec id:", formId);
  console.log("companyId utilisateur connecté:", companyId);

  try {
    const form = await prisma.form.findUnique({
      where: { id: formId },
      include: {
        responses: true,
      },
    });
    if (!form) {
      return res.status(404).json({ error: "Formulaire non trouvé" });
    }

    if (form.companyId !== companyId) {
      return res.status(403).json({ error: "Accès interdit à ce formulaire" });
    }

    return res.status(200).json(form);
  } catch (error) {
    console.error("Erreur lors de la récupération des réponses :", error);
    return res.status(500).json({ error: "Erreur interne du serveur" });
  }
};

const deleteResponse = async (req, res) => {
  const { formId } = req.params;
  const { responseId } = req.body;
  const { companyId } = req.user;
// check first if form bellongs to company
  try {
    const formResponse = await prisma.formResponse.findFirst({
      where: { formId },
    });
    if (!formResponse || !formResponse.responses) {
      return res
        .status(404)
        .json({ error: "No responses found for this form" });
    }

    console.log("Réponses avant suppression:", formResponse.responses);

    // Vérifie la bonne clé ici (id, responseId, etc.)
    const updatedResponses = formResponse.responses.filter(
      (r) => r.id !== responseId // <-- ici, utilise .id
    );

    console.log("Réponses après suppression:", updatedResponses);

    if (updatedResponses.length === formResponse.responses.length) {
      return res.status(404).json({ error: "Response not found" });
    }

    if (updatedResponses.length === 0) {
      await prisma.formResponse.delete({
        where: { id: formResponse.id },
      });

      return res.status(200).json({
        message: "Last response deleted — formResponse removed entirely",
      });
    }

    const updated = await prisma.formResponse.update({
      where: { id: formResponse.id },
      data: {
        responses: updatedResponses,
      },
    });

    res.status(200).json({
      message: "Response deleted successfully",
      updated,
    });
  } catch (error) {
    console.error("Error deleting response:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

const exportForm = async (req, res) => {
  const { formId } = req.params;
  const { companyId } = req.user;
  const { format } = req.query; // "json" ou "csv"

  try {
    const form = await prisma.form.findUnique({ where: { id: formId } });
    if (!form) return res.status(404).json({ error: "Form Not Found" });
    if (form.companyId !== companyId)
      return res.status(403).json({ error: "Forbidden" });

    if (format === "json" || !format) {
      // Export uniquement la structure du formulaire
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${form.title
          .toLowerCase()
          .replace(/\s+/g, "-")}-structure.json"`
      );
      res.setHeader("Content-Type", "application/json");
      return res.status(200).send(JSON.stringify(form.fields, null, 2));
    } else if (format === "csv") {
      // Export uniquement les réponses du formulaire en CSV
      const formResponses = await prisma.formResponse.findMany({
        where: { formId },
      });

      // Fusionner toutes les réponses de tous les objets formResponse
      const allResponses = formResponses.flatMap((fr) =>
        Array.isArray(fr.responses) ? fr.responses : []
      );

      if (!allResponses || allResponses.length === 0)
        return res
          .status(404)
          .json({ error: "No responses found for this form" });

      // On prend les données dans resp.data si ça existe, sinon dans resp directement
      const allKeys = new Set();
      allResponses.forEach((resp) => {
        const obj =
          resp.data && typeof resp.data === "object" ? resp.data : resp;
        if (obj && typeof obj === "object") {
          Object.keys(obj).forEach((key) => allKeys.add(key));
        }
      });
      const headers = Array.from(allKeys);

      // Construit les lignes du CSV
      const csvRows = [
        headers.join(","), // ligne d'entête
        ...allResponses.map((resp) => {
          const obj =
            resp.data && typeof resp.data === "object" ? resp.data : resp;
          return headers
            .map((key) => {
              const value = obj && obj[key] !== undefined ? obj[key] : "";
              return `"${String(value).replace(/"/g, '""')}"`;
            })
            .join(",");
        }),
      ];

      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${form.title
          .toLowerCase()
          .replace(/\s+/g, "-")}-responses.csv"`
      );
      res.setHeader("Content-Type", "text/csv");
      return res.status(200).send(csvRows.join("\n"));
    } else {
      return res
        .status(400)
        .json({ error: `Export to ${format.toUpperCase()} not implemented.` });
    }
  } catch (error) {
    console.error("Error exporting form:", error);
    res.status(500).json({ error: "Failed to export form" });
  }
};

const downloadResponseFile = async (req, res) => {
  const { formId, fileName } = req.params;
  const {companyId}=req.user;

  try {
  
    const uploadPath = path.join(__dirname, '../../uploads', companyId,'form-builder', formId, fileName);
  
    if (!fs.existsSync(uploadPath)) {
      return res.status(404).json({ error: "File not found on server" });
    }
    
    res.download(uploadPath, fileName);
  } catch (error) {
    console.error("Error downloading file:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

module.exports = {
  enhanceDescription,
  generateForm,
  getAllForms,
  getFormById,
  updateForm,
  deleteForm,
  exportForm,
  submitForm,
  getFormResponses,
  deleteResponse,
  downloadResponseFile,
};
