const { clerkClient } = require("@clerk/express");
const { verifyWebhook } = require("@clerk/express/webhooks");
const prisma = require("../config/db");
const { v4: uuidv4 } = require("uuid");
const { sendInviteMail } = require("../services/mail.service");

const handleClerkWebhook = async (req, res) => {
  console.log(
    "------------------------------- Start webhook -------------------------------"
  );
  
  let evt;
  try {
    // Use the simplified verifyWebhook that takes the entire request
    evt = await verifyWebhook(req);
  } catch (err) {
    console.error("Error verifying webhook:", err.message);
    return res
      .status(400)
      .json({ success: false, message: "Invalid webhook signature" });
  }

  const { id } = evt.data;
  const eventType = evt.type;

  console.log(`Received webhook with ID ${id} and event type of ${eventType}`);
  // console.log("Webhook payload:", evt.data);

  // Handle specific event types
  switch (eventType) {
    case "user.created":
      console.log("Processing user creation...");
      const email = evt.data.email_addresses[0].email_address;
      const isInvited = await prisma.invitation.findUnique({
        where: {
          email: email,
        },
      });

      if (isInvited) {
        console.log("User is already invited. Skipping user creation.");
        await prisma.invitation.delete({ where: { email: email } });
        // update the hasCompletedOnboarding
        await prisma.user.update({
          where: { email: email },
          data: { hasCompletedOnboarding: true },
        });
      } else {
        const clerkUserId = evt.data.id;
        const firstName = evt.data.first_name;
        const lastName = evt.data.last_name;
        // Add logic to handle new user creation (e.g., save to database)
        await saveUser(clerkUserId, email, firstName, lastName);
      }
      break;
      
    case "user.updated":
      console.log("Processing user update...");
      const updatedClerkUserId = evt.data.id;
      const publicMetadata = evt.data.public_metadata || {};
      
      // Check if this update includes company ID and user ID in metadata
      if (publicMetadata.companyId && publicMetadata.userId) {
        console.log(`User ${updatedClerkUserId} metadata updated with companyId: ${publicMetadata.companyId}, userId: ${publicMetadata.userId}`);
        
        // Verify the user exists in our database
        const existingUser = await prisma.user.findUnique({
          where: { clerkId: updatedClerkUserId }
        });
        
        if (existingUser) {
          console.log(`Found matching user in database: ${existingUser.id}`);
          
          // Check if the metadata values match our database
          if (existingUser.id !== publicMetadata.userId || existingUser.companyId !== publicMetadata.companyId) {
            console.log(`Metadata mismatch detected. Correcting metadata...`);
            console.log(`Database values: userId=${existingUser.id}, companyId=${existingUser.companyId}`);
            console.log(`Metadata values: userId=${publicMetadata.userId}, companyId=${publicMetadata.companyId}`);
            
            // Update Clerk metadata with correct values from our database
            try {
              await clerkClient.users.updateUserMetadata(updatedClerkUserId, {
                publicMetadata: {
                  companyId: existingUser.companyId,
                  userId: existingUser.id
                }
              });
              console.log(`Corrected Clerk metadata to match database values`);
            } catch (metadataError) {
              console.error(`Error correcting Clerk metadata: ${metadataError.message}`);
            }
          } else {
            console.log(`Metadata values match database. No correction needed.`);
          }
        } else {
          console.log(`No matching user found in database for Clerk ID: ${updatedClerkUserId}`);
        }
      } else {
        console.log(`User ${updatedClerkUserId} updated, but no company/user metadata changes`);
      }
      break;
      
    case "user.deleted":
      const clerkUserId = evt.data.id;
      console.log(`Processing user deletion for Clerk ID: ${clerkUserId}`);

      try {
        // Find the user from our database
        const user = await prisma.user.findUnique({
          where: { clerkId: clerkUserId },
          include: {
            overrides: true,
            groups: true
          }
        });

        if (user) {
          console.log(`Found user with ID: ${user.id} to delete`);
          
          // Step 1: Delete user's permission overrides if they exist
          if (user.overrides) {
            console.log(`Deleting permission overrides for user ${user.id}`);
            
            // Delete extra permissions
            await prisma.permissionOverride.deleteMany({
              where: { extraOverrideId: user.overrides.id }
            });
            
            // Delete revoked permissions
            await prisma.permissionOverride.deleteMany({
              where: { revokedOverrideId: user.overrides.id }
            });
            
            // Delete the user override record
            await prisma.userOverride.delete({
              where: { id: user.overrides.id }
            });
            
            console.log(`Successfully deleted permission overrides`);
          }
          
          // Step 2: Delete user's group memberships
          console.log(`Deleting group memberships for user ${user.id}`);
          await prisma.userGroup.deleteMany({
            where: { userId: user.id }
          });
          console.log(`Successfully deleted group memberships`);
          
          // Step 3: Finally delete the user
          await prisma.user.delete({
            where: { id: user.id }
          });
          
          console.log(`Successfully deleted user with ID: ${user.id}`);
        } else {
          console.log(`No user found with Clerk ID: ${clerkUserId}`);
        }
      } catch (error) {
        console.error(`Error deleting user with Clerk ID ${clerkUserId}:`, error);
      }
      break;
  }
  res.status(200).json({ success: true, message: "Webhook received" });

  console.log(
    "------------------------------- End webhook -------------------------------"
  );
};

const saveUser = async (
  clerkUserId,
  email,
  firstName = "Your Name",
  lastName = "Your Name"
) => {
  try {
    console.log("Creating a new company...");
    const companyName = uuidv4();
    const company = await prisma.company.create({
      data: {
        name: companyName,
        description: "Newly created company",
      },
    });
    console.log(
      `Company created with ID: ${company.id} and name: ${companyName}`
    );

    console.log("Creating a new group for the company...");
    const companyGroup = await prisma.group.create({
      data: {
        name: "Company", // Group name with capital C
        description: "Default group for the company",
        companyId: company.id, // Associate the group with the company
      },
    });
    console.log(
      `Group created with ID: ${companyGroup.id} and name: ${companyGroup.name}`
    );

    console.log("Creating a new user...");
    const existingUser = await prisma.user.create({
      data: {
        clerkId: clerkUserId,
        email,
        firstName,
        lastName,
        company: {
          connect: { id: company.id },
        },
        roles: {
          connect: { name: "ADMIN" },
        },
      },
    });
    console.log(`User created with ID: ${existingUser.id}`);

    console.log("Adding the user to the company group...");
    await prisma.userGroup.create({
      data: {
        userId: existingUser.id,
        groupId: companyGroup.id,
      },
    });
    console.log("User successfully added to the company group.");

    console.log("Updating user metadata...");
    const updatedUser = await clerkClient.users.updateUserMetadata(
      clerkUserId,
      {
        publicMetadata: {
          companyId: company.id,
          userId: existingUser.id,
        },
      }
    );
    console.log("User metadata updated successfully.");
  } catch (error) {
    console.log("Error creating user:", error);
  }
};

const createAndInviteUser = async (req, res, next) => {
  try {
    // console.log("Authenticated User ID:", req.user.userId);
    console.log("Authenticated Company ID:", req.user.companyId);
    const { email, password, firstName, lastName } = req.body;
    const companyId = req.user.companyId;
    if (!email || !password || !firstName || !lastName || !companyId) {
      return res
        .status(400)
        .json({ success: false, message: "Missing required fields" });
    }
    console.log(
      `Creating user in clerk with email: ${email} for company ID: ${companyId}`
    );

    const clerkUser = await clerkClient.users.createUser({
      emailAddress: [email],
      password: password,
    });

    if (clerkUser) {
      console.log(`Clerk user created successfully with ID: ${clerkUser.id}`);

      const createdUser = await prisma.user.create({
        data: {
          clerkId: clerkUser.id,
          email,
          firstName,
          lastName,
          company: {
            connect: { id: companyId },
          },
          roles: {
            connect: { name: "MEMBER" },
          },
        },
      });
      console.log(`User data saved to database with ID: ${createdUser.id}`);

      const updatedUser = await clerkClient.users.updateUserMetadata(
        clerkUser.id,
        {
          publicMetadata: {
            companyId: companyId,
            userId: createdUser.id,
          },
        }
      );
      console.log("User metadata updated successfully.");

      const invitation = await prisma.invitation.upsert({
        where: { email },
        update: { companyId },
        create: {
          email,
          companyId,
        },
      });

      console.log(
        `Invitation created for email: ${email} in company ID: ${companyId} with ID: ${invitation.id}`
      );

      await sendInviteMail(email, password);

      res.status(200).json({
        success: true,
        message: "User created and invited successfully",
      });
    }
  } catch (error) {
    console.error("Error occurred while creating and inviting user:", error);
    res
      .status(500)
      .json({ success: false, message: "Error occurred while creating user" });
  }
};

module.exports = { handleClerkWebhook, createAndInviteUser };
