const stripe = require("../config/stripe");
const prisma = require("../config/db");

// Utility to extract feature value
const getFeatureValue = (features, key) => {
  return features.find((f) => f.key === key)?.value || 0;
};

const getPlans = async (req, res) => {
  try {
    const products = await stripe.products.list({
      active: true,
      expand: ["data.default_price"],
    });

    const plans = [];

    // Define which metadata keys are considered features and their display info
    const FEATURE_DEFINITIONS = {
      maxEmailAgents: {
        id: "email-builder",
        name: "Email Builder",
        unit: "emails/month",
        description: "AI-powered email templates",
      },
      maxFormAgents: {
        id: "form-builder",
        name: "Form Builder",
        unit: "forms/month",
        description: "AI-powered form creation",
      },
      maxAiInterviewHours: {
        id: "ai-interviewer",
        name: "AI Interviewer",
        unit: "minutes/month",
        description: "Smart candidate screening",
        convert: (v) => Math.round(parseFloat(v) * 60), // convert hours to minutes
      },
      maxSearchQueries: {
        id: "search",
        name: "Search Queries",
        unit: "queries/month",
        description: "AI-powered search",
      },
      maxChatMessages: {
        id: "chat",
        name: "Chat Messages",
        unit: "messages/month",
        description: "AI chat messages",
      },
      maxUsers: {
        id: "users",
        name: "Users",
        unit: "users",
        description: "Maximum users allowed",
      },
      storageLimitGb: {
        id: "storage",
        name: "Storage",
        unit: "GB",
        description: "Total storage limit",
      },
      fileStorageLimitGb: {
        id: "file-storage",
        name: "File Storage",
        unit: "GB/file",
        description: "Max file size per upload",
      },
    };

    for (const product of products.data) {
      if (product.metadata.is_online !== "false") continue;

      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
      });

      // Build features array dynamically
      const features = Object.entries(FEATURE_DEFINITIONS)
        .map(([key, def]) => {
          let rawValue = product.metadata[key];
          if (rawValue === undefined) return null;
          let value =
            typeof def.convert === "function"
              ? def.convert(rawValue)
              : parseInt(rawValue);
          return {
            id: def.id,
            name: def.name,
            limit:
              value !== null && !isNaN(value)
                ? `${value} ${def.unit}`
                : "N/A",
            description: def.description,
          };
        })
        .filter(Boolean);

      // Add static features if needed
      features.push({
        id: "priority-support",
        name: "Priority Support",
        limit: "24/7 email support",
        description: "Dedicated assistance",
      });

      const monthly = prices.data.find(
        (p) => p.recurring?.interval === "month"
      );
      const yearly = prices.data.find((p) => p.recurring?.interval === "year");

      let annualDiscount = 0;
      if (monthly && yearly) {
        const monthlyFromAnnual = yearly.unit_amount / 12;
        annualDiscount = Math.round(
          (1 - monthlyFromAnnual / monthly.unit_amount) * 100
        );
      }

      plans.push({
        name: product.name,
        monthlyPrice: monthly ? monthly.unit_amount / 100 : 0,
        monthlyPriceId: monthly?.id || null,
        yearlyPriceId: yearly?.id || null,
        annualDiscount: annualDiscount > 0 ? annualDiscount : 0,
        features,
      });
    }

    res.status(200).json(plans.sort((a, b) => a.monthlyPrice - b.monthlyPrice));
  } catch (error) {
    console.error("Error fetching plans:", error);
    res.status(500).json({ error: "Failed to fetch subscription plans" });
  }
};

const createCheckoutSession = async (req, res) => {
  try {
    const { priceId, planName } = req.body;
    const { companyId } = req.user;
    console.log(companyId);
    if (!priceId || !companyId) {
      return res
        .status(400)
        .json({ error: "Price ID and Company ID are required" });
    }

    const sessionData = {
      mode: "subscription",
      payment_method_types: ["card"],
      line_items: [{ price: priceId, quantity: 1 }],
      metadata: {
        companyId: companyId,
      },
      success_url: `${process.env.FRONTEND_NGINX_CHECKOUT_SUCESS}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: process.env.FRONTEND_NGINX_CHECKOUT_CANCEL,
      allow_promotion_codes: true,
    };

    if (planName === "Free") {
      sessionData.payment_method_collection = "if_required";
      sessionData.subscription_data = {
        trial_period_days: 7,
        trial_settings: { end_behavior: { missing_payment_method: "cancel" } },
      };
    }
    console.log(sessionData);
    const session = await stripe.checkout.sessions.create(sessionData);
    res.status(200).json({ url: session.url });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    res.status(500).json({ error: "Failed to create checkout session" });
  }
};

const handleStripeWebhook = async (req, res) => {
  console.log("Received Stripe webhook:", req.body);
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
  const sig = req.headers["stripe-signature"];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    const handleSubscription = async (session, subscriptionId, customerId) => {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      const product = await stripe.products.retrieve(
        subscription.items.data[0].price.product
      );
      const companyId = session.metadata?.companyId;

      const dbSubscription = await prisma.subscription.upsert({
        where: { stripeProductId: product.id },
        update: { name: product.name },
        create: { name: product.name, stripeProductId: product.id },
      });
      console.log("dbSubscription: ", dbSubscription);

      // Dynamically upsert all feature keys from product.metadata
      for (const [key, value] of Object.entries(product.metadata)) {
        // Skip non-feature metadata keys if needed
        if (["is_online", "description"].includes(key)) continue;
        await prisma.subscriptionFeature.upsert({
          where: {
            subscriptionId_key: {
              subscriptionId: dbSubscription.id,
              key,
            },
          },
          update: { value: parseFloat(value) },
          create: {
            subscriptionId: dbSubscription.id,
            key,
            value: parseFloat(value),
          },
        });
      }
      console.log("companyId: ", companyId);

      if (companyId) {
        await prisma.company.update({
          where: { id: companyId },
          data: {
            stripeCustomerId: customerId,
            stripeSubscriptionId: subscription.id,
            subscriptionStatus: subscription.status,
            subscriptionId: dbSubscription.id,
          },
        });
      }
    };

    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object;
        await handleSubscription(
          session,
          session.subscription,
          session.customer
        );
        break;
      }

      case "customer.subscription.updated": {
        const subscription = event.data.object;
        const company = await prisma.company.findFirst({
          where: { stripeSubscriptionId: subscription.id },
        });
        if (company) {
          await handleSubscription(
            { metadata: { companyId: company.id } },
            subscription.id,
            subscription.customer
          );
        }
        break;
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object;
        const company = await prisma.company.findFirst({
          where: { stripeSubscriptionId: subscription.id },
        });

        if (company) {
          await prisma.company.update({
            where: { id: company.id },
            data: {
              subscriptionStatus: "canceled",
              subscriptionId: null,
            },
          });
        }
        break;
      }
    }

    res.json({ received: true });
  } catch (err) {
    console.error("Error processing webhook:", err);
    res.status(500).json({ error: "Webhook processing failed" });
  }
};

const checkSubscriptionStatus = async (req, res) => {
  try {
    const { companyId } = req.user;

    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: {
            features: true,
          },
        },
      },
    });

    if (!company) return res.status(404).json({ error: "Company not found" });

    if (!company.stripeSubscriptionId) {
      return res.status(200).json({
        status: "no_subscription",
        subscription: null,
      });
    }

    const stripeSubscription = await stripe.subscriptions.retrieve(
      company.stripeSubscriptionId
    );

    const maxEmailAgents = getFeatureValue(
      company.subscription?.features || [],
      "maxEmailAgents"
    );
    const maxAiInterviewHours = getFeatureValue(
      company.subscription?.features || [],
      "maxAiInterviewHours"
    );

    const subscriptionData = {
      status: company.subscriptionStatus,
      stripeStatus: stripeSubscription.status,
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      limits: {
        maxEmailAgents,
        maxAiInterviewHours,
      },
    };

    const isActive = ["active", "trialing"].includes(stripeSubscription.status);

    if (company.subscriptionStatus !== stripeSubscription.status) {
      await prisma.company.update({
        where: { id: companyId },
        data: { subscriptionStatus: stripeSubscription.status },
      });
    }

    return res.status(200).json({
      active: isActive,
      subscription: subscriptionData,
    });
  } catch (error) {
    console.error("Error checking subscription status:", error);
    return res
      .status(500)
      .json({ error: "Failed to check subscription status" });
  }
};

const getSubscriptionDetails = async (req, res) => {
  try {
    const { companyId } = req.user;

    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: { features: true },
        },
        usage: true,
      },
    });

    if (!company) return res.status(404).json({ error: "Company not found" });
    console.log(company);
    if (!company.subscriptionId) {
      return res.status(200).json({
        planName: "No Active Subscription",
        email_builder: {
          limit: "0 emails/month",
          description: "AI-powered email templates",
        },
        ai_interviewer: {
          limit: "0 minutes/month",
          description: "AI interview sessions",
        },
        support: {
          limit: "Basic",
          description: "Standard support",
        },
      });
    }

    const subscription = company.subscription;
    const maxEmailAgents = getFeatureValue(
      subscription.features,
      "maxEmailAgents"
    );
    const maxAiInterviewHours = getFeatureValue(
      subscription.features,
      "maxAiInterviewHours"
    );

    const emailUsage =
      company.usage.find((u) => u.key === "emailAgentsUsed")?.value || 0;
    const aiUsage =
      company.usage.find((u) => u.key === "aiInterviewHoursUsed")?.value || 0;

    const subscriptionDetails = {
      planName: subscription.name,
      email_builder: {
        limit: `${maxEmailAgents} emails/month`,
        description: "AI-powered email templates",
      },
      ai_interviewer: {
        limit: `${Math.floor(maxAiInterviewHours * 60)} minutes/month`,
        description: "AI interview sessions",
      },
      support: {
        limit: "24/7 Priority",
        description: "Premium support",
      },
      usage: {
        email_builder: {
          used: emailUsage,
          remaining: maxEmailAgents - emailUsage,
        },
        ai_interviewer: {
          used: aiUsage,
          remaining: maxAiInterviewHours - aiUsage,
        },
      },
    };

    return res.status(200).json(subscriptionDetails);
  } catch (error) {
    console.error("Error fetching subscription details:", error);
    return res
      .status(500)
      .json({ error: "Failed to fetch subscription details" });
  }
};

const createPortalSession = async (req, res) => {
  try {
    const { companyId } = req.user;
    const company = await prisma.company.findUnique({
      where: { id: companyId },
    });

    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }

    if (!company.stripeCustomerId) {
      return res
        .status(400)
        .json({ error: "No Stripe customer found for this company" });
    }

    const session = await stripe.billingPortal.sessions.create({
      customer: company.stripeCustomerId,
      return_url: `${process.env.FRONTEND_URL}/enterprise/dashboard`,
    });

    res.status(200).json({ url: session.url });
  } catch (error) {
    console.error("Error creating portal session:", error);
    res.status(500).json({ error: "Failed to create portal session" });
  }
};

module.exports = {
  getPlans,
  createCheckoutSession,
  handleStripeWebhook,
  checkSubscriptionStatus,
  getSubscriptionDetails,
  createPortalSession,
};
