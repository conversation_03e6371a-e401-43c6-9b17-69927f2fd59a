const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Get all groups for a company with their permissions and member count
 */
const getAllGroups = async (req, res) => {
  try {
    const { companyId } = req.user;
    
    // Get all groups with their permissions and users
    const groups = await prisma.group.findMany({
      where: {
        companyId
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: true // Include users to count them
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    // Format groups with their permissions and member count
    const formattedGroups = groups.map(group => ({
      id: group.id,
      name: group.name,
      description: group.description,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt,
      memberCount: group.users.length, // Add the member count
      permissions: group.permissions.map(gp => ({
        id: gp.id,
        permissionId: gp.permissionId,
        action: gp.permission.action,
        category: gp.permission.category,
        scopeType: gp.scopeType,
        scopeId: gp.scopeId
      }))
    }));
    
    return res.status(200).json(formattedGroups);
  } catch (error) {
    console.error("Error fetching groups:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch groups",
      error: error.message
    });
  }
};

/**
 * Get a group by ID with its members and permissions
 */
const getGroupById = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { companyId } = req.user;
    
    // Get the group with its permissions and users
    const group = await prisma.group.findUnique({
      where: {
        id: groupId,
        companyId // Ensure the group belongs to the user's company
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: {
          include: {
            user: {
              include: {
                roles: true
              }
            }
          }
        }
      }
    });
    
    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group not found or does not belong to your company"
      });
    }
    
    // Format the group with its members and permissions
    const formattedGroup = {
      id: group.id,
      name: group.name,
      description: group.description,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt,
      memberCount: group.users.length,
      permissions: group.permissions.map(gp => ({
        id: gp.id,
        permissionId: gp.permissionId,
        action: gp.permission.action,
        category: gp.permission.category,
        scopeType: gp.scopeType,
        scopeId: gp.scopeId
      })),
      members: group.users.map(userGroup => ({
        id: userGroup.user.id,
        firstName: userGroup.user.firstName,
        lastName: userGroup.user.lastName,
        email: userGroup.user.email,
        role: userGroup.user.roles.map(role => ({
          id: role.id,
          name: role.name
        }))
      }))
    };
    
    return res.status(200).json(formattedGroup);
  } catch (error) {
    console.error("Error fetching group by ID:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch group",
      error: error.message
    });
  }
};

/**
 * Create a new group with permissions
 */
const createGroup = async (req, res) => {
  try {
    const { name, description, permissions } = req.body;
    const { companyId } = req.user;
    
    // Validate input
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        message: "Name and description are required"
      });
    }
    
    // Check if a group with the same name already exists in this company
    const existingGroup = await prisma.group.findFirst({
      where: {
        name,
        companyId
      }
    });
    
    if (existingGroup) {
      return res.status(409).json({
        success: false,
        message: "A group with this name already exists in your company"
      });
    }
    
    // Create the group
    const group = await prisma.group.create({
      data: {
        name,
        description,
        companyId
      }
    });
    
    // If permissions are provided, assign them to the group
    if (Array.isArray(permissions) && permissions.length > 0) {
      // Verify permissions exist
      const validPermissions = await prisma.permission.findMany({
        where: {
          id: {
            in: permissions
          }
        }
      });
      
      const validPermissionIds = validPermissions.map(p => p.id);
      
      // Create group permissions
      if (validPermissionIds.length > 0) {
        const groupPermissions = validPermissionIds.map(permissionId => ({
          groupId: group.id,
          permissionId,
          scopeType: "TEAM",
          scopeId: `team-id:${group.id}`
        }));
        
        await prisma.groupPermission.createMany({
          data: groupPermissions
        });
      }
    }
    
    // Get the created group with its permissions
    const createdGroup = await prisma.group.findUnique({
      where: {
        id: group.id
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });
    
    // Format the response
    const formattedGroup = {
      id: createdGroup.id,
      name: createdGroup.name,
      description: createdGroup.description,
      createdAt: createdGroup.createdAt,
      updatedAt: createdGroup.updatedAt,
      memberCount: 0, // New group has no members
      permissions: createdGroup.permissions.map(gp => ({
        id: gp.id,
        permissionId: gp.permissionId,
        action: gp.permission.action,
        category: gp.permission.category,
        scopeType: gp.scopeType,
        scopeId: gp.scopeId
      }))
    };
    
    return res.status(201).json(formattedGroup);
  } catch (error) {
    console.error("Error creating group:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create group",
      error: error.message
    });
  }
};

/**
 * Update an existing group with new details and permissions
 */
const updateGroup = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { name, description, permissions } = req.body;
    const { companyId } = req.user;
    
    // Validate input
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        message: "Name and description are required"
      });
    }
    
    // Check if the group exists and belongs to the company
    const existingGroup = await prisma.group.findUnique({
      where: {
        id: groupId,
        companyId
      }
    });
    
    if (!existingGroup) {
      return res.status(404).json({
        success: false,
        message: "Group not found or does not belong to your company"
      });
    }
    
    // Check if another group with the same name already exists in this company
    if (name !== existingGroup.name) {
      const duplicateGroup = await prisma.group.findFirst({
        where: {
          name,
          companyId,
          id: { not: groupId } // Exclude the current group
        }
      });
      
      if (duplicateGroup) {
        return res.status(409).json({
          success: false,
          message: "Another group with this name already exists in your company"
        });
      }
    }
    
    // Update the group
    await prisma.group.update({
      where: { id: groupId },
      data: {
        name,
        description
      }
    });
    
    // Update permissions if provided
    if (Array.isArray(permissions)) {
      // Delete existing permissions
      await prisma.groupPermission.deleteMany({
        where: { groupId }
      });
      
      // Add new permissions if any
      if (permissions.length > 0) {
        // Verify permissions exist
        const validPermissions = await prisma.permission.findMany({
          where: {
            id: {
              in: permissions
            }
          }
        });
        
        const validPermissionIds = validPermissions.map(p => p.id);
        
        // Create group permissions
        if (validPermissionIds.length > 0) {
          const groupPermissions = validPermissionIds.map(permissionId => ({
            groupId,
            permissionId,
            scopeType: "TEAM",
            scopeId: groupId
          }));
          
          await prisma.groupPermission.createMany({
            data: groupPermissions
          });
        }
      }
    }
    
    // Get the updated group with its permissions and member count
    const updatedGroup = await prisma.group.findUnique({
      where: {
        id: groupId
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: true // Include users to count them
      }
    });
    
    // Format the response
    const formattedGroup = {
      id: updatedGroup.id,
      name: updatedGroup.name,
      description: updatedGroup.description,
      createdAt: updatedGroup.createdAt,
      updatedAt: updatedGroup.updatedAt,
      memberCount: updatedGroup.users.length,
      permissions: updatedGroup.permissions.map(gp => ({
        id: gp.id,
        permissionId: gp.permissionId,
        action: gp.permission.action,
        category: gp.permission.category,
        scopeType: gp.scopeType,
        scopeId: gp.scopeId
      }))
    };
    
    return res.status(200).json(formattedGroup);
  } catch (error) {
    console.error("Error updating group:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to update group",
      error: error.message
    });
  }
};

/**
 * Add members to a group
 */
const addMembersToGroup = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { userIds } = req.body;
    const { companyId } = req.user;
    
    // Validate input
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "User IDs array is required and cannot be empty"
      });
    }
    
    // Check if the group exists and belongs to the company
    const group = await prisma.group.findUnique({
      where: {
        id: groupId,
        companyId
      }
    });
    
    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group not found or does not belong to your company"
      });
    }
    
    // Verify all users exist and belong to the company
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
        companyId
      }
    });
    
    if (users.length !== userIds.length) {
      return res.status(400).json({
        success: false,
        message: "Some user IDs are invalid or do not belong to your company"
      });
    }
    
    // Get existing user-group relationships to avoid duplicates
    const existingUserGroups = await prisma.userGroup.findMany({
      where: {
        groupId,
        userId: { in: userIds }
      }
    });
    
    const existingUserIds = existingUserGroups.map(ug => ug.userId);
    const newUserIds = userIds.filter(id => !existingUserIds.includes(id));
    
    // Add new users to the group
    if (newUserIds.length > 0) {
      const userGroupData = newUserIds.map(userId => ({
        userId,
        groupId
      }));
      
      await prisma.userGroup.createMany({
        data: userGroupData
      });
    }
    
    // Get the updated group with its members
    const updatedGroup = await prisma.group.findUnique({
      where: {
        id: groupId
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: {
          include: {
            user: {
              include: {
                roles: true
              }
            }
          }
        }
      }
    });
    
    // Format the response
    const formattedGroup = {
      id: updatedGroup.id,
      name: updatedGroup.name,
      description: updatedGroup.description,
      createdAt: updatedGroup.createdAt,
      updatedAt: updatedGroup.updatedAt,
      memberCount: updatedGroup.users.length,
      permissions: updatedGroup.permissions.map(gp => ({
        id: gp.id,
        permissionId: gp.permissionId,
        action: gp.permission.action,
        category: gp.permission.category,
        scopeType: gp.scopeType,
        scopeId: gp.scopeId
      })),
      members: updatedGroup.users.map(userGroup => ({
        id: userGroup.user.id,
        firstName: userGroup.user.firstName,
        lastName: userGroup.user.lastName,
        email: userGroup.user.email,
        role: userGroup.user.roles.map(role => ({
          id: role.id,
          name: role.name
        }))
      }))
    };
    
    return res.status(200).json({
      success: true,
      message: `${newUserIds.length} new members added to the group`,
      group: formattedGroup
    });
  } catch (error) {
    console.error("Error adding members to group:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to add members to group",
      error: error.message
    });
  }
};

/**
 * Remove a member from a group
 */
const removeMemberFromGroup = async (req, res) => {
  try {
    const { groupId, userId } = req.params;
    const { companyId } = req.user;
    
    // Check if the group exists and belongs to the company
    const group = await prisma.group.findUnique({
      where: {
        id: groupId,
        companyId
      }
    });
    
    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group not found or does not belong to your company"
      });
    }
    
    // Check if the user exists and belongs to the company
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        companyId
      }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found or does not belong to your company"
      });
    }
    
    // Check if the user is a member of the group
    const userGroup = await prisma.userGroup.findFirst({
      where: {
        userId,
        groupId
      }
    });
    
    if (!userGroup) {
      return res.status(404).json({
        success: false,
        message: "User is not a member of this group"
      });
    }
    
    // Check if this is the Company group
    if (group.name === "Company") {
      return res.status(403).json({
        success: false,
        message: "Cannot remove users from the Company group"
      });
    }
    
    // Remove the user from the group
    await prisma.userGroup.delete({
      where: {
        id: userGroup.id
      }
    });
    
    // Get the updated group with its members
    const updatedGroup = await prisma.group.findUnique({
      where: {
        id: groupId
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: {
          include: {
            user: {
              include: {
                roles: true
              }
            }
          }
        }
      }
    });
    
    // Format the response
    const formattedGroup = {
      id: updatedGroup.id,
      name: updatedGroup.name,
      description: updatedGroup.description,
      createdAt: updatedGroup.createdAt,
      updatedAt: updatedGroup.updatedAt,
      memberCount: updatedGroup.users.length,
      permissions: updatedGroup.permissions.map(gp => ({
        id: gp.id,
        permissionId: gp.permissionId,
        action: gp.permission.action,
        category: gp.permission.category,
        scopeType: gp.scopeType,
        scopeId: gp.scopeId
      })),
      members: updatedGroup.users.map(userGroup => ({
        id: userGroup.user.id,
        firstName: userGroup.user.firstName,
        lastName: userGroup.user.lastName,
        email: userGroup.user.email,
        role: userGroup.user.roles.map(role => ({
          id: role.id,
          name: role.name
        }))
      }))
    };
    
    return res.status(200).json({
      success: true,
      message: `User removed from the group successfully`,
      group: formattedGroup
    });
  } catch (error) {
    console.error("Error removing member from group:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to remove member from group",
      error: error.message
    });
  }
};

/**
 * Delete a group
 */
const deleteGroup = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { companyId } = req.user;
    
    // Check if the group exists and belongs to the company
    const group = await prisma.group.findUnique({
      where: {
        id: groupId,
        companyId
      }
    });
    
    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group not found or does not belong to your company"
      });
    }
    
    // Check if this is the Company group
    if (group.name === "Company") {
      return res.status(403).json({
        success: false,
        message: "Cannot delete the Company group as it is required for all users"
      });
    }
    
    // Use a transaction to ensure all related records are deleted
    await prisma.$transaction(async (prisma) => {
      // Step 1: Delete all group permissions
      await prisma.groupPermission.deleteMany({
        where: { groupId }
      });
      
      // Step 2: Delete all user-group relationships
      await prisma.userGroup.deleteMany({
        where: { groupId }
      });
      
      // Step 3: Delete the group itself
      await prisma.group.delete({
        where: { id: groupId }
      });
    });
    
    return res.status(200).json({
      success: true,
      message: "Group deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting group:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to delete group",
      error: error.message
    });
  }
};

/**
 * Get all integrations for a specific group
 */
async function getGroupIntegrations(req, res) {
  const { groupId } = req.params; // Group ID
  const { companyId } = req.user;

  try {
    // First verify the group belongs to the company
    const group = await prisma.group.findFirst({
      where: {
        id: groupId,
        companyId
      }
    });

    if (!group) {
      return res.status(404).json({ message: 'Group not found' });
    }

    // Get all integrations associated with this group
    const groupIntegrations = await prisma.groupIntegration.findMany({
      where: {
        groupId: groupId
      },
      include: {
        integration: {
          include: {
            ftp: {
              select: {
                id: true,
                isSecure: true,
                server: true,
                port: true,
                username: true,
              },
            },
            jira: {
              select: {
                id: true,
                domain: true,
                email: true,
                project: true,
              },
            },
            web: {
              select: {
                id: true,
                url: true,
              },
            },
          }
        }
      }
    });

    // Extract just the integration data
    const integrations = groupIntegrations.map(gi => gi.integration);

    return res.status(200).json(integrations);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get group integrations:`, error);
    return res.status(500).json({ message: 'Server error' });
  }
}

module.exports = {
  getAllGroups,
  getGroupById,
  createGroup,
  updateGroup,
  addMembersToGroup,
  removeMemberFromGroup,
  deleteGroup,
  getGroupIntegrations
};
