const prisma = require('../../lib/prisma');
const { validationResult } = require('express-validator');

// Process a speech-to-text transcription
exports.processTranscription = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { workflowRunId, nodeId, transcription, source, sourceType, demoMode, service } = req.body;

    // Get the workflow execution service
    const workflowExecutionService = req.app.get('workflowExecutionService');

    // Get the node executor
    const nodeExecutorFactory = workflowExecutionService.nodeExecutorFactory;
    const executor = nodeExecutorFactory.getExecutor('speech-to-text');

    // Process the transcription
    const result = await executor.processTranscription(workflowRunId, nodeId, {
      transcription,
      source,
      sourceType,
      demoMode,
      service
    });

    if (result.success) {
      res.json(result.output);
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error('Speech-to-text processing error:', error);
    res.status(500).json({ error: error.message });
  }
};
