const prisma = require('../../lib/prisma');

// Get all tasks assigned to the current user
exports.getAssignedTasks = async (req, res) => {
  try {
    const userId = req.user.userId; // Assuming user info is added by auth middleware

    // Get all active workflows
    const workflows = await prisma.workflow.findMany({
      where: { status: 'active' }
    });

    // Extract tasks assigned to the user from all workflows
    const assignedTasks = workflows.flatMap(workflow => {
      return workflow.nodes
        .filter(node => node.type === 'task' && node.data.assignee === userId)
        .map(node => ({
          id: node.id,
          label: node.data.label,
          description: node.data.description,
          dueDate: node.data.dueDate,
          critical: node.data.critical || false,
          workflowName: workflow.name,
          status: node.data.status || 'pending'
        }));
    });

    res.json(assignedTasks);
  } catch (error) {
    console.error('Error fetching assigned tasks:', error);
    res.status(500).json({ error: 'Failed to fetch assigned tasks' });
  }
};

// Mark a task as completed
exports.completeTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.userId;

    // Find the workflow containing this task
    const workflows = await prisma.workflow.findMany({
      where: { status: 'active' }
    });

    let taskFound = false;
    for (const workflow of workflows) {
      const taskNode = workflow.nodes.find(node => 
        node.id === taskId && 
        node.type === 'task' && 
        node.data.assignee === userId
      );

      if (taskNode) {
        // Update the task status
        taskNode.data.status = 'completed';
        await prisma.workflow.update({
          where: { id: workflow.id },
          data: { nodes: workflow.nodes }
        });
        taskFound = true;
        break;
      }
    }

    if (!taskFound) {
      return res.status(404).json({ error: 'Task not found or not assigned to user' });
    }

    res.json({ message: 'Task completed successfully' });
  } catch (error) {
    console.error('Error completing task:', error);
    res.status(500).json({ error: 'Failed to complete task' });
  }
}; 