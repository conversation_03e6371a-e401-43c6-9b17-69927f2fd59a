const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { validationResult } = require('express-validator');

// Get all workflows
exports.getWorkflows = async (req, res) => {
  try {
    const workflows = await prisma.workflow.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            email: true
          }
        },
        runs: {
          take: 5, // Get more runs to check execution status
          orderBy: {
            createdAt: 'desc'
          },
          select: {
            id: true,
            status: true,
            startedAt: true,
            finishedAt: true,
            createdAt: true,
            nodeRuns: {
              select: {
                id: true,
                nodeId: true,
                status: true,
                startedAt: true,
                finishedAt: true,
                output: true
              }
            }
          }
        }
      }
    });

    // Enhance workflows with execution status
    const enhancedWorkflows = workflows.map(workflow => {
      // Check if any runs are currently active
      const isExecuting = workflow.runs.some(run =>
        ['RUNNING', 'PENDING', 'WAITING_FOR_USER'].includes(run.status) && !run.finishedAt
      );

      // Get execution data if workflow is running
      let executionData = null;
      if (isExecuting) {
        const activeRun = workflow.runs.find(run =>
          ['RUNNING', 'PENDING', 'WAITING_FOR_USER'].includes(run.status) && !run.finishedAt
        );

        if (activeRun) {
          // Find the current node (RUNNING or WAITING_FOR_USER)
          const currentNodeRun = activeRun.nodeRuns.find(nodeRun =>
            nodeRun.status === 'RUNNING' || nodeRun.status === 'WAITING_FOR_USER'
          );

          // Find completed nodes for execution path
          const completedNodeIds = activeRun.nodeRuns
            .filter(nodeRun => nodeRun.status === 'SUCCESS')
            .map(nodeRun => nodeRun.nodeId);

          executionData = {
            currentNodeId: currentNodeRun?.nodeId,
            executionPath: completedNodeIds
          };
        }
      }

      return {
        ...workflow,
        isExecuting,
        executionData
      };
    });

    res.json(enhancedWorkflows);
  } catch (error) {
    console.error('Error fetching workflows:', error);
    res.status(500).json({
      message: 'Failed to fetch workflows',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Get single workflow
exports.getWorkflow = async (req, res) => {
  try {
    const workflow = await prisma.workflow.findUnique({
      where: { id: req.params.id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            email: true
          }
        },
        runs: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 5,
          select: {
            id: true,
            status: true,
            startedAt: true,
            finishedAt: true,
            createdAt: true,
            nodeRuns: {
              select: {
                id: true,
                nodeId: true,
                status: true,
                startedAt: true,
                finishedAt: true,
                output: true
              }
            }
          }
        }
      }
    });

    if (!workflow) {
      return res.status(404).json({ message: 'Workflow not found' });
    }

    // Check if any runs are currently active
    const isExecuting = workflow.runs.some(run =>
      ['RUNNING', 'PENDING', 'WAITING_FOR_USER'].includes(run.status) && !run.finishedAt
    );

    // Get execution data if workflow is running
    let executionData = null;
    if (isExecuting) {
      const activeRun = workflow.runs.find(run =>
        ['RUNNING', 'PENDING', 'WAITING_FOR_USER'].includes(run.status) && !run.finishedAt
      );

      if (activeRun) {
        // Find the current node (RUNNING or WAITING_FOR_USER)
        const currentNodeRun = activeRun.nodeRuns.find(nodeRun =>
          nodeRun.status === 'RUNNING' || nodeRun.status === 'WAITING_FOR_USER'
        );

        // Find completed nodes for execution path
        const completedNodeIds = activeRun.nodeRuns
          .filter(nodeRun => nodeRun.status === 'SUCCESS')
          .map(nodeRun => nodeRun.nodeId);

        executionData = {
          currentNodeId: currentNodeRun?.nodeId,
          executionPath: completedNodeIds
        };
      }
    }

    // Return enhanced workflow with execution status
    res.json({
      ...workflow,
      isExecuting,
      executionData
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create workflow
exports.createWorkflow = async (req, res) => {
  console.log('Create workflow request body:', JSON.stringify(req.body, null, 2));
  console.log('User from request:', req.user)

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log('Validation errors:', errors.array());
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { name, description, nodes, edges, viewport, status } = req.body;

    // Ensure nodes and edges are properly formatted for the database
    const formattedNodes = Array.isArray(nodes) ? nodes : [];
    const formattedEdges = Array.isArray(edges) ? edges : [];

    console.log('Formatted data for creation:', {
      name,
      description,
      nodesType: typeof formattedNodes,
      edgesType: typeof formattedEdges,
      viewport: viewport,
      status
    });

    // Log AskAI node data for debugging
    if (Array.isArray(formattedNodes)) {
      formattedNodes.forEach(node => {
        if (node.type === 'ask-ai') {
          console.log(`Backend received AskAI node ${node.id} with model:`, node.data?.model);
        }
      });
    }

    const workflow = await prisma.workflow.create({
      data: {
        name,
        description,
        nodes: formattedNodes,
        edges: formattedEdges,
        viewport: viewport,
        status,
        userId: req.user.userId // Assuming you have user info in req.user from auth middleware
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            email: true
          }
        }
      }
    });

    res.status(201).json(workflow);
  } catch (error) {
    console.error('Error creating workflow:', error);
    res.status(400).json({ message: error.message });
  }
};

// Update workflow
exports.updateWorkflow = async (req, res) => {
  console.log('Update workflow request body:', JSON.stringify(req.body, null, 2));

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log('Validation errors:', errors.array());
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { name, description, nodes, edges, viewport, zoom, status } = req.body;

    // Ensure nodes and edges are properly formatted for the database
    const formattedNodes = Array.isArray(nodes) ? nodes : [];
    const formattedEdges = Array.isArray(edges) ? edges : [];

    // Handle viewport data - either use provided viewport or create from zoom
    let viewportData;
    if (viewport) {
      viewportData = viewport;
    } else if (zoom !== undefined) {
      // For backward compatibility, create viewport from zoom
      viewportData = { x: 0, y: 0, zoom: zoom };
    }

    console.log('Formatted data for update:', {
      name,
      description,
      nodesType: typeof formattedNodes,
      edgesType: typeof formattedEdges,
      viewport: viewportData,
      status
    });

    // Log AskAI node data for debugging
    if (Array.isArray(formattedNodes)) {
      formattedNodes.forEach(node => {
        if (node.type === 'ask-ai') {
          console.log(`Backend received AskAI node ${node.id} with model:`, node.data?.model);
        }
      });
    }

    const workflow = await prisma.workflow.update({
      where: { id: req.params.id },
      data: {
        name,
        description,
        nodes: formattedNodes,
        edges: formattedEdges,
        viewport: viewportData,
        status
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            email: true
          }
        }
      }
    });

    res.json(workflow);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'Workflow not found' });
    }
    res.status(400).json({ message: error.message });
  }
};

// Delete workflow
exports.deleteWorkflow = async (req, res) => {
  try {
    // First, delete all associated node runs
    const workflowRuns = await prisma.workflowRun.findMany({
      where: { workflowId: req.params.id },
      select: { id: true }
    });

    for (const run of workflowRuns) {
      await prisma.nodeRun.deleteMany({
        where: { workflowRunId: run.id }
      });
    }

    // Then delete all workflow runs
    await prisma.workflowRun.deleteMany({
      where: { workflowId: req.params.id }
    });

    // Finally delete the workflow
    await prisma.workflow.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Workflow deleted' });
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'Workflow not found' });
    }
    res.status(500).json({ message: error.message });
  }
};

// Execute workflow
exports.executeWorkflow = async (req, res) => {
  try {
    const workflow = await prisma.workflow.findUnique({
      where: { id: req.params.id }
    });

    if (!workflow) {
      return res.status(404).json({ message: 'Workflow not found' });
    }

    // Create a new workflow run
    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId: workflow.id,
        status: 'PENDING'
      }
    });

    // Get socket instance and execution service
    const executionService = req.app.get('workflowExecutionService');

    // Start workflow execution
    executionService.startWorkflowRun(workflowRun.id);

    // Return success response
    res.json({
      message: 'Workflow execution started',
      workflowRunId: workflowRun.id
    });
  } catch (error) {
    console.error('Error executing workflow:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get workflow execution status
exports.getWorkflowStatus = async (req, res) => {
  try {
    const workflow = await prisma.workflow.findUnique({
      where: { id: req.params.id },
      select: {
        id: true,
        name: true,
        isExecuting: true,
        lastExecuted: true,
        executionStatus: true
      }
    });

    if (!workflow) {
      return res.status(404).json({ message: 'Workflow not found' });
    }

    res.json(workflow);
  } catch (error) {
    console.error('Error getting workflow status:', error);
    res.status(500).json({ message: error.message });
  }
};

// Stop workflow execution
exports.stopWorkflow = async (req, res) => {
  try {
    const workflow = await prisma.workflow.findUnique({
      where: { id: req.params.id }
    });

    if (!workflow) {
      return res.status(404).json({ message: 'Workflow not found' });
    }

    // Get execution service
    const executionService = req.app.get('workflowExecutionService');

    // Check if workflow is running
    if (!executionService.isWorkflowRunning(workflow.id)) {
      return res.status(400).json({ message: 'Workflow is not running' });
    }

    // Stop workflow execution
    await executionService.stopExecution(workflow.id);

    res.json({ message: 'Workflow execution stopped' });
  } catch (error) {
    console.error('Error stopping workflow:', error);
    res.status(500).json({ message: error.message });
  }
};