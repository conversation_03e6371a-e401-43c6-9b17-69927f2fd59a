const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const prisma = require('../../lib/prisma');
const { validationResult } = require('express-validator');

// Create upload directory if it doesn't exist
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Upload a file
exports.uploadFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const { nodeRunId, nodeId } = req.body;

    // Either nodeRunId or nodeId must be provided
    if (!nodeRunId && !nodeId) {
      return res.status(400).json({ message: 'Either Node run ID or Node ID is required' });
    }

    // Handle nodeRunId if provided
    let actualNodeRunId = null;
    let actualNodeId = nodeId;

    if (nodeRunId) {
      // Check if it's a concatenated format (workflowRunId-nodeId)
      if (nodeRunId.includes('-')) {
        const [workflowRunId, nodeIdPart] = nodeRunId.split('-');

        // Try to find the node run using the composite key
        const nodeRun = await prisma.nodeRun.findUnique({
          where: {
            workflowRunId_nodeId: {
              workflowRunId,
              nodeId: nodeIdPart
            }
          }
        });

        if (nodeRun) {
          // Use the actual nodeRun.id
          actualNodeRunId = nodeRun.id;
        } else {
          // If we can't find the node run, use the nodeId part for pre-upload
          actualNodeId = nodeIdPart || nodeId;
        }
      } else {
        // It's a direct ID format, check if it exists
        const nodeRun = await prisma.nodeRun.findUnique({
          where: { id: nodeRunId }
        });

        if (nodeRun) {
          actualNodeRunId = nodeRunId;
        } else {
          return res.status(404).json({ message: 'Node run not found' });
        }
      }
    }

    // Generate a unique filename
    const fileExtension = path.extname(req.file.originalname);
    const filename = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(uploadDir, filename);

    // Move the file from temp upload to our storage
    fs.writeFileSync(filePath, req.file.buffer);

    // Create file record in database
    let fileData = {
      filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: filePath,
      url: `/api/files/${filename}`,
      uploadedBy: req.user.userId
    };

    // Add either nodeRunId or nodeId based on what was determined
    if (actualNodeRunId) {
      fileData.nodeRunId = actualNodeRunId;
    } else if (actualNodeId) {
      fileData.nodeId = actualNodeId;
    }

    const file = await prisma.file.create({
      data: fileData
    });

    res.status(201).json(file);
  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({ message: 'Error uploading file', error: error.message });
  }
};

// Get file by ID
exports.getFileById = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await prisma.file.findUnique({
      where: { id }
    });

    if (!file) {
      return res.status(404).json({ message: 'File not found' });
    }

    res.json(file);
  } catch (error) {
    res.status(500).json({ message: 'Error retrieving file', error: error.message });
  }
};

// Get files by node run ID
exports.getFilesByNodeRun = async (req, res) => {
  try {
    const { nodeRunId: requestedNodeRunId } = req.params;
    let actualNodeRunId = requestedNodeRunId;

    // Check if it's a concatenated format (workflowRunId-nodeId)
    if (requestedNodeRunId.includes('-')) {
      const [workflowRunId, nodeId] = requestedNodeRunId.split('-');

      // Try to find the node run using the composite key
      const nodeRun = await prisma.nodeRun.findUnique({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        }
      });

      if (nodeRun) {
        // Use the actual nodeRun.id
        actualNodeRunId = nodeRun.id;
      } else {
        // If we can't find the node run, return empty array
        return res.json([]);
      }
    }

    const files = await prisma.file.findMany({
      where: { nodeRunId: actualNodeRunId }
    });

    res.json(files);
  } catch (error) {
    console.error('Error retrieving files by node run ID:', error);
    res.status(500).json({ message: 'Error retrieving files', error: error.message });
  }
};

// Get files by node ID
exports.getFilesByNodeId = async (req, res) => {
  try {
    const { nodeId } = req.params;

    const files = await prisma.file.findMany({
      where: { nodeId }
    });

    res.json(files);
  } catch (error) {
    res.status(500).json({ message: 'Error retrieving files', error: error.message });
  }
};

// Download file
exports.downloadFile = async (req, res) => {
  try {
    const { filename } = req.params;

    const file = await prisma.file.findFirst({
      where: { filename }
    });

    if (!file) {
      return res.status(404).json({ message: 'File not found' });
    }

    // Check if file exists on disk
    if (!fs.existsSync(file.path)) {
      return res.status(404).json({ message: 'File not found on disk' });
    }

    // Set appropriate headers
    res.setHeader('Content-Type', file.mimetype);
    res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);

    // Stream the file
    const fileStream = fs.createReadStream(file.path);
    fileStream.pipe(res);
  } catch (error) {
    res.status(500).json({ message: 'Error downloading file', error: error.message });
  }
};

// Delete file
exports.deleteFile = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await prisma.file.findUnique({
      where: { id }
    });

    if (!file) {
      return res.status(404).json({ message: 'File not found' });
    }

    // Delete file from disk if it exists
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }

    // Delete file record from database
    await prisma.file.delete({
      where: { id }
    });

    res.json({ message: 'File deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting file', error: error.message });
  }
};
