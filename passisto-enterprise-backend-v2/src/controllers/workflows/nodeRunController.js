const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { validationResult } = require('express-validator');

// Get node runs by workflow run ID
exports.getNodeRunsByWorkflowRun = async (req, res) => {
  try {
    const nodeRuns = await prisma.nodeRun.findMany({
      where: {
        workflowRunId: req.params.workflowRunId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    res.json(nodeRuns);
  } catch (error) {
    console.error('Error fetching node runs:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get single node run
exports.getNodeRun = async (req, res) => {
  try {
    const nodeRun = await prisma.nodeRun.findUnique({
      where: { id: req.params.id },
      include: {
        workflowRun: {
          select: {
            id: true,
            workflow: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    if (!nodeRun) {
      return res.status(404).json({ message: 'Node run not found' });
    }
    res.json(nodeRun);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create node run
exports.createNodeRun = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { workflowRunId, nodeId } = req.body;

    // Check if workflow run exists
    const workflowRun = await prisma.workflowRun.findUnique({
      where: { id: workflowRunId }
    });

    if (!workflowRun) {
      return res.status(404).json({ message: 'Workflow run not found' });
    }

    const nodeRun = await prisma.nodeRun.create({
      data: {
        workflowRunId,
        nodeId,
        status: 'PENDING'
      }
    });

    res.status(201).json(nodeRun);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Update node run status
exports.updateNodeRun = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { status, output } = req.body;

    const nodeRun = await prisma.nodeRun.update({
      where: { id: req.params.id },
      data: {
        status,
        ...(output && { output }),
        ...(status === 'RUNNING' && { startedAt: new Date() }),
        ...(['SUCCESS', 'FAILED', 'SKIPPED'].includes(status) && { finishedAt: new Date() })
      }
    });

    // If this is the last node in the workflow run to complete, update the workflow run status
    if (['SUCCESS', 'FAILED', 'SKIPPED'].includes(status)) {
      const allNodeRuns = await prisma.nodeRun.findMany({
        where: { workflowRunId: nodeRun.workflowRunId }
      });

      const allNodesComplete = allNodeRuns.every(run =>
        run.status === 'SUCCESS' || run.status === 'FAILED' || run.status === 'SKIPPED'
      );

      const anyNodeFailed = allNodeRuns.some(run => run.status === 'FAILED');

      if (allNodesComplete) {
        await prisma.workflowRun.update({
          where: { id: nodeRun.workflowRunId },
          data: {
            status: anyNodeFailed ? 'FAILED' : 'SUCCESS',
            finishedAt: new Date()
          }
        });
      }
    }

    res.json(nodeRun);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'Node run not found' });
    }
    res.status(400).json({ message: error.message });
  }
};

// Delete node run
exports.deleteNodeRun = async (req, res) => {
  try {
    await prisma.nodeRun.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Node run deleted' });
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'Node run not found' });
    }
    res.status(500).json({ message: error.message });
  }
};