const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

/**
 * Get all roles, permissions, and scopes for user creation form
 */
const getFormData = async (req, res) => {
  try {
    const { companyId } = req.user;

    // Get all roles with their permissions
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        name: true,
        permissions: {
          select: {
            id: true,
            permissionId: true,
            scopeType: true,
            scopeId: true,
            permission: {
              select: {
                id: true,
                action: true,
                category: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Format roles with their permissions
    const formattedRoles = roles.map((role) => ({
      id: role.id,
      name: role.name,
      permissions: role.permissions.map((rp) => ({
        id: rp.permissionId,
        action: rp.permission.action,
        category: rp.permission.category,
        scopeType: rp.scopeType,
        scopeId: rp.scopeId,
      })),
    }));

    // Get all permissions
    const permissions = await prisma.permission.findMany({
      select: {
        id: true,
        action: true,
        category: true,
      },
      orderBy: [{ category: "asc" }, { action: "asc" }],
    });

    // Group permissions by category
    const permissionsByCategory = [];
    const categoriesMap = {};

    permissions.forEach((permission) => {
      if (!categoriesMap[permission.category]) {
        categoriesMap[permission.category] = {
          category: permission.category,
          permissions: [],
        };
        permissionsByCategory.push(categoriesMap[permission.category]);
      }

      categoriesMap[permission.category].permissions.push({
        id: permission.id,
        action: permission.action,
      });
    });

    // Create scopes list as simple strings
    const scopes = [
      "GLOBAL",
      "TEAM",
      //  "PROJECT",
      "SELF",
    ];

    return res.status(200).json({
      success: true,
      roles: formattedRoles,
      permissionsByCategory,
      scopes,
    });
  } catch (error) {
    console.error("Error fetching form data:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch form data",
      error: error.message,
    });
  }
};

module.exports = {
  getFormData,
};
