const prisma = require("../config/db");

const getFeatureValue = (features, key) =>
  features.find((f) => f.key === key)?.value || 0;

// ✅ Updated
const checkUsageLimit = async (req, res) => {
  const { companyId } = req.user;
  const { type } = req.body;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: { features: true }
        },
        usage: true
      }
    });

    if (!company || !company.subscription) {
      return res.status(403).json({
        success: false,
        message: "No active subscription found"
      });
    }

    const maxEmailAgents = getFeatureValue(company.subscription.features, "maxEmailAgents");
    const maxAiInterviewHours = getFeatureValue(company.subscription.features, "maxAiInterviewHours");

    const usedEmail = company.usage.find(u => u.key === "emailAgentsUsed")?.value || 0;
    const usedInterview = company.usage.find(u => u.key === "aiInterviewHoursUsed")?.value || 0;

    let isExceeded = false;
    let remaining = 0;
    let limit = 0;
    let currentUsage = 0;

    if (type === "interview") {
      limit = maxAiInterviewHours;
      currentUsage = usedInterview;
      isExceeded = usedInterview >= limit;
      remaining = limit - usedInterview;
    } else if (type === "email") {
      limit = maxEmailAgents;
      currentUsage = usedEmail;
      isExceeded = usedEmail >= limit;
      remaining = limit - usedEmail;
    } else {
      return res.status(400).json({
        success: false,
        message: "Invalid usage type"
      });
    }

    res.status(200).json({
      success: true,
      isExceeded,
      remaining: type === "interview" ? Math.floor(remaining * 60) : Math.floor(remaining),
      currentUsage: type === "interview" ? Math.floor(currentUsage * 60) : currentUsage,
      limit: type === "interview" ? Math.floor(limit * 60) : limit
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to check usage limit"
    });
  }
};

// ✅ Updated
const checkUsageLimitByCompanyId = async (req, res) => {
  const { type, companyId } = req.body;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: { features: true }
        },
        usage: true
      }
    });

    if (!company || !company.subscription) {
      return res.status(403).json({
        success: false,
        message: "No active subscription found"
      });
    }

    const maxEmailAgents = getFeatureValue(company.subscription.features, "maxEmailAgents");
    const maxAiInterviewHours = getFeatureValue(company.subscription.features, "maxAiInterviewHours");

    const usedEmail = company.usage.find(u => u.key === "emailAgentsUsed")?.value || 0;
    const usedInterview = company.usage.find(u => u.key === "aiInterviewHoursUsed")?.value || 0;

    let isExceeded = false;
    let remaining = 0;
    let limit = 0;
    let currentUsage = 0;

    if (type === "interview") {
      limit = maxAiInterviewHours;
      currentUsage = usedInterview;
      isExceeded = usedInterview >= limit;
      remaining = limit - usedInterview;
    } else if (type === "email") {
      limit = maxEmailAgents;
      currentUsage = usedEmail;
      isExceeded = usedEmail >= limit;
      remaining = limit - usedEmail;
    } else {
      return res.status(400).json({
        success: false,
        message: "Invalid usage type"
      });
    }

    res.status(200).json({
      success: true,
      isExceeded,
      remaining: type === "interview" ? Math.floor(remaining * 60) : Math.floor(remaining),
      currentUsage: type === "interview" ? Math.floor(currentUsage * 60) : currentUsage,
      limit: type === "interview" ? Math.floor(limit * 60) : limit
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to check usage limit"
    });
  }
};

// ✅ Updated to use CompanyUsage table instead of fields
const updateCompanyUsage = async (req, res) => {
  const { companyId } = req.user;
  const { type, value } = req.body;

  const key = type === "interview"
    ? "aiInterviewHoursUsed"
    : type === "email"
      ? "emailAgentsUsed"
      : null;

  if (!key) {
    return res.status(400).json({
      success: false,
      message: "Invalid update type"
    });
  }

  try {
    const usage = await prisma.companyUsage.upsert({
      where: {
        companyId_key: {
          companyId,
          key
        }
      },
      update: {
        value: {
          increment: parseFloat(value)
        }
      },
      create: {
        companyId,
        key,
        value: parseFloat(value)
      }
    });

    res.status(200).json({
      success: true,
      usage
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to update company usage"
    });
  }
};

const getCompanySubscriptionName = async (req, res) => {
  const { companyId } = req.user;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found"
      });
    }

    return res.status(200).json({
      success: true,
      subscriptionName: company.subscription?.name || "No Active Subscription"
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch subscription name"
    });
  }
};

// ✅ Updated to use `SubscriptionFeature` + `CompanyUsage`
const getCompanyUsageInfo = async (req, res) => {
  const { companyId } = req.user;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: { features: true }
        },
        usage: true
      }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found"
      });
    }

    const maxEmailAgents = getFeatureValue(company.subscription?.features || [], "maxEmailAgents");
    const maxAiInterviewHours = getFeatureValue(company.subscription?.features || [], "maxAiInterviewHours");

    const usedEmail = company.usage.find(u => u.key === "emailAgentsUsed")?.value || 0;
    const usedInterview = company.usage.find(u => u.key === "aiInterviewHoursUsed")?.value || 0;

    return res.status(200).json({
      success: true,
      emailBuilder: {
        limit: maxEmailAgents,
        used: usedEmail,
        remaining: Math.max(0, maxEmailAgents - usedEmail)
      },
      aiInterviewer: {
        limit: Math.floor(maxAiInterviewHours * 60),
        used: Math.floor(usedInterview * 60),
        remaining: Math.max(0, Math.floor((maxAiInterviewHours - usedInterview) * 60))
      }
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch usage information"
    });
  }
};

module.exports = {
  updateCompanyUsage,
  checkUsageLimit,
  getCompanySubscriptionName,
  checkUsageLimitByCompanyId,
  getCompanyUsageInfo
};
