const Candidate = require("../models/Candidate");
const Feedback = require("../models/Feedback");
const Interview = require("../models/Interview");
const { sendInterviewMail } = require("../services/mail.service");
const {
  PREPARE_INTERVIEW_PROMPT,
  GENERATE_INTERVIEW_FEEDBACK_PROMPT,
} = require("../utils/PROMPTS");
const { v4: uuidv4 } = require("uuid");
const bcrypt = require("bcryptjs");
const { parseFile } = require("../services/multer.service");
const { GoogleGenerativeAI } = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI({
  apiKey: "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4"
});


let currentKeyIndex = 0;

const getApiKey = () => {
  const keys = [
    process.env.GEMINI_API_KEY_1,
    process.env.GEMINI_API_KEY_2,
    process.env.GEMINI_API_KEY_3,
  ];

  const key = keys[currentKeyIndex];
  currentKeyIndex = (currentKeyIndex + 1) % keys.length;
  return key;
};

const generateWithAI = async (prompt) => {
  try {
    const apiKey = getApiKey();
    console.log(`Using API Key: ${apiKey}`);
    console.log(`Endpoint: ${process.env.GEMINI_API_URL}`);
    const response = await fetch(`${process.env.GEMINI_API_URL}?key=${apiKey}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    // console.log(data);
    // console.log(data.candidates?.[0]?.content?.parts?.[0]?.text);
    return data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    throw error;
  }
};

const createInterview = async (req, res, next) => {
  try {
    const { role, type, level, techstack, amount, createdBy, companyId, companyName } = req.body;

    const prompt = PREPARE_INTERVIEW_PROMPT.replace("{role}", role)
      .replace("{level}", level)
      .replace("{techstack}", techstack)
      .replace("{type}", type)
      .replace("{amount}", amount);
    const questions = await generateWithAI(prompt);
    const interview = {
      role,
      type,
      level,
      techstack: Array.isArray(techstack) ? techstack : techstack.split(","),
      questions: JSON.parse(questions),
      createdBy: createdBy,
      finalized: true,
      company: {
        id: companyId,
        name: companyName
      },
      createdAt: new Date().toISOString(),
    };
    const newInterview = new Interview(interview);
    await newInterview.save();
    console.log("Interview saved:");

    res.status(201).send(newInterview);
  } catch (error) {
    console.error("Error creating interview:", error);
    res.status(500).send({ message: "Internal server error" });
  }
};
const getInterviewsTemplate = async (req, res, next) => {
  try {
    const { companyId } = req.user;
    if (!companyId) {
      return res.status(400).json({ message: "Company ID is required" });
    }
    const interviews = await Interview.find({ "company.id": companyId }).sort({ createdAt: -1 });
    // const interviews = await Interview.find().sort({ createdAt: -1 });
    res.status(200).json(interviews);
  } catch (error) {
    console.error("Error fetching interviews:", error);
    res.status(500).json({ message: "Failed to fetch interviews" });
  }
};
const getInterviewById = async (req, res) => {
  const { interviewId } = req.params;
  const { includeCandidates = "true" } = req.query;

  try {
    let interviewQuery = Interview.findById(interviewId);

    if (includeCandidates === "true") {
      interviewQuery = interviewQuery.populate("candidates");
    } else {
      interviewQuery = interviewQuery.select("-candidates");
    }

    const interview = await interviewQuery.exec();

    if (!interview) {
      return res.status(404).json({ message: "Interview not found" });
    }

    res.status(200).json(interview);
  } catch (error) {
    console.error("Error fetching interview:", error);
    res.status(500).json({ message: "Server error", error });
  }
};
const updateInterview = async (req, res) => {
  const { interviewId } = req.params;
  const { role, level, type, techstack, questions } = req.body;

  try {
    const updatedInterview = await Interview.findByIdAndUpdate(
      interviewId,
      { role, level, type, techstack, questions },
      { new: true, runValidators: true }
    );

    if (!updatedInterview) {
      return res.status(404).json({ message: "Interview not found" });
    }

    res.status(200).json(updatedInterview);
  } catch (error) {
    console.error("Error updating interview:", error);
    res.status(500).json({ message: "Server error", error });
  }
};
const deleteInterviewById = async (req, res) => {
  const { interviewId } = req.params;

  try {
    const interview = await Interview.findById(interviewId);

    if (!interview) {
      return res.status(404).json({ message: "Interview not found" });
    }

    if (interview.candidates && interview.candidates.length > 0) {
      await Candidate.deleteMany({ _id: { $in: interview.candidates } });
    }

    await Interview.findByIdAndDelete(interviewId);

    res.status(200).json({
      message: "Interview and related candidates deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting interview and candidates:", error);
    res.status(500).json({ message: "Server error", error });
  }
};
const getCandidateByInterviewAndCandidateId = async (req, res) => {
  const { interviewId, candidateId } = req.params;

  try {
    const interview = await Interview.findById(interviewId).populate(
      "candidates"
    );

    if (!interview) {
      return res.status(404).json({ message: "Interview not found" });
    }

    const candidate = interview.candidates.find(
      (c) => c._id.toString() === candidateId
    );

    if (!candidate) {
      return res
        .status(404)
        .json({ message: "Candidate not found in this interview" });
    }

    // Clone the interview and replace candidates with the matched candidate only
    const interviewWithSingleCandidate = {
      ...interview.toObject(),
      candidates: [candidate],
    };

    res.status(200).json(interviewWithSingleCandidate);
  } catch (error) {
    console.error(
      "Error fetching candidate by interview and candidate ID:",
      error
    );
    res.status(500).json({ message: "Server error", error });
  }
};
const getFeedbackByInterviewAndCandidate = async (req, res) => {
  try {
    const { interviewId, candidateId } = req.params;
    // console.log(interviewId);
    // console.log(candidateId);
    const feedback = await Feedback.findOne({ interviewId, candidateId });

    if (!feedback) {
      return res.status(404).json({ message: "Feedback not found" });
    }

    const interview = await Interview.findById(interviewId);
    if (!interview) {
      return res.status(404).json({ message: "Interview not found" });
    }

    const candidate = await Candidate.findById(candidateId);
    if (!candidate) {
      return res.status(404).json({ message: "Candidate not found" });
    }

    const response = {
      id: feedback._id.toString(),
      userId: interview.createdBy,
      candidateName: candidate.name,
      candidateEmail: candidate.email,
      interviewId: interview._id.toString(),
      interviewRole: interview.role,
      interviewLevel: interview.level,
      totalScore: feedback.totalScore,
      categoryScores: feedback.categoryScores,
      strengths: feedback.strengths,
      areasForImprovement: feedback.areasForImprovement,
      finalAssessment: feedback.finalAssessment,
      createdAt: feedback._id.getTimestamp().toISOString(),
    };
    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching feedback:", error);
    res.status(500).json({ message: "Server error", error });
  }
};
// const sendInterviewToCandidat = async (req, res, next) => {
//   try {
//     const { interviewId } = req.params;

//     const file = req.file;

//     if (!file) {
//       return res.status(400).send("No file uploaded.");
//     }

//     const interview = await Interview.findById(interviewId);
//     if (!interview) {
//       return res.status(404).send("Interview not found");
//     }
//     const jobRole = interview.role;
//     const companyName = "Passisto";
//     const companyEmail = "<EMAIL>";
//     const candidates = await parseFile(file);
//     const candidatePromises = candidates.map(async (candidate) => {
//       const { fullName, email } = candidate;
//       const password = generateRandomPassword();
//       const hashedPassword = await bcrypt.hash(password, 10);

//       const newCandidate = new Candidate({
//         name: fullName,
//         email: email,
//         password: hashedPassword,
//         status: "pending",
//         hasFeedback: false,
//       });

//       await newCandidate.save();
//       const interviewLink = `${process.env.FRONTEND_URL}/public-page/interview/${interviewId}/${newCandidate._id}`
//       await sendInterviewMail(
//         fullName,
//         jobRole,
//         companyName,
//         interviewLink,
//         email,
//         password,
//         companyEmail
//       );
//       return newCandidate._id;
//     });

//     const newCandidateIds = await Promise.all(candidatePromises);

//     interview.candidates = [
//       ...new Set([...interview.candidates, ...newCandidateIds]),
//     ];

//     await interview.save();

//     res
//       .status(200)
//       .send("Interview invitations sent and candidates updated successfully.");
//   } catch (error) {
//     console.error(error);
//     res
//       .status(500)
//       .send("Failed to send interview invitations or update candidates.");
//   }
// };

const sendInterviewToCandidat = async (req, res, next) => {
  try {
    const { interviewId } = req.params;

    const file = req.file;
    const manualCandidates = req.body.candidates;

    const interview = await Interview.findById(interviewId);
    if (!interview) {
      return res.status(404).send("Interview not found");
    }

    const jobRole = interview.role;
    const companyName = "Passisto";
    const companyEmail = "<EMAIL>";

    let candidates = [];

    if (file) {
      candidates = await parseFile(file);
    } else if (manualCandidates && Array.isArray(manualCandidates)) {
      candidates = manualCandidates;
    } else {
      return res.status(400).send("No candidates provided.");
    }

    const candidatePromises = candidates.map(async (candidate) => {
      const { fullName, email } = candidate;
      const password = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(password, 10);

      const newCandidate = new Candidate({
        name: fullName,
        email: email,
        password: hashedPassword,
        status: "pending",
        hasFeedback: false,
      });

      await newCandidate.save();

      const interviewLink = `${process.env.FRONTEND_URL}/public-page/interview/${interviewId}/${newCandidate._id}`;
      await sendInterviewMail(
        fullName,
        jobRole,
        companyName,
        interviewLink,
        email,
        password,
        companyEmail
      );

      return newCandidate._id;
    });

    const newCandidateIds = await Promise.all(candidatePromises);
    interview.candidates = [
      ...new Set([...interview.candidates, ...newCandidateIds]),
    ];

    await interview.save();

    res
      .status(200)
      .send("Interview invitations sent and candidates updated successfully.");
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .send("Failed to send interview invitations or update candidates.");
  }
};

const createFeedback = async (req, res, next) => {
  try {
    const { formattedTranscript } = req.body;
    const { interviewId, candidateId } = req.params;

    const prompt = GENERATE_INTERVIEW_FEEDBACK_PROMPT(formattedTranscript);
    const rawFeedback = await generateWithAI(prompt);
    const cleanedFeedback = rawFeedback
      .replace(/```json\s*|\s*```/g, "") // remove ```json and closing ```
      .trim();

    const feedback = JSON.parse(cleanedFeedback);

    const newFeedback = new Feedback({
      interviewId,
      candidateId,
      totalScore: feedback.totalScore,
      categoryScores: feedback.categoryScores,
      strengths: feedback.strengths,
      areasForImprovement: feedback.areasForImprovement,
      finalAssessment: feedback.finalAssessment,
    });
    // console.log(newFeedback);
    const updatedCandidate = await Candidate.findByIdAndUpdate(
      candidateId,
      {
        $set: {
          score: feedback.totalScore,
          hasFeedback: true,
          status: "completed",
          completedAt: new Date().toISOString(),
        },
        $push: {
          transcript: {
            $each: formattedTranscript.split("\n").map((line) => {
              const [role, ...contentArr] = line.split(": ");
              const content = contentArr.join(": ");
              return {
                role: role.trim(),
                content: content.trim(),
                timestamp: new Date().toISOString(),
              };
            }),
          },
        },
      },
      { new: true }
    );
    const savedFeedback = await newFeedback.save();
    res.status(201).json({
      success: true,
      message: "Feedback saved successfully",
      feedbackId: savedFeedback._id,
    });
  } catch (error) {
    console.log(error);
  }
};
const checkCandidateAccess = async (req, res) => {
  const { email, password } = req.body;
  const { interviewId, candidateId } = req.params;

  try {
    const interview = await Interview.findById(interviewId).populate(
      "candidates"
    );

    if (!interview) {
      return res.status(404).json({ message: "Interview not found" });
    }

    const candidate = interview.candidates.find(
      (c) => c._id.toString() === candidateId && c.email.trim() === email.trim()
    );
    if (!candidate) {
      return res
        .status(401)
        .json({ message: "Invalid email or candidate not part of interview" });
    }

    const isPasswordValid = await bcrypt.compare(password, candidate.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        message:
          "Invalid email or access code. Please check your credentials and try again.",
      });
    }

    res.status(200).json({ message: "Access granted" });
  } catch (error) {
    console.error("Error checking candidate access:", error);
    res.status(500).json({ message: "Server error", error });
  }
};
const generateRandomPassword = (length = 10) => {
  const chars =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()";
  return Array.from(
    { length },
    () => chars[Math.floor(Math.random() * chars.length)]
  ).join("");
};
const seedData = async (req, res) => {
  try {
    await Interview.deleteMany({});
    await Candidate.deleteMany({});
    await Feedback.deleteMany({});

    const statuses = ["pending", "in_progress", "completed"];

    for (let i = 1; i <= 4; i++) {
      const interview = new Interview({
        role: `Frontend Developer ${i}`,
        type: "Technical",
        level: "Junior",
        techstack: ["React", "Next.js", "Tailwind"],
        questions: [
          `What is JSX?`,
          `Explain SSR in Next.js`,
          `What is Context API?`,
        ],
        createdBy: `Manager ${i}`,
        candidates: [],
        finalized: true,
        createdAt: new Date().toISOString(),
      });

      const savedCandidates = [];

      for (let j = 1; j <= 5; j++) {
        const hasFeedback = j === 1; // Only first candidate per interview has feedback
        const hashedPassword = await bcrypt.hash("abcd12345", 10);
        const candidateData = {
          name: `Candidate ${i}-${j}`,
          email: `candidate${i}${j}@email.com`,
          password: hashedPassword,
          status: statuses[0],
          hasFeedback,
          transcript: [
            {
              role: "user",
              content: "Tell me about your experience with React.",
              timestamp: new Date().toISOString(),
            },
          ],
        };

        if (hasFeedback) {
          candidateData.score = Math.floor(Math.random() * 100);
          candidateData.completedAt = new Date().toISOString();
          candidateData.status = statuses[2];
        }

        const savedCandidate = await new Candidate(candidateData).save();
        savedCandidates.push(savedCandidate._id);

        if (hasFeedback) {
          const feedback = new Feedback({
            interviewId: interview._id,
            candidateId: savedCandidate._id,
            totalScore: candidateData.score,
            categoryScores: [
              {
                name: "Communication Skills",
                score: 40,
                comment: "Needs improvement in clarity and confidence.",
              },
              {
                name: "Technical Knowledge",
                score: 60,
                comment: "Basic understanding of React and Next.js.",
              },
            ],
            strengths: ["Familiar with React", "Basic understanding of SSR"],
            areasForImprovement: [
              "Improve articulation",
              "Deepen Next.js knowledge",
            ],
            finalAssessment: "Not ready for the role yet.",
          });

          await feedback.save();
        }
      }

      interview.candidates = savedCandidates;
      interview.candidateCount = savedCandidates.length;
      interview.completedCount = 1; // only 1 candidate per interview has completed
      await interview.save();
    }

    res.status(200).json({ message: "Mock data seeded successfully!" });
  } catch (error) {
    console.error("Seeding error:", error);
    res.status(500).json({ message: "Seeding failed", error });
  }
};

module.exports = {
  createInterview,
  getInterviewsTemplate,
  sendInterviewToCandidat,
  createFeedback,
  seedData,
  getInterviewById,
  getCandidateByInterviewAndCandidateId,
  getFeedbackByInterviewAndCandidate,
  updateInterview,
  deleteInterviewById,
  checkCandidateAccess,
};