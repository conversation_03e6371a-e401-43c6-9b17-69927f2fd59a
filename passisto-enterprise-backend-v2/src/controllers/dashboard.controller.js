// dashboardController.js

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Helper function to serialize BigInt values to Number for JSON response.
 * @param {object} obj - The object to traverse and serialize.
 * @returns {object} The object with BigInts converted to Numbers.
 */
function serializeBigInt(obj) {
  // If the object is null or not an object, return it as is
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // If it's a BigInt, convert it to a Number
  if (typeof obj === 'bigint') {
    // Be careful here: if BigInts can exceed Number.MAX_SAFE_INTEGER,
    // converting to string might be safer to avoid precision loss.
    // For most count values, Number should suffice.
    return Number(obj);
  }

  // If it's an array, map over its elements
  if (Array.isArray(obj)) {
    return obj.map(item => serializeBigInt(item));
  }

  // If it's a plain object, recursively process its properties
  const newObj = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      newObj[key] = serializeBigInt(obj[key]);
    }
  }
  return newObj;
}


/**
 * @desc Fetches dashboard statistics for a specific company.
 * @route GET /api/dashboard/stats
 * @access Private (requires authentication and companyId in req.user)
 * @param {object} req - Express request object
 * @param {object} req.user - Authenticated user object (expected to have companyId)
 * @param {object} res - Express response object
 */
export const getDashboardStats = async (req, res) => {
  // Ensure companyId is available from the authenticated user
  const { companyId } = req.user;

  if (!companyId) {
    return res.status(400).json({ error: 'Company ID is required' });
  }

  try {
    // Fetch data from various materialized views, filtered by companyId
    const companyStats = await prisma.$queryRaw`
      SELECT * FROM mv_company_overview_stats
      WHERE company_id = ${companyId}
    `;

    const groupDistribution = await prisma.$queryRaw`
      SELECT
        group_name as name,
        member_count as members
      FROM mv_group_distribution
      WHERE company_id = ${companyId}
      ORDER BY member_count DESC
    `;

    // Derive team activities (active/inactive) from mv_group_distribution
    const teamActivities = await prisma.$queryRaw`
      SELECT
        CASE
          WHEN member_count > 0 THEN 'active'
          ELSE 'inactive'
        END as name,
        COUNT(*) as value
      FROM mv_group_distribution
      WHERE company_id = ${companyId}
      GROUP BY
        CASE
          WHEN member_count > 0 THEN 'active'
          ELSE 'inactive'
        END
    `;

    // Fetch user growth data, ensuring chronological order by sorting on the actual date/time field
    const userGrowth = await prisma.$queryRaw`
      SELECT
        TO_CHAR(month, 'Mon') as month,
        SUM(count) as users,
        month as sort_key -- Include the actual month value for correct chronological sorting
      FROM mv_monthly_growth_stats
      WHERE company_id = ${companyId}
      AND month >= CURRENT_DATE - INTERVAL '6 months'
      GROUP BY month
      ORDER BY sort_key ASC
    `;

    // Fetch AI tools usage statistics
    const aiToolsStats = await prisma.$queryRaw`
      SELECT * FROM mv_ai_tools_usage_stats
      WHERE company_id = ${companyId}
    `;

    // Extract first row or an empty object if no data
    const stats = companyStats[0] || {};
    const aiStats = aiToolsStats[0] || {};

    // Construct the final response object for the frontend
    const response = {
      // Explicitly convert BigInts to Number using Number()
      totalTeams: Number(stats.total_groups) || 0,
      totalUsers: Number(stats.total_users) || 0,
      activeUsers: Number(stats.active_users) || 0,
      // Find the count of 'active' teams from the aggregated teamActivities and convert to Number
      teamsWithMembers: Number(teamActivities.find(t => t.name === 'active')?.value) || 0,
      
      // Map over arrays to ensure nested BigInts are converted
      teamDistribution: groupDistribution.map(item => ({
        name: item.name,
        members: Number(item.members)
      })),
      
      // userGrowth is already mapped in the original code, but ensure 'users' is Number
      userGrowth: userGrowth.map(item => ({ month: item.month, users: Number(item.users) })),
      
      teamActivities: teamActivities.map(item => ({
        name: item.name,
        value: Number(item.value)
      })),
      
      interviewStats: {
        total: Number(aiStats.total_interviews) || 0,
        completed: Number(aiStats.completed_interviews) || 0,
        pending: Number(aiStats.pending_interviews) || 0,
      },
      emailStats: {
        total: Number(aiStats.total_emails) || 0,
        sent: Number(aiStats.emails_sent) || 0,
        opened: Number(aiStats.emails_opened) || 0,
      },
      formStats: {
        total: Number(aiStats.total_forms) || 0,
        submissions: Number(aiStats.form_submissions) || 0,
        conversionRate: Number(aiStats.conversion_rate) || 0,
      },
    };

    // The serializeBigInt function will act as a final safeguard,
    // though most conversions should now happen above.
    const serializedResponse = serializeBigInt(response);
    return res.status(200).json(serializedResponse);

  } catch (error) {
    console.error('Dashboard API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * @desc Triggers a refresh of all dashboard materialized views.
 * @route POST /api/dashboard/refresh-views
 * @access Private (Should be restricted to admins or internal services)
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
export const refreshDashboardViews = async (req, res) => {
  try {
    // Execute the PostgreSQL function to refresh all materialized views
    await prisma.$executeRaw`SELECT refresh_dashboard_materialized_views()`;
    console.log('Dashboard materialized views refreshed successfully');
    return res.status(200).json({ message: 'Materialized views refreshed' });
  } catch (error) {
    console.error('Error refreshing dashboard views:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};
