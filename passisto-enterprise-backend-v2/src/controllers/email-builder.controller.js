const prisma = require("@/config/db");

const {
  EMAIL_BUILDER_ENHANCE_DESCRIPTION_PROMPT,
  EMAIL_BUILDER_GENERATE_TEMPLATE_PROMPT,
} = require("../utils/PROMPTS");
const { v4: uuidv4 } = require("uuid");
const { sendEmailBuilder } = require("../services/mail.service");

const { GoogleGenerativeAI } = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI({
  apiKey: "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4"
});

const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
const GEMINI_API_KEY = "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4";


const generateWithAI = async (prompt) => {
  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    throw error;
  }
};

// const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";
// const OPENROUTER_API_KEY =
//   "Bearer 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";

// const generateWithAI = async (prompt) => {
//   try {
//     const response = await fetch(OPENROUTER_API_URL, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//         Authorization: OPENROUTER_API_KEY,
//       },
//       body: JSON.stringify({
//         model: "google/gemini-2.0-flash-lite-001",
//         messages: [{ role: "user", content: prompt }],
//       }),
//     });
//     if (!response.ok) {
//       throw new Error(`API request failed with status ${response.status}`);
//     }
//     const data = await response.json();
//     console.log(data);
//     return data.choices[0].message.content;
//   } catch (error) {
//     console.error("Error calling OpenRouter API:", error);
//     throw error;
//   }
// };

const enhanceDescription = async (req, res) => {
  const { userInput } = req.body;

  if (!userInput || !userInput.trim()) {
    return res.status(400).json({ error: "Description is required" });
  }

  try {
    const prompt = EMAIL_BUILDER_ENHANCE_DESCRIPTION_PROMPT(userInput);
    const enhancedDescription = await generateWithAI(prompt);
    res.json({ enhancedDescription });
  } catch (error) {
    res.status(500).json({ error: "Failed to enhance description" });
  }
};

const generateEmail = async (req, res) => {
  const { userInput } = req.body;
  const { companyId } = req.user;

  if (!userInput || !userInput.trim()) {
    return res.status(400).json({ error: "Email intent is required" });
  }

  try {
    const prompt = EMAIL_BUILDER_GENERATE_TEMPLATE_PROMPT(userInput);
    const text = await generateWithAI(prompt);
    const jsonMatch = text.match(/\[([\s\S]*)\]/);

    if (!jsonMatch) {
      es.status(500).json({
        error: "Failed to generate a valid email template",
      });
    }
    let aiResponseJson = JSON.parse(jsonMatch[0]);
    aiResponseJson = aiResponseJson.map((template) => ({
      ...template,
      fields: template.fields.map((field) => ({
        ...field,
        id: uuidv4(),
      })),
    }));
    const emailData = {
      title: aiResponseJson[0].title,
      description: aiResponseJson[0].description || "",
      fields: aiResponseJson[0].fields,
      html: generateHTML(aiResponseJson[0].fields),
      companyId,
    };
    const savedEmail = await prisma.email.create({
      data: emailData,
    });

    res.status(200).json(savedEmail);
  } catch (error) {
    res.status(500).json({ error: "Failed to generate email and save email" });
  }
};

const getEmailById = async (req, res) => {
  const { emailId } = req.params;
  const { companyId } = req.user;

  try {
    const email = await prisma.email.findUnique({
      where: { id: emailId },
      include: { company: true },
    });

    if (!email) {
      return res.status(404).json({ error: "Email not found" });
    }

    if (email.companyId !== companyId) {
      return res
        .status(403)
        .json({ error: "Forbidden: Email does not belong to the company" });
    }
    res.status(200).json(email);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Failed to retrieve email" });
  }
};

const getAllEmails = async (req, res) => {
  const { companyId } = req.user;

  try {
    const emails = await prisma.email.findMany({
      where: { companyId },
    });
    res.status(200).json(emails);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Failed to retrieve email list" });
  }
};

const updateEmail = async (req, res) => {
  const { emailId } = req.params;
  const { companyId } = req.user;
  const { title, fields } = req.body;
  if (!fields || !fields.length) {
    return res.status(400).json({ error: "At least one field is required" });
  }
  try {
    const email = await prisma.email.findUnique({
      where: { id: emailId },
      include: { company: true },
    });

    if (!email) {
      return res.status(404).json({ error: "Email not found" });
    }

    if (email.companyId !== companyId) {
      return res
        .status(403)
        .json({ error: "Forbidden: Email does not belong to the company" });
    }
    const html = generateHTML(fields);

    await prisma.email.update({
      where: { id: emailId },
      data: {
        title,
        fields,
        html,
      },
    });

    res.status(200).json({ message: "Email fields updated successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Failed to update email fields" });
  }
};

const sendEmail = async (req, res) => {
  const { from, to, subject, fromName, message, htmlBody } = req.body;
  if (!from || !to || !subject || !fromName || !htmlBody) {
    return res.status(400).json({ error: "Missing required fields" });
  }
  sendEmailBuilder(fromName, to, htmlBody, subject);
  res.status(200).json({ message: "Email sent successfully" });
};
const generateHTML = (components) => {
  let html = `<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Email Template</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
<table cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">`;

  components.forEach((component) => {
    const { type, content, settings } = component;
    const style = generateComponentStyle(settings);

    switch (type) {
      case "header":
        html += `
<tr>
  <td style="${style}">
    <h1 style="margin: 0; font-weight: ${
      settings.fontWeight || "bold"
    }; line-height: ${settings.lineHeight || "1.2"};">${content}</h1>
  </td>
</tr>`;
        break;
      case "text":
        html += `
<tr>
  <td style="${style}">
    <p style="margin: 0; font-weight: ${
      settings.fontWeight || "normal"
    }; line-height: ${settings.lineHeight || "1.5"};">${content}</p>
  </td>
</tr>`;
        break;
      case "image":
        const imgHtml = `<img src="${
          settings.url || "/placeholder.svg?height=200&width=600"
        }" alt="${settings.altText || "Image"}" style="width: ${
          settings.width || "100%"
        }; height: ${settings.height || "auto"}; border-radius: ${
          settings.borderRadius || "0px"
        };" />`;

        html += `
<tr>
  <td style="${style}">
    ${
      settings.linkImage === "true"
        ? `<a href="${
            settings.linkUrl || "#"
          }" target="_blank" rel="noopener noreferrer" style="text-decoration: none;">${imgHtml}</a>`
        : imgHtml
    }
  </td>
</tr>`;
        break;
      case "button":
        html += `
<tr>
  <td style="${style}">
    <table cellpadding="0" cellspacing="0" style="margin: ${
      settings.alignment === "center"
        ? "0 auto"
        : settings.alignment === "right"
        ? "0 0 0 auto"
        : "0"
    }; width: ${settings.buttonWidth === "full" ? "100%" : "auto"};">
      <tr>
        <td style="background-color: ${
          settings.backgroundColor || "#0070f3"
        }; border-radius: ${
          settings.borderRadius || "4px"
        }; padding: 12px 24px; border: ${settings.borderWidth || "0px"} solid ${
          settings.borderColor || "transparent"
        };">
          <a href="${
            settings.url || "#"
          }" target="_blank" rel="noopener noreferrer" style="color: ${
          settings.color || "#ffffff"
        }; text-decoration: none; display: inline-block; font-weight: ${
          settings.fontWeight || "normal"
        };">${content}</a>
        </td>
      </tr>
    </table>
  </td>
</tr>`;
        break;
      case "divider":
        html += `
<tr>
  <td style="${style}">
    <hr style="border: none; border-top: ${settings.dividerWidth || "1px"} ${
          settings.dividerStyle || "solid"
        } ${settings.dividerColor || "#e5e7eb"}; margin: 0;" />
  </td>
</tr>`;
        break;
      case "footer":
        html += `
<tr>
  <td style="${style}">
    <p style="margin: 0; font-size: ${settings.fontSize || "12px"}; color: ${
          settings.color || "#6b7280"
        }; font-weight: ${settings.fontWeight || "normal"}; line-height: ${
          settings.lineHeight || "1.5"
        };">${content}</p>
  </td>
</tr>`;
        break;
      case "list":
        const listItems = component.items || [];
        const listType = settings.listType === "number" ? "ol" : "ul";
        const listItemsHtml = listItems
          .map((item) => `<li style="margin-bottom: 8px;">${item}</li>`)
          .join("");

        html += `
<tr>
<td style="${style}">
<${listType} style="margin: 0; padding-left: 20px; color: ${
          settings.color || "#333333"
        }; font-size: ${settings.fontSize || "16px"};">
  ${listItemsHtml}
</${listType}>
</td>
</tr>`;
        break;

      case "table":
        const headers = component.headers || [];
        const rows = component.rows || [];

        let tableHtml = `<table cellpadding="8" cellspacing="0" width="100%" style="border-collapse: collapse;">`;

        // Add headers
        if (headers.length > 0) {
          tableHtml += `<tr>`;
          headers.forEach((header) => {
            tableHtml += `<th style="text-align: left; background-color: ${
              settings.headerBackgroundColor || "#f3f4f6"
            }; border: 1px solid ${settings.borderColor || "#e5e7eb"}; color: ${
              settings.color || "#333333"
            }; font-size: ${settings.fontSize || "14px"};">${header}</th>`;
          });
          tableHtml += `</tr>`;
        }

        // Add rows
        rows.forEach((row) => {
          tableHtml += `<tr>`;
          row.forEach((cell) => {
            tableHtml += `<td style="border: 1px solid ${
              settings.borderColor || "#e5e7eb"
            }; color: ${settings.color || "#333333"}; font-size: ${
              settings.fontSize || "14px"
            };">${cell}</td>`;
          });
          tableHtml += `</tr>`;
        });

        tableHtml += `</table>`;

        html += `
<tr>
<td style="${style}">
${tableHtml}
</td>
</tr>`;
        break;

      case "social":
        const socialLinks = component.socialLinks || [];
        const iconSize = settings.iconSize || "32px";
        const iconSpacing = settings.iconSpacing || "10px";
        const iconColor = settings.iconColor || "#333333";

        // Filter only enabled social links
        const enabledSocialLinks = socialLinks.filter((link) => link.enabled);

        // Create the social icons HTML
        let socialIconsHtml = `<div style="font-size: 0;">`;
        enabledSocialLinks.forEach((link) => {
          // Get appropriate icon for each platform
          const iconSvg = getSocialIconSvg(link.platform, iconColor);

          socialIconsHtml += `
          <a href="${
            link.url
          }" target="_blank" rel="noopener noreferrer" style="display: inline-block; margin-right: ${iconSpacing}; text-decoration: none;">
            <img src="data:image/svg+xml;base64,${btoa(iconSvg)}" alt="${
            link.platform
          }" width="${iconSize}" height="${iconSize}" style="border: 0;" />
          </a>`;
        });
        socialIconsHtml += `</div>`;

        html += `
<tr>
<td style="${style}; text-align: ${settings.alignment || "center"};">
${socialIconsHtml}
</td>
</tr>`;
        break;
    }
  });

  html += `
</table>
</body>
</html>`;

  return html;
};

const generateComponentStyle = (settings) => {
  let style = "padding: " + (settings.padding || "16px") + "; ";

  if (settings.margin) style += "margin: " + settings.margin + "; ";
  if (settings.color) style += "color: " + settings.color + "; ";
  if (settings.backgroundColor)
    style += "background-color: " + settings.backgroundColor + "; ";
  if (settings.fontSize) style += "font-size: " + settings.fontSize + "; ";
  if (settings.alignment) style += "text-align: " + settings.alignment + "; ";

  return style;
};
const getSocialIconSvg = (platform, color) => {
  const svgColor = color.replace("#", "%23"); // URL-encode the color for SVG

  switch (platform.toLowerCase()) {
    case "facebook":
      return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>`;
    case "twitter":
      return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>`;
    case "instagram":
      return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>`;
    case "linkedin":
      return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle></svg>`;
    case "youtube":
      return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon></svg>`;
    default:
      return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>`;
  }
};
const deleteEmail = async (req, res) => {
  const { emailId } = req.params;
  const { companyId } = req.user;
  try {
    const email = await prisma.email.findUnique({ where: { id: emailId } });
    if (!email) {
      return res.status(404).json({ error: "Email not found" });
    }
    if (email.companyId !== companyId) {
      return res.status(403).json({ error: "Forbidden: Email does not belong to the company" });
    }
    await prisma.email.delete({ where: { id: emailId } });
    res.status(200).json({ message: "Email deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: "Failed to delete email" });
  }
};

module.exports = {
  enhanceDescription,
  generateEmail,
  getEmailById,
  getAllEmails,
  updateEmail,
  sendEmail,
  deleteEmail,
};
