const prisma = require('@/config/db');
const { checkWebDuplicate, checkWebUrlReachable } = require('@/services/integrations/web.service');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const opensearchClient = require('@/config/opensearch'); // Import the OpenSearch client


async function createWebIntegration(req, res) {
  const {
    name,
    providerType,
    url,
    updateTime,
  } = req.body;
  const { companyId, userId } = req.user;

  const isDuplicate = await checkWebDuplicate({ url, companyId });
  if (isDuplicate) {
    return res.status(400).json({ error: 'Web integration already exists for this company.' });
  }

  const isReachable = await checkWebUrlReachable(url);
  if (!isReachable) {
    return res.status(400).json({ error: 'URL is not reachable or invalid.' });
  }

  const opensearchIndexId = uuidv4();

  try {
    console.log(`[INFO] [${new Date().toISOString()}] Sending Web task to Scrapyd for URL ${url}`);

    const scrapydResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/schedule.json`, null, {
      headers: { 'Content-Type': 'multipart/form-data' },
      auth: {
        username: process.env.SCRAPYD_USERNAME,
        password: process.env.SCRAPYD_PASSWORD
      },
      params: {
        project: process.env.SCRAPYD_PROJECT,
        spider: process.env.SCRAPYD_SPIDER,
        index_name: opensearchIndexId,
        website_url: url,
      }
    });

    const jobId = scrapydResponse?.data?.jobid;
    if (!jobId) {
      console.error(`[ERROR] Scrapyd did not return a job ID`);
      return res.status(500).json({ error: 'Failed to trigger Scrapyd job' });
    }

    const userEmail = await prisma.user.findUnique({
      where: {
        id: userId, // Use the correct field name for the `User` model's ID
      },
      select: {
        email: true, // Select only the email field
      },
    });
    
    const integration = await prisma.Integration.create({
      data: {
        name,
        providerType,
        updateTime,
        opensearchIndexId,
        celeryTaskId: jobId,
        createdBy: userEmail.email, 
        company: {
          connect: { id: companyId },
        }, 
        web: {
          create: {
            url,
          },
        },
      },
      include: { web: true },
    });

    console.log(`[INFO] [${new Date().toISOString()}] Web integration created and Scrapyd job ${jobId} triggered for company ${companyId}`);

    return res.status(201).json({
      message: 'Integration created and Scrapyd job dispatched',
      data: integration,
      jobId,
    });
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to create Web integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get Web Integrations for a specific company
 */
async function getWebIntegrations(req, res) {
  const { companyId, userId } = req.user;

  try {
    const integrations = await prisma.Integration.findMany({
      where: {
        companyId,
        providerType: 'web',
      },
      include: { 
        web: true,
      },
    });

    return res.status(200).json(integrations);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get Web integrations:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


async function deleteWebIntegration(req, res) {
  const { id } = req.params;
  try {
    // Fetch the integration to get the Scrapy job ID and project
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: { web: true },
    });

    if (!integration) {
      return res.status(404).json({ error: 'Integration not found' });
    }

    const { opensearchIndexId, celeryTaskId } = integration;

    // Remove the OpenSearch index
    if (opensearchIndexId) {
      console.log(`[INFO] [${new Date().toISOString()}] Deleting OpenSearch index ${opensearchIndexId}`);
      try{
          const response = await opensearchClient.indices.delete({
            index: opensearchIndexId,
          });
          if (response.body.acknowledged) {
            console.log(`[INFO] [${new Date().toISOString()}] OpenSearch index ${opensearchIndexId} deleted successfully`);
          } else {
            console.warn(`[WARN] [${new Date().toISOString()}] Failed to delete OpenSearch index ${opensearchIndexId}`);
          }
      }catch{
        console.warn(`[WARN] [${new Date().toISOString()}] OpenSearch index ${opensearchIndexId} doesn't exist`);
      }
    }

    const project = process.env.SCRAPYD_PROJECT;
    if (celeryTaskId) {
      console.log(`[INFO] [${new Date().toISOString()}] Canceling Scrapy job ${celeryTaskId} for project ${project}`);
      const cancelResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/cancel.json`, null, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        auth: {
          username: process.env.SCRAPYD_USERNAME,
          password: process.env.SCRAPYD_PASSWORD,
        },
        params: {
          project,
          job: celeryTaskId,
        },
      });
      if (cancelResponse?.data?.status !== 'ok') {
        console.warn(`[WARN] [${new Date().toISOString()}] Failed to cancel Scrapy job ${jobId}`);
      } else {
        console.log(`[INFO] [${new Date().toISOString()}] Scrapy job ${celeryTaskId} canceled successfully`);
      }
    }
    // Delete the integration and its associated web integration
    await prisma.$transaction(async (prisma) => {
      await prisma.GroupIntegration.deleteMany({
        where: { integrationId: id },
      });
      await prisma.WebIntegration.delete({
        where: { id },
      });
      await prisma.Integration.delete({
        where: { id },
      });
    });
    console.log(`[INFO] [${new Date().toISOString()}] Deleted Web integration with ID ${id}`);
    return res.status(200).json({ message: 'Integration deleted successfully' });
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to delete Web integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


const updateWebIntegration = async (req, res) => {
  const { id } = req.params;
  const { companyId, userId } = req.user;

  const {
    name,
    providerType,
    url,
    updateTime,
  } = req.body;

  try {
    const existing = await prisma.Integration.findUnique({
      where: { id },
      include: { web: true }
    });

    if (!existing || existing.companyId !== companyId) {
      return res.status(404).json({ message: 'Integration not found or unauthorized' });
    }

    const isReachable = await checkWebUrlReachable(url);
    if (!isReachable) {
      return res.status(400).json({ error: 'URL is not reachable or invalid.' });
    }

    console.log(`[INFO] [${new Date().toISOString()}] Sending Web task to Scrapyd for URL ${url}`);

    const scrapydResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/schedule.json`, null, {
      headers: { 'Content-Type': 'multipart/form-data' },
      auth: {
        username: process.env.SCRAPYD_USERNAME,
        password: process.env.SCRAPYD_PASSWORD,
      },
      params: {
        project: process.env.SCRAPYD_PROJECT,
        spider: process.env.SCRAPYD_SPIDER,
        index_name: existing.opensearchIndexId,
        website_url: url,
      }
    });

    const jobId = scrapydResponse?.data?.jobid;
    if (!jobId) {
      console.error(`[ERROR] Scrapyd did not return a job ID`);
      return res.status(500).json({ error: 'Failed to trigger Scrapyd job' });
    }
    const updatedIntegration = await prisma.Integration.update({
      where: { id },
      data: {
        name,
        providerType,
        updateTime,
        celeryTaskId: jobId, // Store Scrapyd job ID
        firstLoad: true,
        status: 'loading'
      }
    });
    await prisma.WebIntegration.update({
      where: { id },
      data: {
        url,
      }
    });

    console.log(`[INFO] Web integration updated (ID: ${id})`);
    res.json({ message: 'Web integration updated', updatedIntegration });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Error updating Web integration' });
  }
};


// async function retryWebIntegration(req, res) {
//   const { integrationId } = req.params;
//   try {
//     const integration = await prisma.integration.findUnique({
//       where: { id: integrationId },
//       include: { web: true },
//     });
//     if (!integration) {
//       return res.status(404).json({ error: 'Web integration not found' });
//     }

//     console.log(`[INFO] [${new Date().toISOString()}] Retrying Web task with Scrapyd for URL ${integration.web.url}`);

//     const scrapydResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/schedule.json`, null, {
//       headers: { 'Content-Type': 'multipart/form-data' },
//       auth: {
//         username: process.env.SCRAPYD_USERNAME,
//         password: process.env.SCRAPYD_PASSWORD,
//       },
//       params: {
//         project: process.env.SCRAPYD_PROJECT,
//         spider: process.env.SCRAPYD_SPIDER,
//         index_name: integration.name,
//         period: integration.updateTime,
//         aliasName: integration.name,
//         max_data_size: 200,
//         website_url: integration.web.url,
//       }
//     });

//     const jobId = scrapydResponse?.data?.jobid;
//     if (!jobId) {
//       console.error(`[ERROR] Scrapyd did not return a job ID during Web retry`);
//       return res.status(500).json({ error: 'Failed to retry Scrapyd job' });
//     }

//     await prisma.integration.update({
//       where: { id: integrationId },
//       data: { celeryTaskId: jobId }, // Store Scrapyd job ID
//     });

//     console.log(`[INFO] [${new Date().toISOString()}] Retried Web task for integration ${integrationId}, new job ID: ${jobId}`);
//     return res.status(200).json({ message: 'Retry successful', jobId });
//   } catch (error) {
//     console.error(`[ERROR] [${new Date().toISOString()}] Failed to retry Web integration:`, error);
//     return res.status(500).json({ error: 'Internal server error' });
//   }
// }

module.exports = {
  createWebIntegration,
  getWebIntegrations,
  deleteWebIntegration,
  updateWebIntegration,
  // retryWebIntegration
};
