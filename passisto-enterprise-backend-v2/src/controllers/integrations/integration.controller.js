const prisma = require("@/config/db");
const opensearch = require("@/config/opensearch");


async function getIntegrations(req, res) {
  try {
    const { companyId } = req.user; // Get companyId from the middleware
    const integrations = await prisma.Integration.findMany({
      where: { companyId },
      include: {
        
        ftp: {
          select: {
            id: true,
            isSecure: true,
            server: true,
            port: true,
            username: true,
          },
        },
        jira: {
          select: {
            id: true,
            domain: true,
            email: true,
            project: true,
          },
        },
        web: {
          select: {
            id: true,
            url: true,
          },
        },
      },
    });
    return res.json(integrations);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get integrations:`, error);
    return res.status(500).json({ message: 'Server error' });
  }
}


async function getIntegrationsByGroup(req, res) {
  try {
    const { userId, companyId } = req.user;

    const user = await prisma.user.findUnique({
      where: { id: userId, companyId: companyId },
      include: {
        roles: true,
      },
    });
    
    const isAdmin = user?.roles.some(role => role.name === "ADMIN");
    
    if (isAdmin) {
      // Admin gets all integrations for the company
      const integrations = await prisma.integration.findMany({
        where: {
          companyId,
        },
        select: {
          id: true,
          name: true,
          opensearchIndexId: true,
          providerType: true,
        }
      });
      return res.status(200).json(integrations);
    } 
    else {
      // Get integrations only in the groups the user is part of
      const integrations = await prisma.integration.findMany({
        where: {
          companyId,
          groups: {
            some: {
              group: {
                users: {
                  some: {
                    userId: userId,
                  },
                },
              },
            },
          },
        },
        select: {
          id: true,
          name: true,
          opensearchIndexId: true,
          providerType: true,
        }
      });
      return res.status(200).json(integrations);
    }
  } catch (error) {
    console.error(
      `[ERROR] [${new Date().toISOString()}] Failed to get integrations by group:`,
      error
    );
    return res.status(500).json({ message: "Server error" });
  }
}


async function getIntegrationMetrics(req, res) {
  try {
    const { companyId } = req.user; // Get companyId from the middleware
    const integrations = await prisma.Integration.findMany({
      where: { companyId },
      select: {
        status: true,
      },
    });
    const metrics = {
      total: integrations.length,
      active: integrations.filter(i => i.status === 'loading' || i.status === 'refreshing').length,
      completed: integrations.filter(i => i.status === 'completed' || i.status === 'not_updated').length,
      failed: integrations.filter(i => i.status === 'failed').length,
    };
    return res.json(metrics);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get integration metrics:`, error);
    return res.status(500).json({ message: 'Server error' });
  }
}

async function getIntegrationById(req, res) {
  const { id } = req.params;
  try {
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: {
        ftp: {
          select: {
            id: true,
            server: true,
            port: true,
            username: true,
            isSecure: true,
          },
        },
        jira: {
          select: {
            id: true,
            domain: true,
            email: true,
            project: true,
          },
        },
        web: {
          select: {
            id: true,
            url: true,
          },
        },
      },
    });

    if (!integration) {
      return res.status(404).json({ message: "Integration not found" });
    }
    return res.status(200).json(integration);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get integration by ID:`, error);
    return res.status(500).json({ message: "Internal server error" });
  }
}


async function attachIntegrationToGroup(req, res) {
  const { companyId } = req.user; // Get companyId from the middleware
  const { integrationId, groupId } = req.body;
  try {
    // Get OpenSearch index
    const integration = await prisma.Integration.findUnique({
      where: { 
        id: integrationId, 
        companyId: companyId 
      },
      select: { opensearchIndexId: true },
    });
    // Get OpenSearch alias
    const group = await prisma.Group.findUnique({
      where: { 
        id: groupId, 
        companyId: companyId 
      },
      select: { opensearchAliasId: true },
    });
    
    const indexName = integration?.opensearchIndexId;
    const aliasName = group?.opensearchAliasId;

    if (!indexName || !aliasName) {
      return res.status(400).json({
        message: 'Missing index or alias for the given integration/group',
      });
    }
    // Attach index to alias in OpenSearch
    await opensearch.indices.updateAliases({
      body: {
        actions: [
          {
            add: {
              index: indexName,
              alias: aliasName,
            },
          },
        ],
      },
    });

    // Register the integration-group relation in the database
    await prisma.GroupIntegration.create({
      data: {
        groupId,
        integrationId,
      },
    });

    return res.status(200).json({
      message: `integration successfully attached to group`,
    });
  
  } catch (error) {
    console.error('Failed to attach index to alias:', error);
    return res.status(500).json({
      message: 'Internal server error while attaching integration to group'
    });
  }
}

async function removeIntegrationFromGroup(req, res) {
  const { companyId } = req.user; // Get companyId from the middleware
  const { integrationId, groupId } = req.body;
  try {
    // Get OpenSearch index
    const integration = await prisma.Integration.findUnique({
      where: { 
        id: integrationId, 
        companyId: companyId 
      },
      select: { opensearchIndexId: true },
    });
    // Get OpenSearch alias
    const group = await prisma.Group.findUnique({
      where: { 
        id: groupId, 
        companyId: companyId 
      },
      select: { opensearchAliasId: true },
    });
    
    const indexName = integration?.opensearchIndexId;
    const aliasName = group?.opensearchAliasId;

    if (!indexName || !aliasName) {
      return res.status(400).json({
        message: 'Missing index or alias for the given integration/group',
      });
    }
    // Remove alias association in OpenSearch
    await opensearch.indices.updateAliases({
      body: {
        actions: [
          {
            remove: {
              index: indexName,
              alias: aliasName,
            },
          },
        ],
      },
    });

    // Remove the integration-group relation from the database
    await prisma.GroupIntegration.deleteMany({
      where: {
        groupId,
        integrationId,
      },
    });
    
    return res.status(200).json({
      message: `integration successfully removed from group`,
    });
  
  } catch (error) {
    console.error('Failed to remove index from alias:', error);
    return res.status(500).json({
      message: 'Internal server error while removing integration from group'
    });
  }
}

module.exports = { getIntegrations, getIntegrationById, 
  getIntegrationMetrics, attachIntegrationToGroup, 
  removeIntegrationFromGroup, getIntegrationsByGroup
};