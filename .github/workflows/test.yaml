name: <PERSON><PERSON> Docker Compose to Test Server
on:
  push:
    paths:
      - 'docker-compose.test.yaml'

jobs:
  copy-file:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Co<PERSON> docker-compose.test.yaml to test server
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.SERVER_HOST_TEST }}       
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}     
          source: 'docker-compose.test.yaml'          
          target: '/pe-test'  

      - name: Rename file
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_TEST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-test
            mv docker-compose.test.yaml docker-compose.yml