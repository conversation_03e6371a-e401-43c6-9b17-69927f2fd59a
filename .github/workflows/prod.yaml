name: <PERSON><PERSON> Docker Compose to Test Server
on:
  push:
    paths:
      - 'docker-compose.prod.yaml'

jobs:
  copy-file:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Co<PERSON> docker-compose.test.yaml to test server
        uses: appleboy/scp-action@v0.1.3
        with:
          host: ${{ secrets.SERVER_HOST_PROD }}       
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}     
          source: 'docker-compose.prod.yaml'          
          target: '/pe-prod'  
          
      - name: Rename file
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_PROD }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-prod
            mv docker-compose.prod.yaml docker-compose.yml