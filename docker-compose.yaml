services:

  passisto-enterprise-frontend-v2:
    container_name: passisto-enterprise-frontend-v2
    build:
      context: ./passisto-enterprise-frontend-v2
      dockerfile: Dockerfile.development
    env_file:
      - ./passisto-enterprise-frontend-v2/.env.local
    environment:
      - PORT=3030
    volumes:
      - ./passisto-enterprise-frontend-v2:/app
    ports:
      - 127.0.0.1:3030:3030
      - 10.0.4.1:3020:3020
    # command: npm run dev
    networks:
      - pe-net  
  
  passisto-enterprise-backend-v2:
    container_name: passisto-enterprise-backend-v2
    build:
      context: ./passisto-enterprise-backend-v2
      dockerfile: Dockerfile
    env_file:
      - ./passisto-enterprise-backend-v2/.env.local
    environment:
      - PORT=5000
    ports:
      - "127.0.0.1:5000:5000"
      # - 10.0.4.1:5000:5000

    # volumes:
    #   - ./passisto-enterprise-backend-v2:/app
    networks:
      - pe-net
  
  pe-db:
    image: postgres:14-alpine
    container_name: pe-db
    ports:
      - 127.0.0.1:5432:5432
      - 10.0.4.1:5432:5432
    restart: always
    volumes:
      - ~/db_tasks_data:/var/lib/postgresql/tasks_data
    environment:
      - POSTGRES_PASSWORD=K5EadzuxNRaIGO0
      - POSTGRES_USER=postgres
      - POSTGRES_DB=payment
    networks:
      - pe-net
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "127.0.0.1:5050:80"
      - 10.0.4.1:5050:80
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - pe-net
    depends_on:
      - pe-db

  mongo_db:
    image: mongo:latest
    container_name: mongo_db
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: root
    volumes:
      - mongo_data:/data/db
    networks:
      - pe-net

  redis_celery_backend:
    image: redis:latest
    container_name: redis_celery_backend
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - pe-net

  ps-jira-worker:
    build: ./passisto-enterprise-workers/jira-worker
    container_name: ps-jira-worker
    env_file:
      - ./passisto-enterprise-workers/jira-worker/.env
    command: celery -A tasks worker -l info -Q jira -n jira_worker@%h
    networks:
      - pe-net
  
  ps-ftp-worker:
    build: ./passisto-enterprise-workers/ftp-worker
    container_name: ps-ftp-worker
    env_file:
      - ./passisto-enterprise-workers/ftp-worker/.env
    command: celery -A tasks worker -l info -Q ftp -c 1 -n ftp_worker@%h -Ofair
    networks:
      - pe-net

  scrapyd-server:
    build: ./passisto-enterprise-workers/passisto-scrapyd-server
    container_name: scrapyd-server
    env_file:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/.env
    ports:
      - 127.0.0.1:6800:6800
    volumes:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/eggs:/.scrapy/eggs/website_scraper
      - ./passisto-enterprise-workers/passisto-scrapyd-server/logs:/.scrapy/logs/website_scraper/generalspider 
    networks:
      - pe-net
  
  web-worker:
    build: ./passisto-enterprise-workers/web-worker
    container_name: web-worker
    depends_on:
      - scrapyd-server 
    env_file:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/.env
    networks:
      - pe-net


networks:
  pe-net:
    external: true

volumes:
  mongo_data:
  pgadmin_data:
  redis-data:

