services:

  passisto-enterprise-frontend-v2:
    image: passisto/passisto-enterprise-frontend-v2:prod
    container_name: passisto-enterprise-frontend-v2
    restart: always
    environment:
      - PORT=3030
    ports:
      - 3030:3030
    networks:
      - prod-net  
  
  passisto-enterprise-backend-v2:
    image: passisto/passisto-enterprise-backend-v2:prod
    container_name: passisto-enterprise-backend-v2
    env_file:
      - ./passisto-enterprise-backend-v2/.env
    environment:
      - PORT=5000
    ports:
      # - "5000:5000"
      - 127.0.0.1:5000:5000
      - 10.0.3.1:5000:5000
    # volumes:
    #   - ./certs/pg.crt:/app/certs/pg.crt
    # command: ["node", "src/index.js"]
    restart: always
    networks: 
      - prod-net

  chatbot:
    image: passisto/pe-chatbot:prod
    container_name: chatbot
    restart: always
    ports:
      # - 5921:5921
      - 127.0.0.1:5921:5921
      - 10.0.3.1:5921:5921
    environment:
      - APP_PORT=5921
    env_file:
      - ./passisto-enterprise-chatbot/.env
    depends_on:
      - mongo_db
    networks:
      - prod-net

  mongo_db:
    image: mongo:latest
    container_name: mongo_db
    restart: always
    ports:
      # - "28017:27017"
      - "127.0.0.1:28017:27017"
      - "10.0.3.1:28017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: root
    volumes:
      - mongo_data:/data/db
    networks:
      - prod-net

  # nginx:
  #   image: nginx:latest
  #   container_name: nginx
  #   restart: always
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
  #   networks:
  #     - prod-net

  # opensearch:
  #   image: opensearchproject/opensearch:2.12.0
  #   container_name: opensearch
  #   restart: always
  #   environment:
  #     - discovery.type=single-node
  #     - bootstrap.memory_lock=true
  #     - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
  #     - plugins.security.disabled=true
  #     - node.name=opensearch-node
  #     - cluster.name=opensearch-cluster
  #     - OPENSEARCH_INITIAL_ADMIN_PASSWORD=Hasdgg52u1
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #   volumes:
  #     - opensearch-data:/usr/share/opensearch/data
  #   ports:
  #     # - 9200:9200
  #     # - 9600:9600 # for performance analyzer
  #     - 127.0.0.1:9200:9200
  #     - 10.0.3.1:9200:9200
  #     - 127.0.0.1:9600:9600
  #     - 10.0.3.1:9600:9600
  #   networks:
  #     - prod-net


  rabbitmq-celery:
    image: rabbitmq:3-management
    container_name: rabbitmq-celery
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: rabbit
      RABBITMQ_DEFAULT_PASS: Fqb3Ita2lsb
    ports:
      # - "5672:5672"
      # - "15672:15672"
      - "127.0.0.1:5672:5672"
      - "127.0.0.1:15672:15672"
      - "10.0.3.1:5672:5672"
      - "10.0.3.1:15672:15672"
    networks:
      - prod-net


  redis-celery:
    image: redis:latest
    container_name: redis-celery
    ports:
      # - "6278:6379"
      - "127.0.0.1:6278:6379"
      - 10.0.3.1:6278:6379
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: always
    networks:
      - prod-net


  passisto-flower:
    image: mher/flower
    container_name: passisto-flower
    restart: always

    ports:
      # - "5555:5555"
      - "127.0.0.1:5555:5555"
      - "10.0.3.1:5555:5555"
    environment:
      - FLOWER_BASIC_AUTH=flower:Yk6AUPVDEg
      - CELERY_BROKER_URL=amqp://rabbit:Fqb3Ita2lsb@rabbitmq-celery:5672
      - CELERY_RESULT_BACKEND=redis://redis-celery:6379
    depends_on:
      - rabbitmq-celery
      - redis-celery
      - passisto-enterprise-backend-v2
    networks:
      - prod-net


  ps-jira-worker:
    image: passisto/jira-worker:prod
    container_name: ps-jira-worker
    restart: always

    depends_on:
      - passisto-flower

    env_file:
      - ./passisto-enterprise-workers/jira-worker/.env
    command: celery -A tasks worker -l info -Q jira -n jira_worker@%h
    networks:
      - prod-net


  ps-ftp-worker:
    image: passisto/ftp-worker:prod
    container_name: ps-ftp-worker
    restart: always
    depends_on:
      - passisto-flower
    env_file:
      - ./passisto-enterprise-workers/ftp-worker/.env
    command: celery -A tasks worker -l info -Q ftp -c 1 -n ftp_worker@%h -Ofair
    networks:
      - prod-net


  scrapyd-server:
    image: passisto/scrapyd-server:prod
    container_name: pe-scrapyd-server
    restart: always
    depends_on:
      - passisto-flower
    env_file:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/.env
    ports:
      # - 6800:6800
      - 127.0.0.1:6820:6800
      - 10.0.3.1:6820:6800
    volumes:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/eggs:/.scrapy/eggs/website_scraper
      - ./passisto-enterprise-workers/passisto-scrapyd-server/logs:/.scrapy/logs/website_scraper/generalspider
    networks:
      - prod-net


  web-worker:
    image: passisto/web-worker:prod
    container_name: web-worker
    restart: always
    depends_on:
      - scrapyd-server
    env_file:
      - ./passisto-enterprise-workers/web-worker/.env
    networks:
      - prod-net


networks:
  prod-net:
    external: true

volumes:
  mongo_data:
  pgadmin_data:
  redis-data:
  opensearch-data: