
# include:
#   - ./passisto-enterprise-chatbot/docker-compose.yml

services:

  chatbot:
    container_name: chatbot
    build:
      context: ./passisto-enterprise-chatbot
      dockerfile: Dockerfile
    ports:
      - 127.0.0.1:5921:5921
      - ********:5921:5921
    environment:
      - EXPRESS_PORT=5921
    # env_file:
      # - ./passisto-enterprise-chatbot/.env
    depends_on:
      - redis-history
    networks:
      - pe-net

  redis-history:
    image: redis:latest
    container_name: redis-history
    ports:
      - 127.0.0.1:6379:6379
    networks:
      - pe-net

  django:
    container_name: django
    restart: always
    build: ./passisto-enterprise-backend
    # env_file: ./passisto-enterprise-backend/.env
    environment:
      - PORT=8090
    depends_on:
      - db_backend
    ports:  
      - 127.0.0.1:8090:8090
      - ********:8090:8090
    volumes:
      - ./volumes/dj-celery:/usr/django-celery
    networks:
      - pe-net
  
  pe_frontend:
    container_name: pe_frontend
    build:
      context: ./passisto-enterprise-frontend
      dockerfile: Dockerfile

    # env_file:
      # - ./passisto-enterprise-frontend/.env.local

    environment:
      - PORT=3001

    ports:
      - 127.0.0.1:3001:3001
      - ********:3001:3001

    networks:
      - pe-net    
  
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: 'rabbitmq'
    environment:
        - RABBITMQ_DEFAULT_USER=guest
        - RABBITMQ_DEFAULT_PASS=jqnY4_12JEd5_8_
    ports:
        - 127.0.0.1:5672:5672
        - 127.0.0.1:15672:15672
        - ********:5672:5672
        - ********:15672:15672
    volumes:
        - ~/.docker-conf/rabbitmq/data/:/var/lib/rabbitmq/
        - ~/.docker-conf/rabbitmq/log/:/var/log/rabbitmq
    networks:
      - pe-net
    
  flower:
    image: mher/flower:2.0.0
    environment: 
      - CELERY_BROKER_URL=pyamqp://guest:jqnY4_12JEd5_8_@rabbitmq:5672//
      - FLOWER_PORT=5555
      - FLOWER_BASIC_AUTH=flower:IrgjgX;L9C0X
    ports:
      - 127.0.0.1:5555:5555
      - ********:5555:5555
    command: celery flower --url-prefix=flower
    depends_on:
      - rabbitmq
    networks:
      - pe-net

  db_backend:
    image: postgres:14-alpine
    container_name: db_backend
    ports:
      - 127.0.0.1:5321:5432
      - ********:5321:5432
    restart: always
    volumes:
      - ~/db_tasks_data:/var/lib/postgresql/tasks_data
    environment:
      - POSTGRES_PASSWORD=K5EadzuxNRaIGO0
      - POSTGRES_USER=postgres
      - POSTGRES_DB=BACKEND
    networks:
      - pe-net

  jira-worker:
    build: ./passisto-enterprise-workers/jira-worker/
    container_name: jira-worker
    # env_file: ./passisto-enterprise-workers/jira-worker/.env
    command: celery -A tasks worker -l info -Q jira -c 1 -n jira_worker@%h -Ofair
    networks:
      - pe-net
    
  web-worker:
    build: ./passisto-enterprise-workers/web-worker
    container_name: web-worker
    # env_file: ./passisto-enterprise-workers/web-worker/.env
    ports:
     - 127.0.0.1:6801:6801
     - ********:6801:6801
    networks:
      - pe-net

  ftp-worker:
    build: ./passisto-enterprise-workers/ftp-worker
    # env_file: ./passisto-enterprise-workers/ftp-worker/.env
    container_name: ftp-worker
    environment:
      - C_FORCE_ROOT=true
    command: celery -A tasks worker -l info -Q ftp -c 1 -n ftp_worker@%h -Ofair
    networks:
      - pe-net


  

  opensearch-node1:
    image: opensearchproject/opensearch:latest
    container_name: opensearch-node1
    restart: always
    environment:
      - cluster.name=opensearch-cluster
      - node.name=opensearch-node1
      - discovery.seed_hosts=opensearch-node1
      - cluster.initial_cluster_manager_nodes=opensearch-node1
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - OPENSEARCH_USERNAME=admin
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=Hasdgg52u1
      # - DISABLE_SECURITY_PLUGIN=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - opensearch-data1:/usr/share/opensearch/data
    ports:
      - "********:9200:9200"
      - "127.0.0.1:9200:9200"
      - "********:9600:9600"
      - "127.0.0.1:9600:9600"
    networks:
      - pe-net

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:latest
    container_name: opensearch-dashboards
    restart: always
    ports:
      - "********:5601:5601"
      - "127.0.0.1:5601:5601"
    expose:
      - "5601"
    environment:
      - OPENSEARCH_HOSTS=["https://opensearch-node1:9200"]
      # - DISABLE_SECURITY_DASHBOARDS_PLUGIN=true
      # - server.ssl.clientAuthentication=none
      # - server.ssl.certificate=
      # - server.ssl.certificate=
    
    # volumes:
    #   - ./certs:/usr/share/opensearch-dashboards
    networks:
      - pe-net

networks:
  pe-net:
    external: true

volumes:
  opensearch-data1:

