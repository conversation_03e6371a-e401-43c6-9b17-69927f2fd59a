
# include:
#   - ./passisto-enterprise-chatbot/docker-compose.test.yml
  # - ./passisto-enterprise-vectorstore/docker-compose.yaml

services:

  chatbot:
    image: passisto/pe-chatbot:dev
    container_name: chatbot
    ports:
      - 127.0.0.1:5921:5921
      - 10.0.2.1:5921:5921
    env_file: 
      - ./passisto-enterprise-chatbot/.env
    environment:
      - EXPRESS_PORT=5921
    # depends_on:
    #   - redis-history

    command: ["bash", "./entry.test.sh"]
    networks:
      - test-net

  django:
    image: passisto/pe-django-backend:dev
    restart: always
    environment:
      - PORT=8090
    env_file: 
      - ./passisto-django-backend/.env
    depends_on:
      - db_backend
    ports:
      - 127.0.0.1:8090:8090
      - 10.0.2.1:8090:8090
    volumes:
      - ./volumes/dj-celery:/usr/django-celery

    command: ['bash', './entry.test.sh']
    networks:
      - test-net
  
  pe-frontend:
    image: passisto/pe-frontend:dev
    container_name: pe-frontend
    environment:
      - PORT=3020
    ports:
      - 127.0.0.1:3020:3020
      - 10.0.2.1:3020:3020
    networks:
      - test-net    
  
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: 'rabbitmq'
    environment:
        - RABBITMQ_DEFAULT_USER=guest
        - RABBITMQ_DEFAULT_PASS=jqnY4_12JEd5_8_
    ports:
        - 127.0.0.1:5672:5672
        - 127.0.0.1:15672:15672
        - 10.0.2.1:5672:5672
        - 10.0.2.1:15672:15672
    # volumes:
    #     - ~/.docker-conf/rabbitmq/data/:/var/lib/rabbitmq/
    #     - ~/.docker-conf/rabbitmq/log/:/var/log/rabbitmq
    networks:
      - test-net
    
  flower:
    image: mher/flower:2.0.0
    environment: 
      - CELERY_BROKER_URL=pyamqp://guest:jqnY4_12JEd5_8_@rabbitmq:5672//
      - FLOWER_PORT=5555
      - FLOWER_BASIC_AUTH=flower:IrgjgX;L9C0X
    ports:
      - 127.0.0.1:5555:5555
      - 10.0.2.1:5555:5555
    command: celery flower --url-prefix=flower
    depends_on:
      - rabbitmq
    networks:
      - test-net

  db_backend:
    image: postgres:14-alpine
    container_name: db_backend
    ports:
      - 127.0.0.1:5321:5432
      - 10.0.2.1:5321:5432
    restart: always
    # volumes:
    #   - ~/db_tasks_data:/var/lib/postgresql/tasks_data
    environment:
      - POSTGRES_PASSWORD=K5EadzuxNRaIGO0
      - POSTGRES_USER=postgres
      - POSTGRES_DB=BACKEND
    networks:
      - test-net

  jira-worker:
    image: passisto/jira-worker:dev
    container_name: jira-worker
    env_file:
      - ./jira-worker/.env
    command:  ["bash", "./entry.test.sh"]
    networks:
      - test-net    
  ftp-worker:
    image: passisto/ftp-worker:dev
    container_name: ftp-worker
    env_file:
      - ./ftp-worker/.env
    environment:
      - C_FORCE_ROOT=true
    command:  ["bash", "./entry.test.sh"]
    networks:
      - test-net

  opensearch-node1:
    image: opensearchproject/opensearch:latest
    container_name: opensearch-node1
    restart: always
    environment:
      - cluster.name=opensearch-cluster
      - node.name=opensearch-node1
      - discovery.seed_hosts=opensearch-node1
      - cluster.initial_cluster_manager_nodes=opensearch-node1
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - OPENSEARCH_USERNAME=admin
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=Hasdgg52u1
      # - DISABLE_SECURITY_PLUGIN=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - opensearch-data1:/usr/share/opensearch/data
    ports:
      - "********:9200:9200"
      - "127.0.0.1:9200:9200"
      - "********:9600:9600"
      - "127.0.0.1:9600:9600"
    networks:
      - test-net

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:latest
    container_name: opensearch-dashboards
    restart: always
    ports:
      - "********:5601:5601"
      - "127.0.0.1:5601:5601"
    expose:
      - "5601"
    environment:
      - OPENSEARCH_HOSTS=["https://opensearch-node1:9200"]
      # - DISABLE_SECURITY_DASHBOARDS_PLUGIN=true
      # - server.ssl.clientAuthentication=none
      # - server.ssl.certificate=
      # - server.ssl.certificate=
    
    # volumes:
    #   - ./certs:/usr/share/opensearch-dashboards
    networks:
      - test-net
volumes:
  opensearch-data1:
  

networks:
  test-net:
    external: true

