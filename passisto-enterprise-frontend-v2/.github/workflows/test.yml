name: Test Next.js Frontend CI CD

on:
  push:
    branches: [dev]
  pull_request:
    branches: [dev]

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Use Node.js 20.14.9
        uses: actions/setup-node@v2
        with:
          node-version: '20.0.0'

      - name: Install dependencies
        run: npm install

      
      - name: Rename .env.test to .env
        run: mv .env.test .env

      - name: Remove all other .env files
        run: rm -f .env.*
      
      - name: Build Next.js app
        run: npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          file: Dockerfile.development
          tags: ${{ secrets.DOCKER_USERNAME }}/passisto-enterprise-frontend-v2:dev
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest

    steps:

      - name: Deploy to Ubuntu server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_TEST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-test
            docker pull ${{ secrets.DOCKER_USERNAME }}/passisto-enterprise-frontend-v2:dev
            
            docker compose up -d passisto-enterprise-frontend-v2 --force-recreate