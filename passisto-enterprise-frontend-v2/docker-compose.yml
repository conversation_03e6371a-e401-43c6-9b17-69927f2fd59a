
services:
  client_front_end:
    container_name: pe_client_frontend_v2-payment
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=development

    env_file:
      - .env.local

    environment:
      - PORT=3040

    ports:
      - 127.0.0.1:3040:3040
      - 10.0.1.1:3040:3040

    command: "npm run dev"

    volumes:
      - ./src:/app/src

    networks:
      - pe-net       


networks:
  pe-net:
    external: true
