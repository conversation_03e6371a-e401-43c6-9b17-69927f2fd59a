{"24-7-available": "24/7 disponible", "a-descriptive-name-for-this-integration": "Un nom descriptif pour cette intégration", "a-descriptive-name-for-this-integration-0": "Un nom descriptif pour cette intégration", "a-descriptive-name-for-this-integration-1": "Un nom descriptif pour cette intégration", "access-anytime": "Accéder à tout moment", "access-code": "Code d'accès", "access-granted": "<PERSON><PERSON>ès <PERSON>", "access-interview": "<PERSON><PERSON><PERSON>", "access-powerful-ai-tools-to-streamline-your-recruitment-process-and-find-the-perfect-candidates-faster": "Accédez à de puissants outils d'IA pour rationaliser votre processus de recrutement et trouver les candidats parfaits plus rapidement", "acme-inc": "ACME Inc.", "add-column": "Ajouter la colonne", "add-item": "Ajouter", "add-platform": "Ajouter une plate-forme", "add-row": "Ajouter une ligne", "advanced-ai-technology": "Technologie AI avancée", "agent": "Agent", "ai-interviewer": "AI Interviewer", "alignment": "Alignement", "all-in-one-ai-recruitment-suite": "Suite de recrutement AI tout-en-un", "alt-text": "Texte alt", "an-error-occurred-please-try-again-later": "Une erreur s'est produite. \nVeuillez réessayer plus tard.", "an-unexpected-error-occurred-please-try-again-or-return-to-the-dashboard": "Une erreur inattendue s'est produite. \nVeuillez réessayer ou revenir au tableau de bord.", "annual": "<PERSON><PERSON>", "api-token": "Jeton API", "api-token-is-required": "Le jeton API est requis.", "are-you-sure-you-want-to-delete-the-integration": "Êtes-vous sûr de vouloir supprimer l'intégration \"", "areas-of-interest": "Zones d'intérêt", "auto": "Auto", "auto-0": "Auto", "back-to-home": "Retour à la maison", "background-color": "<PERSON><PERSON><PERSON> de fond", "bank-grade-encryption": "Cryptage de qualité bancaire", "bg-background-shadow-none-border-rounded-lg": "BG-Background Shadow-None <PERSON> Ron<PERSON>ée-LG", "bio": "Bio", "bold": "Audacieux", "bolder": "Plus audacieux", "border-color": "<PERSON><PERSON><PERSON> de bordure", "border-radius": "Rayon de frontière", "border-radius-0": "Rayon de frontière", "border-width": "Largeur des frontières", "built-for-your-workflow": "Construit pour votre flux de travail", "bullet-list": "Liste de balles", "button-url": "URL du bouton", "button-width": "<PERSON><PERSON> du bouton", "call": "<PERSON><PERSON>", "camera-is-turned-off": "La caméra est éteinte", "cancel": "Annuler", "cancel-anytime": "✓ Annuler à tout moment", "center": "Centre", "choose-between-ftp-and-secure-sftp": "Choisissez entre FTP et sécurisé SFTP.", "choose-your-plan": "Choisissez votre plan", "company": "Entreprise", "competitor-analysis": "Analy<PERSON> des <PERSON>s", "complete": "Complet", "complete-your-profile": "Complétez votre profil", "configuration": "Configuration", "configure-your-ftp-integration-by-providing-the-required-details-below": "Configurez votre intégration FTP en fournissant les détails requis ci-dessous.", "configure-your-jira-integration-by-providing-the-required-details-below": "Configurez votre intégration Jira en fournissant les détails requis ci-dessous.", "configure-your-web-integration-by-providing-the-required-details-below": "Configurez votre intégration Web en fournissant les détails requis ci-dessous.", "connection-type": "Type de connexion", "contact-sales": "Ventes de contact", "contact-support": "Support de contact", "content": "Contenu", "create-an-account-and-get-access-to-all-features-for-15-days-no-credit-card-required": "Créez un compte et accédez à toutes les fonctionnalités pour 15 jours, aucune carte de crédit requise.", "create-ftp-integration": "Créer une intégration FTP", "create-jira-integration": "Créer une intégration Jira", "create-web-integration": "Créer une intégration Web", "created-at": "<PERSON><PERSON><PERSON>", "created-by": "C<PERSON><PERSON> par", "current-integration-status": "État d'intégration actuelle", "currently-subscribed": "Actuellement a<PERSON>", "custom": "Coutume", "custom-features": "Fonctionnalités personnalisées", "custom-integration": "Intégration personnalisée", "custom-solutions-for-large-teams": "Solutions personnalisées pour les grandes équipes", "dashboard": "Tableau de bord", "dashed": "En pointillé", "dedicated-support": "Support dédié", "delete": "<PERSON><PERSON><PERSON><PERSON>", "delete-integration": "Supprimer l'intégration", "deleting": "Suppression ...", "divider-color": "<PERSON>uleur de diviseur", "divider-style": "Style de séparation", "divider-width": "Largeur de diviseur", "doe": "Biche", "domain": "Domaine", "domain-name": "Nom de domaine", "domain-name-is-required": "Le nom de domaine est requis.", "dotted": "Pointé", "e-g-prd": "par exemple \nPRC", "e-g-prd-not-the-full-project-name": "(par exemple \"PRD\", pas le nom complet du projet).", "e-g-web-development-ai-ml": "par exemple, développement Web, AI / ML", "edit": "Modifier", "edit-integration": "Modifier l'intégration", "email": "E-mail", "email-associated-with-your-jira-account": "E-mail associé à votre compte Jira", "email-builder": "Email Builder", "end": "Fin", "enter-the-code-from-your-email": "Entrez le code de votre e-mail", "enter-the-code-from-your-invitation": "Entrez le code à partir de votre invitation", "enter-your-email-address-and-well-send-you-a-password-reset-code": "Entrez votre adresse e-mail et nous vous enverrons un code de réinitialisation de mot de passe", "enter-your-email-address-and-well-send-you-a-password-reset-code-0": "Entrez votre adresse e-mail et nous vous enverrons un code de réinitialisation de mot de passe", "enter-your-email-and-access-code-to-start-your-interview": "Entrez votre e-mail et votre code d'accès pour démarrer votre entretien", "enter-your-new-password-and-the-reset-code-we-sent-to-your-email": "Entrez votre nouveau mot de passe et le code de réinitialisation que nous avons envoyé à votre e-mail", "enterprise": "Entreprise", "enterprise-features": "Fonctionnalités d'entreprise", "enterprise-security": "Sécurité de l'entreprise", "every-integration-updatetime-days": "<PERSON>que {0} jours", "everything-worked-perfectly-your-integration-successfully-brought-in-the-data": "Tout a parfaitement fonctionné. \nVotre intégration a apporté avec succès les données.", "experience-level": "Niveau d'expérience", "failed-to-initialize-checkout": "Échec de l'initialisation du paiement", "failed-to-load-plans": "Échec du chargement des plans", "failed-to-load-subscription-details": "Impossible de charger les détails de l'abonnement", "failed-to-load-subscription-information": "Impossible de charger les informations d'abonnement", "failed-to-update-usage-tracking": "Échec de la mise à jour du suivi de l'utilisation", "financial-reports": "Rapports financiers", "first-name": "Prénom", "font-size": "Taille de la police", "font-weight": "Poids de la police", "forgot-your-password": "Votre mot de passe oublié?", "form-not-found": "Formulaire introuvable", "free": "<PERSON><PERSON><PERSON>", "free-0": "<PERSON><PERSON><PERSON>", "free-plan-subscription-limit-reached": "Limite d'abonnement au plan gratuit atteint", "ftp-account-password-stored-securely": "Mot de passe du compte FTP (stocké en sécurité)", "ftp-account-username": "Nom d'utilisateur du compte FTP", "ftp-integration": "Intégration FTP", "ftp-integration-0": "Intégration FTP", "ftp-not-encrypted": "FTP (non crypté)", "ftp-server-address": "<PERSON>resse du serveur FTP", "ftp-server-port-usually-21": "Port du serveur FTP (généralement 21)", "full-width": "<PERSON><PERSON>e largeur", "generate-the-interview": "Générer l'entretien", "get-your-token-here": "Obtenez votre jeton ici", "go-to-dashboard": "<PERSON>er au tableau de bord", "have-questions-about-which-plan-is-right-for-you-our-team-is-happy-to-help": "Vous avez des questions sur quel plan vous convient? \nNotre équipe est heureuse d'aider.", "having-trouble-contact-support-at": "Vous avez des problèmes? \nContacter le support à", "header-background-color": "<PERSON><PERSON><PERSON> d'arrière-tête", "height": "<PERSON><PERSON>", "how-often-to-sync-data-minimum-1-day": "À quelle fréquence synchroniser les données (minimum 1 jour)", "icon-color": "Couleur icône", "icon-size": "Taille de l'icône", "icon-spacing": "Espacement des icônes", "image-description": "Description de l'image", "image-url": "URL d'image", "included-for-all-plans": "Inclus pour tous les plans", "instant-feedback": "Rétroaction instantanée", "integration": "Intégration", "integration-name": "Nom d'intégration", "integration-status-guide": "Guide de l'état d'intégration", "interview-access": "Accès aux entretiens", "interview-id-or-user-id-is-missing": "L'ID d'entrevue ou l'ID utilisateur est manquant.", "interview-questions": "Questions d'entrevue", "invalid-email-or-access-code-please-check-your-credentials-and-try-again": "E-mail ou code d'accès non valide. \nVeuillez vérifier vos informations d'identification et réessayer.", "invalid-email-or-access-code-please-check-your-credentials-and-try-again-0": "E-mail ou code d'accès non valide. \nVeuillez vérifier vos informations d'identification et réessayer.", "it-looks-like-no-messages-have-been-sent-yet": "Il semble qu'aucun message n'ait encore été envoyé.", "jira-integration": "Intégration Jira", "john": "<PERSON>", "join-100-users": "Rejoignez 100 utilisateurs", "junior-0-2-years": "Junior (0-2 ans)", "last-name": "Nom de famille", "last-rerun": "Dernière rediffusion", "leave-blank-to-keep-current-password": "Laisser blanc pour garder le mot de passe actuel", "leave-blank-to-keep-current-token": "<PERSON>sser blanc pour garder le jeton actuel", "left": "G<PERSON><PERSON>", "lighter": "Plus léger", "line-height": "<PERSON><PERSON> <PERSON> ligne", "link-image": "Image de liaison", "link-url": "URL de liaison", "linkedin-profile-optional": "Profil <PERSON> (facultatif)", "list-items": "Énumérer les éléments", "list-type": "Type de liste", "listening": "Écoute", "loading-subscription-details": "Chargement des détails de l'abonnement ...", "margin": "Marge", "mid-level-3-5-years": "De niveau intermédiaire (3-5 ans)", "monthly": "<PERSON><PERSON><PERSON>", "name-must-be-at-least-2-characters": "Le nom doit comporter au moins 2 caractères.", "name-must-be-at-least-2-characters-0": "Le nom doit comporter au moins 2 caractères.", "new-password": "Nouveau mot de passe", "next": "Suivant", "no": "Non", "no-active-subscription": "Aucun abonnement actif", "no-active-subscription-0": "Aucun abonnement actif", "no-configuration-available": "Aucune configuration disponible.", "no-credit-card-required": "✓ Aucune carte de crédit requise", "no-messages-found": "Aucun message trouvé", "no-questions-available-for-this-interview": "Aucune question disponible pour cette interview", "normal": "Normale", "not-updated": "Non mis à jour:", "numbered-list": "Liste numérotée", "overview": "<PERSON><PERSON><PERSON><PERSON>", "padding": "Rembourrage", "password": "Mot de passe", "password-is-required": "Le mot de passe est requis.", "payment-successful": "Paiement ré<PERSON>!", "permission-denied": "Permission refusée", "personal-info": "Informations personnelles", "plan-features": "Planifier les fonctionnalités", "platform": "Plate-forme", "please-enter-a-valid-email-address": "S'il vous plaît, mettez une adresse email valide.", "please-enter-a-valid-url": "Veuillez saisir une URL valide.", "please-enter-both-email-and-access-code": "Veuillez saisir les e-mails et le code d'accès", "port": "Port", "port-is-required": "Le port est requis.", "preferences": "Préférences", "previous": "Précédent", "priority-24-7-support": "Priorité Support 24/7", "product-backlog": "Arri<PERSON><PERSON> de produit", "professional": "Professionnel", "project": "Projet", "project-is-required": "Le projet est requis.", "project-key": "Clé du projet", "project-key-0": "clé du projet", "questions": "Questions", "quick-actions": "Actions rapides", "real-time-analysis": "Analyse en temps réel", "redirecting-to-checkout": "Redirection vers le paiement ...", "redirecting-you-to-your-interview": "Vous rediriger vers votre interview ...", "remove-column": "Re<PERSON>rer la colonne", "remove-row": "Supprimer la ligne", "reset-code": "Réinitialiser le code", "reset-password": "Réinitialiser le mot de passe", "retry": "<PERSON><PERSON><PERSON><PERSON>", "retry-integration": "Réessier l'intégration", "retrying": "Réessayer ...", "return-to-dashboard": "Retour au tableau de bord", "right": "<PERSON><PERSON><PERSON>", "role": "R<PERSON><PERSON>", "save-20": "(Économisez 20%)", "save-changes": "Enregistrer les modifications", "secure-connection": "Connexion sécurisée", "select-alignment": "Sélectionnez l'alignement", "select-connection-type": "Sélectionner le type de connexion", "select-experience-level": "Sélectionnez le niveau d'expérience", "select-list-type": "Sélectionner le type de liste", "select-style": "Sélectionner le style", "select-weight": "Sélectionner le poids", "select-width": "Sélectionner la largeur", "send-reset-code": "Envoyer le code de réinitialisation", "send-reset-code-0": "Envoyer le code de réinitialisation", "senior-5-years": "Senior (5 ans)", "server": "Ser<PERSON><PERSON>", "server-ip": "IP de serveur", "server-ip-is-required": "IP de serveur est requis.", "settings": "Paramètres", "sftp-secure": "SFTP (sécurisé)", "social-media-links": "Liens de médias sociaux", "software-engineer": "Ingénieur logiciel", "solid": "Solide", "something-went-wrong": "<PERSON><PERSON><PERSON> chose s'est mal passé.", "something-went-wrong-0": "<PERSON><PERSON><PERSON> chose s'est mal passé!", "something-went-wrong-the-integration-couldnt-fetch-the-data-properly": "<PERSON><PERSON><PERSON> chose s'est mal passé. \nL'intégration n'a pas pu récupérer les données correctement.", "start-free-trial": "<PERSON><PERSON><PERSON><PERSON> l'essai gratuit", "start-growing-your-business-quickly": "Commencez à développer votre entreprise rapidement", "status": "Statut", "subscribe-now": "Abonnez-vous maintenant", "support": "Support", "table-headers": "Têtes de <PERSON>", "table-rows": "Lignes de <PERSON>", "tailored-pricing-for-your-specific-needs": "Prix ​​sur mesure pour vos besoins spécifiques", "tailored-to-your-needs": "Adapté à vos besoins", "tell-us-about-yourself": "Parlez-nous de vous", "tell-us-more-about-yourself-to-get-started": "Parlez-nous de vous pour commencer", "text-color": "Couleur de texte", "the-form-youre-looking-for-doesnt-exist-or-has-been-deleted": "Le formulaire que vous recherchez n'existe pas ou a été supprimé.", "the-integration-already-worked-before-and-now-its-trying-to-update-its-data-again": "L'intégration a déjà fonctionné auparavant, et il essaie maintenant de mettre à jour ses données.", "the-system-tried-to-refresh-the-data-but-it-didnt-work-this-time-it-stayed-as-it-was-before": "Le système a essayé de raf<PERSON><PERSON>r les données, mais cela n'a pas fonctionné cette fois. \nIl est resté comme avant.", "the-web-url-to-fetch-data-from": "L'URL Web pour récupérer des données à partir de", "this-action-cannot-be-undone": "Cette action ne peut pas être annulée.", "this-is-the-unique": "C'est l'unique", "this-means-your-integration-just-started-collecting-information-its-the-first-step-after-setting-it-up": "Cela signifie que votre intégration vient de commencer à collecter des informations. \nC'est la première étape après l'avoir configurée.", "transcript": "Transcription", "try-again": "Essayer à nouveau", "try-again-0": "Essayer à nouveau", "two-factor-authentication-is-required-but-this-ui-does-not-handle-that": "Une authentification à deux facteurs est requise, mais cette interface utilisateur ne gère pas cela", "understand-what-each-status-means-in-simple-words": "Comprendre ce que chaque statut signifie en mots simples.", "unit": "Unité", "unit-0": "Unité", "update-frequency": "Mettre à jour la fréquence", "update-frequency-days": "Mettre à jour la fréquence (jours)", "update-frequency-days-0": "Mettre à jour la fréquence (jours)", "update-time-must-be-at-least-1-day": "Le temps de mise à jour doit être au moins 1 jour.", "update-time-must-be-at-least-1-day-0": "Le temps de mise à jour doit être au moins 1 jour.", "user-not-found": "Utilisateur introuvable.", "username": "Nom d'utilisateur", "username-is-required": "Le nom d'utilisateur est requis.", "visit-website": "Visiter le site Web", "web-integration": "Intégration Web", "web-integration-0": "Intégration Web", "welcome-to": "Bienvenue dans", "width": "<PERSON><PERSON>", "yes": "O<PERSON>", "you": "<PERSON><PERSON>", "you-can-generate-a-jira-api-token-from-your-atlassian-account-settings": "Vous pouvez générer un jeton API JIRA à partir de vos paramètres de compte Atlassian.", "you-can-only-subscribe-to************************-if-your-previous-free-plan-subscription-is-no-longer-active-you-cannot-subscribe-to-the-free-plan-again-to-continue-using-the-service-please-choose-a-paid-plan": "Vous ne pouvez vous abonner au plan gratuit qu'une seule fois. \nMême si votre abonnement au plan gratuit précédent n'est plus actif, vous ne pouvez pas vous abonner à nouveau au plan gratuit. \nPour continuer à utiliser le service, veuillez choisir un plan payant.", "you-dont-have-permission-to-access-the-integrations-page": "Vous n'avez pas la permission d'accéder à la page des intégrations.", "your-account-has-been-successfully-upgraded": "Votre compte a été mis à niveau avec succès.", "your-jira-instance-domain": "Votre domaine d'instance Jira", "ai-powered-interviewer": "Interviewer <PERSON><PERSON>", "choose-role-level-and-tech-stack": "Choisissez le rôle, le niveau et la pile technologique", "create-an-interview": "<PERSON><PERSON><PERSON> une interview", "customizable": "Personnalisable", "get-immediate-insights-on-your-performance": "Obtenez des informations immédiates sur vos performances", "instant-feedback-0": "Rétroaction instantanée", "monitor-your-improvement-over-time": "Surveillez votre amélioration au fil du temps", "practice-your-interview-skills-with-our-ai-interviewer-get-real-time-feedback-and-improve-your-chances-of-landing-your-dream-job": "Simplifiez votre processus de recrutement. Automatisez le tri des candidats et identifiez rapidement le profil idéal plus vite, plus intelligemment et avec une meilleure précision.", "realistic-experience": "Expérience réaliste", "simulates-real-interview-scenarios": "Simule de vrais scénarios d'interview", "track-progress": "<PERSON>g<PERSON><PERSON> de la voie", "view-interviews-templates": "Voir les modèles d'entretiens", "you-dont-have-permission-to-access-the-interviews-page": "Vous n'avez pas la permission d'accéder à la page des entretiens.", "active": "Actif", "active-teams": "équipes actives", "active-users": "utilisateurs actifs", "ai-interview": "Entretien de l'IA", "apr": "Avr", "avg-users-team": "Moy. Utilisateurs / équipe", "conv-rate": "% Conv. \ntaux", "feb": "Fév", "form-builder": "Créateur de formulaires", "forms": "formulaires", "inactive": "Inactif", "interviews": "Interviews", "jan": "Jan", "jun": "Juin", "key": "%", "mar": "Mar", "may": "Peut", "of-teams-have-members": "des équipes ont des membres", "team-activity-rate": "Taux d'activité de l'équipe", "team-status": "Statut d'équipe", "total-teams": "Total des équipes", "total-users": "Total utilisateurs", "user-growth": "Croissance d'utilisateurs", "users": "Utilisateurs", "users-per-team": "utilisateurs par équipe", "ai-email-template-generator": "Générateur de modèle de messagerie AI", "copy-prompt": "Co<PERSON>r l'invite", "copy-the-json-response": "Copiez la réponse JSON", "copy-the-prompt-above": "<PERSON><PERSON>z l'invite ci-dessus", "describe-the-email-you-want-to-create-for-example-create-a-welcome-email-for-new-customers-of-my-fitness-company-fitlife-with-a-blue-color-scheme": "Décrivez l'e-mail que vous souhaitez créer. \nPar exemple: «Créez un e-mail de bienvenue pour les nouveaux clients de ma société de fitness Fitlife, avec une palette de couleurs bleues.", "describe-the-email-you-want-to-create-then-use-the-generated-prompt-with-your-preferred-ai-model": "Décrivez l'e-mail que vous souhaitez créer, puis utilisez l'invite générée avec votre modèle d'IA préféré.", "describe-the-tone-formal-casual-exciting": "<PERSON><PERSON><PERSON><PERSON><PERSON> le ton (formel, décontracté, excitant)", "description-tips": "Conseils de description:", "edit-description": "Modifier la description", "email-description": "Description par e-mail", "empty-description": "Description vide", "generate-ai-prompt": "<PERSON><PERSON><PERSON><PERSON> une invite AI", "generated-prompt": "<PERSON><PERSON><PERSON>", "how-to-use-this-prompt": "Comment utiliser cette invite:", "include-specific-sections-you-want-in-the-email": "Inclure des sections spécifiques que vous souhaitez dans l'e-mail", "include-your-company-or-brand-name": "Incluez votre entreprise ou votre marque", "mention-any-color-preferences": "Mentionner toutes les préférences de couleur", "mention-any-specific-images-or-buttons-you-want": "Mentionner les images ou les boutons spécifiques que vous souhaitez", "paste-it-to-chatgpt-claude-or-your-preferred-ai-model": "Collez<PERSON><PERSON> sur chatppt, claude ou votre modèle d'IA préféré", "please-enter-a-description-of-the-email-you-want-to-create": "V<PERSON><PERSON>z saisir une description de l'e-mail que vous souhaitez créer.", "prompt-copied": "Copié rapide!", "specify-the-type-of-email-welcome-newsletter-promotion-etc": "Spécifiez le type d'e-mail (bienvenue, newsletter, promotion, etc.)", "the-ai-will-generate-a-json-template-based-on-your-description": "L'IA générera un modèle JSON en fonction de votre description", "use-the-import-json-feature-in-our-email-builder-to-load-your-template": "Utilisez la fonction \"Importer JSON\" dans notre générateur de messagerie pour charger votre modèle", "you-can-now-paste-this-prompt-to-your-preferred-ai-model": "Vous pouvez d<PERSON>ormais coller cette invite à votre modèle d'IA préféré.", "an-unknown-error-occurred": "Une erreur inconnue s'est produite", "button": "Bouton", "components": "Composants", "describe-the-type-of-email-you-want-to-create-and-our-ai-will-generate-a-template-for-you": "Décrivez le type d'e-mail que vous souhaitez créer, et notre IA générera un modèle pour vous.", "description-enhanced": "Description Enhanced!", "divider": "Diviseur", "drag-components-to-the-canvas-or-click-to-add-them-to-the-bottom-of-your-email": "Faites glisser les composants vers la toile ou cliquez pour les ajouter au bas de votre e-mail.", "edit-template": "Modifier le modèle", "empty-prompt": "Invite vide", "enhance-description": "Améliorer la description", "enhanced-description": "Description améliorée", "enhancement-failed": "L'amélioration a échoué", "enhancing": "Amélioration ...", "error": "<PERSON><PERSON><PERSON>", "footer": "Pied de page", "generate-template": "Générer un modèle", "generating": "Génération ...", "generation-failed": "Échec de la génération", "header": "<PERSON>-tête", "image": "Image", "instructions": "Instructions", "list": "Liste", "mention-if-you-want-special-elements-like-social-icons": "Mentionnez si vous voulez des éléments spéciaux comme les icônes sociales", "please-enter-a-description-to-enhance": "<PERSON><PERSON><PERSON>z saisir une description à améliorer.", "please-enter-a-prompt-to-generate-a-template": "<PERSON><PERSON><PERSON><PERSON> saisir une invite pour générer un modèle.", "preview": "Prévisualisation", "prompt": "Rapide", "prompt-tips": "Conseils rapides:", "regenerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "social-icons": "Icônes sociales", "table": "<PERSON><PERSON>", "template-generated": "Mod<PERSON>le généré!", "text-block": "Bloc de texte", "there-was-an-error-enhancing-your-description-please-try-again": "Il y a eu une erreur améliorant votre description. \nVeuillez réessayer.", "there-was-an-error-generating-your-template-please-try-again": "Il y avait une erreur générant votre modèle. \nVeuillez réessayer.", "use-template": "Utiliser le modèle", "use-this": "<PERSON><PERSON><PERSON><PERSON>i", "your-description-has-been-enhanced-with-additional-details": "Votre description a été améliorée avec des détails supplémentaires.", "your-email-template-has-been-created-successfully": "Votre modèle de messagerie a été créé avec succès.", "ai-powered-form-builder": "Formulaire de formulaire alimenté par AI", "an-error-occurred-while-generating-the-form-please-try-again": "Une erreur s'est produite lors de la génération du formulaire. \nVeuillez réessayer.", "create-custom-forms-with-intelligent-field-suggestions-based-on-your-business-needs": "Créez des formulaires personnalisés avec des suggestions de champ intelligentes en fonction des besoins de votre entreprise", "describe-any-conditional-logic-e-g-show-extra-fields-based-on-selection": "Décrivez toute logique conditionnelle (par exemple, montrez des champs supplémentaires en fonction de la sélection).", "describe-validation-rules-e-g-required-fields-email-format-character-limits": "Décrire les règles de validation (par exemple, champs requis, format de messagerie, limites de caractère).", "describe-your-form": "Décrivez votre formulaire", "describe-your-form-purpose-e-g-job-application-form-for-a-marketing-position-event-registration-form-for-a-conference": "Décrivez votre objectif de formulaire (par exemple, «formulaire de demande d'emploi pour un poste de marketing», «formulaire d'inscription à l'événement pour une conférence»)", "enhance-description-0": "Améliorer la description", "enhanced-description-0": "Description améliorée:", "enhanced-description-applied": "Description améliorée appliquée!", "failed-to-enhance-description-please-try-again": "Échec de l'amélioration de la description. \nVeuillez réessayer.", "generate-form": "Générer une forme", "generating-form": "Formulaire de génération ...", "include-your-company-or-brand-name-for-customization": "Incluez votre entreprise ou votre marque pour la personnalisation.", "indicate-if-you-want-branding-elements-like-logos-and-theme-colors": "Indiquez si vous voulez des éléments de marque comme les logos et les couleurs du thème.", "mention-if-you-need-file-uploads-e-g-resumes-images-documents": "Mentionnez si vous avez besoin de téléchargements de fichiers (par exemple, curriculum vitae, images, documents).", "mention-required-fields-e-g-name-email-phone-number": "Mentionner les champs requis (par exemple, nom, e-mail, numéro de téléphone).", "please-describe-your-form-purpose": "Veuillez décrire votre objectif de formulaire", "please-enter-a-form-description-first": "Veuillez d'abord saisir une description de formulaire", "specify-form-actions-e-g-email-responses-save-to-database-integrate-with-apis": "Spécifiez les actions de formulaire (par exemple, les réponses par e-mail, Enregistrer dans la base de données, intégrer aux API).", "specify-if-you-need-auto-confirmation-emails-or-notifications": "Spécifiez si vous avez besoin d'e-mails ou de notifications de confirmation automatique.", "specify-the-purpose-of-the-form-job-application-event-registration-feedback-etc": "Spécifiez l'objectif du formulaire (demande d'emploi, enregistrement des événements, commentaires, etc.).", "tell-us-what-kind-of-form-you-need-and-our-ai-will-generate-it-for-you": "Dites-nous de quel type de formulaire vous avez besoin, et notre IA le générera pour vous", "use-this-description": "Utilisez cette description", "view-your-forms-dashboard": "Affichez vos formulaires Tableau de bord", "anyone-with-this-link-can-view-and-submit-the-form": "Toute personne ayant ce lien peut afficher et soumettre le formulaire", "api-integration": "Intégration API", "api-key-optional": "Clé API (facultatif)", "brand-colors": "Couleurs de marque", "branding": "Marque", "color-preview": "Aperçu des couleurs", "connect-your-form-to-external-services": "Connectez votre formulaire aux services externes", "customize-the-colors-to-match-your-brand-identity": "Personnalisez les couleurs pour correspondre à l'identité de votre marque", "embed-code": "Code de code", "export": "Exporter", "export-form-as-document": "Formulaire d'exportation comme document", "export-form-configuration": "Configuration du formulaire d'exportation", "export-form-responses": "Réponses de formulaire d'exportation", "export-options": "Options d'exportation", "export-your-form-in-different-formats": "Exportez votre formulaire dans différents formats", "form-submissions-will-be-sent-to-this-url": "Les soumissions de formulaires seront envoyées à cette URL", "logo": "Logo", "logo-preview": "Aperçu du logo", "no-logo-uploaded": "Aucun logo téléchargé", "primary-button": "Bouton primaire", "primary-color": "Couleur primaire", "public-form-url": "URL de forme publique", "save-integration-settings": "Enregistrer les paramètres d'intégration", "secondary-button": "Bouton secondaire", "share": "Partager", "share-your-form": "Partagez votre formulaire", "share-your-form-with-others-to-collect-responses": "Partagez votre formulaire avec les autres pour collecter les réponses", "upload-logo": "Télécharger le logo", "upload-your-company-logo-to-display-on-the-form": "Téléchargez le logo de votre entreprise à afficher sur le formulaire", "url-copied-to-clipboard": "URL copiée dans le presse-papiers!", "webhook-url": "URL de webhook", "checkbox": "<PERSON><PERSON>", "dropdown": "<PERSON><PERSON><PERSON><PERSON>", "email-field": "Champ de courrier électronique", "file-upload": "Téléchargement de fichiers", "radio-group": "Groupe de radio", "text-area": "Zone de texte", "text-field": "Champ de texte", "ai-agent": "Agent d'I<PERSON>", "chat": "Cha<PERSON>", "file-storage": "Stockage de fichiers", "files": "Fichiers", "integrations": "Intégrations", "new-chat": "Nouveau chat", "recent": "<PERSON><PERSON><PERSON>", "roles-and-permissions": "Rôles & Permissions", "search": "Recherche", "shared-with-me": "Partagé avec moi", "teams": "Équipes", "today": "<PERSON><PERSON><PERSON>'hui", "trash": "<PERSON><PERSON><PERSON>", "are-you-sure-you-want-to-delete-this-chat-session-this-action-cannot-be-undone": "Êtes-vous sûr de vouloir supprimer cette session de chat? \nCette action ne peut pas être annulée.", "chat-session-deleted-successfully": "Session de chat supprimée avec succès", "delete-chat-session": "Supprimer la session de chat", "delete-session": "Supprimer la session", "failed-to-delete-chat-session": "Échec de la suppression de la session de chat", "previous-30-days": "30 jours précédents", "previous-7-days": "7 jours précédents", "yesterday": "<PERSON>er", "all": "Tous", "configure-your-search-experience": "Configurez votre expérience de recherche", "error-loading-knowledge-bases": "Erreur de chargement des bases de connaissances:", "jira": "<PERSON><PERSON>", "knowledge-bases": "Bases de connaissances", "knowledge-bases-0": "Bases de connaissances", "loading-knowledge-bases": "Chargement des bases de connaissances ...", "search-settings": "Paramètres de recherche", "select-which-knowledge-bases-to-search-when-performing-searches": "Sélectionnez les bases de connaissances à rechercher lors des recherches", "web": "Web", "a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "Un utilisateur avec le rôle \"Manager\" dans l'équipe \"Engineering\" peut gérer les ressources d'ingénierie", "add-users-to-teams-they-dont-need-to-be-in": "Ajouter des utilisateurs aux équipes dans lesquelles ils n'ont pas besoin d'être", "adds-all-permissions-from-the-users-teams": "Ajoute toutes les autorisations des équipes de l'utilisateur", "adds-any-extra-permissions-granted-directly-to-the-user": "Ajoute toute autorisation supplémentaire accordée directement à l'utilisateur", "administrator-role": "Rôle d'administrateur", "always-grant-users-the-minimum-permissions-necessary-to-perform-their-job-functions-this-reduces-the-risk-of-accidental-or-intentional-misuse-of-privileges": "Accordez toujours aux utilisateurs les autorisations minimales nécessaires pour remplir leurs fonctions de travail. \nCela réduit le risque de mauvais utilisation accidentelle ou intentionnelle des privilèges.", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "À mesure que votre organisation se développe, les rôles facilitent l'intégration de nouveaux utilisateurs avec les niveaux d'accès appropriés sans avoir à configurer les autorisations individuellement.", "assign-administrator-roles-unnecessarily": "Attribuer des rôles administratifs inutilement", "assign-multiple-roles-when-one-would-suffice": "Attribuer plusieurs rôles quand on suffirait", "assign-roles-based-on-job-responsibilities": "Attribuer des rôles en fonction des responsabilités professionnelles", "assign-user-to-team": "Affecter l'utilisateur à l'équipe", "audit-checklist": "Liste de contrôle de l'audit", "auditability": "Auditabilité", "back-to-roles-and-permissions": "Retour aux rôles", "benefits-of-role-based-access": "Avantages de l'accès basé sur les rôles", "best-practices": "Meilleures pratiques", "caution-with-overrides": "Prudence avec les remplacements", "check-for-users-with-excessive-permissions": "Vérifiez les utilisateurs avec des autorisations excessives", "collects-all-permissions-from-the-users-roles": "Recueille toutes les autorisations des rôles de l'utilisateur", "combined-permissions": "Autorisations combinées", "conduct-regular-audits-of-your-permission-system-to-ensure-it-remains-effective-and-secure": "Effectuez des audits réguliers de votre système d'autorisation pour vous assurer qu'il reste efficace et sécurisé.", "considers-the-scope-of-each-permission-global-team-project-self": "Considère la portée de chaque autorisation (Global, Team, Project, Self)", "create-data-provider": "<PERSON><PERSON><PERSON> un fournisseur de données", "create-data-provider-with-team-scope": "C<PERSON>er un fournisseur de données avec la portée de l'équipe", "create-roles-with-excessive-permissions": "Créer des rôles avec des autorisations excessives", "create-teams-solely-for-permission-management": "Créer des équipes uniquement pour la gestion de l'autorisation", "create-teams-with-overlapping-responsibilities": "Créer des équipes avec des responsabilités qui se chevauchent", "create-user": "C<PERSON>er un utilisateur", "creating-more-specialized-teams-with-appropriate-permissions": "Créer des équipes plus spécialisées avec des autorisations appropriées", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Différentes équipes peuvent avoir des ensembles d'autorisation différents en fonction de leurs besoins", "do": "Faire", "document-any-exceptions-to-standard-security-policies": "Documenter toutes les exceptions aux politiques de sécurité standard", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Chaque rôle contient un ensemble d'autorisations avec des portées spécifiques.\nPar exemple :", "effective-permissions": "Autorisation efficace", "engineering-team": "<PERSON><PERSON><PERSON>", "example-scope-resolution": "Exemple: Rés<PERSON><PERSON> de la portée", "excessive-use-of-permission-overrides-can-make-your-security-model-difficult-to-understand-and-audit-if-you-find-yourself-frequently-using-overrides-consider": "L'utilisation excessive des remplacements d'autorisation peut rendre votre modèle de sécurité difficile à comprendre et à auditer. \nSi vous vous retrouvez fréquemment à utiliser des remplacements, consid<PERSON><PERSON>:", "export-data-with-project-scope": "Exporter des données avec la portée du projet", "extra-permissions": "Autorisation supplémentaire", "extra-permissions-0": "Autorisation supplémentaire", "global": "Mondial", "global-0": "Mondial", "global-scope": "Portée mondiale", "grant-global-permissions-to-teams-unnecessarily": "Accorder des autorisations mondiales aux équipes inutilement", "grant-when-a-user-needs-a-specific-permission-that-isnt-included-in-their-roles-or-teams-but-doesnt-warrant-a-role-change": "Accorder lorsqu'un utilisateur a besoin d'une autorisation spécifique qui n'est pas incluse dans ses rôles ou ses équipes, mais ne justifie pas un changement de rôle.", "guidelines-for-implementing-effective-access-control": "Lignes directrices pour la mise en œuvre du contrôle d'accès efficace", "how-roles-provide-a-foundation-for-permissions-in-the-system": "Comment les rôles fournissent une base pour les autorisations dans le système", "how-teams-provide-contextual-access-to-resources": "Comment les équipes offrent un accès contextuel aux ressources", "identify-and-review-permission-overrides": "Identifier et revoir les remplacements de l'autorisation", "if-a-user-has-the-view-analytics-permission-from": "Si un utilisateur a l'autorisation de \"View Analytics\" de:", "implementing-a-more-granular-permission-model": "Implémentation d'un modèle d'autorisation plus granulaire", "important-note": "Note importante", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "Les utilisateurs individuels peuvent bénéficier d'autorisations supplémentaires ou les autorisations héritées révoquées pour gérer les exceptions.", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "Au lieu d'attribuer des autorisations individuelles à chaque utilisateur, vous pouvez attribuer des rôles contenant des ensembles prédéfinis d'autorisations, ce qui rend la gestion des utilisateurs plus efficace.", "jane-smith": "<PERSON>", "john-doe": "<PERSON>", "key-0": "→", "leave-unused-roles-assigned-to-users": "Laissez des rôles inutilisés affectés aux utilisateurs", "limit-team-permissions-to-necessary-resources": "Limitez les autorisations d'équipe aux ressources nécessaires", "manage-billing": "<PERSON><PERSON><PERSON> la facturation", "manage-profile-with-self-scope": "<PERSON><PERSON><PERSON> le profil avec l'auto-portée", "manager-role": "Rôle de gestionnaire", "multi-layered-permission-system": "Système d'autorisation multicouche", "organize-teams-based-on-functional-areas": "Organisez des équipes en fonction des domaines fonctionnels", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Notre système de sécurité utilise une approche multicouche des autorisations, permettant un contrôle d'accès à grain fin tout en maintenant la flexibilité et la facilité de gestion.", "overview-0": "<PERSON><PERSON><PERSON><PERSON>", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "L'autorisation s'applique à l'ensemble du système, à toutes les ressources du type pertinent.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "L'autorisation s'applique uniquement aux ressources créées par ou directement attribuées à l'utilisateur.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "L'autorisation s'applique uniquement aux ressources détenues ou associées aux équipes de l'utilisateur.", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "L'autorisation ne s'applique qu'aux projets spécifiques, quelle que soit la propriété de l'équipe.", "permission-inheritance-flow": "Flux d'héritage d'autorisation", "permission-model-overview": "Présentation du modèle d'autorisation", "permission-overrides": "Overrités d'autorisation", "permission-resolution": "Résolution d'autorisation", "permission-scopes": "Écarts de l'autorisation", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Les portées d'autorisation définissent les limites dans lesquelles une autorisation s'applique. \nIls permettent un contrôle à grains fins sur les ressources d'un utilisateur accéder avec une autorisation donnée.", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Les autorisations ne s'appliquent qu'aux ressources appartenant à l'équipe. \nC'est la portée la plus courante pour les autorisations d'équipe.", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Autorisations accordées à tous les membres de l'équipe, généralement portée aux ressources de l'équipe", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Les autorisations peuvent être limitées à des projets spécifiques sur lesquels l'équipe travaille, même si ces projets sont partagés avec d'autres équipes.", "principle-of-least-privilege": "Principe du moindre privilège", "project-scope": "Portée du projet", "regular-security-audits": "Audits de sécurité réguliers", "regularly-review-and-audit-role-assignments": "Examiner et audit régulièrement les affectations de rôle", "regularly-review-team-memberships": "Revoir régulièrement les adhésions à l'équipe", "remove-permissions-for-inactive-users": "Supprimer les autorisations pour les utilisateurs inactifs", "remove-unnecessary-roles-when-job-functions-change": "Supprimer les rôles inutiles lorsque les fonctions du travail changent", "removes-any-permissions-explicitly-revoked-for-the-user": "Supprime toutes les autorisations révoquées explicitement pour l'utilisateur", "resource-scope": "Portée des ressources", "review-user-role-assignments-for-appropriateness": "Passez en revue les affectations de rôle des utilisateurs pour la pertinence", "reviewing-and-adjusting-your-role-definitions": "Examiner et ajuster les définitions de vos rôles", "revoked-permissions": "Autorisations révoquées", "revoked-permissions-0": "Autorisations révoquées", "robert-johnson": "<PERSON>", "role-assignment": "Affectation de rôle", "role-based-access-control": "Contrôle d'accès basé sur les rôles", "role-permissions": "Autorisation de rôle", "role-structure": "Structure de rôle", "roles": "<PERSON><PERSON><PERSON>", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "Les rôles sont définis au niveau du système et ne peuvent pas être créés ou modifiés par des utilisateurs réguliers. \nContactez votre administrateur système si vous avez besoin d'un nouveau rôle ou de modifications dans les rôles existants.", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "Les rôles sont des ensembles prédéfinis d'autorisations qui représentent des fonctions d'emploi ou des niveaux de responsabilité communs au sein de votre organisation. \nChaque utilisateur peut se voir attribuer un ou plusieurs rôles.", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "Les rôles garantissent que les utilisateurs ayant des responsabilités similaires ont des droits d'accès cohérents, ce qui réduit le risque d'incohérences d'autorisation.", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "Les rôles fournissent une structure claire pour les droits d'accès, ce qui facilite la vérification qui a accès à quoi et pourquoi ils ont cet accès.", "scalability": "Évolutivité", "scope-examples": "Exemples de portée", "scope-hierarchy-broadest-to-narrowest": "Hiérarchie de portée (la plus large à la plus étroite)", "scope-resolution": "Résolution de portée", "scope-types": "Types de portée", "scopes": "Portées", "security-best-practices": "Meilleures pratiques de sécurité", "security-system": "Système de sécurité", "simplified-management": "Gestion simplifiée", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Certaines autorisations d'équipe peuvent avoir une portée mondiale, permettant aux membres de l'équipe d'effectuer des actions sur l'ensemble du système.", "standardization": "Standardisation", "team": "Équipe", "team-0": "Équipe", "team-1": "Équipe", "team-based-permissions": "Autorisation d'équipe", "team-management": "Gestion de l'équipe", "team-members": "Membres de l'équipe", "team-members-0": "Membres de l'équipe:", "team-permission-scopes": "Élèves de l'autorisation de l'équipe", "team-permissions": "Autorisation d'équipe", "team-permissions-0": "Autorisation d'équipe", "team-permissions-1": "Autorisations d'équipe:", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Les autorisations d'équipe sont généralement portée aux ressources de l'équipe, mais elles peuvent également avoir différents types de portée:", "team-scope": "Portée de l'équipe", "team-structure": "Structure d'équipe", "team-vs-role-permissions": "Autorisations d'équipe vs rôle", "teams-and-permissions": "Équipes et autorisations", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "Les équipes (ou groupes) vous permettent d'organiser des utilisateurs et d'accorder des autorisations en fonction des ressources dont ils ont besoin pour accéder. \nLes équipes sont particulièrement utiles pour le contrôle d'accès départemental ou basé sur des projets.", "the-effective-scope-will-be-global-as-its-broader-than-team": "La portée efficace sera mondiale, car elle est plus large que l'équipe.", "the-leadership-team-with-global-scope": "L'équipe \"Leadership\" avec une portée mondiale", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "Le même utilisateur de l'équipe \"marketing\" peut gérer les ressources marketing", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "Les ressources spécifiques (projets, données, etc.) auxquelles l'équipe a accès à", "their-manager-role-with-team-scope": "Leur rôle de \"manager\" avec l'équipe de l'équipe", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Comprendre comment les portées d'autorisation limitent l'étendue des autorisations", "understanding-how-permissions-work-in-our-enterprise-system": "Comprendre comment les autorisations fonctionnent dans notre système d'entreprise", "understanding-the-permission-model-and-access-control": "Comprendre le modèle d'autorisation et le contrôle d'accès.", "use-permission-overrides-sparingly-and-only-when-necessary-they-should-be-the-exception-not-the-rule": "Utiliser l'autorisation de remplacement parcimonie et uniquement lorsque cela est nécessaire. \nIls devraient être l'exception, pas la règle.", "use-team-scoped-permissions-when-possible": "Utilisez des autorisations à l'équipe lorsque cela est possible", "use-the-most-restrictive-role-that-meets-the-users-needs": "Utilisez le rôle le plus restrictif qui répond aux besoins de l'utilisateur", "use-when-a-user-has-a-role-or-team-membership-that-grants-permissions-they-shouldnt-have-but-they-still-need-the-role-or-team-for-other-permissions": "Utilisez lorsqu'un utilisateur a un rôle ou une adhésion à l'équipe qui accorde les autorisations qu'ils ne devraient pas avoir, mais ils ont toujours besoin du rôle ou de l'équipe pour d'autres autorisations.", "user-can-create-data-providers-only-for-their-team-s": "L'utilisateur ne peut créer des fournisseurs de données uniquement pour leurs équipes.", "user-can-export-data-only-from-specific-projects-they-have-access-to": "L'utilisateur ne peut exporter des données que à partir de projets spécifiques auxquels ils ont accès.", "user-can-only-manage-their-own-profile-information": "L'utilisateur ne peut gérer que ses propres informations de profil.", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "L'utilisateur peut afficher l'analyse pour toutes les équipes et projets du système.", "user-overrides": "Remplacez les utilisateurs", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "Les utilisateurs reçoivent des rôles qui accordent un ensemble d'autorisations. \nLes rôles fournissent une base d'accès appropriée pour différentes fonctions de travail.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Les utilisateurs appartiennent à des équipes qui accordent des autorisations supplémentaires. \nLes autorisations d'équipe sont généralement portée aux ressources de l'équipe.", "users-who-belong-to-the-team-and-inherit-its-permissions": "Les utilisateurs qui appartiennent à l'équipe et héritent de ses autorisations", "verify-team-memberships-and-permissions": "Vérifiez les abonnements et autorisations d'équipe", "view-analytics": "Voir l'analyse", "view-analytics-0": "Voir l'analyse", "view-analytics-with-global-scope": "Voir l'analyse avec une portée mondiale", "what-are-permission-scopes": "Que sont les portées d'autorisation ?", "what-are-roles": "Que sont les rôles?", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "Lorsqu’un utilisateur possède la même autorisation avec différentes portées (par exemple, provenant de différents rôles ou équipes), le système utilise la portée la plus large :", "when-determining-if-a-user-has-a-specific-permission-the-system": "Lors de la détermination du si un utilisateur a une autorisation spécifique, le système:", "when-to-use-permission-overrides": "Quand utiliser des remplacements d'autorisation", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "Bien que les rôles définissent ce qu'un utilisateur peut faire en fonction de sa fonction de travail, les équipes définissent les ressources qu'il peut accéder. \nCette combinaison fournit un système de contrôle d'accès flexible et puissant:", "authentication-required": "Authentification requise", "back-to-team-details": "Retour aux détails de l'équipe", "description": "Description", "edit-team": "Éditer l'équipe", "error-loading-data": "Erreur de chargement des données", "form-errors": "Erreurs de formation:", "has-errors": "A des erreurs:", "loading-team": "Équipe de chargement ...", "permission-denied-0": "Permission refusée", "permissions-assigned-to-this-team-will-be-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Les autorisations attribuées à cette équipe seront accordées à tous les membres de l'équipe, sauf si les utilisateurs spécifiques.", "please-fill-in-the-following-fields": "Veuillez remplir les champs suivants:", "team-description-is-required": "La description de l'équipe est requise.", "team-details": "Détails de l'équipe", "team-name": "Nom de l'équipe", "team-name-is-required": "Le nom de l'équipe est requis.", "team-permissions-2": "Autorisation d'équipe", "team-updated": "É<PERSON>pe mise à jour", "the-team-already-exists": "L'équipe existe déjà", "there-was-an-error-loading-the-team-data": "Il y a eu une erreur de chargement des données de l'équipe.", "there-was-an-error-updating-the-team-information": "Il y a eu une erreur à jour les informations de l'équipe.", "update-failed": "La mise à jour a échoué", "update-team-information-and-permissions": "Met<PERSON>z à jour les informations et les autorisations de l'équipe.", "you-dont-have-permission-to-edit-teams": "Vous n'avez pas la permission de modifier les équipes.", "activity-log-coming-soon": "Journal d'activités à venir bientôt", "back-to-teams": "Retour aux équipes", "detailed-information-about-this-team": "Des informations détaillées sur cette équipe", "edit-permissions": "Modifier les autorisations", "edit-team-0": "Éditer l'équipe", "last-updated": "Dernière mise à jour:", "loading-team-details": "Détails de l'équipe de chargement ...", "manage-members": "<PERSON><PERSON><PERSON> les membres", "no-description-provided": "Aucune description fournie", "no-permissions-assigned": "Aucune autorisation attribuée", "permissions": "Autorisation", "permissions-assigned": "autorisation attribuée", "permissions-assigned-to-this-team-are-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Les autorisations attribuées à cette équipe sont accordées à tous les membres de l'équipe, sauf si les utilisateurs spécifiques.", "recent-activity": "Activité récente", "team-activity-tracking-will-be-available-in-a-future-update": "Le suivi des activités de l'équipe sera disponible dans une future mise à jour.", "team-details-0": "Détails de l'équipe", "team-information": "Informations sur l'équipe", "team-information-and-statistics": "Informations et statistiques de l'équipe", "team-members-1": "membres de l'équipe", "team-permissions-3": "Autorisation d'équipe", "this-team-doesnt-have-any-permissions-assigned-yet": "Cette équipe n'a pas encore d'autorisations attribuées.", "view-integrations": "Afficher les intégrations", "view-team-members": "Voir les membres de l'équipe", "you-dont-have-permission-to-view-team-details": "Vous n'avez pas la permission de voir les détails de l'équipe.", "active-0": "Actif", "assign-integrations": "Attribuer des intégrations", "configuration-details-not-available": "Détails de configuration non disponibles", "error-loading-integrations": "Erreur de chargement des intégrations", "failed": "<PERSON><PERSON><PERSON>", "failed-to-load-team-integrations": "Échec du chargement des intégrations d'équipe", "manage-integrations": "<PERSON><PERSON><PERSON> les intégrations", "no-integrations-found": "Aucune intégration trouvée", "not-updated-0": "Non mis à jour", "team-integrations": "Intégrations d'équipe", "this-team-doesnt-have-any-integrations-assigned-yet": "Cette équipe n'a pas encore aucune intégration affectée", "updating": "Mise à jour", "you-dont-have-permission-to-view-team-integrations": "Vous n'avez pas la permission de visualiser les intégrations de l'équipe.", "first-page": "Première page", "last-page": "Dernière page", "next-page": "<PERSON> suivante", "previous-page": "<PERSON> p<PERSON>", "rows-per-page": "<PERSON><PERSON><PERSON> par page:", "showing": "Projection", "add-members": "Ajouter des membres", "more-options": "Plus d'options", "no-team-members-found": "Aucun membre de l'équipe trouvé", "remove-from-team": "Retirer de l'équipe", "this-team-has-no-members-or-none-match-your-search": "Cette équipe n'a pas de membres ou aucun ne correspond à votre recherche", "actions": "Actes", "name": "Nom", "no-members-found": "Aucun membre trouvé.", "remove": "<PERSON><PERSON><PERSON>", "add": "Ajouter", "add-members-0": "Ajouter des membres", "add-team-members": "Ajouter les membres de l'équipe", "are-you-sure-you-want-to-remove": "Êtes-vous sûr de vouloir supprimer", "authentication-required-0": "Authentification requise", "back-to-team-details-0": "Retour aux détails de l'équipe", "error-adding-members": "<PERSON><PERSON><PERSON> ajoutant des membres", "error-loading-team-data": "Erreur de chargement des données de l'équipe", "error-removing-member": "Erreur de suppression du membre", "failed-to-add-team-members": "N'a pas réussi à ajouter des membres de l'équipe.", "failed-to-load-team-and-member-data": "Échec du chargement des données de l'équipe et des membres.", "failed-to-remove-team-member": "N'a pas réussi à supprimer le membre de l'équipe.", "loading-team-members": "Chargement des membres de l'équipe ...", "manage-members-of-this-team": "<PERSON><PERSON><PERSON> les membres de cette équipe.", "member": "Membre", "member-removed": "Membre supprimé", "members": "Me<PERSON><PERSON>", "members-added": "Les membres ont ajouté", "no-available-users-found": "Aucun utilisateur disponible trouvé.", "remove-team-member": "Supprimer le membre de l'équipe", "search-members": "Membres de la recherche ...", "search-users": "Rechercher les utilisateurs ...", "select-users-to-add-to-the": "Sélectionnez les utilisateurs à ajouter au", "team-not-found": "L'équipe n'est pas trouvée", "actions-0": "Actes", "assign-integration": "Affecter l'intégration", "cannot-delete-default-team": "Impossible de supprimer l'équipe par défaut", "create-team": "Créer une équipe", "details": "Détails", "edit-0": "Modifier", "members-0": "Me<PERSON><PERSON>", "no-description-provided-0": "Aucune description fournie", "no-permissions": "Aucune autorisation", "no-teams-found": "Aucune équipe trouvée", "no-teams-match-your-search-criteria-or-no-teams-have-been-created-yet": "Aucune équipe ne correspond à vos critères de recherche ou aucune équipe n'a encore été créée.", "open-menu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view-details": "Aff<PERSON>r les détails", "empty": "Vide", "manage-members-0": "<PERSON><PERSON><PERSON> les membres", "no-teams-found-0": "Aucune équipe trouvée.", "open-menu-0": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view-details-0": "Aff<PERSON>r les détails", "activate-user": "Activer l'utilisateur", "add-user": "Ajouter l'utilisateur", "all-roles": "To<PERSON> les rôles", "all-teams": "Toutes les équipes", "are-you-sure-you-want-to-delete": "Êtes-vous sûr de vouloir supprimer", "ban-user": "Interdire l'utilisateur", "delete-user": "Supprimer l'utilisateur", "error-deleting-user": "Erreur de suppression de l'utilisateur", "error-loading-users": "Erreur Chargement des utilisateurs", "error-updating-user-status": "Erreur mise à jour de l'état de l'utilisateur", "filter": "Filtre", "filter-by-role": "Filtre par rôle", "filter-by-team": "Filtre par équipe", "loading-users": "Chargement des utilisateurs ...", "manage-your-organizations-users": "Gérer les utilisateurs de votre organisation.", "permission-denied-1": "Permission refusée", "select-role": "Sélectionner un rôle", "select-team": "Sélectionner l'équipe", "this-action-cannot-be-undone-0": "? \nCette action ne peut pas être annulée.", "user-deleted": "Supprimé par l'utilisateur", "users-0": "Utilisateurs", "you-dont-have-permission-to-access-the-users-page": "Vous n'avez pas la permission d'accéder à la page des utilisateurs.", "active-data-connections": "Connexions de données actives", "all-integrations": "Toutes les intégrations", "completed": "Complété", "currently-processing": "Traitement actuellement", "data-integrations": "Intégrations de données", "failed-0": "<PERSON><PERSON><PERSON>", "failed-to-fetch-integration-metrics": "Échec de la récupération des mesures d'intégration", "loading-integration-metrics": "Chargement des métriques d'intégration ...", "manage-and-connect-your-enterprise-data-from-a-variety-of-sources-all-in-one-place": "<PERSON><PERSON><PERSON> et connectez vos données d'entreprise à partir d'une variété de sources - toutes en un seul endroit.", "needs-attention": "A besoin d'attention", "new-integration": "Nouvelle intégration", "successfully-processed": "Traité avec succès", "total-integrations": "Intégrations totales", "view-filter-and-manage-your-data-integrations": "<PERSON><PERSON><PERSON><PERSON>, filtrer et gérer vos intégrations de données", "all-sources": "Toutes les sources", "all-statuses": "Tous les statuts", "columns": "Colonnes", "failed-to-fetch-integrations": "Échec de la récupération des intégrations", "filter-by-name": "Filtre par nom ...", "filter-by-source": "Filtre par source", "filter-by-status": "Filtre par statut", "integration-s-total": "Intégration (s) total", "loading": "Chargement", "loading-integrations": "Chargement des intégrations ...", "no-integrations-found-0": "Aucune intégration trouvée.", "not-updated-1": "Non mis à jour", "refreshing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "source-type": "Type de source", "are-you-sure-you-want-to-delete-this-form": "Êtes-vous sûr de vouloir supprimer ce formulaire?", "create-new-form": "<PERSON><PERSON>er une nouvelle forme", "create-your-first-form": "Créez votre premier formulaire", "create-your-first-form-by-describing-what-you-need-and-our-ai-will-generate-it-for-you": "<PERSON><PERSON>ez votre premier formulaire en décrivant ce dont vous avez besoin, et notre IA le générera pour vous.", "no-description": "Aucune description", "no-forms-yet": "Pas encore de formulaires", "responses": "Réponses", "view": "Voir", "you-havent-created-any-forms-yet-use-the-form-builder-to-get-started": "Vous n'avez pas encore créé de formulaires. \nUtilisez le Form Builder pour commencer.", "your-forms-dashboard": "Vos formulaires Tableau de bord", "an-unexpected-error-occurred": "Une erreur inattendue s'est produite.", "are-you-sure-you-want-to-assign": "Êtes-vous sûr de vouloir attribuer", "assign-integrations-to": "Attribuer des intégrations à", "assign-to-team": "Attribuer à l'équipe", "assigning": "Attribution ...", "error-assigning-integration": "Erreur d'attribution d'intégration:", "failed-to-assign-integration": "Échec de l'intégration", "failed-to-load-data": "Échec du chargement des données", "integration-assigned": "Intégration attribuée", "integration-has-been-successfully-assigned-to-currentteam-name": "L'intégration a été affectée avec succès à {0}.", "no-integrations-available": "Aucune intégration disponible", "please-try-refreshing-the-page": "Veuillez essayer de raf<PERSON><PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server-0": "Serveur:", "syncing": "Synchronisation", "there-are-no-integrations-to-assign-at-this-time": "Il n'y a pas d'intégrations à affecter pour le moment", "this-will-give-all-team-members-access-to-this-integration": "? \nCela donnera à tous les membres de l'équipe l'accès à cette intégration.", "unknown": "Inconnu", "update-time": "Temps de mise à jour:", "you-dont-have-permission-to-assign-integrations-to-teams": "Vous n'avez pas la permission d'attribuer des intégrations aux équipes.", "english": "🇺🇸 <PERSON><PERSON><PERSON>", "error-verifying-account-status": "Erreur Vérification de l'état du compte", "francais": "🇫🇷 Français", "please-complete-onboarding-first": "Veuillez terminer en premier", "switch-language": "Commutation de la langue", "verifying-account-status": "Vérification de l'état du compte ...", "InterviewDialog": {"description": "Envoyez l'interview \"{role} {Level}\" aux candidats."}, "all-levels": "To<PERSON> les niveaux", "all-types": "Tous types", "behavioral": "Comportemental", "create-and-manage-interview-templates-to-send-to-candidates": "<PERSON><PERSON><PERSON> et gérer les modèles d'interview à envoyer aux candidats", "created-by-0": "Créé par:", "csv-file-processed-successfully-interviews-will-be-sent-to-the-candidates": "Fichier CSV traité avec succès. \nDes entretiens seront envoyés aux candidats.", "delete-error": "Supprimer l'erreur:", "error-while-fetching-the-templates": "Erreur tout en récupérant les modèles", "failed-to-delete-the-template-please-try-again": "Échec de la suppression du modèle. \nVeuillez réessayer.", "file-uploaded": "<PERSON><PERSON><PERSON>", "filter-templates": "<PERSON><PERSON><PERSON><PERSON> fi<PERSON>", "find-templates-by-role-type-or-level": "Trouver des modèles par rôle, type ou niveau", "interview-templates": "Mod<PERSON><PERSON> d'entrevue", "invitations-sent-successfully": "Les invitations ont été envoyées avec succès!", "junior": "Junior", "lead": "Plomb", "level": "Niveau", "loading-interviews": "Chargement des interviews ...", "mid-level": "De niveau intermédiaire", "missing-file": "<PERSON><PERSON><PERSON> man<PERSON>", "missing-template": "<PERSON><PERSON><PERSON><PERSON>", "mixed": "Mixte", "more-questions": "Plus de questions", "new-template": "Nouveau modèle", "no-candidates-added": "Aucun candidat n'a ajou<PERSON>.", "please-select-an-interview-template": "Veuillez sélectionner un modèle d'entrevue.", "please-upload-a-csv-file-with-email-addresses": "Veuillez télécharger un fichier CSV avec des adresses e-mail.", "search-by-role-or-tech": "Recherche par rôle ou technologie ...", "select-level": "Sélectionner le niveau", "select-type": "Sélectionner le type", "send": "Envoyer", "senior": "Senior", "server-error": "<PERSON><PERSON><PERSON> <PERSON>", "something-went-wrong-while-deleting-the-template": "<PERSON><PERSON><PERSON> chose s'est mal passé en supprimant le modèle.", "system-design": "Conception du système", "tech-stack": "Pile technologique:", "technical": "Technique", "template-deleted": "<PERSON><PERSON><PERSON><PERSON> supprimé", "there-was-an-error-processing-the-file-please-try-again": "Il y a eu une erreur de traitement du fichier. \nVeuillez réessayer.", "type": "Type", "upload-failed": "Le téléchargement a échoué", "add-candidate": "Ajouter un candidat", "all-statuses-0": "Tous les statuts", "browse-files": "Parcourir les fichiers", "candidate": "Candidat", "completed-0": "Complété", "completed-at": "<PERSON><PERSON><PERSON><PERSON>", "csv-file-should-have-2-columns-full-name-and-email": "Le fichier CSV doit avoir 2 colonnes: \"nom complet\" et \"e-mail\"", "csv-upload": "Téléchargement de CSV", "dialogDescription": "Envoyez l'interview \"{role} {Level}\" aux candidats.", "drag-and-drop-a-csv-file-or-click-to-browse": "Faites glisser et déposez un fichier CSV ou cliquez pour parcourir", "e-g-frontend-developer": "par exemple \nDéveloppeur de frontend", "e-g-react": "par exemple \nR<PERSON>agir", "edit-interview-template": "Modifier le modèle d'interview", "enter-a-question": "Entrez une question ...", "error-uploading-file": "<PERSON><PERSON>er de téléchargement d'erreur:", "filter-candidates": "Filtrer les candidats", "find-candidates-by-name-email-or-status": "Trouver les candidats par nom, e-mail ou statut", "full-name": "Nom et prénom", "in-progress": "En cours", "information-about-this-interview": "Informations sur cette interview", "interview-candidates": "Interviewer des candidats", "interview-details": "<PERSON><PERSON><PERSON> de l'interview", "interview-not-found": "Interview introuvable", "interview-type": "Type d'interview", "invite-candidates": "Inviter les candidats", "loading-candidates": "Chargement des candidats ...", "loading-template": "<PERSON><PERSON><PERSON><PERSON> de chargement ...", "manual-entry": "En<PERSON><PERSON> man<PERSON>le", "missing-information": "Informations manquantes", "no-candidates-found": "<PERSON><PERSON>n candidat trouvé", "no-questions-added-yet-add-some-questions-to-create-your-interview-template": "Aucune question ajou<PERSON>e encore. \nAjoutez quelques questions pour créer votre modèle d'interview.", "pending": "En attente", "please-fill-in-all-required-fields-before-saving": "<PERSON><PERSON><PERSON>z remplir tous les champs requis avant d'économiser.", "return-to-interviews": "Retour aux entretiens", "return-to-templates": "Retour aux modèles", "role-0": "R<PERSON><PERSON>", "save-template": "Enregistrer le modèle", "score": "Score", "search-0": "Recherche", "search-by-name-or-email": "Recherche par nom ou e-mail ...", "select-status": "Sélectionner l'état", "send-invitations": "Envoyer des invitations", "something-went-wrong-please-try-again": "<PERSON><PERSON><PERSON> chose s'est mal passé. \nVeuillez réessayer.", "status-0": "Statut", "status-1": "Statut", "tech-stack-0": "Pile technologique", "template-not-found": "<PERSON><PERSON><PERSON><PERSON> introuvable", "template-updated": "<PERSON><PERSON><PERSON><PERSON> mis à jour", "update-failed-0": "La mise à jour a échoué", "upload-and-send": "Télécharger et envoyer", "upload-csv-file": "Télécharger le fichier CSV", "upload-failed-0": "Le téléchargement a échoué", "we-couldnt-find-the-interview-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "Nous n'avons pas pu trouver l'interview que vous recherchez. \nIl a peut-être été supprimé ou n'existe pas.", "we-couldnt-find-the-template-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "Nous n'avons pas pu trouver le modèle que vous recherchez. \nIl a peut-être été supprimé ou n'existe pas.", "your-interview-template-has-been-updated-successfully": "Votre modèle d'interview a été mis à jour avec succès.", "c-2025-your-company-all-rights-reserved": "© 2025 Votre entreprise. \nTous droits réservés.", "click-me": "Cliquez sur moi", "copy": "<PERSON><PERSON>", "drag-components-here": "Faites glisser les composants ici", "error-loading-template": "Mod<PERSON><PERSON> de chargement d'erreur", "error-saving-template": "Modèle d'enregistrement d'erreur", "or-select-components-from-the-sidebar": "ou sélectionnez des composants dans la barre latérale", "pick-a-color": "<PERSON><PERSON> une couleur", "template-loaded": "<PERSON><PERSON><PERSON><PERSON> char<PERSON>", "template-name-has-been-loaded-successfully": "Le modèle \"{0}\" a été chargé avec succès.", "template-name-has-been-saved-successfully": "Le modèle \"{0}\" a été enregistré avec succès.", "template-saved": "<PERSON><PERSON><PERSON><PERSON>", "there-was-an-error-loading-your-template": "Il y avait une erreur de chargement de votre modèle.", "there-was-an-error-saving-your-template": "Il y a eu une erreur enregistrant votre modèle.", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "Ceci est un paragraphe de texte. \nVous pouvez modifier ce texte pour ajouter votre propre contenu.", "your-email-header": "<PERSON><PERSON><PERSON> en<PERSON>tê<PERSON> de messagerie", "addFieldsHeading": "Ajouter des champs", "aiFieldSuggestions": {"anErrorOccurred": "Une erreur s'est produite lors de la génération de suggestions. \nVeuillez réessayer.", "applySuggestionsButton": "Appliquer des suggestions", "describeFormPurposeError": "Veuillez décrire votre objectif de formulaire", "generateFieldsButton": "Générer des champs", "generateSuggestionsFailedError": "N'a pas réussi à générer des suggestions. \nVeuillez réessayer.", "generatingButton": "Générateur...", "suggestionsCardFieldsCount": "{count} champs suggé<PERSON>s", "suggestionsCardTitle": "{titre}", "textareaPlaceholder": "Décrivez votre objectif de formulaire (par exemple, «formulaire de demande d'emploi pour un poste de marketing», «formulaire d'inscription à l'événement pour une conférence»)"}, "close": "<PERSON><PERSON><PERSON>", "dashboardPreview": {"createdLabel": "Créé:", "deleteButtonTooltip": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirmation": "Êtes-vous sûr de vouloir supprimer ce formulaire?", "editButton": "Modifier", "fieldsCount": "{count} champs", "loadingForms": "Formulaires de chargement ...", "noDescription": "Aucune description", "noFormsCardDescription": "Vous n'avez pas encore créé de formulaires. \nUtilisez le générateur de formulaire ci-dessus pour commencer.", "noFormsCardTitle": "Vos formulaires", "responsesButton": "Réponses", "viewButton": "Voir", "yourFormsTitle": "Vos formulaires"}, "designTab": "Conception", "editorPage": {"backToGeneratorButton": "Retour au générateur", "loadingEditor": "Éditeur de chargement ...", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page du générateur de messagerie.", "permissionDeniedToastTitle": "Permission refusée"}, "email-preview": "Aperçu par e-mail", "email-preview-0": "Aperçu par e-mail", "emailBuilderPage": {"permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page du générateur de messagerie.", "permissionDeniedToastTitle": "Permission refusée", "viewTemplatesButton": "Afficher les modèles", "aiTemplateGenerator": {"aiProcessingToastDescription": "Votre modèle de messagerie est généré. \n<PERSON><PERSON> pourrait prendre un moment.", "aiProcessingToastTitle": "L'IA traite ...", "emailDescriptionLabel": "Description par e-mail (facultatif)", "emailDescriptionPlaceholder": "Fournissez plus de détails pour l'IA, par exemple, «un e-mail de bienvenue pour les nouvelles inscriptions, mettant en évidence nos meilleures fonctionnalités et un appel à l'action pour explorer les produits».", "emailSubjectLabel": "Sujet par e-mail", "emailSubjectPlaceholder": "Par exemple, «Bienvenue dans notre newsletter», «Annonce de lancement de nouveaux produits»", "enhanceDescriptionButton": "Améliorer la description avec l'IA", "errorEnhancingDescriptionToastDescription": "Il y a eu une erreur améliorant votre description. \nVeuillez réessayer.", "errorEnhancingDescriptionToastTitle": "Erreur améliorant la description", "errorGeneratingTemplateToastDescription": "Il y avait une erreur générant votre modèle de messagerie. \nVeuillez réessayer.", "errorGeneratingTemplateToastTitle": "Modèle de génération d'erreur", "generateTemplateButton": "Générer un modèle", "generatingTemplateButton": "Modèle de génération ...", "heading": "Générateur de modèle de messagerie AI", "noSubjectError": "Veuillez fournir un sujet par e-mail.", "subheading": "G<PERSON><PERSON>rez de beaux modèles de messagerie avec l'IA en quelques secondes."}, "c-2025-your-company-all-rights-reserved": "© 2025 Votre entreprise. \nTous droits réservés.", "click-me": "Cliquez sur moi", "email-image-alt-text": "Image par e-mail", "email-template-title": "<PERSON><PERSON><PERSON><PERSON>", "error-loading-template": "Mod<PERSON><PERSON> de chargement d'erreur", "error-saving-template": "Modèle d'enregistrement d'erreur", "template-loaded": "<PERSON><PERSON><PERSON><PERSON> char<PERSON>", "template-name-has-been-loaded-successfully": "Le modèle \"{name}\" a été chargé avec succès.", "template-name-has-been-saved-successfully": "Le modèle \"{name}\" a été enregistré avec succès.", "template-saved": "<PERSON><PERSON><PERSON><PERSON>", "there-was-an-error-loading-your-template": "Il y avait une erreur de chargement de votre modèle.", "there-was-an-error-saving-your-template": "Il y a eu une erreur enregistrant votre modèle.", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "Ceci est un paragraphe de texte. \nVous pouvez modifier ce texte pour ajouter votre propre contenu.", "your-email-header": "<PERSON><PERSON><PERSON> en<PERSON>tê<PERSON> de messagerie"}, "exportCsvFutureUpdate": "L'exportation vers CSV sera implémentée dans une future mise à jour.", "exportPdfFutureUpdate": "L'exportation vers PDF sera implémentée dans une future mise à jour.", "formSavedSuccess": "Formulaire Sauvé avec succès!", "no-components-added-yet": "Aucun composant ajou<PERSON> encore", "preview-and-send": "Prévisualisation", "preview-how-your-email-will-look-to-recipients": "Aperçu à quoi ressemblera votre e-mail aux destinataires", "previewPage": {"editTemplateButton": "Modifier le modèle", "editToGenerateHtml": "Modifier pour générer du HTML", "errorTitle": "<PERSON><PERSON><PERSON>", "failedToLoadTemplate": "Échec du modèle de chargement. \nVeuillez réessayer plus tard.", "goBackToTemplates": "Revenez aux modèles", "loadingEditor": "Éditeur de chargement ...", "noHtmlPreview": "Aucun aperçu HTML disponible pour ce modèle.", "templateNotFoundDescription": "Le modèle que vous recherchez n'existe pas ou a été supprimé.", "templateNotFoundTitle": "<PERSON><PERSON><PERSON><PERSON> introuvable"}, "previewTab": "Prévisualisation", "saveButton": "<PERSON><PERSON><PERSON><PERSON>", "sendEmailDialog": {"cancelButton": "Annuler", "description": "Envoyez votre modèle de messagerie vers un destinataire. \nRemplissez les détails ci-dessous.", "emailContentError": "Le contenu par e-mail n'a pas pu être généré", "fromEmailLabel": "De l'e-mail", "fromEmailPlaceholder": "<EMAIL>", "fromNameLabel": "De nom", "fromNamePlaceholder": "Votre nom ou votre entreprise ou e-mail", "htmlGenerationError": "Erreur générant un e-mail HTML. \nVeuillez réessayer.", "recipientSubjectRequiredError": "Le courrier électronique et le sujet du destinataire sont requis", "sendButton": "Envoyer un e-mail", "sendFailToastDescription": "Il y a eu une erreur envoyant votre e-mail. \nVeuillez réessayer.", "sendFailToastTitle": "Échec de l'envoi par e-mail", "sendSuccessToastDescription": "Un e-mail a été envoyé à {destinatairemail}", "sendSuccessToastTitle": "E-mail envoy<PERSON> ave<PERSON> succès", "sendingButton": "Envoi...", "subjectLabel": "Sujet", "subjectPlaceholder": "Sujet par e-mail", "title": "Envoyer un e-mail", "toLabel": "À", "toPlaceholder": "ré***********************"}, "settingsTab": "Paramètres", "templatesPage": {"cancelButton": "Annuler", "componentsCount": "{count} Composants", "createFirstTemplateButton": "<PERSON><PERSON>ez votre premier modèle", "createNewTemplateButton": "<PERSON><PERSON>er un nouveau modèle", "deleteAlertDialogDescription": "Cette action ne peut pas être annulée. \nCela supprimera en permanence le modèle et le supprimera de nos serveurs.", "deleteAlertDialogTitle": "Es-tu sûr?", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "deleteErrorToastDescription": "Échec de la suppression du modèle. \nVeuillez réessayer.", "deleteMenuItem": "<PERSON><PERSON><PERSON><PERSON>", "duplicateMenuItem": "Double", "editMenuItem": "Modifier", "editTooltip": "Modifier ce modèle", "emailTemplatesTitle": "<PERSON><PERSON><PERSON><PERSON>rie", "errorToastTitle": "<PERSON><PERSON><PERSON>", "failedToLoadTemplates": "Échec du chargement des modèles. \nVeuillez réessayer plus tard.", "noDescription": "Aucune description", "noTemplatesFoundDescription": "Vous n'avez pas encore créé de modèles de messagerie.", "noTemplatesFoundTitle": "<PERSON><PERSON><PERSON> mod<PERSON>le trouvé", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page du générateur de messagerie.", "permissionDeniedToastTitle": "Permission refusée", "previewMenuItem": "Prévisualisation", "previewTooltip": "Aperçu de ce modèle", "templateDeletedToastDescription": "Le modèle a été supprimé avec succès.", "templateDeletedToastTitle": "<PERSON><PERSON><PERSON><PERSON> supprimé"}, "toolbar": {"invalidJsonToastDescription": "Veuillez fournir un tableau JSON valide de composants.", "invalidJsonToastTitle": "JSON non valide", "loadButton": "Charger", "loadTemplateButton": "<PERSON><PERSON><PERSON><PERSON> de chargement", "loadTemplateDialogTitle": "<PERSON><PERSON><PERSON><PERSON> de chargement", "mobileView": "Vue mobile", "pasteJsonDescription": "Collez le tableau JSON généré par un modèle d'IA en fonction de votre invite.", "pasteJsonLabel": "<PERSON><PERSON> le modèle JSON", "pasteJsonPlaceholder": "[{\"id\": \"1\", \"type\": \"en-tête\", \"contenu\": \"bienvenue\", \"paramètres\": {...}}]", "previewMode": "Mode d'aperçu", "redoButton": "<PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON><PERSON><PERSON>", "saveTemplateButton": "Enregistrer le modèle", "saveTemplateDialogTitle": "Enregistrer le modèle", "selectTemplateLabel": "Sélectionner le modèle", "selectTemplatePlaceholder": "Sélectionnez un modèle", "tabletView": "Vue de tablette", "templateImportedToastDescription": "Le modèle JSON a été importé avec succès.", "templateImportedToastTitle": "Modèle importé", "templateNameLabel": "Nom de modèle", "templateNamePlaceholder": "<PERSON> modèle de messagerie", "undoButton": "<PERSON><PERSON><PERSON><PERSON>", "viewAllTemplatesButton": "Afficher tous les modèles", "cancelButton": "Annuler", "copyHtmlButton": "Copier HTML", "designMode": "Mode de conception", "desktopView": "Vue de bureau", "htmlCode": "Code html", "htmlCopiedToastDescription": "Le code HTML a été copié dans votre presse-papiers.", "htmlCopiedToastTitle": "HTML copié", "importButton": "Importer", "importJsonDialogTitle": "Importer un modèle JSON", "importJsonTemplateButton": "Importer un modèle JSON"}, "viewButton": "Voir", "interviewTemplatesPage": {"addCandidateButton": "Ajouter un candidat", "all-levels": "To<PERSON> les niveaux", "all-types": "Tous types", "behavioral": "Comportemental", "browseFilesButton": "Parcourir les fichiers", "candidatesCount": "{count} candidats", "completedCount": "{count} terminé", "created": "Créé:", "createdBy": "Créé par:", "csvDropzoneText": "Faites glisser et déposez un fichier CSV ou cliquez pour parcourir", "csvFormatHint": "Le fichier CSV doit avoir 2 colonnes: \"nom complet\" et \"e-mail\"", "csvUploadTab": "Téléchargement de CSV", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "editButton": "Modifier", "emailPlaceholder": "E-mail", "errorDeletingTemplateToastDescription": "Échec de la suppression du modèle. \nVeuillez réessayer.", "errorDeletingTemplateToastTitle": "<PERSON><PERSON><PERSON>", "errorSendingCandidates": "Erreur d'envoi des candidats:", "fileUploadedToastDescription": "Fichier CSV traité avec succès. \nDes entretiens seront envoyés aux candidats.", "fileUploadedToastTitle": "<PERSON><PERSON><PERSON>", "filterTemplatesDescription": "Trouver des modèles par rôle, type ou niveau", "filterTemplatesTitle": "<PERSON><PERSON><PERSON><PERSON> fi<PERSON>", "fullNamePlaceholder": "Nom et prénom", "invitationsSentSuccessToast": "Les invitations ont été envoyées avec succès!", "junior": "Junior", "lead": "Plomb", "level": "Niveau", "loading-interviews": "Chargement des modèles d'interview ...", "manualEntryTab": "En<PERSON><PERSON> man<PERSON>le", "mid-level": "De niveau intermédiaire", "missingFileToastDescription": "Veuillez télécharger un fichier CSV avec des adresses e-mail.", "missingFileToastTitle": "<PERSON><PERSON><PERSON> man<PERSON>", "missingInformationToastDescriptionEmails": "Veuillez saisir au moins une adresse e-mail.", "missingInformationToastTitle": "Informations manquantes", "missingTemplateToastDescription": "Veuillez sélectionner un modèle d'entrevue.", "missingTemplateToastTitle": "<PERSON><PERSON><PERSON><PERSON>", "mixed": "Mixte", "moreQuestions": "{count} Plus de questions", "newTemplateButton": "Nouveau modèle", "noTemplatesFoundDescription": "<PERSON><PERSON>ez votre premier modèle d'interview pour commencer.", "noTemplatesFoundTitle": "<PERSON><PERSON><PERSON> mod<PERSON>le trouvé", "pageDescription": "<PERSON><PERSON><PERSON> et gérer les modèles d'interview à envoyer aux candidats", "pageTitle": "Mod<PERSON><PERSON> d'entrevue", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page des entretiens.", "permissionDeniedToastTitle": "Permission refusée", "questionsLabel": "Questions: {count}", "search": "Recherche", "search-by-role-or-tech": "Recherche par rôle ou technologie", "select-level": "Sélectionner le niveau", "select-type": "Sélectionner le type", "selectedFile": "Sélectionné: {nom de fichier}", "sendButton": "Envoyer", "sendInterviewDialogDescription": "Envoyez l'interview \"{role} {Level}\" aux candidats.", "sendInterviewDialogTitle": "Inviter les candidats", "sendInvitationsButton": "Envoyer des invitations", "senior": "Senior", "serverErrorToastDescription": "<PERSON><PERSON><PERSON> chose s'est mal passé en supprimant le modèle.", "serverErrorToastTitle": "<PERSON><PERSON><PERSON> <PERSON>", "system-design": "Conception du système", "techStackLabel": "Pile technologique:", "technical": "Technique", "templateDeletedToastDescription": "\"{role} {niveau}\" Le modèle a été supprimé.", "templateDeletedToastTitle": "<PERSON><PERSON><PERSON><PERSON> supprimé", "type": "Type", "unexpectedError": "Une erreur inattendue s'est produite.", "uploadAndSendButton": "Télécharger et envoyer", "uploadCsvFileLabel": "Télécharger le fichier CSV", "uploadFailedToastDescription": "Il y a eu une erreur de traitement du fichier. \nVeuillez réessayer.", "uploadFailedToastTitle": "Le téléchargement a échoué", "viewButton": "Voir"}, "createInterviewTemplatePage": {"addButton": "Ajouter", "addTechnologiesHint": "Ajouter des technologies pertinentes pour ce rôle", "aiQuestionGenerationDescription": "Configurer le nombre de questions que l'IA devrait générer pour cette interview", "aiQuestionGenerationTitle": "Génération de questions IA", "basedOnInterviewType": "Bas<PERSON> sur le type d'interview sélectionné", "basicInformationTitle": "Informations de base", "behavioralInterview": "Entretien comportemental", "cancelButton": "Annuler", "completeRequiredFieldsTitle": "Compléter les champs requis", "createTemplateButton": "<PERSON><PERSON><PERSON> un modèle", "creatingButton": "Création ...", "creationFailedToastDescription": "<PERSON><PERSON><PERSON> chose s'est mal passé. \nVeuillez réessayer.", "creationFailedToastTitle": "La création a échoué", "experienceLevelLabel": "Niveau d'expérience", "interviewTypeLabel": "Type d'interview", "juniorLevel": "Junior (0-2 ans)", "leadLevel": "Plomb / directeur (8 ans)", "levelRequired": "Le niveau d'expérience est requis", "midLevel": "De niveau intermédiaire (2-5 ans)", "missingInformationToastDescription": "<PERSON><PERSON><PERSON>z remplir tous les champs requis avant d'économiser.", "missingInformationToastTitle": "Informations manquantes", "mixedInterview": "Interview mixte", "noTechnologiesAdded": "Aucune technologie n'a encore ajouté", "numberOfQuestionsLabel": "Nombre de questions", "numberOfQuestionsPlaceholder": "10", "pageDescription": "Concevoir un modèle pour les interviews alimentées par l'IA - des questions seront générées automatiquement", "pageTitle": "<PERSON><PERSON><PERSON> un modèle d'interview", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à cette page.", "permissionDeniedToastTitle": "Permission refusée", "questionCountHint": "Choisissez entre 1 et 50 questions", "questionCountRange": "Le nombre de questions doit être entre 1 et 50", "questionsGeneratedTitle": "{questionCount, plural, one {1 question sera géné<PERSON>e} other {# questions seront géné<PERSON>es}}", "recommendedRangeLabel": "Gamme recommandée", "requiredField": "(requis)", "roleRequired": "Le titre de rôle est requis", "roleTitleLabel": "<PERSON><PERSON><PERSON> <PERSON> rô<PERSON>", "roleTitlePlaceholder": "par exemple \nDéveloppeur de frontend senior", "selectExperienceLevelPlaceholder": "Sélectionnez le niveau d'expérience", "selectInterviewTypeFirst": "Sélectionnez d'abord le type d'interview", "selectInterviewTypePlaceholder": "Sélectionnez le type d'interview", "seniorLevel": "Senior (5 ans)", "systemDesign": "Conception du système", "techStackPlaceholder": "par exemple \nReact, node.js, dactylographié", "techStackRequired": "Au moins une technologie est requise", "technicalInterview": "Entretien technique", "technologyStackTitle": "Pile technologique", "templateCreatedToastDescription": "Votre modèle d'interview a été créé avec des questions {QuesterCount} Générées AI.", "templateCreatedToastTitle": "<PERSON><PERSON><PERSON><PERSON>", "typeRequired": "Le type d'entrevue est requis", "questionsGeneratedDescription": "L'IA créera {questionCount} questions {type, select, technical {techniques} behavioral {comportementales} system_design {de conception de systèmes} mixed {mixtes} other {d'entretien}} pertinentes basées sur votre modèle"}, "SearchPage": {"noSearchQueryToastDescription": "<PERSON>euillez saisir une requête de recherche avant de rechercher.", "noSearchQueryToastTitle": "Recherche vide", "searchErrorDescription": "Échec de la récupération des résultats de recherche. \nVeuillez réessayer.", "searchErrorTitle": "<PERSON><PERSON><PERSON> de recherche", "searchNowButton": "Rechercher maintenant", "searchingButton": "Recherche", "title": "Search Passisto", "titleHighlight": "<PERSON><PERSON>", "related-searches": "Recherches connexes:", "search-anything": "Recherchez n'importe quoi ...", "searching-in": "Recherche dans:"}, "integrationPage": {"backToIntegrations": "Retour aux intégrations", "integrationDescription": "Affichez la configuration et l'état de cette intégration.", "integrationNameTitle": "{intégrationName}", "integrationNotFoundDescription": "L'intégration demandée n'existe pas ou a été supprimée.", "integrationNotFoundTitle": "L'intégration n'est pas trouvée", "loadingIntegration": "Intégration de chargement ...", "overviewTitle": "Présentation de l'intégration", "permissionDeniedToastDescription": "Vous n'avez pas la permission de visualiser les intégrations.", "permissionDeniedToastTitle": "Permission refusée"}, "editIntegrationPage": {"backToIntegrationDetails": "Retour aux détails de l'intégration", "editIntegrationTitle": "Modifier l'intégration", "integrationDetailsTitle": "Détails de l'intégration", "integrationNotFound": "Intégration introuvable.", "loadingIntegration": "Intégration de chargement ...", "modifySettingsDescription": "Modifiez les champs ci-dessous pour mettre à jour vos paramètres d'intégration.", "permissionDeniedToastDescription": "Vous n'avez pas la permission de modifier cette intégration.", "permissionDeniedToastTitle": "Permission refusée", "updateDescription": "Met<PERSON>z à jour la configuration de {intégrationName}."}, "newIntegrationPage": {"backToIntegrations": "Retour aux intégrations", "createIntegrationDescription": "Connectez vos sources de données pour automatiser la synchronisation des données.", "createIntegrationTitle": "<PERSON><PERSON>er une nouvelle intégration", "fillFormDescription": "Remplissez le formulaire ci-dessous pour configurer votre intégration.", "integrationDetailsTitle": "Détails de l'intégration"}, "DashboardPage": {"active": "Actif", "active-teams": "équipes actives", "active-users": "utilisateurs actifs", "ai-interview": "Entretien de l'IA", "apr": "Avr", "aug": "Août", "avg-users-team": "Avg. \nUtilisateurs par équipe", "completed": "comp<PERSON><PERSON>", "conv-rate": "% taux de conversion", "dec": "Déc", "email-builder": "Email Builder", "email-opened": "{count} ouvert", "email-sent": "{count} envoy<PERSON>", "emails": "E-mails", "feb": "Fév", "form-builder": "Créateur de formulaires", "form-submissions": "{count} Soumissions", "forms": "Formes", "inactive": "Inactif", "interview-completed": "{count} terminé", "interview-pending": "{count} en attente", "interviews": "Interviews", "jan": "Jan", "jul": "<PERSON><PERSON><PERSON>", "jun": "Juin", "mar": "Mar", "may": "Peut", "nov": "Nov", "oct": "Octobre", "of-teams-have-members": "des équipes ont des membres", "opened": "ouvert", "pending": "en attente", "quick-actions": "Actions rapides", "sent": "envoy<PERSON>", "sep": "Sep", "submissions": "soumissions", "team-activity-rate": "Taux d'activité de l'équipe", "team-status": "Statut d'équipe", "total-teams": "Total des équipes", "total-users": "Total utilisateurs", "user-growth": "Croissance de l'utilisateur", "users": "Utilisateurs", "users-per-team": "utilisateurs par équipe"}, "TeamsPage": {"authenticationRequiredToast": "Authentification requise", "cancelButton": "Annuler", "createTeamButton": "Créer une équipe", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "deleteCompanyTeamWarning": "L'équipe «Company» par défaut ne peut pas être supprimée.", "deleteTeamDialogDescription": "Êtes-vous sûr de vouloir supprimer {TeamName}? \nCette action ne peut pas être annulée et supprimera toutes les associations d'équipe.", "deleteTeamDialogTitle": "Supprimer l'équipe", "deletingButton": "Suppression ...", "errorDeletingTeamDescription": "N'a pas réussi à supprimer l'équipe.", "errorDeletingTeamTitle": "Équipe de suppression d'erreur", "errorLoadingTeamsDescription": "Il y a eu une erreur de chargement des données de vos équipes.", "errorLoadingTeamsTitle": "Équipes de chargement d'erreur", "loadingTeams": "Chargement des équipes ..", "noTeamsFoundDescription": "<PERSON><PERSON>ez votre première équipe pour commencer.", "noTeamsFoundTitle": "Aucune équipe trouvée.", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page des équipes.", "permissionDeniedToastTitle": "Permission refusée", "searchTeamsPlaceholder": "Équipes de recherche ...", "teamDeletedToastDescription": "{TeamName} a été supprimé avec succès.", "teamDeletedToastTitle": "L'équipe supprimée", "teamsDescription": "G<PERSON>rez les équipes de votre organisation.", "teamsTitle": "Équipes"}, "NewTeamPage": {"authenticationRequiredToast": "Authentification requise", "backToTeams": "Retour aux équipes", "breadcrumbCreateNewTeam": "Créer une nouvelle équipe", "breadcrumbTeams": "Équipes", "cancelButton": "Annuler", "cardDescription": "Créez une nouvelle équipe et attribuez des autorisations.", "cardTitle": "Créer une nouvelle équipe", "createTeamButton": "Créer une équipe", "creatingTeamButton": "Création ...", "creationFailedToastDescription": "Une erreur s'est produite pendant la création.", "creationFailedToastTitle": "La création a échoué", "descriptionLabel": "Description", "descriptionPlaceholder": "Une brève description du but et des responsabilités de l'équipe", "descriptionRequired": "Description est requise.", "failedToLoadPermissions": "Échec du chargement des autorisations", "formErrorsGenericTitle": "<PERSON><PERSON><PERSON>", "formErrorsTitleFillFields": "Veuillez remplir les champs suivants:", "formErrorsTitleTeamExists": "L'équipe existe déjà", "loadingPermissions": "Permissions de chargement ...", "permissionDeniedToastDescription": "Vous n'avez pas la permission de créer des équipes.", "permissionDeniedToastTitle": "Permission refusée", "permissionsDescription": "Les autorisations attribuées à cette équipe seront accordées à tous les membres de l'équipe, sauf si les utilisateurs spécifiques.", "permissionsSelected": "{count, plural, one {# permission sélectionnée} other {# permissions sélectionnées}}", "teamAlreadyExists": "Une équipe avec le nom \"{TeamName}\" existe déjà.", "teamCreatedToastDescription": "{TeamName} a été créé avec succès.", "teamCreatedToastTitle": "L'équipe a créé", "teamNameLabel": "Nom de l'équipe", "teamNamePlaceholder": "Ingénierie", "teamNameRequired": "Le nom de l'équipe est requis.", "teamPermissionsTitle": "Autorisation d'équipe"}, "RolesPermissionsPage": {"allCategoriesOption": "Toutes les catégories", "categoryTableHead": "<PERSON><PERSON><PERSON><PERSON>", "errorLoadingData": "Erreur de chargement des données", "errorLoadingDataDescription": "Échec du chargement des rôles et des autorisations.", "infoBoxDescription1": "Dans ce système, les autorisations sont attribuées directement aux utilisateurs, et non héritées par des rôles. \nLes rôles sont utilisés à des fins organisationnelles uniquement. \nPour attribuer des autorisations à un utilisateur, modifiez l'utilisateur directement à partir de la page des utilisateurs.", "infoBoxDescription2": "Sur cette page, vous pouvez afficher tous les rôles et autorisations disponibles. \nUtilisez les onglets pour basculer entre les vues, la zone de recherche pour trouver des éléments spécifiques et le filtre de catégorie pour organiser les autorisations par type.", "infoBoxTitle": "Affectation d'autorisation directe", "loadingPermissions": "Permissions de chargement ...", "noPermissionsFound": "Aucune autorisation trouvée", "noRolesFound": "<PERSON><PERSON><PERSON> rôle trouvé", "pageDescription": "<PERSON><PERSON><PERSON> les rôles et les autorisations pour votre organisation.", "pageTitle": "<PERSON><PERSON><PERSON>", "permissionNameTableHead": "Nom d'autorisation", "permissionSystemInfoButton": "Système de gestion de l'autorisation", "permissionsManagementDescription": "Afficher et filtrer les autorisations dans votre système", "permissionsManagementTitle": "Gestion des autorisations", "permissionsTab": "Autorisation", "roleAdministrator": "Administrateur", "roleGuest": "Invi<PERSON>", "roleManager": "Directeur", "roleMember": "Membre", "roleNameTableHead": "Nom de rôle", "rolesManagementDescription": "Afficher et filtrer les rôles dans votre système", "rolesManagementTitle": "Gestion des rôles", "rolesTab": "<PERSON><PERSON><PERSON>", "searchPermissionsPlaceholder": "Autorisations de recherche ...", "searchRolesPlaceholder": "Rôles de recherche ...", "systemNameTableHead": "Nom du système", "uncategorized": "Non classé"}, "EditorPage": {"backToGenerator": "Retour au générateur", "loadingEditor": "Éditeur de chargement ...", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page du générateur de messagerie.", "permissionDeniedToastTitle": "Permission refusée"}, "SecurityInfoPage": {"a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "Un utilisateur avec le rôle de gestionnaire dans l'équipe d'ingénierie peut gérer les ressources d'ingénierie.", "adds-all-permissions-from-the-users-teams": "Ajoute toutes les autorisations des équipes de l'utilisateur.", "adds-any-extra-permissions-granted-directly-to-the-user": "Ajoute toutes les autorisations supplémentaires accordées directement à l'utilisateur.", "administrator-role": "Rôle d'administrateur", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "À mesure que votre organisation se développe, les rôles facilitent l'intégration de nouveaux utilisateurs avec les niveaux d'accès appropriés sans avoir à configurer les autorisations individuellement.", "assign-user-to-team": "Affecter l'utilisateur à l'équipe", "auditability": "Auditabilité", "back-to-roles-and-permissions": "Retour aux rôles", "benefits-of-role-based-access": "Avantages de l'accès basé sur les rôles", "best-practices": "Meilleures pratiques", "collects-all-permissions-from-the-users-roles": "Recueille toutes les autorisations des rôles de l'utilisateur.", "combined-permissions": "Autorisations combinées", "considers-the-scope-of-each-permission-global-team-project-self": "Considère la portée de chaque autorisation (Global, Team, Project, Self).", "create-data-provider": "<PERSON><PERSON><PERSON> un fournisseur de données", "create-data-provider-with-team-scope": "<PERSON><PERSON>er un fournisseur de données (avec la portée de l'équipe)", "create-user": "C<PERSON>er un utilisateur", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Différentes équipes peuvent avoir différents ensembles d'autorisations en fonction de leurs besoins.", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Chaque rôle contient une collection d'autorisations avec des lunettes spécifiques. \nPar exemple:", "effective-permissions": "Autorisation efficace", "engineering-team": "<PERSON><PERSON><PERSON>", "example-scope-resolution": "Exemple: Rés<PERSON><PERSON> de la portée", "export-data-with-project-scope": "Données d'exportation (avec portée du projet)", "extra-permissions": "Autorisation supplémentaire", "global": "Mondial", "global-0": "Mondial", "global-scope": "Portée mondiale", "how-roles-provide-a-foundation-for-permissions-in-the-system": "Comment les rôles fournissent une base pour les autorisations dans le système.", "how-teams-provide-contextual-access-to-resources": "Comment les équipes offrent un accès contextuel aux ressources.", "if-a-user-has-the-view-analytics-permission-from": "Si un utilisateur a l'autorisation de «View Analytics» de:", "important-note": "Note importante", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "Les utilisateurs individuels peuvent bénéficier d'autorisations supplémentaires ou les autorisations héritées révoquées pour gérer les exceptions.", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "Au lieu d'attribuer des autorisations individuelles à chaque utilisateur, vous pouvez attribuer des rôles contenant des ensembles prédéfinis d'autorisations, ce qui rend la gestion des utilisateurs plus efficace.", "jane-smith": "<PERSON>", "john-doe": "<PERSON>", "key-0": "→", "manage-billing": "<PERSON><PERSON><PERSON> la facturation", "manage-profile-with-self-scope": "<PERSON><PERSON><PERSON> le profil (avec l'auto-portée)", "manager-role": "Rôle de gestionnaire", "multi-layered-permission-system": "Système d'autorisation multicouche", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Notre système de sécurité utilise une approche multicouche des autorisations, permettant un contrôle d'accès à grain fin tout en maintenant la flexibilité et la facilité de gestion.", "overview-0": "<PERSON><PERSON><PERSON><PERSON>", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "L'autorisation s'applique à l'ensemble du système à toutes les ressources du type pertinent.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "L'autorisation s'applique uniquement aux ressources créées par ou directement attribuées à l'utilisateur.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "L'autorisation s'applique uniquement aux ressources détenues ou associées aux équipes de l'utilisateur.", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "L'autorisation ne s'applique qu'aux projets spécifiques, quelle que soit la propriété de l'équipe.", "permission-inheritance-flow": "Flux d'héritage d'autorisation", "permission-model-overview": "Présentation du modèle d'autorisation", "permission-resolution": "Résolution d'autorisation", "permission-scopes": "Écarts de l'autorisation", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Les portées d'autorisation définissent les limites dans lesquelles une autorisation s'applique. \nIls permettent un contrôle à grains fins sur les ressources d'un utilisateur accéder avec une autorisation donnée.", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Les autorisations ne s'appliquent qu'aux ressources appartenant à l'équipe. \nC'est la portée la plus courante pour les autorisations d'équipe.", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Autorisations accordées à tous les membres de l'équipe, généralement portée aux ressources de l'équipe.", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Les autorisations peuvent être limitées à des projets spécifiques sur lesquels l'équipe travaille, même si ces projets sont partagés avec d'autres équipes.", "project-scope": "Portée du projet", "removes-any-permissions-explicitly-revoked-for-the-user": "Supprime toutes les autorisations révoquées explicitement pour l'utilisateur.", "resource-scope": "Portée des ressources", "revoked-permissions": "Autorisations révoquées", "robert-johnson": "<PERSON>", "role-based-access-control": "Contrôle d'accès basé sur les rôles", "role-permissions": "Autorisation de rôle", "role-structure": "Structure de rôle", "roles": "<PERSON><PERSON><PERSON>", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "Les rôles sont définis au niveau du système et ne peuvent pas être créés ou modifiés par des utilisateurs réguliers. \nContactez votre administrateur système si vous avez besoin d'un nouveau rôle ou de modifications dans les rôles existants.", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "Les rôles sont des ensembles prédéfinis d'autorisations qui représentent des fonctions d'emploi ou des niveaux de responsabilité communs au sein de votre organisation. \nChaque utilisateur peut se voir attribuer un ou plusieurs rôles.", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "Les rôles garantissent que les utilisateurs ayant des responsabilités similaires ont des droits d'accès cohérents, ce qui réduit le risque d'incohérences d'autorisation.", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "Les rôles fournissent une structure claire pour les droits d'accès, ce qui facilite la vérification qui a accès à quoi et pourquoi ils ont cet accès.", "scalability": "Évolutivité", "scope-examples": "Exemples de portée", "scope-hierarchy-broadest-to-narrowest": "Hiérarchie de portée (la plus large à la plus étroite)", "scope-resolution": "Résolution de portée", "scope-types": "Types de portée", "scopes": "Portées", "security-system": "Système de sécurité", "simplified-management": "Gestion simplifiée", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Certaines autorisations d'équipe peuvent avoir une portée mondiale, permettant aux membres de l'équipe d'effectuer des actions sur l'ensemble du système.", "standardization": "Standardisation", "team": "Équipe", "team-0": "Équipe", "team-1": "Équipe", "team-based": "En équipe", "team-based-permissions": "Autorisation d'équipe", "team-members": "Membres de l'équipe", "team-members-0": "Membres de l'équipe", "team-permission-scopes": "Élèves de l'autorisation de l'équipe", "team-permissions": "Autorisation d'équipe", "team-permissions-0": "Autorisation d'équipe", "team-permissions-1": "Autorisation d'équipe", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Les autorisations d'équipe sont généralement portée aux ressources de l'équipe, mais elles peuvent également avoir différents types de portée.", "team-scope": "Portée de l'équipe", "team-structure": "Structure d'équipe", "team-vs-role-permissions": "Autorisations d'équipe vs rôle", "teams": "Équipes", "teams-and-permissions": "Équipes et autorisations", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "Les équipes (ou groupes) vous permettent d'organiser des utilisateurs et d'accorder des autorisations en fonction des ressources dont ils ont besoin pour accéder. \nLes équipes sont particulièrement utiles pour le contrôle d'accès départemental ou basé sur des projets.", "the-effective-scope-will-be-global-as-its-broader-than-team": "La portée efficace sera mondiale, car elle est plus large que l'équipe.", "the-leadership-team-with-global-scope": "L'équipe de direction (avec une portée mondiale)", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "Le même utilisateur de l'équipe marketing peut gérer les ressources marketing.", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "Les ressources spécifiques (projets, données, etc.) auxquelles l'équipe a accès.", "their-manager-role-with-team-scope": "Leur rôle de manager (avec l'équipe de l'équipe)", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Comprendre comment les lunettes d'autorisation limitent la portée des autorisations.", "understanding-how-permissions-work-in-our-enterprise-system": "Comprendre comment les autorisations fonctionnent dans notre système d'entreprise.", "understanding-the-permission-model-and-access-control": "Comprendre le modèle d'autorisation et le contrôle d'accès.", "user-can-create-data-providers-only-for-their-team-s": "L'utilisateur ne peut créer des fournisseurs de données uniquement pour leurs équipes.", "user-can-export-data-only-from-specific-projects-they-have-access-to": "L'utilisateur ne peut exporter des données que à partir de projets spécifiques auxquels ils ont accès.", "user-can-only-manage-their-own-profile-information": "L'utilisateur ne peut gérer que ses propres informations de profil.", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "L'utilisateur peut afficher l'analyse pour toutes les équipes et projets du système.", "user-overrides": "Remplacez les utilisateurs", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "Les utilisateurs reçoivent des rôles qui accordent un ensemble d'autorisations. \nLes rôles fournissent une base d'accès appropriée pour différentes fonctions de travail.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Les utilisateurs appartiennent à des équipes qui accordent des autorisations supplémentaires. \nLes autorisations d'équipe sont généralement portée aux ressources de l'équipe.", "users-who-belong-to-the-team-and-inherit-its-permissions": "Les utilisateurs qui appartiennent à l'équipe et héritent de ses autorisations.", "view-analytics": "Voir l'analyse", "view-analytics-0": "Voir l'analyse", "view-analytics-with-global-scope": "Voir l'analyse (avec portée mondiale)", "what-are-permission-scopes": "Quelles sont les lunettes d'autorisation?", "what-are-roles": "Que sont les rôles?", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "Lorsqu'un utilisateur a la même autorisation avec différentes lunettes (par exemple, à partir de différents rôles ou équipes), le système utilise la portée la plus large.", "when-determining-if-a-user-has-a-specific-permission-the-system": "Lors de la détermination du si un utilisateur a une autorisation spécifique, le système:", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "Bien que les rôles définissent ce qu'un utilisateur peut faire en fonction de sa fonction de travail, les équipes définissent les ressources qu'il peut accéder. \nCette combinaison fournit un système de contrôle d'accès flexible et puissant.", "must-avoid": "Doit <PERSON>!"}, "must-avoid": "Doit <PERSON>!", "UserCardView": {"actionsSrOnly": "Actes", "activate": "Activer", "ban": "Interdire", "createdLabel": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "emailLabel": "E-mail", "inactiveAccount": "Compte inactif", "noPermissions": "Aucune autorisation", "noRolesAssigned": "Aucun rôle attribué", "noTeams": "Aucune équipe", "noUsersFoundDescription": "Essayez d'ajuster votre recherche ou vos filtres", "noUsersFoundTitle": "Aucun utilisateur trouvé", "permissionsCount": "{count} Permission {count, pluriel, un {} autre {s}} attribué", "permissionsLabel": "Autorisation", "rolesLabel": "<PERSON><PERSON><PERSON>", "statusActive": "Actif", "statusInactive": "Inactif", "teamsLabel": "Équipes", "viewDetails": "Aff<PERSON>r les détails"}, "CandidateInterviewPage": {"aiInterviewer": "Interviewer AI", "candidateInterviewTitle": "Entretien des candidats", "candidateLabel": "Candidat", "interviewNotFoundDescription": "Nous n'avons pas pu trouver l'interview que vous recherchez. \nIl a peut-être été supprimé ou n'existe pas.", "interviewNotFoundTitle": "Interview introuvable", "interviewTypeCardTitle": "Type d'interview", "levelBadge": "Niveau", "loadingInterviewDetails": "Chargement des détails de l'interview ...", "permissionDeniedToastDescription": "Vous n'avez pas la permission d'accéder à la page des entretiens.", "permissionDeniedToastTitle": "Permission refusée", "returnToInterviews": "Retour aux entretiens", "roleCardTitle": "R<PERSON><PERSON>", "scoreCardTitle": "Score", "scoreExcellent": "Excellent", "scoreGood": "Bien", "scoreNeedsImprovement": "Besoin d'amélioration", "scoreNotScoredYet": "Pas encore marqué", "statusCompleted": "Complété", "statusInProgress": "En cours", "transcriptDescription": "Enregistrement complet de la conversation", "transcriptTitle": "Transcription de l'interview", "viewFeedbackButton": "Afficher les commentaires"}, "UserTableView": {"actionsTableHead": "Actes", "activateAction": "Activer", "banAction": "Interdire", "deleteAction": "<PERSON><PERSON><PERSON><PERSON>", "editUserButtonTitle": "Modifier l'utilisateur", "editUserSrOnly": "Modifier", "emailTableHead": "E-mail", "moreOptionsSrOnly": "Plus d'options", "nameTableHead": "Nom", "noRoles": "<PERSON><PERSON>", "noTeams": "Aucune équipe", "noUsersFound": "Aucun utilisateur trouvé.", "rolesTableHead": "<PERSON><PERSON><PERSON>", "teamsTableHead": "Équipes", "viewDetailsButtonTitle": "Aff<PERSON>r les détails", "viewDetailsSrOnly": "Aff<PERSON>r les détails"}, "InterviewDetailPage": {"candidateInformationCardTitle": "Informations sur les candidats", "completedAtLabel": "<PERSON><PERSON><PERSON><PERSON>", "completedBadge": "Complété", "failedToStartInterviewToast": "N'a pas réussi à commencer l'interview. \nVeuillez réessayer.", "feedbackAvailable": "Commentaires disponibles", "feedbackOnlyVisibleToCompany": "Les commentaires sont uniquement visibles pour l'entreprise", "feedbackStatusLabel": "Statut de rétroaction", "interviewCompletedCardTitle": "<PERSON><PERSON><PERSON>", "interviewCompletedDescription": "<PERSON><PERSON> interview est terminée. \nDes commentaires ont été fournis à l'entreprise. \nVous pouvez afficher votre transcription d'entrevue ci-dessous.", "interviewDetailsTitle": "<PERSON><PERSON><PERSON> de l'interview", "interviewOrCandidateNotFoundDescription": "Nous n'avons pas pu trouver l'entretien ou le candidat que vous recherchez. \nIl a peut-être été supprimé ou n'existe pas.", "interviewOrCandidateNotFoundTitle": "Entretien ou candidat non trouvé", "interviewTypeCardTitle": "Type d'interview", "loadingInterviewDetails": "Chargement des détails de l'interview ...", "noFeedback": "Pas de commentaires", "notStartedBadge": "Pas commencé", "readyToStartInterviewDescription": "Vous êtes sur le point de commencer une interview {InterviewType} pour le poste {interviewrole} {interviewlevel}. \nCliquez sur le bouton d'appel lorsque vous êtes prêt.", "readyToStartInterviewTitle": "<PERSON>rêt à commencer votre entretien?", "returnToInterviews": "Retour aux entretiens", "roleCardTitle": "R<PERSON><PERSON>", "scoreLabel": "Score", "startInterviewButton": "Commencer l'entretien", "statusCompleted": "Complété", "statusInProgress": "En cours", "statusLabel": "Statut", "statusPending": "En attente", "techStackCardTitle": "Pile technologique", "viewFullTranscriptButton": "Afficher la transcription complète", "callStartedToastDescription": "Vous êtes maintenant connecté avec l'Interviewer de l'IA.", "callStartedToastTitle": "L'appel a commencé", "interviewNotFoundDescription": "Nous n'avons pas pu trouver l'interview que vous recherchez. \nIl a peut-être été supprimé ou n'existe pas.", "interviewNotFoundTitle": "Interview introuvable", "loadingInterview": "Entretien de chargement ...", "startButton": "Commencer", "webcamErrorDescription": "Je ne pouvais pas accéder à votre webcam. \nVeuillez vérifier les autorisations.", "webcamErrorTitle": "E<PERSON>ur de webcam"}, "ChatSettings": {"allTab": "Tous", "ariaLabelSettings": "Paramètres", "dialogDescription": "Configurez votre expérience de chat", "dialogTitle": "Paramètres de chat", "errorLoadingKnowledgeBases": "Bases de connaissances de chargement d'erreur: {erreur}", "ftpTab": "FTP", "jiraTab": "<PERSON><PERSON>", "knowledgeBasesDescription": "Sélectionnez les bases de connaissances à rechercher lorsqu'ils posent des questions", "knowledgeBasesLabel": "Bases de connaissances", "loadingKnowledgeBases": "Chargement des bases de connaissances ...", "webTab": "Web", "knowledge": "Bases de connaissances:"}, "Chatbot": {"errors": {"apiEndpointNotFound": "CHATBOT API Point de terminaison introuvable. \nVeuillez vérifier la configuration de l'URL de l'API.", "authenticationError": "Erreur d'authentification. \nVous n'aurez peut-être pas la permission d'accéder au chatbot.", "connectionRefused": "Impossible de se connecter au serveur de chatbot. \nVeuillez vérifier si le serveur s'exécute sur le port 5921.", "corsError": "Erreur CORS: le serveur Chatbot n'est pas configuré pour accepter les demandes de cette origine.", "createSessionFailed": "Échec de la création d'une nouvelle session de chat. \nVeuillez réessayer.", "deleteSessionFailed": "Impossible de supprimer la session de chat. \nVeuillez réessayer.", "loadHistory": "N'a pas réussi à charger l'historique du chat. \nVeuillez réessayer.", "loadSessions": "Échec du chargement des sessions de chat. \nVeuillez réessayer.", "newSessionWelcome": "Nouvelle session détectée, montrant un message de bienvenue", "noSessionsFound": "Aucune sessions de chat trouvées pour cet utilisateur.", "sendMessageFailed": "Échec de l'envoi du message.", "serverError": "Erreur du serveur: {message}", "userNotFound": "Informations utilisateur non disponibles. \nVeuillez réessayer dans un instant.", "errorMessagePrefix": "<PERSON><PERSON><PERSON><PERSON>, j'ai rencontré une erreur:", "pleaseTryAgain": "Assurez-vous que vous êtes connecté à Internet et configurez correctement le chatbot."}, "logs": {"newSessionCreated": "C<PERSON>é une nouvelle session de chat avec ID: {SessionID}", "newSessionDetected": "Nouvelle session détectée, montrant un message de bienvenue", "waitingForUser": "En attendant que BackEndUser soit disponible avant de récupérer les séances utilisateur", "waitingForUserHistory": "En attendant que BackEndUser soit disponible avant de récupérer l'historique du chat"}, "welcomeMessage": "Bonjour! \nComment puis-je vous aider aujourd'hui?"}, "UserCreation": {"cancelButton": "Annuler", "cardDescription": "Ajoutez un nouvel utilisateur avec des rôles, des autorisations et des affectations d'équipe.", "cardTitle": "Créer un nouvel utilisateur", "emailLabel": "E-mail", "emailPlaceholder": "<EMAIL>", "extraPermissions": {"description": "Accordez des autorisations supplémentaires au-delà de ce que fournissent les rôles et les équipes de l'utilisateur.", "loading": "Permissions de chargement ...", "noPermissions": "Aucune autorisation supplémentaire sélectionnée.", "title": "Autorisation supplémentaire"}, "firstNameLabel": "Prénom", "firstNamePlaceholder": "<PERSON>", "formErrors": {"emailConflict": "Un utilisateur avec cet e-mail existe déjà.", "emailInvalid": "S'il vous plaît, mettez une adresse email valide.", "emailRequired": "Un e-mail est requis.", "error": "<PERSON><PERSON><PERSON>", "fillInFields": "Veuillez remplir les champs suivants:", "firstNameRequired": "Le prénom est requis.", "lastNameRequired": "Le nom de famille est requis.", "roleRequired": "Au moins un rôle doit être sélectionné.", "userExists": "L'utilisateur existe déjà."}, "inheritedPermissions": {"description": "Ces autorisations sont automatiquement accordées par le biais des rôles et des équipes de l'utilisateur.", "noPermissions": "Aucune autorisation héritée. \nAttribuer des rôles ou des équipes d'abord.", "revokeAction": "Révoquer", "sourceRole": "Rôle: {nom}", "sourceTeam": "Équipe: {nom}", "title": "Autorisation héritée"}, "lastNameLabel": "Nom de famille", "lastNamePlaceholder": "Biche", "rolesSection": {"label": "Rôles des utilisateurs", "loading": "Chargement des rôles ...", "noRoles": "Aucun rôle disponible"}, "submitButton": "C<PERSON>er un utilisateur", "tabs": {"permissions": "Overrités d'autorisation", "roles": "<PERSON><PERSON><PERSON>", "teams": "Équipes"}, "teamsSection": {"companyTeamRequired": "(Requis)", "failedToLoad": "Échec du chargement des équipes: {error}", "label": "Équipes", "loading": "Chargement des équipes ...", "memberCount": "{count, pluriel, un {membre} d'autres {membres}}", "noTeams": "Aucune équipe disponible"}, "toasts": {"authRequired": "Authentification requise", "creationFailedDescription": "Une erreur s'est produite pendant la création.", "creationFailedTitle": "La création a échoué", "dataError": "Erreur de chargement des données", "permissionDeniedDescription": "Vous n'avez pas la permission de créer des utilisateurs.", "permissionDeniedTitle": "Permission refusée", "userCreatedSuccess": "L'utilisateur a créé avec succès"}}, "EditUserPage": {"buttons": {"cancel": "Annuler", "save": "Enregistrer les modifications", "saving": "Économie..."}, "description": "<PERSON><PERSON>z à jour les détails de l'utilisateur, les rôles, les autorisations et les équipes.", "errors": {"loadData": "Erreur de chargement des données", "saveFailed": "Échec de la mise à jour de l'utilisateur", "teamsLoadFailed": "Échec du chargement des équipes"}, "formErrors": {"emailExists": "Un utilisateur avec cet e-mail existe déjà.", "emailInvalid": "S'il vous plaît, mettez une adresse email valide.", "emailRequired": "Un e-mail est requis.", "fillFields": "Veuillez remplir les champs suivants:", "firstName": "Le prénom est requis.", "lastName": "Le nom de famille est requis.", "roles": "Au moins un rôle doit être sélectionné.", "userExists": "L'utilisateur existe déjà"}, "formLabels": {"email": "E-mail", "firstName": "Prénom", "lastName": "Nom de famille"}, "loading": {"permissions": "Permissions de chargement ...", "roles": "Chargement des rôles ...", "teams": "Chargement des équipes ..."}, "permissionDenied": {"description": "Vous n'avez pas la permission de mettre à jour les utilisateurs.", "title": "Permission refusée"}, "permissions": {"effectiveDescription": "Ce sont les autorisations que cet utilisateur aura après avoir appliqué tous les rôles, équipes et remplacements.", "effectiveTitle": "Autorisation efficace", "extraDescription": "Accordez des autorisations supplémentaires au-delà de ce que fournissent les rôles et les équipes de l'utilisateur.", "extraTitle": "Autorisation supplémentaire", "inheritedDescription": "Ces autorisations sont automatiquement accordées par le biais des rôles et des équipes de l'utilisateur.", "inheritedTitle": "Autorisation héritée", "noEffective": "Aucune autorisation efficace", "noInherited": "Aucune autorisation héritée. \nAttribuer des rôles ou des équipes d'abord.", "noTeamsDescription": "Veuillez d'abord sélectionner les équipes de l'onglet Équipes.", "noTeamsTitle": "Aucune équipe sélectionnée", "revoke": "Révoquer", "selectProjects": "Sélectionner des projets", "selectTeams": "Sélectionnez des équipes", "sourceRole": "Rôle:", "sourceTeam": "Équipe:"}, "placeholders": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Biche"}, "roles": {"title": "Rôles des utilisateurs"}, "successMessage": "Utilisateur mis à jour avec succès", "tabs": {"permissions": "Overrités d'autorisation", "roles": "<PERSON><PERSON><PERSON>", "teams": "Équipes"}, "teams": {"member": "membre", "members": "membres", "noTeams": "Aucune équipe disponible", "required": "Requis", "title": "Équipes"}, "title": "Modifier l'utilisateur"}, "UserViewPage": {"accountDetailsHeading": "<PERSON>é<PERSON> du compte", "activeBadge": "Actif", "activityNotEnabled": "Le suivi des activités n'est pas activé", "activityTab": "Activité", "authenticationErrorToastDescription": "Veuillez vous connecter pour afficher les détails de l'utilisateur.", "authenticationErrorToastTitle": "Erreur d'authentification", "backButton": "<PERSON><PERSON>", "backToUsersButton": "Retour aux utilisateurs", "createdLabel": "<PERSON><PERSON><PERSON>", "editUserButton": "Modifier l'utilisateur", "enableActivityTracking": "Activer le suivi des activités dans les paramètres", "extraPermissionSource": "Autorisation supplémentaire", "extraPermissionsHeading": "Autorisation supplémentaire", "fromRoleSource": "<PERSON> {rolename}", "fromTeamSource": "De l'équipe {TeamName}", "inactiveBadge": "Inactif", "loadingUserDetails": "Chargement des détails de l'utilisateur ...", "noExtraPermissions": "Aucune autorisation supplémentaire attribuée", "noPermissionsAssigned": "Cet utilisateur n'a aucune autorisation", "noRevokedPermissions": "Aucune autorisation révoquée", "noRolesAssigned": "Aucun rôle attribué", "noTeamsAssigned": "Aucune équipe affectée", "otherCategory": "<PERSON><PERSON>", "permissionDeniedToastDescription": "Vous n'avez pas la permission de voir les utilisateurs.", "permissionDeniedToastTitle": "Permission refusée", "permissionOverridesTab": "Overrités d'autorisation", "permissionsLabel": "Autorisation", "permissionsTab": "Autorisation", "revokedPermissionsHeading": "Autorisations révoquées", "rolesHeading": "<PERSON><PERSON><PERSON>", "teamsHeading": "Équipes", "unknownSource": "Source inconnue", "userDetailsCardDescription": "Informations complètes sur cet utilisateur", "userDetailsCardTitle": "Coordonnées", "userNotFoundDescription": "L'utilisateur demandé n'a pas pu être trouvé.", "userNotFoundTitle": "Utilisateur introuvable", "userNotFoundToastDescription": "L'utilisateur demandé n'a pas pu être trouvé.", "userNotFoundToastTitle": "Utilisateur introuvable"}, "members-of-team": "Membres de {teamName}", "NavUser": {"billingSettings": "Paramètres de facturation", "failedToAccessBillingPortalToast": "Échec de l'accès au portail de facturation", "upgradeToPro": "Passer à Pro", "usageInformation": "Informations sur l'utilisation", "userAltText": "Utilisa<PERSON>ur"}, "ChatPage": {"creatingNewChatSession": "Création d'une nouvelle session de chat ..."}, "en": "🇺🇸 <PERSON><PERSON><PERSON>", "es": "🇪🇸 Espagnol", "it": "🇮🇹 Italien", "fr": "🇫🇷 Français", "ai-powered": "AI alimenté", "CompanyUsagePage": {"activeStatus": "Actif", "aiInterviewerUsageTitle": "Utilisation de l'intervieweur AI", "emailBuilderUsageTitle": "Utilisation du générateur de courriels", "emailsUnit": "{count} e-mails", "exceededStatus": "Dépassé", "failedToLoadUsageInfoToast": "Impossible de charger les informations d'utilisation", "loadingPageTitle": "Informations d'utilisation de l'entreprise", "minutesUnit": "{comte} Minutes", "tableHeadRemaining": "Restant", "tableHeadTotalLimit": "Limite totale", "tableHeadUsagePercentage": "Pourcentage d'utilisation", "tableHeadUsed": "<PERSON><PERSON><PERSON><PERSON>"}, "NotFoundPage": {"dashboardButton": "Tableau de bord", "footerText": "© {année} PASSISTO. \nTous droits réservés.", "goBackButton": "Remonter", "pageDescription": "Nous n'avons pas pu trouver la page que vous recherchez. \nIl a peut-être été supprimé, renommé ou n'existe pas.", "pageHeading": "Page introuvable", "pageTitle": "404"}, "chatPage": {"actions": {"copy": "Copier dans le presse-papiers", "helpful": "Utile", "notHelpful": "Pas utile", "readAloud": "Lire à haute voix", "regenerate": "Régénérer la réponse"}, "avatars": {"ai": "IA", "user": "U"}, "input": {"placeholder": "Tapez votre message ici ...", "waitingPlaceholder": "En attente de réponse ..."}, "recording": {"start": "Commencer l'enregistrement", "started": "A commencé à enregistrer", "stop": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "stopped": "Enregistré"}, "session": {"label": "Session"}, "sources": {"label": "Sources"}, "title": "<PERSON><PERSON><PERSON>"}, "ThemeToggle": {"darkOption": "Sombre", "lightOption": "<PERSON><PERSON><PERSON>", "systemOption": "Système", "toggleThemeSrOnly": "Thème de basculement"}, "workflows-automation": "Automatisation des workflows"}