import { Node, <PERSON> } from "@xyflow/react";
import socketService from "./socket";

export interface WorkflowData {
  nodes: Node[];
  edges: Edge[];
}

export interface ExecutionOptions {
  onNodeExecute?: (nodeId: string) => void;
  onNodeComplete?: (nodeId: string, data: any) => void;
  onExecutionComplete?: (executionPath: string[]) => void;
  onError?: (error: Error) => void;
}

export class WorkflowExecutionEngine {
  public static executeWithSockets(
    workflowId: string,
    workflowData: WorkflowData,
    options: ExecutionOptions = {}
  ): () => void {
    // Connect to socket if not already connected
    const socket = socketService.connect();
    let isExecuting = true;
    // Create a fresh execution path array for each workflow execution
    const executionPath: string[] = [];

    // Set up progress listener
    const progressListener = (progress: any) => {
      console.log("Received workflow progress:", progress);
      if (progress.workflowId === workflowId) {
        switch (progress.status) {
          case 'started':
            console.log("Workflow execution started");
            // Clear the execution path when workflow starts
            executionPath.length = 0;
            break;
          case 'executing':
            console.log("Node execution started:", progress.nodeId);
            executionPath.push(progress.nodeId);
            if (options.onNodeExecute) {
              options.onNodeExecute(progress.nodeId);
            }
            break;
          case 'nodeCompleted':
            console.log("Node completed:", progress.nodeId, "Data:", progress.data);
            if (options.onNodeComplete) {
              options.onNodeComplete(progress.nodeId, progress.data);
            }
            break;
          case 'completed':
            console.log("Workflow completed. Path:", executionPath);
            isExecuting = false;
            if (options.onExecutionComplete) {
              options.onExecutionComplete([...executionPath]);
            }
            break;
          case 'error':
            console.error("Workflow error:", progress.message);
            isExecuting = false;
            if (options.onError) {
              options.onError(new Error(progress.message));
            }
            break;
        }
      }
    };

    // Add progress listener
    socketService.addListener('workflowProgress', progressListener);

    // Emit workflow start event
    socket.emit("workflow:start", {
      workflowId,
      workflow: workflowData
    });

    // Return stop function
    return () => {
      if (isExecuting) {
        console.log("Stopping workflow execution");
        socket.emit("workflow:stop", { workflowId });
        isExecuting = false;
        // Remove progress listener
        socketService.removeListener('workflowProgress', progressListener);
      }
    };
  }
} 