import axios from "@/config/axios";
import { SEARCH } from "@/utils/routes";

// Define types for the search results
export interface SearchSource {
  content: string;
  metadata: {
    source: string;
    title?: string;
    url?: string;
    [key: string]: any;
  };
}

export interface SearchResponse {
  query: string;
  expanded_queries: string[];
  sources: SearchSource[];
  user_id: string | null;
}

export const searchApi = {
  // Send a search query to the backend
  search: async (
    query: string,
    userId: string | null = null,
    aliasId: Array<string> = [] // Will be provided by the component using ChatSettingsContext
  ): Promise<SearchResponse> => {
    try {
      const response = await axios.post(SEARCH, {
        query,
        indices: aliasId,
      });

      return response.data;
    } catch (error) {
      console.error('Error performing search:', error);
      throw error;
    }
  }
};
