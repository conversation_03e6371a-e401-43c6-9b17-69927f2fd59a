import api from '@/config/axios-workflows';
import socketService from './socket';

export interface FormField {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'date';
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
}

export interface UserTask {
  id: string;
  nodeId: string;
  workflowRunId: string;
  workflowId: string;
  workflowName: string;
  workflowOwner: {
    id: string;
    name: string;
  };
  assignedAt: string;
  status: string;
  nodeType: string;
  nodeLabel: string;
  nodeDescription: string;
  nodeCritical: boolean;
  formFields?: FormField[];
  contextualData?: Record<string, any>;
}

export const userTaskApi = {
  // Get all tasks assigned to the current user
  getAssignedTasks: async (): Promise<UserTask[]> => {
    const response = await api.get('/workflow-runs/assigned-tasks');
    return response.data;
  },

  // Complete a task using REST API
  completeTask: async (workflowRunId: string, nodeId: string, taskData: any = {}): Promise<void> => {
    await api.post(`/workflow-runs/${workflowRunId}/nodes/${nodeId}/complete`, { taskData });
  },

  // Complete a task using WebSockets for real-time updates
  completeTaskWithSocket: (workflowRunId: string, nodeId: string, userId: string, taskData: any = {}): Promise<void> => {
    return new Promise((resolve, reject) => {
      const socket = socketService.connect();

      // Set up one-time listeners for completion or error
      const completedListener = (data: any) => {
        if (data.workflowRunId === workflowRunId && data.nodeId === nodeId) {
          socket.off('userTask:completed', completedListener);
          socket.off('userTask:error', errorListener);
          resolve();
        }
      };

      const errorListener = (data: any) => {
        if (data.workflowRunId === workflowRunId && data.nodeId === nodeId) {
          socket.off('userTask:completed', completedListener);
          socket.off('userTask:error', errorListener);
          reject(new Error(data.message));
        }
      };

      // Add the listeners
      socket.on('userTask:completed', completedListener);
      socket.on('userTask:error', errorListener);

      // Emit the task completion event
      socket.emit('userTask:complete', {
        workflowRunId,
        nodeId,
        userId,
        taskData
      });

      // Set a timeout to prevent hanging promises
      setTimeout(() => {
        socket.off('userTask:completed', completedListener);
        socket.off('userTask:error', errorListener);
        reject(new Error('Task completion timed out'));
      }, 10000); // 10 second timeout
    });
  }
};
