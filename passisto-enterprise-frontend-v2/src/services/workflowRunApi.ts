import api from '@/config/axios-workflows';
import { type WorkflowRun } from '@/components/workflow-runs-table'

const API_URL = process.env.NEXT_PUBLIC_WORKFLOWS_API_URL || 'http://localhost:5000/api'

// Get all workflow runs
export const getAllWorkflowRuns = async (): Promise<WorkflowRun[]> => {
  const response = await api.get(`${API_URL}/workflow-runs`)
  return response.data
}

// Get workflow runs by workflow ID
export const getWorkflowRunsByWorkflowId = async (workflowId: string): Promise<WorkflowRun[]> => {
  const response = await api.get(`${API_URL}/workflow-runs/workflow/${workflowId}`, {
    
  })
  return response.data
}

// Get a specific workflow run by ID
export const getWorkflowRun = async (id: string): Promise<WorkflowRun> => {
  const response = await api.get(`${API_URL}/workflow-runs/${id}`)
  return response.data
}

// Get a workflow run with detailed node information including assignees
export const getWorkflowRunWithDetails = async (id: string): Promise<WorkflowRun> => {
  const response = await api.get(`${API_URL}/workflow-runs/${id}/details`)
  return response.data
}

// Create a new workflow run for a workflow
export const createWorkflowRun = async (workflowId: string): Promise<WorkflowRun> => {
  const response = await api.post(
    `${API_URL}/workflow-runs`,
    { workflowId }
  )
  return response.data
}

// Update workflow run status
export const updateWorkflowRunStatus = async (
  id: string,
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED'
): Promise<WorkflowRun> => {
  const response = await api.put(
    `${API_URL}/workflow-runs/${id}/status`,
    { status }
  )
  return response.data
}

// Delete a workflow run
export const deleteWorkflowRun = async (id: string): Promise<void> => {
  await api.delete(`${API_URL}/workflow-runs/${id}`)
}

// Stop a workflow run
export const stopWorkflowRun = async (id: string): Promise<WorkflowRun> => {
  const response = await api.post(
    `${API_URL}/workflow-runs/${id}/stop`,
    {}
  )
  return response.data
}

// Group of workflow run related API services
export const workflowRunApi = {
  getAllWorkflowRuns,
  getWorkflowRunsByWorkflowId,
  getWorkflowRun,
  getWorkflowRunWithDetails,
  createWorkflowRun,
  updateWorkflowRunStatus,
  deleteWorkflowRun,
  stopWorkflowRun,
}