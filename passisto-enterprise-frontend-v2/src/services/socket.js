import { io } from 'socket.io-client';

// Use the backend URL directly or the Next.js API route
const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000';

class SocketService {
  constructor() {
    this.socket = null;
    this.listeners = new Map();
    this.reconnectCallbacks = [];
  }

  connect() {
    if (!this.socket) {
      console.log('Attempting to connect to WebSocket server at:', SOCKET_URL);
      this.socket = io(SOCKET_URL, {
        transports: ['websocket', 'polling'],
        autoConnect: true,
        withCredentials: true,
        reconnection: true,
        reconnectionAttempts: 10,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        path: '/socket.io',
        secure: SOCKET_URL.startsWith('https'),
        rejectUnauthorized: false, // Allow self-signed certificates in development
      });

      this.socket.on('connect', () => {
        console.log('Connected to WebSocket server');
        // Notify all reconnect callbacks
        this.reconnectCallbacks.forEach(callback => callback());
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        console.error('Connection details:', {
          url: SOCKET_URL,
          options: this.socket.io.opts,
          transport: this.socket.io.engine?.transport?.name || 'unknown',
          protocol: window.location.protocol,
          host: window.location.host
        });

        // Try to reconnect with different transport if multiple attempts fail
        if (this.socket.io.backoff?.attempts > 3) {
          console.log('Multiple connection attempts failed, trying alternative transport...');
          // Toggle between websocket and polling as primary transport
          const currentTransports = this.socket.io.opts.transports;
          if (currentTransports[0] === 'websocket') {
            this.socket.io.opts.transports = ['polling', 'websocket'];
          } else {
            this.socket.io.opts.transports = ['websocket', 'polling'];
          }
          console.log('Switched to transport order:', this.socket.io.opts.transports);
        }
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Disconnected from WebSocket server:', reason);
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log('Reconnected to WebSocket server after', attemptNumber, 'attempts');
        // Notify all reconnect callbacks
        this.reconnectCallbacks.forEach(callback => callback());
      });

      this.socket.on('reconnect_error', (error) => {
        console.error('WebSocket reconnection error:', error);
      });

      // Set up default event listeners
      this.setupDefaultListeners();
    }
    return this.socket;
  }

  setupDefaultListeners() {
    // Listen for workflow updates
    this.socket.on('workflow-updated', (workflow) => {
      console.log('Received workflow update:', workflow);
      const listeners = this.listeners.get('workflow-updated') || [];
      listeners.forEach(callback => callback(workflow));
    });

    // Listen for workflow progress
    this.socket.on('workflowProgress', (progress) => {
      console.log('Received workflow progress:', progress);
      const listeners = this.listeners.get('workflowProgress') || [];
      listeners.forEach(callback => callback(progress));
    });

    // Listen for workflow run progress
    this.socket.on('workflowRunProgress', (progress) => {
      console.log('Received workflow run progress:', progress);
      const listeners = this.listeners.get('workflowRunProgress') || [];
      listeners.forEach(callback => callback(progress));
    });

    // Listen for node execution details
    this.socket.on('nodeExecution', (executionDetails) => {
      console.log('Received node execution details:', executionDetails);
      const listeners = this.listeners.get('nodeExecution') || [];
      listeners.forEach(callback => callback(executionDetails));
    });

    // Listen for node run progress
    this.socket.on('nodeRunProgress', (progress) => {
      console.log('Received node run progress:', progress);
      const listeners = this.listeners.get('nodeRunProgress') || [];
      listeners.forEach(callback => callback(progress));
    });

    // Listen for decision node results
    this.socket.on('decisionNodeResult', (result) => {
      console.log('Received decision node result:', result);
      const listeners = this.listeners.get('decisionNodeResult') || [];
      listeners.forEach(callback => callback(result));
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Emit workflow update
  emitWorkflowUpdate(workflow) {
    if (this.socket) {
      this.socket.emit('workflow-update', workflow);
    }
  }

  // Listen for workflow updates
  onWorkflowUpdate(callback) {
    if (this.socket) {
      this.addListener('workflow-updated', callback);
    }
  }

  // Remove workflow update listener
  offWorkflowUpdate(callback) {
    if (this.socket) {
      this.removeListener('workflow-updated', callback);
    }
  }

  // Add a listener for any event
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove a listener for any event
  removeListener(event, callback) {
    if (this.listeners.has(event)) {
      const listeners = this.listeners.get(event);
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // Register a callback to be called on reconnection
  onReconnect(callback) {
    this.reconnectCallbacks.push(callback);
    return () => {
      const index = this.reconnectCallbacks.indexOf(callback);
      if (index !== -1) {
        this.reconnectCallbacks.splice(index, 1);
      }
    };
  }
}

export const socketService = new SocketService();
export default socketService;