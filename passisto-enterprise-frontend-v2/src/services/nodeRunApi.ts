import axios from 'axios'
import { type NodeRun } from '@/components/workflows/workflow-runs-table'

const API_URL = process.env.NEXT_PUBLIC_WORKFLOWS_API_URL || 'http://localhost:5000/api'

// Get all node runs for a workflow run
export const getNodeRunsByWorkflowRunId = async (workflowRunId: string): Promise<NodeRun[]> => {
  const token = localStorage.getItem('token')
  const response = await axios.get(`${API_URL}/node-runs/workflow-run/${workflowRunId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  return response.data
}

// Get a specific node run by ID
export const getNodeRun = async (id: string): Promise<NodeRun> => {
  const token = localStorage.getItem('token')
  const response = await axios.get(`${API_URL}/node-runs/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
  return response.data
}

// Create a new node run
export const createNodeRun = async (workflowRunId: string, nodeId: string): Promise<NodeRun> => {
  const token = localStorage.getItem('token')
  const response = await axios.post(
    `${API_URL}/node-runs`,
    { workflowRunId, nodeId },
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return response.data
}

// Update node run status and output
export const updateNodeRun = async (
  id: string,
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED',
  output?: any
): Promise<NodeRun> => {
  const token = localStorage.getItem('token')
  const response = await axios.put(
    `${API_URL}/node-runs/${id}`,
    { status, output },
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  )
  return response.data
}

// Delete a node run
export const deleteNodeRun = async (id: string): Promise<void> => {
  const token = localStorage.getItem('token')
  await axios.delete(`${API_URL}/node-runs/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
}

// Group of node run related API services
export const nodeRunApi = {
  getNodeRunsByWorkflowRunId,
  getNodeRun,
  createNodeRun,
  updateNodeRun,
  deleteNodeRun,
} 