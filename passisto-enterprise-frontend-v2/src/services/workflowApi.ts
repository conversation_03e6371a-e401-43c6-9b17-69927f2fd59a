import api from '@/config/axios-workflows';
import type { Workflow } from '@/components/workflows-table';

export const workflowApi = {
  getAllWorkflows: async (): Promise<Workflow[]> => {
    const response = await api.get('/workflows');
    return response.data;
  },

  getWorkflow: async (id: string): Promise<Workflow> => {
    const response = await api.get(`/workflows/${id}`);
    return response.data;
  },

  createWorkflow: async (workflow: Partial<Workflow>): Promise<Workflow> => {
    // Ensure nodes and edges are properly formatted for the API
    const formattedWorkflow = {
      ...workflow,
      // Convert nodes and edges to JSON strings if they're objects
      nodes: Array.isArray(workflow.nodes) ? workflow.nodes : [],
      edges: Array.isArray(workflow.edges) ? workflow.edges : [],
      // Ensure status is a valid value
      status: workflow.status ?? 'draft'
    };

    console.log('Sending formatted workflow data for creation:', formattedWorkflow);

    // Log node data for debugging
    if (Array.isArray(formattedWorkflow.nodes)) {
      formattedWorkflow.nodes.forEach(node => {
        if (node.type === 'ask-ai') {
          console.log(`AskAI node ${node.id} model:`, node.data?.model);
        }
      });
    }
    const response = await api.post('/workflows', formattedWorkflow);
    return response.data;
  },

  updateWorkflow: async (id: string, workflow: Partial<Workflow>): Promise<Workflow> => {
    // Ensure nodes and edges are properly formatted for the API
    const formattedWorkflow = {
      ...workflow,
      // Convert nodes and edges to JSON strings if they're objects
      nodes: Array.isArray(workflow.nodes) ? workflow.nodes : [],
      edges: Array.isArray(workflow.edges) ? workflow.edges : [],
      // Ensure status is a valid value
      status: workflow.status ?? 'draft'
    };

    console.log('Sending formatted workflow data:', formattedWorkflow);

    // Log node data for debugging
    if (Array.isArray(formattedWorkflow.nodes)) {
      formattedWorkflow.nodes.forEach(node => {
        if (node.type === 'ask-ai') {
          console.log(`AskAI node ${node.id} model:`, node.data?.model);
        }
      });
    }
    const response = await api.put(`/workflows/${id}`, formattedWorkflow);
    return response.data;
  },

  deleteWorkflow: async (id: string): Promise<void> => {
    await api.delete(`/workflows/${id}`);
  },

  executeWorkflow: async (id: string): Promise<any> => {
    const response = await api.post(`/workflows/${id}/execute`);
    return response.data;
  }
};