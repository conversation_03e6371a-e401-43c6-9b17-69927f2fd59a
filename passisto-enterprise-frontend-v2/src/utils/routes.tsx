// Define API routes as constants
export const API_BASE_URL = `api/v1`;

// export const API_BASE_URL = "/api/v1";
// User-related routes
export const USER_ROUTE = `${API_BASE_URL}/users`;
export const ONBOARD_ROUTE = `${USER_ROUTE}/onboard`;
export const CHECK_ONBOARDING = `${USER_ROUTE}/check-onboarding`;
export const GET_CURRENT_USER = `${API_BASE_URL}/users/current`;
export const GET_ALL_USERS = `${API_BASE_URL}/users/all`;
export const USER_CREATE = `${API_BASE_URL}/users/create`;
export const USER_GET_BY_ID = (id: string) => `${API_BASE_URL}/users/${id}`;
export const USER_UPDATE = (id: string) => `${API_BASE_URL}/users/${id}`;
export const USER_DELETE = (id: string) => `${API_BASE_URL}/users/${id}`;
export const USER_TOGGLE_STATUS = (id: string) => `${API_BASE_URL}/users/${id}/toggle-status`;
// Role and permission routes
export const GET_ROLES_AND_PERMISSIONS = `${API_BASE_URL}/role-permission/data`;

// teams route
export const TEAM_ROUTE = `${API_BASE_URL}/groups`;
export const GET_ALL_TEAMS = `${TEAM_ROUTE}/all`;
export const GET_TEAM_WITH_MEMBERS = (teamId: string) => `${TEAM_ROUTE}/${teamId}`;
export const CREATE_TEAM = `${TEAM_ROUTE}`;
export const UPDATE_TEAM = (teamId: string) => `${TEAM_ROUTE}/${teamId}`;
export const DELETE_TEAM = (teamId: string) => `${TEAM_ROUTE}/${teamId}`;
export const GET_TEAM_INTEGRATIONS = (teamId: string) => `${TEAM_ROUTE}/${teamId}/integrations`;


// Authentication routes
export const AUTH_ROUTE = `${API_BASE_URL}/auth`;

// AI Agent routes

// form builder
export const AI_AGENT_ROUTE = `${API_BASE_URL}/ai-agent`;
export const FORM_BUILDER_ENHANCE_DESCRIPTION = `${API_BASE_URL}/form-builder/enhance-description`;
export const FORM_BUILDER_GENERATE_FORM = `${API_BASE_URL}/form-builder/generate-form`;
export const FORM_BUILDER_GET_ALL = `${API_BASE_URL}/form-builder/forms`;
export const FORM_BUILDER_DELETE = (formId: String) =>
  `${API_BASE_URL}/form-builder/forms/${formId}`;
export const FORM_BUILDER_EDIT = (formId: String) =>
  `${API_BASE_URL}/form-builder/forms/${formId}`;
export const FORM_BUILDER_GET_BY_ID = (formId: String, companyId: String) =>
  `${API_BASE_URL}/form-builder/forms/${companyId}/${formId}`;
export const FORM_BUILDER_EXPORT = (formId: String) =>
  `${API_BASE_URL}/form-builder/forms/${formId}`;
export const FORM_BUILDER_SUBMIT = (companyId: string, formId: String) =>
  `${API_BASE_URL}/form-builder/${companyId}/forms/${formId}/submit`;
export const FORM_BUILDER_GET_RESPONSES = (formId: string) =>
  `${API_BASE_URL}/form-builder/forms/${formId}/responses`;
export const FORM_BUILDER_DELETE_RESPONSE = (formId: string) =>
  `${API_BASE_URL}/form-builder/forms/${formId}/response`;
// Pas de data-uri ici, on télécharge depuis le backend
export const FORM_BUILDER_EXPORT_FORM = (formId: string, format: "json" | "csv" = "json") =>
  `${API_BASE_URL}/form-builder/forms/${formId}/export?format=${format}`;
export const FORM_BUILDER_DOWNLOAD_RESPONSE_FILE = (formId: string,fileName: string
) =>
  `${API_BASE_URL}/form-builder/forms/${formId}/download/file/${fileName}`;


// email builder
export const EMAIL_BUILDER_ENHANCE_DESCRIPTION = `${API_BASE_URL}/email-builder/enhance-description`;
export const EMAIL_BUILDER_GENERATE_EMAIL = `${API_BASE_URL}/email-builder/generate-email`;
export const EMAIL_BUILDER_GET_BY_ID = (emailId: String) =>
  `${API_BASE_URL}/email-builder/emails/${emailId}`;
export const EMAIL_BUILDER_GET_ALL = `${API_BASE_URL}/email-builder/emails`;
export const EMAIL_BUILDER_EDIT = (emailId: String) =>
  `${API_BASE_URL}/email-builder/emails/${emailId}`;




export const EMAIL_BUILDER_DELETE = (emailId: String) =>
  `${API_BASE_URL}/email-builder/emails/${emailId}`;
export const EMAIL_BUILDER_SEND = `${API_BASE_URL}/email-builder/emails/send`;

// interview assistant
const INTERVIEW_AGENT_BASE_URL = `${API_BASE_URL}/interview-agent`;
export const GET_ALL_INTERVIEWS_TEMPLATES = `${INTERVIEW_AGENT_BASE_URL}/interviews`;
export const GET_INTERVIEW_BY_ID = (interviewId: String) =>`${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}`;
export const GET_INTERVIEW_BY_ID_NO_CANDIDATES = (interviewId: String) =>`${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}/no-candidates`;
export const GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID = (interviewId: String, candidateId: String) => `${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}/candidates/${candidateId}`;
export const GET_FEEDBACK_BY_INTERVIEW_ID_AND_CANDIDATE_ID = (interviewId: String, candidateId: String) => `${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}/feedback/${candidateId}`;
export const SEND_INTERVIEW_TO_CANDIDATS =(interviewId: String) => `${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}/candidates`;
export const CREATE_INTERVIEW_FEEDBACK = (interviewId: String, candidateId: String) =>`${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}/feedback/${candidateId}`;
export const CHECK_CANDIDATE_ACCESS = (interviewId: String, candidateId: String) => `${INTERVIEW_AGENT_BASE_URL}/interviews/${interviewId}/candidates/${candidateId}/access`
export const CREATE_INTERVIEW = `${INTERVIEW_AGENT_BASE_URL}/interviews`;



// stripe routes
export const STRIPE_BASE_URL = `${API_BASE_URL}/stripe`;
export const GET_STRIPE_PLANS = `${STRIPE_BASE_URL}/plans`;
export const CREATE_STRIPE_CHECKOUT_SESSION = `${STRIPE_BASE_URL}/create-checkout-session`;
export const CREATE_STRIPE_PORTAL_SESSION = `${STRIPE_BASE_URL}/create-portal-session`;
export const GET_STRIPE_SUBSCRIPTION_DETAILS = `${STRIPE_BASE_URL}/subscription-details`;
export const CHECK_STRIPE_SUBSCRIPTION_STATUS = `${STRIPE_BASE_URL}/subscription-status`;

// Company routes
export const COMPANY_BASE_URL = `${API_BASE_URL}/company`;
export const CHECK_USAGE_LIMIT = `${COMPANY_BASE_URL}/check-usage`;
export const CHECK_USAGE_LIMIT_BY_COMPANY_ID = `${COMPANY_BASE_URL}/check-usage-by-company-id`;
export const UPDATE_USAGE_LIMIT = `${COMPANY_BASE_URL}/update-usage`;
export const SUBSCRIPTION_NAME = `${COMPANY_BASE_URL}/subscription-name`;
export const COMPANY_USAGE_INFO = `${COMPANY_BASE_URL}/usage-info`;


// Integration routes
export const JIRA_INTEGRATION = `${API_BASE_URL}/integrations/jira`;
export const FTP_INTEGRATION = `${API_BASE_URL}/integrations/ftp`;
export const WEB_INTEGRATION = `${API_BASE_URL}/integrations/web`;
export const ALL_INTEGRATIONS = `${API_BASE_URL}/integrations`;
export const ALL_INTEGRATIONS_GROUPS = `${API_BASE_URL}/integrations/group`;
export const ASSIGN_INTEGRATION_TO_TEAM = `${API_BASE_URL}/integrations/add-integration-group`;

// Chatbot and Search routes
export const ASK_CHATBOT = `${API_BASE_URL}/chatbot/`;
export const GET_SESSION_BY_ID = (sessionId: string) => `${API_BASE_URL}/chatbot/history/${sessionId}`;
export const GET_USER_SESSIONS = `${API_BASE_URL}/chatbot/user-sessions`;
export const NEW_USER_SESSION = (sessionId: string) =>`${API_BASE_URL}/chatbot/user-sessions/new/${sessionId}`;
export const DELETE_SESSION = (sessionId: string) => `${API_BASE_URL}/chatbot/history/${sessionId}`;
export const SEARCH = `${API_BASE_URL}/search/`;