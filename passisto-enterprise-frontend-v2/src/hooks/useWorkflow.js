import { useState, useEffect, useCallback } from 'react';
import { workflowApi } from '../services/workflowApi';
import socketService from '../services/socket';

export const useWorkflow = (initialWorkflow = null) => {
  const [workflow, setWorkflow] = useState(initialWorkflow);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [executionState, setExecutionState] = useState({
    status: 'not_started',
    currentNodeId: null,
    pendingTaskId: null,
    executionPath: []
  });

  // Function to check workflow execution state
  const checkExecutionState = useCallback(async () => {
    if (workflow?.id) {
      try {
        const response = await workflowApi.getWorkflow(workflow.id);
        if (response.isExecuting) {
          setExecutionState(prev => ({
            ...prev,
            status: 'in_progress',
            currentNodeId: response.executionData?.currentNodeId || null,
            executionPath: response.executionData?.executionPath || []
          }));
        } else if (response.executionStatus) {
          setExecutionState(prev => ({
            ...prev,
            status: response.executionStatus.status || 'not_started',
            currentNodeId: response.executionStatus.currentNodeId || null,
            executionPath: response.executionPath || []
          }));
        }
      } catch (err) {
        console.error('Error checking workflow execution state:', err);
      }
    }
  }, [workflow?.id]);

  // Add effect to restore execution state when workflow is loaded
  useEffect(() => {
    if (workflow?.id) {
      checkExecutionState();
    }
  }, [workflow, checkExecutionState]);

  // Connect to WebSocket when component mounts
  useEffect(() => {
    console.log('Initializing WebSocket connection');
    const socket = socketService.connect();

    // Listen for workflow updates
    socketService.onWorkflowUpdate((updatedWorkflow) => {
      console.log('Received workflow update:', updatedWorkflow);
      if (workflow && workflow.id === updatedWorkflow.id) {
        setWorkflow(updatedWorkflow);
        // Update execution state from the workflow
        if (updatedWorkflow.executionStatus) {
          setExecutionState({
            status: updatedWorkflow.executionStatus.status || 'not_started',
            currentNodeId: updatedWorkflow.executionStatus.currentNodeId,
            pendingTaskId: null,
            executionPath: updatedWorkflow.executionPath || []
          });
        }
      }
    });

    // Listen for workflow progress
    const progressListener = (progress) => {
      if (workflow && progress.workflowId === workflow.id) {
        console.log('Received workflow progress:', progress);
        
        switch (progress.status) {
          case 'started':
            setExecutionState(prev => ({
              ...prev,
              status: 'in_progress',
              currentNodeId: null,
              pendingTaskId: null
            }));
            break;
            
          case 'executing':
            setExecutionState(prev => ({
              ...prev,
              status: 'in_progress',
              currentNodeId: progress.nodeId,
              pendingTaskId: null
            }));
            break;
            
          case 'nodeCompleted':
            setExecutionState(prev => ({
              ...prev,
              currentNodeId: null,
              executionPath: [...prev.executionPath, progress.nodeId]
            }));
            break;
            
          case 'completed':
            setExecutionState(prev => ({
              ...prev,
              status: 'completed',
              currentNodeId: null,
              pendingTaskId: null
            }));
            break;
            
          case 'error':
            console.error('Workflow execution error:', progress.message);
            setError(progress.message);
            setExecutionState(prev => ({
              ...prev,
              status: 'not_started',
              currentNodeId: null,
              pendingTaskId: null
            }));
            break;
        }
      }
    };

    // Add the progress listener
    socketService.addListener('workflowProgress', progressListener);

    // Register for reconnection events
    const unregisterReconnect = socketService.onReconnect(() => {
      console.log('Socket reconnected, checking workflow state');
      checkExecutionState();
    });

    // Cleanup function
    return () => {
      console.log('Cleaning up WebSocket listeners');
      socketService.offWorkflowUpdate();
      socketService.removeListener('workflowProgress', progressListener);
      unregisterReconnect();
    };
  }, [workflow?.id, checkExecutionState]);

  // Load workflow
  const loadWorkflow = useCallback(async (workflowId) => {
    try {
      setLoading(true);
      const data = await workflowApi.getWorkflow(workflowId);
      setWorkflow(data);
      // Set initial execution state
      setExecutionState({
        status: data.executionStatus || 'not_started',
        currentNodeId: data.currentNodeId,
        pendingTaskId: null,
        executionPath: data.executionPath || []
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Save workflow to backend
  const saveWorkflow = useCallback(async (workflowData) => {
    try {
      setLoading(true);
      setError(null);
      let savedWorkflow;
      
      if (workflowData._id) {
        savedWorkflow = await workflowApi.updateWorkflow(workflowData._id, workflowData);
      } else {
        savedWorkflow = await workflowApi.createWorkflow(workflowData);
      }

      setWorkflow(savedWorkflow);
      socketService.emitWorkflowUpdate(savedWorkflow);
      return savedWorkflow;
    } catch (err) {
      setError(err.message || 'Failed to save workflow');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete workflow
  const deleteWorkflow = useCallback(async (id) => {
    try {
      setLoading(true);
      setError(null);
      await workflowApi.deleteWorkflow(id);
      setWorkflow(null);
    } catch (err) {
      setError(err.message || 'Failed to delete workflow');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update workflow state locally
  const updateWorkflow = useCallback((updates) => {
    setWorkflow((prev) => ({
      ...prev,
      ...updates,
    }));
  }, []);

  // Execute workflow
  const executeWorkflow = useCallback(async (workflowId) => {
    try {
      console.log('Starting workflow execution:', workflowId);
      setLoading(true);
      setError(null);
      
      // Update execution state to in_progress immediately
      setExecutionState(prev => ({
        ...prev,
        status: 'in_progress'
      }));
      
      // Call backend to start execution
      const result = await workflowApi.executeWorkflow(workflowId);
      console.log('Execution started:', result);
      
      // If there's a pending task, update the execution state
      if (result.pendingTaskId) {
        setExecutionState(prev => ({
          ...prev,
          status: 'paused',
          pendingTaskId: result.pendingTaskId
        }));
      }
    } catch (err) {
      console.error('Execution error:', err);
      setError(err.message || 'Failed to execute workflow');
      setLoading(false);
      // Reset execution state on error
      setExecutionState(prev => ({
        ...prev,
        status: 'not_started',
        currentNodeId: null,
        pendingTaskId: null
      }));
    }
  }, []);

  return {
    workflow,
    loading,
    error,
    executionState,
    loadWorkflow,
    saveWorkflow,
    deleteWorkflow,
    updateWorkflow,
    executeWorkflow,
  };
}; 