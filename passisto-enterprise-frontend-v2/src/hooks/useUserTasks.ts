import { useState, useEffect, useCallback } from 'react';
import { userTaskApi, UserTask } from '../services/userTaskApi';
import { useUser } from "@clerk/nextjs";
import { toast } from './use-toast';
import socketService from '../services/socket';
import { useBackendUser } from './useBackendUser';

export const useUserTasks = () => {
  const [tasks, setTasks] = useState<UserTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUser();
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Fetch assigned tasks
  const fetchTasks = useCallback(async () => {
    if (!user || (backendUserLoading && !backendUser)) return;

    try {
      setLoading(true);
      setError(null);
      const assignedTasks = await userTaskApi.getAssignedTasks();
      setTasks(assignedTasks);
    } catch (err: any) {
      console.error('Error fetching assigned tasks:', err);
      setError(err.message || 'Failed to fetch assigned tasks');
      toast({
        title: 'Error',
        description: 'Failed to fetch your assigned tasks',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user, backendUser, backendUserLoading]);

  // Complete a task
  const completeTask = useCallback(async (workflowRunId: string, nodeId: string, taskData: any = {}) => {
    if (!user || !backendUser) return;

    try {
      // Use the socket-based method for real-time updates with backend userId
      await userTaskApi.completeTaskWithSocket(workflowRunId, nodeId, backendUser.userId, taskData);

      // Update the local state
      setTasks(prevTasks =>
        prevTasks.filter(task =>
          !(task.workflowRunId === workflowRunId && task.nodeId === nodeId)
        )
      );

      toast({
        title: 'Success',
        description: 'Task completed successfully',
      });

      return true;
    } catch (err: any) {
      console.error('Error completing task:', err);
      toast({
        title: 'Error',
        description: err.message || 'Failed to complete task',
        variant: 'destructive',
      });
      return false;
    }
  }, [user, backendUser]);

  // Set up socket listeners for real-time updates
  useEffect(() => {
    if (!user || !backendUser) return;

    // Initial fetch
    fetchTasks();

    // Connect to socket
    const socket = socketService.connect();

    // Listen for new task assignments
    const handleNewTask = (data: any) => {
      if (data.assigneeId === backendUser.userId) {
        // Refresh the task list when a new task is assigned
        fetchTasks();

        // Show a notification
        toast({
          title: 'New Task Assigned',
          description: `You have been assigned a new task: ${data.message}`,
        });
      }
    };

    // Listen for workflow status changes
    const handleWorkflowUpdate = (data: any) => {
      // Refresh tasks when any workflow status changes
      fetchTasks();
    };

    // Add listeners
    socket.on('nodeRunProgress', handleNewTask);
    socket.on('workflowRunProgress', handleWorkflowUpdate);

    // Clean up
    return () => {
      socket.off('nodeRunProgress', handleNewTask);
      socket.off('workflowRunProgress', handleWorkflowUpdate);
    };
  }, [user, backendUser, fetchTasks]);

  return {
    tasks,
    loading,
    error,
    fetchTasks,
    completeTask
  };
};
