import { useState, useEffect, useCallback } from 'react';
import api from '@/config/axios';
import { useAuth, useUser } from "@clerk/nextjs";
import { toast } from './use-toast';
import { GET_CURRENT_USER } from '@/utils/routes';

export interface BackendUser {
  userId: string;
  companyId: string;
  fullName: string;
  companyName: string;
  permissions: string[];
}

export const useBackendUser = () => {
  const { getToken } = useAuth()
  const [backendUser, setBackendUser] = useState<BackendUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user: clerkUser, isLoaded: isClerkLoaded } = useUser();

  const fetchBackendUser = useCallback(async () => {
    if (!clerkUser || !isClerkLoaded) return;
    
    try {
      setLoading(true);
      setError(null);
      const token = await getToken()
      const response = await api.get(GET_CURRENT_USER,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log("user session", response.data);
      setBackendUser(response.data);
    } catch (err: any) {
      console.error('Error fetching backend user:', err);
      setError(err.message || 'Failed to fetch user information');
      toast({
        title: 'Error',
        description: 'Failed to fetch your user information',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [clerkUser, isClerkLoaded]);

  useEffect(() => {
    if (isClerkLoaded && clerkUser) {
      fetchBackendUser();
    } else if (isClerkLoaded && !clerkUser) {
      setLoading(false);
    }
  }, [clerkUser, isClerkLoaded, fetchBackendUser]);

  return {
    backendUser,
    loading,
    error,
    fetchBackendUser
  };
};
