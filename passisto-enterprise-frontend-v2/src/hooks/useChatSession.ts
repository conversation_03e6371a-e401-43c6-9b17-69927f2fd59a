import { useState, useEffect, useCallback } from 'react';
import { chatbot<PERSON>pi, ChatMessage, ChatSession } from '@/services/chatbotApi';
import { useBackendUser } from './useBackendUser';
import { useChatSettings } from '@/context/ChatSettingsContext';
import { useTranslations } from 'next-intl'; // Import useTranslations

export const useChatSession = (initialSessionId?: string) => {
  const t = useTranslations('Chatbot'); // Initialize useTranslations with the 'Chatbot' namespace

  const [sessionId, setSessionId] = useState<string>(initialSessionId ?? chatbotApi.createNewSession());
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [sendingMessage, setSendingMessage] = useState<boolean>(false);
  const [switchingSession, setSwitchingSession] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { backendUser } = useBackendUser();
  const { getSelectedAliasIds } = useChatSettings();

  // Fetch chat history for the current session
  const fetchChatHistory = useCallback(async () => {
    if (!sessionId) return;
    if (!backendUser?.userId) {
      console.log(t('logs.waitingForUserHistory'));
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const history = await chatbotApi.getChatHistory(sessionId, backendUser.userId);
      if (history && history.length > 0) {
        setMessages(history);
      } else {
        setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
      }
    } catch (err: any) {
      console.error('Error fetching chat history:', err);
      if (err.response?.status === 404) {
        console.log(t('logs.newSessionDetected'));
        setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
        setError(null);
      } else {
        let errorMsg = t('errors.loadHistory');
        if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
          errorMsg = t('errors.connectionRefused');
        } else if (err.message?.includes('Cross-Origin Request Blocked') || err.message?.includes('CORS')) {
          errorMsg = t('errors.corsError');
        } else if (err.response?.data?.message) {
          errorMsg = t('errors.serverError', { message: err.response.data.message });
        }
        setError(errorMsg);
        setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
      }
    } finally {
      setLoading(false);
    }
  }, [sessionId, backendUser, t]); // Add 't' to dependency array

  // Initialize with a welcome message when component mounts
  useEffect(() => {
    setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
  }, [t]);

  // Fetch chat history when session ID changes
  useEffect(() => {
    if (sessionId) {
      fetchChatHistory();
    }
  }, [sessionId, fetchChatHistory]); // Add fetchChatHistory to dependency array

  // Fetch all chat sessions for the current user
  const fetchUserSessions = useCallback(async () => {
    if (!backendUser?.userId) {
      console.log(t('logs.waitingForUser'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const userSessions = await chatbotApi.getUserSessions(backendUser.userId);
      setSessions(userSessions);
    } catch (err: any) {
      console.error('Error fetching user sessions:', err);
      let errorMsg = t('errors.loadSessions');

      if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
        errorMsg = t('errors.connectionRefused');
      } else if (err.message?.includes('Cross-Origin Request Blocked') || err.message?.includes('CORS')) {
        errorMsg = t('errors.corsError');
      } else if (err.response?.status === 404) {
        // errorMsg = t('errors.noSessionsFound');
        errorMsg = "";
        setSessions([]);
        return
      } else if (err.response?.data?.message) {
        errorMsg = t('errors.serverError', { message: err.response.data.message });
      }

      setError(errorMsg);
      setSessions([]);
    } finally {
      setLoading(false);
    }
  }, [backendUser, t]); // Add 't' to dependency array

  // Send a message to the chatbot
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    if (!backendUser?.userId) {
      const errorMsg = t('errors.userNotFound');
      setError(errorMsg);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `Sorry, I encountered an error: ${errorMsg}`
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }
    setSendingMessage(true);
    setError(null);
    const currentSessionId = sessionId ?? chatbotApi.createNewSession();
    if (currentSessionId !== sessionId) {
      setSessionId(currentSessionId);
    }
    const userMessage: ChatMessage = { role: 'user', content: message };
    setMessages(prev => [...prev, userMessage]);
    try {
      const selectedAliasIds = getSelectedAliasIds();
      const aliasParam = selectedAliasIds;
      const response = await chatbotApi.sendMessage(
        message,
        currentSessionId,
        backendUser.userId,
        aliasParam
      );
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.answer,
        sources: response.sources && response.sources.length > 0 ? response.sources : undefined
      };
      setMessages(prev => [...prev, assistantMessage]);
      fetchUserSessions();
    } catch (err: any) {
      console.error('Error sending message:', err);
      let errorMsg = t('errors.sendMessageFailed');
      if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
        errorMsg = t('errors.connectionRefused');
      } else if (err.message?.includes('Cross-Origin Request Blocked') || err.message?.includes('CORS')) {
        errorMsg = t('errors.corsError');
      } else if (err.response?.status === 404) {
        errorMsg = t('errors.apiEndpointNotFound');
      } else if (err.response?.status === 401 || err.response?.status === 403) {
        errorMsg = t('errors.authenticationError');
      } else if (err.response?.data?.message) {
        errorMsg = t('errors.serverError', { message: err.response.data.message });
      }
      setError(errorMsg);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `${errorMsg} \n ${t('errors.pleaseTryAgain')}`
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setSendingMessage(false);
    }
  }, [sessionId, backendUser, fetchUserSessions, getSelectedAliasIds, t]); // Add 't' to dependency array

  // Create a new chat session
  const createNewChat = useCallback(() => {
    try {
      setSwitchingSession(true);
      const newSessionId = chatbotApi.createNewSession();
      setSessionId(newSessionId);
      setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
      setError(null);
      fetchUserSessions();
      console.log(t('logs.newSessionCreated', { sessionId: newSessionId }));
      return newSessionId;
    } catch (err) {
      console.error('Error creating new chat session:', err);
      setError(t('errors.createSessionFailed'));
      setSwitchingSession(false);
    }
  }, [fetchUserSessions, t]); // Add 't' to dependency array

  // Switch to an existing chat session
  const switchSession = useCallback((id: string) => {
    setSwitchingSession(true);
    setSessionId(id);
    setError(null);
    setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
    setTimeout(() => {
      setSwitchingSession(false);
    }, 300);
  }, [t]); // Add 't' to dependency array

  // Delete a chat session
  const deleteSession = useCallback(async (id: string) => {
    if (!backendUser?.userId) {
      setError(t('errors.userNotFound'));
      return { success: false };
    }

    setLoading(true);
    setError(null);

    try {
      await chatbotApi.deleteSession(id, backendUser.userId);
      if (id === sessionId) {
        const newId = chatbotApi.createNewSession();
        setSessionId(newId);
        setMessages([{ role: 'assistant', content: t('welcomeMessage') }]);
        await fetchUserSessions();
        return { success: true, newSessionId: newId };
      }
      await fetchUserSessions();
      return { success: true };
    } catch (err: any) {
      console.error('Error deleting session:', err);
      let errorMsg = t('errors.deleteSessionFailed');
      if (err.code === 'ECONNREFUSED' || err.message?.includes('Network Error')) {
        errorMsg = t('errors.connectionRefused');
      } else if (err.response?.status === 404) {
        await fetchUserSessions();
        return { success: true };
      } else if (err.response?.data?.message) {
        errorMsg = t('errors.serverError', { message: err.response.data.message });
      }
      setError(errorMsg);
      return { success: false };
    } finally {
      setLoading(false);
    }
  }, [sessionId, backendUser, fetchUserSessions, t]); // Add 't' to dependency array

  return {
    sessionId,
    messages,
    sessions,
    loading,
    sendingMessage,
    switchingSession,
    error,
    sendMessage,
    createNewChat,
    switchSession,
    deleteSession,
    fetchChatHistory,
    fetchUserSessions
  };
};