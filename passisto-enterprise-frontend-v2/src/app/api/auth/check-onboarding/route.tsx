import { NextRequest, NextResponse } from "next/server"
import { getAuth } from "@clerk/nextjs/server"

export async function POST(req: NextRequest) {
    const { userId } = getAuth(req)
    // get token 
    const token = req.headers.get('authorization')?.split('Bearer ')[1]

    if (!userId) {
        console.log("Error checking onboarding from frontend API")
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        // const body = await req.json()
        // const { onboarding } = body

        // if (!onboarding) {
        //     return NextResponse.json({ error: "Onboarding status is required" }, { status: 400 })
        // }

        // userRouter.post("/check-onboarding", authMiddleware, checkOnboarding);
        // make a request to db to check if user has completed onboarding using axios
        const response = await fetch("http:/nginx/api/v1/users/check-onboarding", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
            }
        }) 

        const hasCompletedOnboarding = await response.json()
        console.log("Onboarding response:", hasCompletedOnboarding);

        return NextResponse.json({
            userId,
            hasCompletedOnboarding
        })

    } catch (error) {
        console.log("Error checking onboarding from frontend API", error)
        return NextResponse.json({ error: "Invalid request body" }, { status: 400 })
    }
}