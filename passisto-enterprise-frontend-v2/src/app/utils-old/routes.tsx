// Define API routes as constants
export const API_BASE_URL = 'api/v1';

// User-related routes
export const USER_ROUTE = `${API_BASE_URL}/users`;
export const ONBOARD_ROUTE = `${USER_ROUTE}/onboard`;

// Authentication routes
export const AUTH_ROUTE = `${API_BASE_URL}/auth`;

// AI Agent routes
export const AI_AGENT_ROUTE = `${API_BASE_URL}/ai-agent`;
export const FORM_BUILDER_ENHANCE_DESCRIPTION = `${API_BASE_URL}/form-builder/enhance-description`;
export const FORM_BUILDER_GENERATE_FORM = `${API_BASE_URL}/form-builder/generate-form`;

export const EMAIL_BUILDER_ENHANCE_DESCRIPTION = `${API_BASE_URL}/email-builder/enhance-description`;
export const EMAIL_BUILDER_GENERATE_EMAIL = `${API_BASE_URL}/email-builder/generate-email`;
export const EMAIL_BUILDER_GET_BY_ID = (emailId:String) => `${API_BASE_URL}/email-builder/emails/${emailId}`;
export const EMAIL_BUILDER_GET_ALL =  `${API_BASE_URL}/email-builder/emails`;
export const EMAIL_BUILDER_EDIT = (emailId:String) =>  `${API_BASE_URL}/email-builder/emails/${emailId}`;
export const EMAIL_BUILDER_SEND =  `${API_BASE_URL}/email-builder/emails/send`;

// Integration routes
export const JIRA_INTEGRATION = `${API_BASE_URL}/integrations/jira`
export const FTP_INTEGRATION = `${API_BASE_URL}/integrations/ftp`
export const WEB_INTEGRATION = `${API_BASE_URL}/integrations/web`
export const ALL_INTEGRATIONS = `${API_BASE_URL}/integrations`