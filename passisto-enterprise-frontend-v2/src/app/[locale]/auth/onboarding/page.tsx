"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { Check, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth, useUser } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { ONBOARD_ROUTE } from "@/utils/routes";
import { useTranslations } from "next-intl";
import { AxiosResponse, AxiosError } from "axios";

// Type definitions
interface OnboardingFormData {
  firstName: string;
  lastName: string;
  bio: string;
  company: string;
  role: string;
  experience: string;
  interests: string;
  linkedin: string;
}

interface OnboardingResponse {
  message: string;
  user?: any;
}

interface ErrorResponse {
  error: string;
}

interface StepType {
  id: number;
  name: string;
}

type ExperienceLevel = "junior" | "mid" | "senior" | "lead";

const OnboardingForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const router = useRouter();
  const { getToken } = useAuth();
  const { isSignedIn } = useUser();
  const t = useTranslations("OnboardingPage");

  const steps: StepType[] = [
    { id: 1, name: t("personal-info") },
    { id: 2, name: t("professional") },
    { id: 3, name: t("preferences") },
  ];

  const [formData, setFormData] = useState<OnboardingFormData>({
    firstName: "",
    lastName: "",
    bio: "",
    company: "",
    role: "",
    experience: "",
    interests: "",
    linkedin: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ): void => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSelectChange = (value: ExperienceLevel): void => {
    setFormData((prev) => ({ ...prev, experience: value }));
    if (error) setError(null);
  };

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        return !!(formData.firstName.trim() && formData.lastName.trim());
      case 2:
        return !!(formData.company.trim() && formData.role.trim() && formData.experience);
      case 3:
        return true; // Step 3 fields are optional
      default:
        return false;
    }
  };

  const getStepErrorMessage = (): string => {
    switch (currentStep) {
      case 1:
        return t("please-fill-required-fields-first-last-name");
      case 2:
        return t("please-fill-required-fields-company-role-experience");
      default:
        return t("please-fill-required-fields");
    }
  };

  const handleSubmit = async (): Promise<void> => {
    if (!isSignedIn) {
      setError(t("please-sign-in-to-continue"));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get fresh token
      const token = await getToken({ skipCache: true });
      
      if (!token) {
        setError(t("authentication-failed-please-try-again"));
        setLoading(false);
        return;
      }

      console.log("Submitting onboarding data:", formData);

      const response: AxiosResponse<OnboardingResponse> = await axiosInstance.post(
        ONBOARD_ROUTE,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 200) {
        console.log("Onboarding successful:", response.data);
        router.push("/enterprise/ai-agents/email-builder");
      } else {
        console.error("Unexpected response status:", response.status);
        setError(t("something-went-wrong-please-try-again"));
        setLoading(false);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      
      if (error instanceof Error) {
        const axiosError = error as AxiosError<ErrorResponse>;
        
        if (axiosError.response) {
          const { status, data } = axiosError.response;
          
          switch (status) {
            case 400:
              setError(t("invalid-form-data-please-check-your-input"));
              break;
            case 401:
              setError(t("authentication-failed-please-sign-in-again"));
              break;
            case 403:
              setError(t("access-denied-please-contact-support"));
              break;
            case 500:
              setError(t("server-error-please-try-again-later"));
              break;
            default:
              setError(data?.error || t("something-went-wrong-please-try-again"));
          }
        } else if (axiosError.request) {
          setError(t("network-error-please-check-connection"));
        } else {
          setError(t("something-went-wrong-please-try-again"));
        }
      } else {
        setError(t("unexpected-error-occurred"));
      }
      
      setLoading(false);
    }
  };

  const handleNext = (): void => {
    if (currentStep < steps.length) {
      if (!validateCurrentStep()) {
        setError(getStepErrorMessage());
        return;
      }
      setCurrentStep(currentStep + 1);
      setError(null);
    } else {
      // Final step - submit form
      if (!validateCurrentStep()) {
        setError(getStepErrorMessage());
        return;
      }
      handleSubmit();
    }
  };

  const handlePrevious = (): void => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setError(null);
    }
  };

  const isNextDisabled = (): boolean => {
    return loading || !validateCurrentStep();
  };

  useEffect(() => {
    console.log("OnboardingForm mounted");
    
    // Clear any existing errors when component mounts
    setError(null);
  }, []);

  return (
    <div className="mx-auto max-w-2xl px-4 py-16">
      {/* Progress Steps */}
      <div className="mb-12">
        <div className="flex items-center justify-center gap-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                  currentStep >= step.id
                    ? "border-primary bg-primary text-primary-foreground"
                    : "border-muted-foreground text-muted-foreground"
                }`}
              >
                {currentStep > step.id ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <span>{step.id}</span>
                )}
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`mx-4 h-0.5 w-12 ${
                    currentStep > step.id ? "bg-primary" : "bg-border"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div className="mt-4 text-center">
          <h1 className="text-2xl font-bold">{t("complete-your-profile")}</h1>
          <p className="mt-2 text-sm text-muted-foreground">
            {t("step")} {currentStep} {t("of")} {steps.length}
          </p>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 rounded-md border border-red-200 bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {t("error")}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Steps */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="firstName">
                    {t("first-name")} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="firstName"
                    placeholder={t("john")}
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                    className={!formData.firstName.trim() && error ? "border-red-500" : ""}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="lastName">
                    {t("last-name")} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="lastName"
                    placeholder={t("doe")}
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                    className={!formData.lastName.trim() && error ? "border-red-500" : ""}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="bio">{t("bio")}</Label>
                  <Textarea
                    id="bio"
                    placeholder={t("tell-us-about-yourself")}
                    className="min-h-[100px]"
                    value={formData.bio}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="company">
                    {t("company")} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="company"
                    placeholder={t("acme-inc")}
                    value={formData.company}
                    onChange={handleChange}
                    required
                    className={!formData.company.trim() && error ? "border-red-500" : ""}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="role">
                    {t("role")} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="role"
                    placeholder={t("software-engineer")}
                    value={formData.role}
                    onChange={handleChange}
                    required
                    className={!formData.role.trim() && error ? "border-red-500" : ""}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="experience">
                    {t("experience-level")} <span className="text-red-500">*</span>
                  </Label>
                  <Select 
                    onValueChange={handleSelectChange}
                    value={formData.experience}
                  >
                    <SelectTrigger className={!formData.experience && error ? "border-red-500" : ""}>
                      <SelectValue placeholder={t("select-experience-level")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="junior">
                        {t("junior-0-2-years")}
                      </SelectItem>
                      <SelectItem value="mid">
                        {t("mid-level-3-5-years")}
                      </SelectItem>
                      <SelectItem value="senior">
                        {t("senior-5-years")}
                      </SelectItem>
                      <SelectItem value="lead">
                        {t("lead-architect")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="interests">{t("areas-of-interest")}</Label>
                  <Textarea
                    id="interests"
                    placeholder={t("e-g-web-development-ai-ml")}
                    className="min-h-[100px]"
                    value={formData.interests}
                    onChange={handleChange}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="linkedin">
                    {t("linkedin-profile-optional")}
                  </Label>
                  <Input
                    id="linkedin"
                    placeholder="https://linkedin.com/in/username"
                    value={formData.linkedin}
                    onChange={handleChange}
                    type="url"
                  />
                </div>
              </div>
            </div>
          )}

          <div className="mt-8 flex justify-between">
            <Button
              onClick={handlePrevious}
              variant="outline"
              className="flex items-center gap-2"
              disabled={currentStep === 1 || loading}
            >
              <ChevronLeft className="h-4 w-4" /> {t("previous")}
            </Button>

            <Button
              onClick={handleNext}
              className="flex items-center gap-2"
              disabled={isNextDisabled()}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {currentStep === steps.length ? t("complete") : t("next")}
              {!loading && <ChevronRight className="h-4 w-4" />}
            </Button>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default OnboardingForm;