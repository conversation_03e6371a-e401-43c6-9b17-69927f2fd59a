import { Clock, Mail, Phone, Settings, AlertTriangle } from "lucide-react"

export default function MaintenancePage() {
  return (
    <div className="h-screen bg-slate-900 text-white flex items-center justify-center overflow-hidden">
      <div className="max-w-4xl w-full px-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-amber-500/20 rounded-full mb-6">
            <Settings className="w-8 h-8 text-amber-400 animate-spin" style={{ animationDuration: "3s" }} />
          </div>
          <h1 className="text-5xl font-light mb-4 tracking-tight">System Maintenance</h1>
          <p className="text-xl text-slate-300 font-light">Enhancing our platform for optimal performance</p>
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {/* Status Card */}
          <div className="bg-slate-800/50 backdrop-blur border border-slate-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-5 h-5 text-amber-400 mr-3" />
              <h3 className="text-lg font-medium">Current Status</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-slate-300">Service</span>
                <span className="px-3 py-1 bg-red-500/20 text-red-400 rounded-full text-sm font-medium">Offline</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-300">Progress</span>
                <span className="text-amber-400 font-medium">45%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-amber-500 to-amber-400 h-2 rounded-full transition-all duration-1000"
                  style={{ width: "45%" }}
                ></div>
              </div>
            </div>
          </div>

          {/* Timeline Card */}
          <div className="bg-slate-800/50 backdrop-blur border border-slate-700 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Clock className="w-5 h-5 text-blue-400 mr-3" />
              <h3 className="text-lg font-medium">Maintenance Window</h3>
            </div>
            <div className="space-y-3 text-sm">
              <div>
                <p className="text-slate-400 mb-1">Started</p>
                <p className="font-medium">Jun 7, 2025 • 02:00 UTC</p>
              </div>
              <div>
                <p className="text-slate-400 mb-1">Expected Completion</p>
                <p className="font-medium">Jun 9, 2025 • 06:00 UTC</p>
              </div>
              <div className="pt-2 border-t border-slate-700">
                <p className="text-amber-400 font-medium">~48 hours remaining</p>
              </div>
            </div>
          </div>

          {/* Contact Card */}
          <div className="bg-slate-800/50 backdrop-blur border border-slate-700 rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Emergency Support</h3>
            <div className="space-y-3">
              <div className="flex items-center text-sm">
                <Mail className="w-4 h-4 text-slate-400 mr-3" />
                <div>
                  <p className="font-medium"><EMAIL></p>
                  <p className="text-slate-400">Email Support</p>
                </div>
              </div>
              {/* <div className="flex items-center text-sm">
                <Phone className="w-4 h-4 text-slate-400 mr-3" />
                <div>
                  <p className="font-medium">+****************</p>
                  <p className="text-slate-400">24/7 Hotline</p>
                </div>
              </div> */}
            </div>
          </div>
        </div>

        {/* Updates Section */}
        <div className="bg-slate-800/30 backdrop-blur border border-slate-700 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-medium mb-4">System Improvements</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span className="text-slate-300">Security infrastructure upgrade</span>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span className="text-slate-300">Database optimization & migration</span>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span className="text-slate-300">Performance enhancements</span>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-slate-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span className="text-slate-300">Feature deployments</span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center">
          <p className="text-slate-400 text-sm mb-2">We appreciate your patience during this scheduled maintenance</p>
          <div className="flex items-center justify-center text-xs text-slate-500">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
            <span>Real-time updates available at status.company.com</span>
          </div>
        </div>
      </div>
    </div>
  )
}
