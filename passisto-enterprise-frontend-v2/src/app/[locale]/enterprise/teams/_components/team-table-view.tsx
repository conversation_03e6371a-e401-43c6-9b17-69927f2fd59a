"use client"

import { useRouter } from "next/navigation"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Pencil, Trash2, UsersRound, Eye, Database, UserPlus } from "lucide-react"
import { Team } from "@/store/slices/teamSlice"
import { teamPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { BackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"

interface TeamTableViewProps {
  teams: Team[]
  onDeleteTeam: (team: Team) => void
  backendUser: BackendUser | null
}

export function TeamTableView({ teams, onDeleteTeam, backendUser }: TeamTableViewProps) {
  const t = useTranslations()
  const router = useRouter()

  return (
    <div className="w-full border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('team-name')}</TableHead>
            <TableHead>{t('description')}</TableHead>
            <TableHead>{t('members')}</TableHead>
            <TableHead>{t('status')}</TableHead>
            <TableHead className="w-[120px]">{t('actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {teams.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                {t('no-teams-found-0')}
              </TableCell>
            </TableRow>
          ) : (
            teams?.map((team) => (
              <TableRow key={team.id}>
                <TableCell className="font-medium">{team.name}</TableCell>
                <TableCell className="max-w-xs">
                  <div className="truncate">{team.description}</div>
                </TableCell>
                <TableCell>{team.memberCount}</TableCell>
                <TableCell>
                  <Badge variant={team && team.memberCount && team?.memberCount > 0 ? "default" : "outline"}>
                    {team && team.memberCount && team?.memberCount > 0 ? t('active') : t('empty')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {teamPermissions.canView(backendUser?.permissions ?? []) && (
                      <Button variant="ghost" size="icon" onClick={() => router.push(`/enterprise/teams/${team.id}/view`)}>
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">{t('view-details-0')}</span>
                      </Button>
                    )}
                    
                    {teamPermissions.canUpdate(backendUser?.permissions ?? []) && (
                      <Button variant="ghost" size="icon" onClick={() => router.push(`/enterprise/teams/${team.id}`)}>
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">{t('edit')}</span>
                      </Button>
                    )}
                    
                    {teamPermissions.canAssignUser(backendUser?.permissions ?? []) && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => router.push(`/enterprise/teams/${team.id}/members`)}
                      >
                        <UserPlus className="h-4 w-4" />
                        <span className="sr-only">{t('manage-members-0')}</span>
                      </Button>
                    )}
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">{t('open-menu-0')}</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>{t('actions')}</DropdownMenuLabel>
                        
                        {teamPermissions.canView(backendUser?.permissions ?? []) && (
                          <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/view`)}>
                            <Eye className="mr-2 h-4 w-4" />
                            {t('view-details')}
                          </DropdownMenuItem>
                        )}
                        
                        {teamPermissions.canUpdate(backendUser?.permissions ?? []) && (
                          <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/edit`)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            {t('edit')}
                          </DropdownMenuItem>
                        )}
                        
                        {teamPermissions.canAssignUser(backendUser?.permissions ?? []) && (
                          <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/members`)}>
                            <UsersRound className="mr-2 h-4 w-4" />
                            {t('members')}
                          </DropdownMenuItem>
                        )}
                        
                        {integrationPermissions.canAssignToGroup(backendUser?.permissions ?? []) && (
                          <DropdownMenuItem onClick={() => router.push(`/enterprise/teams/${team.id}/integrations`)}>
                            <Database className="mr-2 h-4 w-4" />
                            {t('assign-integration')}
                          </DropdownMenuItem>
                        )}
                        
                        {teamPermissions.canDelete(backendUser?.permissions ?? []) && (
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => onDeleteTeam(team)}
                            disabled={team.name === "Company"}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {team.name === "Company" ? t('cannot-delete-default-team') : t('delete')}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}

