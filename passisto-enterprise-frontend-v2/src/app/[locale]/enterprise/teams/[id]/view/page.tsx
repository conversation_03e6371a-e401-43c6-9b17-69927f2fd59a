"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { ArrowLeft, Edit, Shield, Users, Calendar, Info, Database } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { fetchTeamWithMembers } from "@/store/slices/teamSlice"
import { fetchRolesPermissions } from "@/store/slices/rolePermissionSlice"
import { useAuth } from "@clerk/nextjs"
import { Link } from '@/i18n/nvigation';
import { Separator } from "@/components/ui/separator"
import { Breadcrumb } from "@/components/breadcrumb"
import { teamPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"

export default function TeamDetailsPage() {
  const t  = useTranslations()
  const router = useRouter()
  const { id } = useParams<{ id: string }>()
  const dispatch = useAppDispatch()
  const { getToken } = useAuth()
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  

// Add permission check at the beginning of the component:
useEffect(() => {
  // Only check permissions after backendUser has loaded
  if (!backendUserLoading && backendUser) {
    // Redirect if user doesn't have permission to view teams
    if (!teamPermissions.canView(backendUser?.permissions ?? [])) {
      toast.error(t('permission-denied'), {
        description: t('you-dont-have-permission-to-view-team-details')
      });
      router.push("/enterprise/teams");
    }
  }
}, [backendUser, backendUserLoading, router]);  
  // Get team from Redux store
  const { currentTeam, status } = useAppSelector((state) => state.teams)
  // Get permissions from rolePermissionSlice
  const { permissionsByCategory, loading: permissionsLoading } = useAppSelector((state) => state.rolePermission)

  // Fetch team data and permissions
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken()
        
        // Fetch team with members
        await dispatch(fetchTeamWithMembers({ teamId: id as string, token: token! })).unwrap()
        
        // Fetch roles and permissions
        await dispatch(fetchRolesPermissions(token!)).unwrap()
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error(t('error-loading-data'), {
          description: t('there-was-an-error-loading-the-team-data')
        })
        router.push("/enterprise/teams")
      }
    }

    if (id) {
      fetchData()
    }
  }, [id, dispatch, getToken, router])

  // Format permission name for display
  const formatPermissionName = (permission: string) => {
    return permission
      .replace("CAN_", "")
      .split("_")
      .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ")
  }

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  if (status === 'loading' || permissionsLoading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t('loading-team-details')}</p>
        </div>
      </div>
    )
  }

  if (!currentTeam) return null

  return (
    <div className="w-full p-4">
      <div className="flex items-center justify-between mb-6">
        <div className="space-y-2">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.push("/enterprise/teams")}
            className="gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back-to-teams')}
          </Button>
          
          <Breadcrumb 
            items={[
              { label: t('teams'), href: "/enterprise/teams" },
              { label: currentTeam.name }
            ]}
            className="ml-1"
          />
        </div>
        
        <div className="flex gap-2">
          {teamPermissions.canAssignUser(backendUser?.permissions ?? []) && (
            <Button 
              variant="outline" 
              onClick={() => router.push(`/enterprise/teams/${id}/members`)}
            >
              <Users className="mr-2 h-4 w-4" />
              {t('manage-members')}
            </Button>
          )}
          
          {teamPermissions.canUpdate(backendUser?.permissions ?? []) && (
            <Button 
              onClick={() => router.push(`/enterprise/teams/${id}`)}
            >
              <Edit className="mr-2 h-4 w-4" />
              {t('edit-team-0')}
            </Button>
          )}
          {teamPermissions.canManageTeamIntegration(backendUser?.permissions ?? []) && (
            <Button 
              variant="outline" 
              onClick={() => router.push(`/enterprise/teams/${id}/team-integrations`)}
            >
              <Database className="mr-2 h-4 w-4" />
              {t('view-integrations')}
            </Button>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Team Summary Card */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              {t('team-details-0')}
              <Badge variant="outline" className="ml-2">
                {currentTeam.members?.length || 0} members
              </Badge>
            </CardTitle>
            <CardDescription>{t('team-information-and-statistics')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-xl font-semibold">{currentTeam.name}</h3>
              <p className="text-muted-foreground mt-1">{currentTeam.description || t('no-description-provided')}</p>
            </div>
            
            <Separator />
            
            <div className="space-y-3">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">
                  {currentTeam.members?.length || 0} {t('team-members-1')}
                </span>
              </div>
              
              <div className="flex items-center">
                <Shield className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">
                  {currentTeam.permissions?.length || 0} {t('permissions-assigned')}
                </span>
              </div>
              
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">
                  Created: {formatDate(currentTeam.createdAt)}
                </span>
              </div>
              
              {currentTeam.updatedAt && (
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-sm">
                    {t('last-updated')} {formatDate(currentTeam.updatedAt)}
                  </span>
                </div>
              )}
            </div>
            
            <Separator />
            
            <div className="pt-2">
              <Link href={`/enterprise/teams/${id}/members`}>
                <Button variant="secondary" className="w-full">
                  <Users className="mr-2 h-4 w-4" />
                  {t('view-team-members')}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{t('team-information')}</CardTitle>
            <CardDescription>
              {t('detailed-information-about-this-team')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="permissions">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="permissions">{t('permissions')}</TabsTrigger>
                <TabsTrigger value="activity">{t('recent-activity')}</TabsTrigger>
              </TabsList>
              
              <TabsContent value="permissions" className="space-y-4 pt-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium flex items-center">
                    <Shield className="h-4 w-4 mr-2 text-primary" />
                    {t('team-permissions-3')}
                  </h3>
                  <Badge variant="outline">
                    {currentTeam.permissions?.length || 0} permission{(currentTeam.permissions?.length || 0) !== 1 ? "s" : ""}
                  </Badge>
                </div>

                <p className="text-sm text-muted-foreground">
                  {t('permissions-assigned-to-this-team-are-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users')}
                </p>

                {currentTeam.permissions?.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg bg-muted/10">
                    <Shield className="h-10 w-10 text-muted-foreground mb-2" />
                    <h3 className="text-lg font-medium">{t('no-permissions-assigned')}</h3>
                    <p className="text-sm text-muted-foreground mt-1 mb-4">
                      {t('this-team-doesnt-have-any-permissions-assigned-yet')}
                    </p>
                    <Button 
                      variant="outline" 
                      onClick={() => router.push(`/enterprise/teams/${id}`)}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {t('edit-permissions')}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {permissionsByCategory.map((category) => {
                      // Filter permissions that belong to this category
                      const teamPermissionsInCategory = currentTeam.permissions?.filter(p => 
                        category.permissions.some(cp => 
                          cp.action === p.action || cp.action === p.permissionId
                        )
                      );
                      
                      // Skip categories with no permissions
                      if (!teamPermissionsInCategory?.length) return null;
                      
                      return (
                        <div key={category.category} className="space-y-2">
                          <h4 className="text-sm font-medium">{category.category}</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                            {teamPermissionsInCategory.map((permission) => {
                              const permAction = permission.action || permission.permissionId;
                              return (
                                <div key={permission.id || permAction} className="flex items-center space-x-2">
                                  <Badge variant="outline" className="px-2 py-0.5 text-xs">
                                    {formatPermissionName(permAction)}
                                  </Badge>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="activity" className="pt-4">
                <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg bg-muted/10">
                  <Info className="h-10 w-10 text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">{t('activity-log-coming-soon')}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {t('team-activity-tracking-will-be-available-in-a-future-update')}
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
