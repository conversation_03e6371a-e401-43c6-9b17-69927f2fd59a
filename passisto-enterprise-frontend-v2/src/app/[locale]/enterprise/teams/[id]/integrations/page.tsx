"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle2,
  Database,
  Loader2,
  RefreshCw,
  Server,
  Globe,
  FileCode,
} from "lucide-react"
import { Link } from '@/i18n/nvigation';
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { fetchTeamWithMembers, fetchIntegrations, assignIntegrationToTeam } from "@/store/slices/teamSlice"
import { useAuth } from "@clerk/nextjs"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertD<PERSON>og<PERSON>ooter,
  <PERSON><PERSON>DialogHeader,
  AlertDialog<PERSON><PERSON>le,
} from "@/components/ui/alert-dialog"
import { integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"
// import { useMobile } from "@/hooks/use-mobile"

export default function TeamIntegrationsPage() {
  const { id } = useParams<{ id: string }>()
  const router = useRouter()
  const dispatch = useAppDispatch()
  const { getToken } = useAuth()
  // const isMobile = useMobile()
  const t = useTranslations()

  const { currentTeam, integrations, status } = useAppSelector((state) => state.teams)

  const [processingIntegration, setProcessingIntegration] = useState<string | null>(null)
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; integrationId: string; integrationName: string }>(
    {
      open: false,
      integrationId: "",
      integrationName: "",
    },
  )

  const { backendUser, loading: backendUserLoading } = useBackendUser();

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to assign integrations to teams
      if (!integrationPermissions.canAssignToGroup(backendUser?.permissions ?? [])) {
        toast.error(t('permission-denied'), {
          description: t('you-dont-have-permission-to-assign-integrations-to-teams')
        });
        router.push(`/enterprise/teams/`);
      }
    }
  }, [backendUser, backendUserLoading, router, id]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken()
        if (!token) return

        // Fetch team details
        await dispatch(fetchTeamWithMembers({ teamId: id as string, token })).unwrap()

        // Fetch integrations
        await dispatch(fetchIntegrations(token)).unwrap()
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error(t('failed-to-load-data'))
      }
    }

    fetchData()
  }, [dispatch, id, getToken])

  const handleAssignIntegration = async (integrationId: string) => {
    try {
      setProcessingIntegration(integrationId)

      const token = await getToken()
      if (!token) {
        toast.error(t('authentication-required-0'))
        return
      }

      await dispatch(
        assignIntegrationToTeam({
          integrationId,
          teamId: id as string,
          token,
        }),
      ).unwrap()

      toast.success(t('integration-assigned'), {
        description: t('integration-has-been-successfully-assigned-to-currentteam-name'),
      })
    } catch (error: any) {
      console.error(t('error-assigning-integration'), error)
      toast.error(t('failed-to-assign-integration'), {
        description: error.message || t('an-unexpected-error-occurred'),
      })
    } finally {
      setProcessingIntegration(null)
      setConfirmDialog({ open: false, integrationId: "", integrationName: "" })
    }
  }

  const openConfirmDialog = (integrationId: string, integrationName: string) => {
    setConfirmDialog({
      open: true,
      integrationId,
      integrationName,
    })
  }

  const getIntegrationIcon = (providerType: string) => {
    switch (providerType.toLowerCase()) {
      case "jira":
        return <FileCode className="h-5 w-5 text-blue-500" />
      case "ftp":
        return <Server className="h-5 w-5 text-purple-500" />
      case "web":
        return <Globe className="h-5 w-5 text-green-500" />
      default:
        return <Database className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3 mr-1" /> {t('active')}
          </Badge>
        )
      case "loading":
      case "refreshing":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" /> {t('syncing')}
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" /> {t('error')}
          </Badge>
        )
      default:
        return <Badge variant="outline">{t('unknown')}</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <Link href={`/enterprise/teams/`}>
            <Button variant="outline" size="icon" className="h-9 w-9 rounded-full">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{t('integrations')}</h1>
            <p className="text-muted-foreground mt-1">{t('assign-integrations-to')} {currentTeam?.name}</p>
          </div>
        </div>
      </div>

      {status === "loading" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="overflow-hidden border border-border/40">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-5 w-20" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <div className="pt-4">
                    <Skeleton className="h-9 w-full mt-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {status === "failed" && (
        <div className="flex flex-col justify-center items-center h-64 gap-4">
          <div className="bg-red-50 p-3 rounded-full">
            <AlertCircle className="h-8 w-8 text-red-500" />
          </div>
          <div className="text-center">
            <h3 className="font-semibold text-lg">{t('failed-to-load-data')}</h3>
            <p className="text-muted-foreground">{t('please-try-refreshing-the-page')}</p>
          </div>
          <Button variant="outline" onClick={() => window.location.reload()} className="mt-2">
            <RefreshCw className="mr-2 h-4 w-4" />
            {t('refresh')}
          </Button>
        </div>
      )}

      {status === "succeeded" && (
        <>
          {integrations.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-64 gap-4">
              <div className="bg-muted p-3 rounded-full">
                <Database className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-lg">{t('no-integrations-available')}</h3>
                <p className="text-muted-foreground">{t('there-are-no-integrations-to-assign-at-this-time')}</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {integrations.map((integration) => (
                <Card
                  key={integration.id}
                  className="overflow-hidden border border-border/40 transition-all hover:shadow-md flex flex-col"
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-2">
                        {getIntegrationIcon(integration.providerType)}
                        <CardTitle className="text-lg">{integration.name}</CardTitle>
                      </div>
                      {getStatusBadge(integration.status)}
                    </div>
                    <CardDescription className="mt-1">{integration.providerType.toUpperCase()}</CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <div className="space-y-2 text-sm">
                      {integration.providerType === "jira" && integration.jira && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Domain:</span>
                            <span className="font-medium truncate ml-2">{integration.jira.domain}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Project:</span>
                            <span className="font-medium truncate ml-2">{integration.jira.project}</span>
                          </div>
                        </>
                      )}

                      {integration.providerType === "ftp" && integration.ftp && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">{t('server-0')}</span>
                            <span className="font-medium truncate ml-2">{integration.ftp.server}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Port:</span>
                            <span className="font-medium truncate ml-2">{integration.ftp.port}</span>
                          </div>
                        </>
                      )}

                      {integration.providerType === "web" && integration.web && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">URL:</span>
                          <span className="font-medium truncate ml-2">{integration.web.url}</span>
                        </div>
                      )}

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('update-time')}</span>
                        <span className="font-medium">{integration.updateTime} days</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-4">
                    {integrationPermissions.canAssignToGroup(backendUser?.permissions ?? []) && (
                      <Button
                        onClick={() => openConfirmDialog(integration.id, integration.name)}
                        disabled={processingIntegration === integration.id}
                        className="w-full"
                        variant="default"
                      >
                        {processingIntegration === integration.id ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {t('assigning')}
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            {t('assign-to-team')}
                          </>
                        )}
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </>
      )}

      <AlertDialog open={confirmDialog.open} onOpenChange={(open) => setConfirmDialog((prev) => ({ ...prev, open }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('assign-integration')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('are-you-sure-you-want-to-assign')} <span className="font-medium">{confirmDialog.integrationName}</span> to{" "}
              {currentTeam?.name}{t('this-will-give-all-team-members-access-to-this-integration')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleAssignIntegration(confirmDialog.integrationId)}
              disabled={processingIntegration !== null}
            >
              {processingIntegration ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                t('assign-integration')
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
