import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { UserCircle, UserPlus, MoreHorizontal, Trash2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { TeamMember } from "@/store/slices/teamSlice"
import { teamPermissions } from "@/utils/ACTION_PERMISSIONS"
import { BackendUser } from "@/hooks/useBackendUser"
import { useTranslations } from "next-intl"

interface MemberCardViewProps {
  members: TeamMember[]
  onRemoveMember: (member: TeamMember) => void
  onOpenAddDialog: () => void
  backendUser: BackendUser | null
}

export function MemberCardView({ members, onRemoveMember, onOpenAddDialog, backendUser }: MemberCardViewProps) {
  const t = useTranslations()

  if (members.length === 0) {
    return (
      <div className="text-center py-10 border rounded-lg bg-muted/20">
        <UserCircle className="mx-auto h-10 w-10 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">{t('no-team-members-found')}</h3>
        <p className="text-muted-foreground">{t('this-team-has-no-members-or-none-match-your-search')}</p>
        <Button variant="outline" className="mt-4" onClick={onOpenAddDialog}>
          <UserPlus className="mr-2 h-4 w-4" />
          {t('add-members')}
        </Button>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {members.map((member) => (
        <Card key={member.id} className="overflow-hidden">
          <CardContent className="p-0">
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <UserCircle className="h-10 w-10 text-muted-foreground mr-3" />
                  <div>
                    <h3 className="font-medium">{member.firstName} {member.lastName}</h3>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                  </div>
                </div>
                 {teamPermissions.canRemoveUser(backendUser?.permissions ?? []) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">{t('more-options')}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => onRemoveMember(member)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t('remove-from-team')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                 )}
              </div>
              <div className="mt-4 flex flex-wrap gap-2">
                {member.role && member.role.map((role, index) => (
                  <Badge key={index} variant="outline">
                    {role.name}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}