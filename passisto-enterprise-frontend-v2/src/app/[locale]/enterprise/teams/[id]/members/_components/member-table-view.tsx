import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { UserCircle, MoreHorizontal, Trash2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { TeamMember } from "@/store/slices/teamSlice"
import { BackendUser } from "@/hooks/useBackendUser"
import { teamPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useTranslations } from "next-intl"

interface MemberTableViewProps {
  members: TeamMember[]
  onRemoveMember: (member: TeamMember) => void
    backendUser: BackendUser | null
  
}

export function MemberTableView({ members, onRemoveMember, backendUser }: MemberTableViewProps) {
  const t = useTranslations()

  return (
    <div className="border rounded-md overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('name')}</TableHead>
            <TableHead>{t('email')}</TableHead>
            <TableHead>{t('role')}</TableHead>
            <TableHead className="w-[100px]">{t('actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.length === 0 ? (
            <TableRow>
              <TableCell colSpan={4} className="h-24 text-center">
                {t('no-members-found')}
              </TableCell>
            </TableRow>
          ) : (
            members.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="flex items-center">
                    <UserCircle className="h-8 w-8 text-muted-foreground mr-2" />
                    <span>{member.firstName} {member.lastName}</span>
                  </div>
                </TableCell>
                <TableCell>{member.email}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {member.role && member.role.map((role, index) => (
                      <Badge key={index} variant="outline">
                        {role.name}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>
                  {teamPermissions.canRemoveUser(backendUser?.permissions ?? []) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">{t('more-options')}</span>
                      </Button>
                    </DropdownMenuTrigger>
                     
                    <DropdownMenuContent>
                      <DropdownMenuItem
                        onClick={() => onRemoveMember(member)}
                        className="!cursor-pointer"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {t('remove')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                     )}

                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}

