"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alog<PERSON>eader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { UserPlus, Search, UserCircle, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import { ViewToggle } from "@/components/view-toggle"
import { Pagination } from "@/components/pagination"
import { MemberCardView } from "./_components/member-card-view"
import { MemberTableView } from "./_components/member-table-view"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { 
  fetchTeamWithMembers, 
  TeamWithMembers, 
  TeamMember, 
  add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Add<PERSON>eamMembersDTO,
  remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 
} from "@/store/slices/teamSlice"
import { fetchUsers } from "@/store/slices/userSlice"
import { useAuth } from "@clerk/nextjs"
import { Breadcrumb } from "@/components/breadcrumb"
import { teamPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"

export default function TeamMembersPage() {
  const t  = useTranslations()
  const { backendUser } = useBackendUser();
  // State management
  const router = useRouter()
  const { id } = useParams<{ id: string }>()
  const dispatch = useAppDispatch()
  const { getToken } = useAuth()
  
  // Get data from Redux store
  const { currentTeam, status, error } = useAppSelector((state) => state.teams)
  const { users } = useAppSelector((state) => state.users)
  
  const [searchQuery, setSearchQuery] = useState("")
  const [searchAvailableQuery, setSearchAvailableQuery] = useState("")
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [view, setView] = useState("card")
  const [memberToDelete, setMemberToDelete] = useState<TeamMember | null>(null)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)

  // Load preferences from localStorage
  useEffect(() => {
    const loadPreferences = () => {
      const savedView = localStorage.getItem("teamMembersViewPreference")
      if (savedView) setView(savedView)

      const savedItemsPerPage = localStorage.getItem("teamMembersItemsPerPage")
      if (savedItemsPerPage) setItemsPerPage(Number(savedItemsPerPage))
    }

    loadPreferences()
  }, [])

  // Save preferences to localStorage
  useEffect(() => {
    localStorage.setItem("teamMembersViewPreference", view)
  }, [view])

  useEffect(() => {
    localStorage.setItem("teamMembersItemsPerPage", itemsPerPage.toString())
  }, [itemsPerPage])

  // Reset pagination on search
  useEffect(() => {
    setCurrentPage(1)
  }, [searchQuery])

  // Fetch team with members and available users
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        const token = await getToken()
        
        // Fetch team with members
        await dispatch(fetchTeamWithMembers({ teamId: id as string, token: token! })).unwrap()
        
        // Fetch all users to get available users
        await dispatch(fetchUsers(token!)).unwrap()
      } catch (error) {
        console.error("Error loading team data:", error)
        toast.error(t('error-loading-team-data'), {
          description: t('failed-to-load-team-and-member-data'),
        })
        router.push("/enterprise/teams")
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      fetchData()
    }
  }, [id, dispatch, getToken, router])

  // Get team members and available users
  const teamMembers = currentTeam?.members || []
  
  // Filter out users who are already team members
  const availableUsers = users.filter(user => 
    !teamMembers.some(member => member.id === user.id)
  )

  // Filtered members and pagination calculations
  const filteredMembers = teamMembers.filter(
    (member) =>
      member.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const filteredAvailableUsers = availableUsers.filter(
    (user) =>
      user.firstName.toLowerCase().includes(searchAvailableQuery.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchAvailableQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchAvailableQuery.toLowerCase())
  )

  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage)
  const indexOfLastItem = currentPage * itemsPerPage
  const indexOfFirstItem = indexOfLastItem - itemsPerPage
  const currentItems = filteredMembers.slice(indexOfFirstItem, indexOfLastItem)

  // Event handlers
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }

  const handleUserSelection = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    )
  }

  const handleRemoveMember = async (memberId: string) => {
    try {
      // Get authentication token
      const token = await getToken()
      
      if (!token) {
        toast.error(t('authentication-required-0'))
        return
      }
      
      // Add logging to debug
      console.log("Removing member with ID:", memberId)
      console.log("Team ID:", id)
      console.log("Token available:", !!token)
      
      // Dispatch removeTeamMember action
      const result = await dispatch(removeTeamMember({
        teamId: id as string,
        userId: memberId,
        token
      })).unwrap()
      
      console.log("Remove member result:", result)
      
      toast.success(t('member-removed'), {
        description: `User has been removed from ${currentTeam?.name}.`
      })
      
      // Fetch team with members
      await dispatch(fetchTeamWithMembers({ teamId: id as string, token: token! })).unwrap()
      
      // Reset memberToDelete
      setMemberToDelete(null)
    } catch (error: any) {
      // Enhanced error logging
      console.error("Error removing member:", error)
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        response: error.response?.data
      })
      
      toast.error(t('error-removing-member'), {
        description: error.message || t('failed-to-remove-team-member')
      })
    }
  }

  const confirmRemoveMember = (member: TeamMember) => {
    setMemberToDelete(member)
  }

  const handleAddMembers = async () => {
    if (selectedUsers.length === 0) return

    try {
      // Get authentication token
      const token = await getToken()
      
      if (!token) {
        toast.error(t('authentication-required'))
        return
      }
      
      // Prepare the data
      const membersData: AddTeamMembersDTO = {
        userIds: selectedUsers
      }
      
      // Dispatch addTeamMembers action
      await dispatch(addTeamMembers({
        teamId: id as string,
        membersData,
        token
      })).unwrap()
      
      toast.success(t('members-added'), {
        description: `${selectedUsers.length} user(s) have been added to ${currentTeam?.name}.`
      })
      
      setSelectedUsers([])
      setIsDialogOpen(false)
        await dispatch(fetchTeamWithMembers({ teamId: id as string, token: token! })).unwrap()

    } catch (error: any) {
      console.error("Error adding members:", error)
      toast.error(t('error-adding-members'), {
        description: error.message || t('failed-to-add-team-members')
      })
    }
  }

  if (loading || status === 'loading') {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin" />
          <p className="mt-4 text-muted-foreground">{t('loading-team-members')}</p>
        </div>
      </div>
    )
  }

  if (!currentTeam) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <p className="text-xl font-semibold">{t('team-not-found')}</p>
          <Button className="mt-4" onClick={() => router.push("/enterprise/teams")}>
            {t('back-to-teams')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="space-y-6">
        <div className="space-y-2">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.push(`/enterprise/teams/${id}/view`)}
            className="gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back-to-team-details-0')}
          </Button>
          
          <Breadcrumb 
            items={[
              { label: t('teams'), href: "/enterprise/teams" },
              { label: currentTeam.name, href: `/enterprise/teams/${id}/view` },
              { label: t('members') }
            ]}
            className="ml-1"
          />
        </div>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('members-of-team', { teamName: currentTeam.name })}</h1>
            <p className="text-muted-foreground mt-2">{t('manage-members-of-this-team')}</p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
               {teamPermissions.canAssignUser(backendUser?.permissions ?? []) &&(
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                {t('add-members-0')}
              </Button>
            )}
            </DialogTrigger>
            
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{t('add-team-members')}</DialogTitle>
                <DialogDescription>
                  {t('select-users-to-add-to-the')} {currentTeam.name} team.
                </DialogDescription>
              </DialogHeader>
              
              <div className="py-4">
                <div className="relative mb-4">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t('search-users')}
                    className="pl-8"
                    value={searchAvailableQuery}
                    onChange={(e) => setSearchAvailableQuery(e.target.value)}
                  />
                </div>
                
                <div className="border rounded-md max-h-[300px] overflow-y-auto">
                  {filteredAvailableUsers.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      {t('no-available-users-found')}
                    </div>
                  ) : (
                    <div className="divide-y">
                      {filteredAvailableUsers.map((user) => (
                        <div key={user.id} className="flex items-center p-3 hover:bg-muted">
                          <Checkbox
                            id={`user-${user.id}`}
                            checked={selectedUsers.includes(user.id)}
                            onCheckedChange={() => handleUserSelection(user.id)}
                            className="mr-3"
                          />
                          <div className="flex items-center flex-1">
                            <UserCircle className="h-8 w-8 mr-3 text-muted-foreground" />
                            <div>
                              <p className="font-medium">{user.firstName} {user.lastName}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                          </div>
                          {user.roles && user.roles.length > 0 && (
                            <Badge variant="outline" className="ml-auto">
                              {user.roles[0].name}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button 
                  onClick={handleAddMembers} 
                  disabled={selectedUsers.length === 0}
                >
                  {t('add')} {selectedUsers.length} {selectedUsers.length === 1 ? t('member') : t('members')}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('search-members')}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <ViewToggle view={view} setView={setView} />
        </div>
        
        {filteredMembers.length === 0 ? (
          <div className="text-center py-10 border rounded-lg bg-muted/20">
            <UserCircle className="mx-auto h-10 w-10 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">{t('no-team-members-found')}</h3>
            <p className="text-muted-foreground">{t('this-team-has-no-members-or-none-match-your-search')}</p>
            {teamPermissions.canAssignUser(backendUser?.permissions ?? []) && (
              <Button variant="outline" className="mt-4" onClick={() => setIsDialogOpen(true)}>
                <UserPlus className="mr-2 h-4 w-4" />
                {t('add-members')}
              </Button>
            )}
          </div>
        ) : (
          <>
            {view === "card" ? (
              <MemberCardView 
                members={currentItems} 
                onRemoveMember={confirmRemoveMember} 
                onOpenAddDialog={() => setIsDialogOpen(true)}
                backendUser= {backendUser}
              />
            ) : (
              <MemberTableView 
                members={currentItems} 
                onRemoveMember={confirmRemoveMember} 
                backendUser= {backendUser}
              />
            )}
            
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              totalItems={filteredMembers.length}
              showingFrom={indexOfFirstItem + 1}
              showingTo={Math.min(indexOfLastItem, filteredMembers.length)}
              viewMode={view}
            />
          </>
        )}
      </div>
      {/* Delete Confirmation Dialog */}
      <Dialog open={!!memberToDelete} onOpenChange={(open) => !open && setMemberToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('remove-team-member')}</DialogTitle>
            <DialogDescription>
              {t('are-you-sure-you-want-to-remove')} {memberToDelete?.firstName} {memberToDelete?.lastName} ({memberToDelete?.email}) from the {currentTeam?.name} team? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setMemberToDelete(null)}>
              {t('cancel')}
            </Button>
            {teamPermissions.canRemoveUser(backendUser?.permissions ?? []) && (
              <Button 
                variant="destructive" 
                onClick={() => memberToDelete && handleRemoveMember(memberToDelete.id)}
              >
                {t('remove')}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
    </>
  )
}
