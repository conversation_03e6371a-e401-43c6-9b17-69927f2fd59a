"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from "axios";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";

interface TeamsLayoutProps {
  children: React.ReactNode;
}

export default function TeamsLayout({ children }: TeamsLayoutProps) {
  

  return (
    <div className="container mx-auto py-4 px-4">
      <div className="flex justify-end mb-2">
        {children}
      </div>
    </div>
  );
}