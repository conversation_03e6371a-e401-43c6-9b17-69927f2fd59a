"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { ArrowLeft, Shield } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { createTeam, CreateTeamDTO } from "@/store/slices/teamSlice";
import { fetchRolesPermissions } from "@/store/slices/rolePermissionSlice";
import { useAuth } from "@clerk/nextjs";
import { Breadcrumb } from "@/components/breadcrumb";
import { teamPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function NewTeamPage() {
  const t = useTranslations("NewTeamPage"); // Initialize useTranslations
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Get permissions from Redux store
  const { permissionsByCategory, loading: permissionsLoading } = useAppSelector(
    (state) => state.rolePermission
  );

  const [formData, setFormData] = useState<CreateTeamDTO>({
    name: "",
    description: "",
    permissions: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<{
    name?: string;
    description?: string;
    teamExists?: string;
  }>({});

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to create teams
      if (!teamPermissions.canCreate(backendUser?.permissions ?? [])) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription")
        });
        router.push("/enterprise/teams");
      }
    }
  }, [backendUser, backendUserLoading, router, t]); // Add t to dependencies

  // Fetch roles and permissions when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken();
        if (token) {
          await dispatch(fetchRolesPermissions(token)).unwrap();
        }
      } catch (error) {
        console.error("Error fetching permissions:", error);
        toast.error(t("failedToLoadPermissions"));
      }
    };

    fetchData();
  }, [dispatch, getToken, t]); // Add t to dependencies

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePermissionChange = (permissionId: string) => {
    // Check if the permission is already in the team's permissions
    const hasPermission = formData.permissions.includes(permissionId);

    if (hasPermission) {
      // Remove the permission
      setFormData((prev) => ({
        ...prev,
        permissions: prev.permissions.filter(p => p !== permissionId),
      }));
    } else {
      // Add the permission
      setFormData((prev) => ({
        ...prev,
        permissions: [...prev.permissions, permissionId],
      }));
    }
  };

  // Format permission name for display
  const formatPermissionName = (permission: any) => {
    const action = permission.action || permission;
    return action
      .replace("CAN_", "")
      .split("_")
      .map((word: string) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormErrors({}); // reset errors

    const errors: { name?: string; description?: string; teamExists?: string } = {};

    // Validation côté client
    if (!formData.name.trim()) {
      errors.name = t("teamNameRequired");
    }

    if (!formData.description.trim()) {
      errors.description = t("descriptionRequired");
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      const token = await getToken();

      if (!token) {
        toast.error(t("authenticationRequiredToast"));
        setIsSubmitting(false);
        return;
      }

      await dispatch(
        createTeam({
          teamData: formData,
          token,
        })
      ).unwrap();

      toast.success(t("teamCreatedToastTitle"), {
        description: t("teamCreatedToastDescription", { teamName: formData.name }),
      });

      router.push("/enterprise/teams");
    } catch (error: any) {
      console.error("Error creating team:", error);
      console.error("Error message:", error.message);
      console.error("Error response:", error.response?.data);

      // Check for team already exists error
      if (error.response?.status === 409) {
        setFormErrors({ teamExists: t("teamAlreadyExists", { teamName: formData.name }) });
      } else {
        // Instead of showing a toast, set a form error for any other error
        // You might want to be more specific with error messages here
        setFormErrors({ teamExists: error.response?.data?.message || t("creationFailedToastDescription") });

        // Also show the toast for additional visibility
        toast.error(t("creationFailedToastTitle"), {
          description: error.message || t("creationFailedToastDescription"),
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="w-full p-4">
        <div className="space-y-2 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/enterprise/teams")}
            className="gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            {t("backToTeams")}
          </Button>

          <Breadcrumb
            items={[
              { label: t("breadcrumbTeams"), href: "/enterprise/teams" },
              { label: t("breadcrumbCreateNewTeam") }
            ]}
            className="ml-1"
          />
        </div>

        <Card className="shadow-sm">
          <CardHeader className="pb-3">
            <CardTitle>{t("cardTitle")}</CardTitle>
            <CardDescription>{t("cardDescription")}</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {/* Alert for form errors */}
              {Object.keys(formErrors).length > 0 && (
                <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md mb-4 border border-destructive/30">
                  <h4 className="font-medium mb-1">
                    {formErrors.teamExists
                      ? t("formErrorsTitleTeamExists")
                      : (formErrors.name || formErrors.description)
                        ? t("formErrorsTitleFillFields")
                        : t("formErrorsGenericTitle")}
                  </h4>
                  <ul className="list-disc list-inside text-sm">
                    {formErrors.name && <li>{formErrors.name}</li>}
                    {formErrors.description && <li>{formErrors.description}</li>}
                    {formErrors.teamExists && <li>{formErrors.teamExists}</li>}
                  </ul>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">{t("teamNameLabel")}</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder={t("teamNamePlaceholder")}
                  value={formData.name}
                  onChange={handleChange}
                  className={formErrors.name ? "border-red-500" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t("descriptionLabel")}</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder={t("descriptionPlaceholder")}
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  className={formErrors.description ? "border-red-500" : ""}
                />
              </div>

              {/* Team Permissions Section */}
              <div className="space-y-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium flex items-center">
                    <Shield className="h-4 w-4 mr-2 text-primary" />
                    {t("teamPermissionsTitle")}
                  </h3>
                  <Badge variant="outline">
                    {t("permissionsSelected", { count: formData.permissions?.length || 0 })}
                  </Badge>
                </div>

                <p className="text-sm text-muted-foreground">
                  {t("permissionsDescription")}
                </p>

                {permissionsLoading ? (
                  <div className="flex justify-center p-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <p className="ml-2 text-muted-foreground">{t("loadingPermissions")}</p>
                  </div>
                ) : (
                  permissionsByCategory.map((category) => (
                    <div key={category.category} className="space-y-2">
                      <h4 className="text-sm font-medium">{category.category}</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {category.permissions.map((permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`permission-${permission.id}`}
                              checked={formData.permissions?.includes(permission.id) || false}
                              onCheckedChange={() => handlePermissionChange(permission.id)}
                            />
                            <label htmlFor={`permission-${permission.id}`} className="text-sm">
                              {formatPermissionName(permission.action)}
                              {permission.scopeType && (
                                <span className="text-xs text-muted-foreground ml-1">
                                  ({permission.scopeType})
                                </span>
                              )}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={() => router.push("/enterprise/teams")}>
                {t("cancelButton")}
              </Button>
              <Button type="submit" disabled={isSubmitting || permissionsLoading}>
                {isSubmitting ? t("creatingTeamButton") : t("createTeamButton")}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}