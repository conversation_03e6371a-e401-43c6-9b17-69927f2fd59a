"use client"

import { useEffe<PERSON>, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  UsersRound,
  UserPlus,
  Mail,
  Sparkles
} from "lucide-react"
import {
  <PERSON><PERSON><PERSON> as RechartsBar<PERSON>hart,
  Bar,
  PieChart,
  Pie,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts'
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useTranslations } from "next-intl"

// Interface for the fetched dashboard statistics
interface DashboardStats {
  totalTeams: number
  totalUsers: number
  activeUsers: number
  teamsWithMembers: number
  teamDistribution: Array<{ name: string; members: number }>
  userGrowth: Array<{ month: string; users: number }>
  teamActivities: Array<{ name: string; value: number }>
  interviewStats: {
    total: number
    completed: number
    pending: number
  }
  emailStats: {
    total: number
    sent: number
    opened: number
  }
  formStats: {
    total: number
    submissions: number
    conversionRate: number
  }
}

// Colors for the Pie Chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

export default function Page() {
  // Initialize translation hook for DashboardPage namespace
  const t = useTranslations("DashboardPage")
  const router = useRouter() // Next.js router for navigation

  // State to hold dashboard statistics, initialized with default values
  const [stats, setStats] = useState<DashboardStats>({
    totalTeams: 0,
    totalUsers: 0,
    activeUsers: 0,
    teamsWithMembers: 0,
    teamDistribution: [],
    userGrowth: [],
    teamActivities: [],
    interviewStats: {
      total: 0,
      completed: 0,
      pending: 0
    },
    emailStats: {
      total: 0,
      sent: 0,
      opened: 0
    },
    formStats: {
      total: 0,
      submissions: 0,
      conversionRate: 0
    }
  })
  const [loading, setLoading] = useState(true) // Loading state
  const [error, setError] = useState<string | null>(null) // Error state

  // useEffect hook to fetch dashboard data on component mount
  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true)
        setError(null)
        // Fetch stats from backend API
        // Assumes /api/v1/dashboard/stats handles companyId internally based on session/auth
        const response = await fetch("/api/v1/dashboard/stats", { credentials: "include" })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const data = await response.json()

        // Translate specific data fields for display purposes
        const translatedStats = {
          ...data,
          // Translate month names in userGrowth data
          userGrowth: data.userGrowth.map((item: any) => ({
            month: translateMonth(item.month, t), // Uses helper function for translation
            users: item.users
          })),
          // Translate team activity status (e.g., 'active'/'inactive')
          teamActivities: data.teamActivities.map((item: any) => ({
            name: item.name === 'active' ? t('active') : t('inactive'),
            value: item.value
          }))
        }
        setStats(translatedStats)
      } catch (error) {
        console.error("Error loading stats:", error)
        setError(error instanceof Error ? error.message : t('failed-to-load-stats'))
      } finally {
        setLoading(false)
      }
    }
    loadStats()
  }, [t]) // Dependency array: re-run if translation function changes

  // Helper function to translate month abbreviations
  const translateMonth = (month: string, t: any): string => {
    const monthMap: { [key: string]: string } = {
      'Jan': t('jan'),
      'Feb': t('feb'),
      'Mar': t('mar'),
      'Apr': t('apr'),
      'May': t('may'),
      'Jun': t('jun'),
      'Jul': t('jul'),
      'Aug': t('aug'),
      'Sep': t('sep'),
      'Oct': t('oct'),
      'Nov': t('nov'),
      'Dec': t('dec')
    }
    return monthMap[month] || month // Fallback to original month if no translation found
  }

  // Render loading state
  if (loading) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-4">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">{t('loading')}...</div>
        </div>
      </div>
    )
  }

  // Render error state
  if (error) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <p>{t('error')}: {error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
                variant="outline"
              >
                {t('retry')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Main dashboard content
  return (
    <div className="flex flex-1 flex-col gap-6 p-4">
      {/* Quick Actions Section */}
      <Card>
        <CardHeader>
          <CardTitle>{t('quick-actions')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => router.push("/enterprise/ai-agents/ai-interviewer/create")}
            >
              <UserPlus className="h-4 w-4" />
              {t('ai-interview')}
            </Button>

            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => router.push("/enterprise/ai-agents/email-builder")}
            >
              <Mail className="h-4 w-4" />
              {t('email-builder')}
            </Button>

            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => router.push("/enterprise/ai-agents/form-builder")}
            >
              <Sparkles className="h-4 w-4" />
              {t('form-builder')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Main Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('total-teams')}</CardTitle>
            <UsersRound className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTeams}</div>
            <p className="text-xs text-muted-foreground">
              {stats.teamsWithMembers} {t('active-teams')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('total-users')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeUsers} {t('active-users')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('team-activity-rate')}</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalTeams ? Math.round((stats.teamsWithMembers / stats.totalTeams) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {t('of-teams-have-members')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('avg-users-team')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalTeams ? Math.round(stats.totalUsers / stats.totalTeams) : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('users-per-team')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* AI Tools Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('interviews')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.interviewStats.total}</div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('interview-completed', { count: stats.interviewStats.completed })}</span>
              <span>{t('interview-pending', { count: stats.interviewStats.pending })}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('emails')}</CardTitle> {/* Changed to 'emails' */}
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.emailStats.total}</div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('emails-sent', { count: stats.emailStats.sent })}</span>
              <span>{t('emails-opened', { count: stats.emailStats.opened })}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('forms')}</CardTitle> {/* Changed to 'forms' */}
            <Sparkles className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.formStats.total}</div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{t('submissions', { count: stats.formStats.submissions })}</span>
              <span>{t('conversion-rate', { rate: stats.formStats.conversionRate })}</span> {/* Pass rate as number */}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* User Growth Line Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('user-growth')}</CardTitle>
          </CardHeader>
          <CardContent style={{ height: 300 }}>
            {stats.userGrowth.length === 0 ? (
              <p className="text-muted-foreground text-sm">{t('no-user-growth')}</p>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={stats.userGrowth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="users" stroke="#8884d8" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        {/* Team Distribution Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('team-distribution')}</CardTitle>
          </CardHeader>
          <CardContent style={{ height: 300 }}>
            {stats.teamDistribution.length === 0 ? (
              <p className="text-muted-foreground text-sm">{t('no-team-distribution')}</p>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={stats.teamDistribution}
                    dataKey="members"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label
                  >
                    {stats.teamDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Team Activity Bar Chart */}
      <Card>
        <CardHeader>
          <CardTitle>{t('team-activity')}</CardTitle>
        </CardHeader>
        <CardContent style={{ height: 300 }}>
          {stats.teamActivities.length === 0 ? (
            <p className="text-muted-foreground text-sm">{t('no-team-activity')}</p>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart data={stats.teamActivities}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#00C49F" />
              </RechartsBarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
