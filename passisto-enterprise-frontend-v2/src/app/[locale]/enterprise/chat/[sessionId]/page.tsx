"use client"

import React, { useEffect } from 'react'
import { useParams } from 'next/navigation'
import ChatInterface from '../_components/ChatInterface'
import { useChatSessionContext } from '@/context/ChatSessionContext'
import { Loader2 } from 'lucide-react'

function ChatSessionPage() {
  const params = useParams()
  const sessionId = params.sessionId as string
  const { switchSession, loading, switchingSession, error } = useChatSessionContext()

  useEffect(() => {
    if (sessionId) {
      // Switch to the session from the URL
      switchSession(sessionId)
    }
  }, [sessionId, switchSession])

  // Show loading state while switching sessions
  if (loading || switchingSession) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-muted-foreground">Loading chat session...</span>
      </div>
    )
  }

  // Show error state if there's an issue loading the session
  // But ignore 404 errors for new sessions
  // if (error && !error?.includes('Chat history not found')) {
  //   return (
  //     <div className="flex-1 flex items-center justify-center pt-4">
  //       <div className="text-center">
  //         <p className="text-destructive font-medium">Error loading chat session</p>
  //         <p className="text-muted-foreground">{error}</p>
  //       </div>
  //     </div>
  //   )
  // }

  return (
    <div className="flex-1 overflow-hidden">
      <ChatInterface />
    </div>
  )
}

export default ChatSessionPage
