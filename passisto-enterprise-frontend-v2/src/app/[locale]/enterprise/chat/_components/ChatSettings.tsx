"use client";

import React from "react";
import { Settings, Database, Server, Globe } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useChatSettings, ProviderType } from "@/context/ChatSettingsContext";
import { useTranslations } from "next-intl"; // Import useTranslations

interface Alias {
  id: string;
  name: string;
}

export default function ChatSettings() {
  const t = useTranslations("ChatSettings"); // Initialize useTranslations
  const {
    filteredAliases,
    toggleAlias,
    isAliasSelected,
    selectedAliases,
    isLoading,
    error,
    providerFilter,
    setProviderFilter,
  } = useChatSettings();
  const [open, setOpen] = React.useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label={t("ariaLabelSettings")}
        >
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("dialogTitle")}</DialogTitle>
          <DialogDescription>{t("dialogDescription")}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label className="text-base font-medium">
              {t("knowledgeBasesLabel")}
            </Label>
            <p className="text-sm text-muted-foreground mb-4">
              {t("knowledgeBasesDescription")}
            </p>

            {error ? (
              <div className="text-sm text-red-500 mb-2">
                {t("errorLoadingKnowledgeBases", { error })}
              </div>
            ) : null}

            {/* Filter tabs */}
            <Tabs
              value={providerFilter}
              onValueChange={(value) =>
                setProviderFilter(value as ProviderType)
              }
              className="mb-4"
            >
              <TabsList className="grid grid-cols-4 w-full">
                <TabsTrigger value="all" className="text-xs">
                  <Database className="h-3 w-3 mr-1" />
                  {t("allTab")}
                </TabsTrigger>
                <TabsTrigger value="ftp" className="text-xs">
                  <Server className="h-3 w-3 mr-1" />
                  {t("ftpTab")}
                </TabsTrigger>
                <TabsTrigger value="jira" className="text-xs">
                  <Settings className="h-3 w-3 mr-1" />
                  {t("jiraTab")}
                </TabsTrigger>
                <TabsTrigger value="web" className="text-xs">
                  <Globe className="h-3 w-3 mr-1" />
                  {t("webTab")}
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {isLoading ? (
              <div className="text-sm text-muted-foreground">
                {t("loadingKnowledgeBases")}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredAliases.map((alias: Alias) => (
                  <div key={alias.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`alias-${alias.id}`}
                      checked={isAliasSelected(alias.id)}
                      onCheckedChange={() => toggleAlias(alias)}
                      disabled={
                        isLoading ||
                        (isAliasSelected(alias.id) && selectedAliases.length === 1)
                      }
                    />
                    <Label
                      htmlFor={`alias-${alias.id}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {alias.name}
                    </Label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}