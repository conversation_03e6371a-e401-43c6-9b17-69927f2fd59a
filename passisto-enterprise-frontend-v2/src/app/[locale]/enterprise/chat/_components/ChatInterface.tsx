"use client"

import React from "react"
import Chat<PERSON><PERSON> from "./ChatMain"
import { useChatSessionContext } from "@/context/ChatSessionContext"
import { Loader2 } from "lucide-react"

export default function ChatInterface() {
  const {
    sessionId,
    messages,
    loading,
    sendingMessage,
    switchingSession,
    error,
    sendMessage
  } = useChatSessionContext()

  // Log the session ID when it changes and fetch chat history
  React.useEffect(() => {
    console.log(`Current chat session ID: ${sessionId}`);

    // Don't show errors for new sessions
    if (error?.includes('Chat history not found')) {
      console.log('Ignoring "Chat history not found" error for new session');
    }
  }, [sessionId, error]);

  return (
    <div className="w-full h-[calc(100vh-4rem)] bg-background flex flex-col relative">
      {switchingSession && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Loading chat session...</p>
          </div>
        </div>
      )}

      <ChatMain
        messages={messages}
        onSendMessage={sendMessage}
        isLoading={loading}
        isSending={sendingMessage}
        error={error}
        sessionId={sessionId}
      />
    </div>
  )
}

