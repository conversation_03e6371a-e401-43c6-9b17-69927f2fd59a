"use client"

import React, { useRef, useEffect } from "react"
import { <PERSON><PERSON>, Mic, Refresh<PERSON>w, ThumbsDown, ThumbsUp, Send, Loader2 as Loader, BookO<PERSON> } from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { ChatMessage } from "@/services/chatbotApi"
import ChatSettings from "./ChatSettings"
import SelectedKnowledgeBases from "./SelectedKnowledgeBases"
import { useTranslations } from 'next-intl'

interface ChatMainProps {
  readonly messages: ChatMessage[];
  readonly onSendMessage: (message: string) => Promise<void>;
  readonly isLoading: boolean;
  readonly isSending: boolean;
  readonly error: string | null;
  readonly sessionId?: string;
}

export default function ChatMain({ messages, onSendMessage, isLoading, isSending, error, sessionId }: ChatMainProps) {
  const t = useTranslations('chatPage')
  const [input, setInput] = React.useState("")
  const [isRecording, setIsRecording] = React.useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim() && !isSending) {
      const message = input
      setInput("")
      await onSendMessage(message)
    }
  }

  const handleMicClick = () => {
    setIsRecording(!isRecording)
    // Here you would implement the actual audio recording functionality
    console.log(isRecording ? t('recording.stopped') : t('recording.started'))
  }

  return (
    <main className="flex-1 flex flex-col h-full relative">
      <div className="flex flex-col p-2 border-b">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium">{t('title')}</div>
          <ChatSettings />
        </div>
        <div className="flex flex-col space-y-1 mt-1">
          {sessionId && (
            <div className="text-xs text-muted-foreground flex items-center">
              <span className="font-semibold mr-1">{t('session.label')}:</span>
              <span className="font-mono text-[10px] truncate">{sessionId}</span>
            </div>
          )}
          <SelectedKnowledgeBases />
        </div>
      </div>

      {/* Messages area with padding at the bottom to ensure content isn't hidden behind the input form */}
      <ScrollArea className="flex-1 p-4 pb-20">
        {isLoading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Loader className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {error && (
              <div className="mb-4 p-3 bg-destructive/10 text-destructive rounded-md">
                {error}
              </div>
            )}

            {messages.map((message, i) => (
              <div
                key={`${message.role}-${i}-${message.content.substring(0, 10)}`}
                className={`flex mb-4 ${message.role === "user" ? "justify-end" : "justify-start"}`}
              >
                <div className={`flex items-start max-w-[80%] ${message.role === "user" ? "flex-row-reverse" : ""}`}>
                  <Avatar className="w-8 h-8 mt-0.5 mx-2">
                    <AvatarFallback>{message.role === "user" ? t('avatars.user') : t('avatars.ai')}</AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col">
                    <div
                      className={`p-3 rounded-lg ${message.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted"}`}
                    >
                      {message.content}
                    </div>
                    {message.role === "assistant" && (
                      <>
                        {/* Display sources if available */}
                        {message.sources && message.sources.length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            <div className="flex items-center text-xs text-muted-foreground mr-1">
                              <BookOpen className="h-3 w-3 mr-1" />
                              <span>{t('sources.label')}:</span>
                            </div>
                            {message.sources.map((source, index) => (
                              <Badge
                                key={`source-${index}-${source.substring(0, 10)}`}
                                variant="outline"
                                className="text-xs bg-secondary hover:bg-secondary/80"
                              >
                                {source}
                              </Badge>
                            ))}
                          </div>
                        )}
                        <div className="flex space-x-2 mt-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => navigator.clipboard.writeText(message.content)}
                            title={t('actions.copy')}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon" title={t('actions.readAloud')}>
                            <Mic className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon" title={t('actions.regenerate')}>
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon" title={t('actions.helpful')}>
                            <ThumbsUp className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="icon" title={t('actions.notHelpful')}>
                            <ThumbsDown className="h-4 w-4" />
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </ScrollArea>

      {/* Fixed input form at the bottom */}
      <form onSubmit={handleSubmit} className="absolute bottom-0 left-0 right-0 p-4 border-t bg-background">
        <div className="flex space-x-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={isSending ? t('input.waitingPlaceholder') : t('input.placeholder')}
            className="flex-1"
            disabled={isSending}
          />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={handleMicClick}
                  disabled={isSending}
                >
                  <Mic className={`h-4 w-4 ${isRecording ? "text-red-500" : ""}`} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isRecording ? t('recording.stop') : t('recording.start')}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Button type="submit" disabled={isSending || !input.trim()}>
            {isSending ? <Loader className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
          </Button>
        </div>
      </form>
    </main>
  )
}