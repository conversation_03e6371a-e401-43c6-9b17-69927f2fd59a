import { MessageSquare, Plus, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { ChatSession } from "@/services/chatbotApi"
import { formatDistanceToNow } from "date-fns"

interface ChatSidebarProps {
  readonly sessions: ChatSession[];
  readonly currentSessionId: string;
  readonly onSessionSelect: (sessionId: string) => void;
  readonly onNewChat: () => void;
  readonly loading: boolean;
}

export default function ChatSidebar({
  sessions,
  currentSessionId,
  onSessionSelect,
  onNewChat,
  loading
}: ChatSidebarProps) {
  // Group sessions by date
  const groupedSessions = sessions.reduce((groups, session) => {
    const date = new Date(session.timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Check if the date is today
    if (date.toDateString() === today.toDateString()) {
      groups.today.push(session);
    }
    // Check if the date is yesterday
    else if (date.toDateString() === yesterday.toDateString()) {
      groups.yesterday.push(session);
    }
    // Check if the date is within the last 7 days
    else if (today.getTime() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
      groups.previousWeek.push(session);
    }
    // Check if the date is within the last 30 days
    else if (today.getTime() - date.getTime() < 30 * 24 * 60 * 60 * 1000) {
      groups.previousMonth.push(session);
    }

    return groups;
  }, {
    today: [] as ChatSession[],
    yesterday: [] as ChatSession[],
    previousWeek: [] as ChatSession[],
    previousMonth: [] as ChatSession[]
  });

  return (
    <Sidebar className="w-64 border-r h-full">
      <SidebarHeader className="p-4 space-y-2">
        <Button
          className="w-full justify-start"
          variant="outline"
          onClick={onNewChat}
          disabled={loading}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Chat
        </Button>
        {currentSessionId && (
          <div className="text-xs text-muted-foreground overflow-hidden text-ellipsis">
            <span className="font-semibold">Session ID:</span>
            <br />
            <span className="font-mono">{currentSessionId}</span>
          </div>
        )}
      </SidebarHeader>
      <SidebarContent>
        {loading ? (
          <div className="flex justify-center items-center h-20">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {groupedSessions.today.length > 0 && (
              <HistoryGroup
                title="Today"
                items={groupedSessions.today}
                currentSessionId={currentSessionId}
                onSessionSelect={onSessionSelect}
              />
            )}
            {groupedSessions.yesterday.length > 0 && (
              <HistoryGroup
                title="Yesterday"
                items={groupedSessions.yesterday}
                currentSessionId={currentSessionId}
                onSessionSelect={onSessionSelect}
              />
            )}
            {groupedSessions.previousWeek.length > 0 && (
              <HistoryGroup
                title="Previous 7 Days"
                items={groupedSessions.previousWeek}
                currentSessionId={currentSessionId}
                onSessionSelect={onSessionSelect}
              />
            )}
            {groupedSessions.previousMonth.length > 0 && (
              <HistoryGroup
                title="Previous 30 Days"
                items={groupedSessions.previousMonth}
                currentSessionId={currentSessionId}
                onSessionSelect={onSessionSelect}
              />
            )}
            {sessions.length === 0 && (
              <div className="p-4 text-center text-muted-foreground">
                No chat history yet. Start a new chat!
              </div>
            )}
          </>
        )}
      </SidebarContent>
    </Sidebar>
  )
}

interface HistoryGroupProps {
  readonly title: string;
  readonly items: ChatSession[];
  readonly currentSessionId: string;
  readonly onSessionSelect: (sessionId: string) => void;
}

function HistoryGroup({ title, items, currentSessionId, onSessionSelect }: HistoryGroupProps) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((session) => (
            <SidebarMenuItem key={session.id}>
              <SidebarMenuButton
                onClick={() => onSessionSelect(session.id)}
                active={session.id === currentSessionId}
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span className="text-sm truncate max-w-[180px]">{session.title}</span>
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(session.timestamp), { addSuffix: true })}
                  </span>
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

