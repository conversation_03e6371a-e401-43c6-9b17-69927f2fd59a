"use client"

import React from "react"
import { Settings, Database, Server, Globe } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useChatSettings, ProviderType } from "@/context/ChatSettingsContext"
import { useTranslations } from "next-intl"

interface Alias {
  id: string;
  name: string;
}

interface SearchSettingsProps {
  className?: string;
}

export default function SearchSettings({ className }: Readonly<SearchSettingsProps>) {
  const t = useTranslations()
  const {
    filteredAliases,
    toggleAlias,
    isAliasSelected,
    selectedAliases,
    isLoading,
    error,
    providerFilter,
    setProviderFilter
  } = useChatSettings()
  const [open, setOpen] = React.useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={className}
        >
          <Settings className="h-4 w-4 mr-2" />
          {t('knowledge-bases')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('search-settings')}</DialogTitle>
          <DialogDescription>
            {t('configure-your-search-experience')}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label className="text-base font-medium">
              {t('knowledge-bases-0')}
            </Label>
            <p className="text-sm text-muted-foreground mb-4">
              {t('select-which-knowledge-bases-to-search-when-performing-searches')}
            </p>

            {error ? (
              <div className="text-sm text-red-500 mb-2">
                {t('error-loading-knowledge-bases')} {error}
              </div>
            ) : null}

            {/* Filter tabs */}
            <Tabs
              value={providerFilter}
              onValueChange={(value) => setProviderFilter(value as ProviderType)}
              className="mb-4"
            >
              <TabsList className="grid grid-cols-4 w-full">
                <TabsTrigger value="all" className="text-xs">
                  <Database className="h-3 w-3 mr-1" />
                  {t('all')}
                </TabsTrigger>
                <TabsTrigger value="ftp" className="text-xs">
                  <Server className="h-3 w-3 mr-1" />
                  FTP
                </TabsTrigger>
                <TabsTrigger value="jira" className="text-xs">
                  <Settings className="h-3 w-3 mr-1" />
                  {t('jira')}
                </TabsTrigger>
                <TabsTrigger value="web" className="text-xs">
                  <Globe className="h-3 w-3 mr-1" />
                  {t('web')}
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {isLoading ? (
              <div className="text-sm text-muted-foreground">{t('loading-knowledge-bases')}</div>
            ) : (
              <div className="space-y-3">
                {filteredAliases.map((alias: Alias) => (
                <div key={alias.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`search-alias-${alias.id}`}
                    checked={isAliasSelected(alias.id)}
                    onCheckedChange={() => toggleAlias(alias)}
                    disabled={isLoading || (isAliasSelected(alias.id) && selectedAliases.length === 1)}
                  />
                  <Label
                    htmlFor={`search-alias-${alias.id}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {alias.name}
                  </Label>
                </div>
              ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
