import { Check, Globe, FileArchive, TicketCheck } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const searchTargets = [
  {
    id: "all",
    name: "Search all types",
    icon: Globe,
    description: "Search across the entire system",
  },
  {
    id: "web",
    name: "Web Search",
    icon: Globe,
    description: "Search across the entire web",
  },
  {
    id: "jira",
    name: "Jira Issues",
    icon: TicketCheck,
    description: "Search through Jira tickets",
  },
  {
    id: "ftp",
    name: "FTP Server",
    icon: FileArchive,
    description: "Search files on FTP server",
  },
]

export default function SearchTarget({ activeTarget, setActiveTarget }) {
  const currentTarget = searchTargets.find((target) => target.id === activeTarget)
  const Icon = currentTarget?.icon

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-[180px] justify-start gap-2">
          {Icon && <Icon className="h-4 w-4" />}
          {currentTarget?.name}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[220px]">
        {searchTargets.map((target) => {
          const TargetIcon = target.icon
          return (
            <DropdownMenuItem
              key={target.id}
              onClick={() => setActiveTarget(target.id)}
              className="flex items-center gap-2 justify-between"
            >
              <div className="flex items-center gap-2">
                <TargetIcon className="h-4 w-4" />
                <div className="flex flex-col">
                  <span>{target.name}</span>
                  <span className="text-xs text-muted-foreground">{target.description}</span>
                </div>
              </div>
              {activeTarget === target.id && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

