import { useTranslations } from "next-intl";
import WebResults from "./results/web-results"
import { type SearchSource } from "@/services/searchApi"

interface SearchResultsProps {
  readonly isLoading: boolean;
  readonly searchResults?: SearchSource[];
  readonly expandedQueries?: string[];
}

export default function SearchResults({ isLoading, searchResults = [], expandedQueries = [] }: Readonly<SearchResultsProps>) {
  const t = useTranslations("SearchPage") // Initialize useTranslations hook

  return (
    <div className="mt-6">
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <div className="space-y-8">
          {expandedQueries.length > 0 && (
            <div className="p-4 bg-muted/50 rounded-lg">
              <h3 className="text-sm font-medium mb-2">{t('related-searches')}</h3>
              <div className="flex flex-wrap gap-2">
                {expandedQueries.map((query) => (
                  <span key={`query-${query}`} className="text-xs bg-background px-2 py-1 rounded-md border">
                    {query}
                  </span>
                ))}
              </div>
            </div>
          )}

          <WebResults results={searchResults} />
        </div>
      )}
    </div>
  )
}

