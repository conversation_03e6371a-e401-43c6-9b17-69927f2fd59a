import { type SearchSource } from "@/services/searchApi"

interface WebResultsProps {
  readonly results?: SearchSource[];
}

export default function WebResults({ results = [] }: Readonly<WebResultsProps>) {
  // If no results are provided, show a message
  if (results.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No search results found. Try a different search term.</p>
      </div>
    )
  }

  return (
    <>
      {results.map((result, index) => {
        // Extract useful information from the search result
        const title = result.metadata.title ?? "Untitled Document"
        const url = result.metadata.url ?? "#"
        const source = result.metadata.source ?? ""
        const content = result.content ?? ""

        // Create a snippet from the content (first 200 characters)
        const snippet = content.length > 200
          ? content.substring(0, 200) + "..."
          : content

        return (
          <div key={`result-${source}-${title.substring(0, 20)}`} className="space-y-2">
            <a href={url} className="text-sm text-muted-foreground hover:underline">
              {url}
            </a>
            <h2 className="text-xl font-semibold text-primary hover:underline">
              <a href={url}>{title}</a>
            </h2>
            <p className="text-muted-foreground">{snippet}</p>
            {source && (
              <p className="text-xs text-muted-foreground">
                Source: {source}
              </p>
            )}
          </div>
        )
      })}
    </>
  )
}

