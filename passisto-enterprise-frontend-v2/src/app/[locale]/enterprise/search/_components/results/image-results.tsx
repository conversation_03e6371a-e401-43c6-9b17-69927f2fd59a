const mockResults = [
    {
      title: "Web Development Setup",
      url: "/globe.svg?height=200&width=300",
      source: "devsetups.com",
    },
    {
      title: "Coding Workspace",
      url: "/globe.svg?height=200&width=300",
      source: "workspaces.net",
    },
    {
      title: "Programming Tools",
      url: "/globe.svg?height=200&width=300",
      source: "devtools.io",
    },
  ]
  
  export default function ImageResults() {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {mockResults.map((image, index) => (
          <div key={index} className="space-y-2">
            <img
              src={image.url || "/globe.svg"}
              alt={image.title}
              className="w-full h-48 object-cover rounded-lg"
            />
            <h3 className="text-sm font-medium">{image.title}</h3>
            <p className="text-sm text-muted-foreground">{image.source}</p>
          </div>
        ))}
      </div>
    )
  }
  
  