const mockResults = [
    {
      title: "New JavaScript Framework Released",
      source: "Tech News Daily",
      date: "2 hours ago",
      description:
        "A new JavaScript framework promises to revolutionize web development with improved performance and developer experience.",
    },
    {
      title: "Major Updates to Popular Web Technologies",
      source: "Web Dev Weekly",
      date: "1 day ago",
      description:
        "Several major web technologies received significant updates this week, including new features and security improvements.",
    },
  ]
  
  export default function NewsResults() {
    return (
      <>
        {mockResults.map((news, index) => (
          <div key={index} className="space-y-2">
            <h2 className="text-xl font-semibold text-primary hover:underline cursor-pointer">{news.title}</h2>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>{news.source}</span>
              <span>•</span>
              <span>{news.date}</span>
            </div>
            <p className="text-muted-foreground">{news.description}</p>
          </div>
        ))}
      </>
    )
  }
  
  