import { Badge } from "@/components/ui/badge"
import { Calendar, User } from "lucide-react"

const mockResults = [
  {
    id: "PROJ-123",
    title: "Fix authentication bug in login flow",
    status: "In Progress",
    priority: "High",
    assignee: "<PERSON>",
    updated: "2 hours ago",
    description: "Users are experiencing intermittent login failures when using SSO.",
  },
  {
    id: "PROJ-456",
    title: "Implement new dashboard features",
    status: "Open",
    priority: "Medium",
    assignee: "<PERSON>",
    updated: "1 day ago",
    description: "Add new analytics widgets and improve dashboard performance.",
  },
  {
    id: "PROJ-789",
    title: "Update documentation for API v2",
    status: "Review",
    priority: "Low",
    assignee: "<PERSON>",
    updated: "3 days ago",
    description: "Documentation needs to be updated to reflect recent API changes.",
  },
]

const priorityColors = {
  High: "destructive",
  Medium: "default",
  Low: "secondary",
}

const statusColors = {
  "In Progress": "default",
  Open: "secondary",
  Review: "primary",
}

export default function JiraResults() {
  return (
    <div className="space-y-4">
      {mockResults.map((ticket, index) => (
        <div key={index} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
          <div className="flex items-start justify-between mb-2">
            <div>
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm font-mono text-muted-foreground">{ticket.id}</span>
                <Badge variant={priorityColors[ticket.priority]}>{ticket.priority}</Badge>
                <Badge variant={statusColors[ticket.status]}>{ticket.status}</Badge>
              </div>
              <h2 className="text-lg font-semibold hover:text-primary cursor-pointer">{ticket.title}</h2>
            </div>
          </div>
          <p className="text-muted-foreground mb-3">{ticket.description}</p>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              {ticket.assignee}
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {ticket.updated}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

