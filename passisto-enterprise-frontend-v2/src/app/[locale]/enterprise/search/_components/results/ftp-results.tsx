import { File, FileText, FileImage, FileCode, FileArchive, Download } from "lucide-react"
import { Button } from "@/components/ui/button"

const mockResults = [
  {
    name: "project-documentation.pdf",
    type: "pdf",
    size: "2.5 MB",
    modified: "2024-02-25",
    path: "/docs/2024/",
  },
  {
    name: "asset-library.zip",
    type: "zip",
    size: "156 MB",
    modified: "2024-02-24",
    path: "/assets/",
  },
  {
    name: "config.json",
    type: "json",
    size: "4 KB",
    modified: "2024-02-23",
    path: "/config/",
  },
  {
    name: "banner-image.png",
    type: "png",
    size: "1.2 MB",
    modified: "2024-02-22",
    path: "/images/",
  },
]

const fileIcons = {
  pdf: FileText,
  zip: FileArchive,
  json: FileCode,
  png: FileImage,
  default: File,
}

export default function FTPResults() {
  return (
    <div className="border rounded-lg overflow-hidden">
      <div className="grid grid-cols-[1fr_100px_100px_auto] gap-4 p-3 bg-muted text-sm font-medium">
        <div>Name</div>
        <div>Size</div>
        <div>Modified</div>
        <div></div>
      </div>
      <div className="divide-y">
        {mockResults.map((file, index) => {
          const IconComponent = fileIcons[file.type] || fileIcons.default
          return (
            <div
              key={index}
              className="grid grid-cols-[1fr_100px_100px_auto] gap-4 p-3 items-center hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center gap-2">
                <IconComponent className="h-4 w-4 text-muted-foreground" />
                <div className="flex flex-col">
                  <span className="font-medium">{file.name}</span>
                  <span className="text-xs text-muted-foreground">{file.path}</span>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">{file.size}</div>
              <div className="text-sm text-muted-foreground">{file.modified}</div>
              <div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

