import { Search } from "lucide-react"

const mockSuggestions = [
  "web development tutorial",
  "web development courses",
  "web development tools",
  "web development frameworks",
  "web development jobs",
  "open bugs",
  "high priority tasks",
  "my assigned tickets",
  "sprint backlog",
  "recent updates",
  "backup files",
  "project documents",
  "image assets",
  "config files",
  "log files"
]

export default function SearchSuggestions({
  searchQuery,
  setSearchQuery,
  setShowSuggestions,
  handleSearch,
}) {
  const suggestions = mockSuggestions

  return (
    <div className="absolute w-full mt-2 py-2 bg-background rounded-xl border shadow-lg animate-in fade-in slide-in-from-top-2 z-50">
      {suggestions
        .filter((suggestion) => suggestion.toLowerCase().includes(searchQuery.toLowerCase()))
        .map((suggestion, index) => (
          <button
            key={`suggestion-${suggestion}`}
            className="flex items-center w-full px-4 py-2 text-sm hover:bg-muted transition-colors"
            onClick={() => {
              setSearchQuery(suggestion)
              setShowSuggestions(false)
              handleSearch()
            }}
          >
            <Search className="h-4 w-4 mr-2 text-muted-foreground" />
            {suggestion}
          </button>
        ))}
    </div>
  )
}

