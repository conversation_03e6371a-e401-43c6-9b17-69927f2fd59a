"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
// import Layout from "./components/layout" // Assuming this is not part of the current component logic
import SearchBar from "./_components/search-bar"
import SearchResults from "./_components/search-results"
import SearchSettings from "./_components/search-settings"
import SelectedKnowledgeBases from "./_components/selected-knowledge-bases"
import { useAuth } from "@clerk/nextjs"
import { searchApi, type SearchSource } from "@/services/searchApi"
import { toast } from "@/hooks/use-toast"
import { useChatSettings } from "@/context/ChatSettingsContext"
import { useTranslations } from "next-intl" // Import useTranslations

export default function SearchPage() {
  const t = useTranslations("SearchPage") // Initialize useTranslations hook

  const [searchQuery, setSearchQuery] = useState("")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchSource[]>([])
  const [expandedQueries, setExpandedQueries] = useState<string[]>([])
  const { userId } = useAuth()
  const { getSelectedAliasIds } = useChatSettings()

  const handleSearch = async () => {
    if (!searchQuery) {
      toast({
        title: t("noSearchQueryToastTitle"),
        description: t("noSearchQueryToastDescription"),
        variant: "destructive",
      })
      return // Exit if no search query
    }
    setIsLoading(true)
    setShowSuggestions(false)

    try {
      const selectedAliasIds = getSelectedAliasIds()
      const response = await searchApi.search(searchQuery, userId ?? null, selectedAliasIds)

      setSearchResults(response.sources)
      setExpandedQueries(response.expanded_queries)
      setHasSearched(true)
    } catch (err) {
      console.error("Search error:", err)
      toast({
        title: t("searchErrorTitle"),
        description: t("searchErrorDescription"),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn("w-full max-w-2xl mx-auto transition-all duration-300", hasSearched ? "mt-8" : "mt-32")}>
      <h1
        className={cn(
          "text-4xl md:text-6xl font-bold text-center bg-gradient-to-r from-primary to-primary-foreground bg-clip-text text-transparent pb-2 transition-all duration-300",
          hasSearched && "text-2xl md:text-3xl",
        )}
      >
        {t("title")}<span className="text-primary">{t("titleHighlight")}</span>
      </h1>

      <div className="mt-6 space-y-4">
        <div className="flex justify-center items-center gap-2">
          <SearchSettings />
        </div>

        <div className="flex justify-center">
          <SelectedKnowledgeBases className="mb-2" />
        </div>

        <SearchBar
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          showSuggestions={showSuggestions}
          setShowSuggestions={setShowSuggestions}
          handleSearch={handleSearch}
          setHasSearched={setHasSearched}
          isLoading={isLoading}
        />
      </div>

      {!hasSearched && (
        <div className="flex justify-center space-x-4 mt-6">
          <Button
            className="h-11 px-6 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105"
            onClick={handleSearch}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="mr-2">{t("searchingButton")}</span>
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
              </>
            ) : (
              t("searchNowButton")
            )}
          </Button>
        </div>
      )}

      {hasSearched && <SearchResults isLoading={isLoading} searchResults={searchResults} expandedQueries={expandedQueries} />}
    </div>
  )
}