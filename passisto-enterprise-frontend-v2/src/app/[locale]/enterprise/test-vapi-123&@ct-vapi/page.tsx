'use client'
import React, { useState } from 'react';
import Vapi from '@vapi-ai/web';

const VapiButtonComponent = () => {
  const [callStatus, setCallStatus] = useState<'IDLE' | 'CONNECTING' | 'ACTIVE'>('IDLE');
  const [activeButton, setActiveButton] = useState<number | null>(null);

  // Retrieve tokens and workflows from environment variables
  const tokens = [
    process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T1,
    process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T2,
    process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T3,
  ];

  const workflows = [
    process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W1,
    process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W2,
    process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W3,
  ];

  // Validate the presence of tokens and workflows
  if (tokens.includes(undefined) || workflows.includes(undefined)) {
    console.error('Missing environment variables for VAPI tokens or workflows.');
    return null;
  }

  const handleCall = async (token: string, workflowId: string, index: number) => {
    setCallStatus('CONNECTING');
    setActiveButton(index);
    console.log(token);
    console.log(workflowId);
    const vapi = new Vapi(token);

    try {
      await vapi.start(workflowId, {
        variableValues: {
          // Add specific values as needed
        },
      });
      setCallStatus('ACTIVE');

      // Optionally, handle call events
      vapi.on('call-end', () => {
        setCallStatus('IDLE');
        setActiveButton(null);
      });
    } catch (error) {
      console.error('Error during Vapi call:', error);
      setCallStatus('IDLE');
      setActiveButton(null);
    }
  };

  const getButtonStyle = (status: string, isActive: boolean) => {
    let baseStyle = {
      padding: '10px 20px',
      fontSize: '16px',
      border: 'none',
      borderRadius: '5px',
      cursor: 'pointer',
    };
    if (isActive) {
      baseStyle = { ...baseStyle, backgroundColor: '#FFC107', color: 'white' }; // Connecting
    } else if (status === 'ACTIVE') {
      baseStyle = { ...baseStyle, backgroundColor: '#28A745', color: 'white' }; // Active
    } else {
      baseStyle = { ...baseStyle, backgroundColor: '#007BFF', color: 'white' }; // Idle
    }
    return baseStyle;
  };

  const getStatusText = () => {
    switch (callStatus) {
      case 'CONNECTING':
        return 'Connecting... Please wait.';
      case 'ACTIVE':
        return 'Vapi call is active!';
      default:
        return 'Click a button to start.';
    }
  };

  return (
    <div style={{ textAlign: 'center', padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ fontSize: '24px', marginBottom: '20px' }}>Vapi Call Status</h1>
      <p style={{ fontSize: '18px', marginBottom: '20px' }}>{getStatusText()}</p>

      <div style={{ display: 'flex', justifyContent: 'center', gap: '15px' }}>
        {tokens.map((token, index) => (
          <button
            key={index}
            onClick={() => handleCall(token!, workflows[index]!, index)}
            style={getButtonStyle(callStatus, activeButton === index)}
            disabled={callStatus === 'CONNECTING'}
          >
            Call Vapi {index + 1}
          </button>
        ))}
      </div>
    </div>
  );
};

export default VapiButtonComponent;
