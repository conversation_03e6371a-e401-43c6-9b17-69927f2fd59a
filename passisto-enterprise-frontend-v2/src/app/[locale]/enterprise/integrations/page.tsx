"use client";

import { IntegrationsTable } from "@/components/integrations/integrations-table";
import { Button } from "@/components/ui/button";
import {
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Database,
} from "lucide-react";
import { Link } from '@/i18n/nvigation';;
import { useEffect, useState } from "react";
import { useAuth } from "@clerk/nextjs";
import { ALL_INTEGRATIONS } from "@/utils/routes";
import axiosInstance from "@/config/axios";
import { checkPermissions, integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface IntegrationMetrics {
  total: number;
  active: number;
  completed: number;
  failed: number;
}

export default function IntegrationsPage() {
  const t = useTranslations();
  const [metrics, setMetrics] = useState<IntegrationMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const router = useRouter();


  useEffect(() => {
      // Only check permissions after backendUser has loaded
      if (!backendUserLoading && backendUser) {
        const permissions = backendUser?.permissions ?? [];
  
        // Check if user has at least one integration-related permission
        const hasAnyIntegrationPermission = checkPermissions(permissions, [
         
          integrationPermissions.canView,
          
        ]);
  
        // Redirect if user doesn't have any integration-related permissions
        if (!hasAnyIntegrationPermission) {
          toast.error(t('permission-denied'), {
            description:
              t('you-dont-have-permission-to-access-the-integrations-page'),
          });
          router.back();
        }
      }
    }, [backendUser, backendUserLoading, router]);

  // Replace with your actual company ID or get it from context/params
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const token = await getToken();
        const headers = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        const { data } = await axiosInstance.get<IntegrationMetrics>(
          `${ALL_INTEGRATIONS}/metrics`,
          headers
        );
        setMetrics(data);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : t('failed-to-fetch-integration-metrics')
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchMetrics();
  }, [getToken]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="ml-4 text-muted-foreground">
              {t('loading-integration-metrics')}
            </p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 container py-6">
          <div className="flex items-center justify-center h-64">
            <p className="text-red-500">Error: {error}</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <main className="flex-1 container py-10 px-4 md:px-8">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight dark:text-white">
              {t('data-integrations')}
            </h1>
            <p className="mt-2 text-lg text-gray-600 dark:text-gray-400 max-w-md">
              {t('manage-and-connect-your-enterprise-data-from-a-variety-of-sources-all-in-one-place')}
            </p>
          </div>

          <Link href="/enterprise/integrations/new">
            {integrationPermissions.canCreateAll(
              backendUser?.permissions || []
            ) && (
              <Button className="mt-4 md:mt-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-purple-700">
                <Plus className="mr-2 h-4 w-4" />
                {t('new-integration')}
              </Button>
            )}
          </Link>
        </div>

        {/* Metrics Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <IntegrationSummaryCard
            title={t('total-integrations')}
            value={metrics?.total.toString() || "0"}
            description={t('active-data-connections')}
            icon={<Database className="h-6 w-6 text-blue-500" />}
          />
          <IntegrationSummaryCard
            title={t('active')}
            value={metrics?.active.toString() || "0"}
            description={t('currently-processing')}
            variant="running"
            icon={<RefreshCw className="h-6 w-6 text-yellow-500" />}
          />
          <IntegrationSummaryCard
            title={t('completed')}
            value={metrics?.completed.toString() || "0"}
            description={t('successfully-processed')}
            variant="completed"
            icon={<CheckCircle className="h-6 w-6 text-green-500" />}
          />
          <IntegrationSummaryCard
            title={t('failed-0')}
            value={metrics?.failed.toString() || "0"}
            description={t('needs-attention')}
            variant="failed"
            icon={<AlertCircle className="h-6 w-6 text-red-500" />}
          />
        </div>

        {/* Table Section */}
        <div className="bg-card rounded-lg border shadow-lg">
          <div className="p-6 border-b">
            <h2 className="text-2xl font-semibold text-primary">
              {t('all-integrations')}
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {t('view-filter-and-manage-your-data-integrations')}
            </p>
          </div>
          <div className="p-6">
            <IntegrationsTable />
          </div>
        </div>
      </main>
    </div>
  );
}

interface IntegrationSummaryCardProps {
  title: string;
  value: string;
  description: string;
  variant?: "default" | "running" | "completed" | "failed";
  icon?: React.ReactNode;
}

function IntegrationSummaryCard({
  title,
  value,
  description,
  variant = "default",
  icon,
}: IntegrationSummaryCardProps) {
  const variantStyles = {
    default: "bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700",
    running:
      "bg-yellow-50 border-yellow-200 dark:bg-yellow-900 dark:border-yellow-800",
    completed:
      "bg-green-50 border-green-200 dark:bg-green-900 dark:border-green-800",
    failed: "bg-red-50 border-red-200 dark:bg-red-900 dark:border-red-800",
  };

  const valueStyles = {
    default: "text-gray-800 dark:text-gray-200",
    running: "text-yellow-600 dark:text-yellow-400",
    completed: "text-green-600 dark:text-green-400",
    failed: "text-red-600 dark:text-red-400",
  };

  return (
    <div
      className={`rounded-lg border p-6 shadow-md hover:shadow-lg transition-shadow duration-200 ${variantStyles[variant]}`}
    >
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
        {icon}
      </div>
      <p className={`text-3xl font-bold mt-2 ${valueStyles[variant]}`}>
        {value}
      </p>
      <p className="text-xs text-muted-foreground mt-1">{description}</p>
    </div>
  );
}
