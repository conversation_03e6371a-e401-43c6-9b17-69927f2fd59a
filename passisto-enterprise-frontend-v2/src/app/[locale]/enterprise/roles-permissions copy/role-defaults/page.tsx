"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Info } from "lucide-react"

export default function RoleDefaultsPage() {
  const router = useRouter()

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Role Default Permissions</h1>
          <p className="text-muted-foreground mt-2">Configure default permissions for each role.</p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push("/enterprise/roles-permissions")}
          className="w-full sm:w-auto"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Roles & Permissions
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Info className="h-5 w-5 mr-2 text-blue-500" />
            Direct Permission Model
          </CardTitle>
          <CardDescription>
            This system uses a direct user-to-permission model rather than role-based access control.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              In this system, permissions are assigned directly to users, not inherited through roles. Roles are used
              for organizational and categorization purposes only.
            </p>
            <p>To assign permissions to a user:</p>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Navigate to the Users page</li>
              <li>Select a user to edit</li>
              <li>Use the Permissions tab to directly assign permissions to that user</li>
            </ol>
            <div className="bg-muted p-4 rounded-md mt-4">
              <p className="text-sm">
                This page has been deprecated as it was designed for a role-based access control system. Please use the
                main Roles & Permissions page to manage available roles and permissions.
              </p>
            </div>
            <Button onClick={() => router.push("/enterprise/roles-permissions")} className="w-full sm:w-auto mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to Roles & Permissions
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

