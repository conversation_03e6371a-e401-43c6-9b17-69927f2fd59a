"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Edit } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useAuth } from "@clerk/nextjs";
import { AxiosResponse } from "axios";
import axiosInstance from "@/config/axios";
import { EMAIL_BUILDER_GET_BY_ID } from "@/utils/routes";
import type { GenerateEmailResponse } from "../../_lib/types";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function PreviewPage({ params }: { params: { id: string } }) {
  const { getToken } = useAuth();
  const router = useRouter();
  const t = useTranslations("previewPage"); // Initialize useTranslations

  const [template, setTemplate] = useState<GenerateEmailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplateById = async (emailId: String) => {
    try {
      console.log(emailId);
      const token = await getToken();

      const response: AxiosResponse<GenerateEmailResponse> =
        await axiosInstance.get(EMAIL_BUILDER_GET_BY_ID(emailId), {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      console.log(response.data.fields);
      return response.data;
    } catch (error: any) {
      console.error(
        "Error fetching email:",
        error?.response?.data || error?.message
      );
      throw error;
    }
  };

  useEffect(() => {
    const loadTemplate = async () => {
      try {
        setLoading(true);
        const data = await fetchTemplateById(params.id);
        setTemplate(data);
        setError(null);
      } catch (err) {
        console.error("Failed to load template:", err);
        setError(t("failedToLoadTemplate")); // Use translation
      } finally {
        setLoading(false);
      }
    };

    loadTemplate();
  }, [params.id, t]); // Add 't' to the dependency array

  const handleBack = () => {
    router.push("/enterprise/ai-agents/email-builder/templates");
  };

  const handleEdit = () => {
    router.push(`/enterprise/ai-agents/email-builder/editor/${params.id}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">{t("errorTitle")}</h1>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>{t("errorTitle")}</AlertTitle>
          <AlertDescription>
            {error}.{" "}
            <Button variant="link" onClick={handleBack} className="p-0 h-auto">
              {t("goBackToTemplates")}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">{t("templateNotFoundTitle")}</h1>
        </div>
        <Alert>
          <AlertTitle>{t("templateNotFoundTitle")}</AlertTitle>
          <AlertDescription>
            {t("templateNotFoundDescription")}
            <Button variant="link" onClick={handleBack} className="p-0 h-auto ml-2">
              {t("goBackToTemplates")}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-4 px-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{template.title}</h1>
            {template.description && (
              <p className="text-gray-500 dark:text-gray-400 mt-1">
                {template.description}
              </p>
            )}
          </div>
        </div>
        <Button onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" /> {t("editTemplateButton")}
        </Button>
      </div>

      <div className="bg-white dark:bg-gray-800 border rounded-lg p-6 shadow-sm">
        {template.html ? (
          <div className="max-w-3xl mx-auto">
            <iframe
              srcDoc={template.html}
              title={template.title}
              className="w-full min-h-[600px] border-0"
              sandbox="allow-same-origin"
            />
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              {t("noHtmlPreview")}
            </p>
            <Button variant="outline" onClick={handleEdit} className="mt-4">
              <Edit className="mr-2 h-4 w-4" /> {t("editToGenerateHtml")}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}