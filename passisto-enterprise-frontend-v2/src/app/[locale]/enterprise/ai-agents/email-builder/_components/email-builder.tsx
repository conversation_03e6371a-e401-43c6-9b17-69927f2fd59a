"use client";

import { useState, useEffect, forwardRef, useImper<PERSON><PERSON><PERSON>le } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Sidebar } from "./sidebar";
import { Canvas } from "./canvas";
import { SettingsPanel } from "./settings-panel";
import { PreviewPanel } from "./preview-panel";
import { Toolbar } from "./toolbar";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import type {
  ComponentType,
  EmailComponent,
  ViewMode,
  EditorMode,
  EmailBuilderProps,
  GenerateEmailResponse,
} from "../_lib/types";
import axiosInstance from "@/config/axios";
import { EMAIL_BUILDER_EDIT, EMAIL_BUILDER_GET_BY_ID } from "@/utils/routes";
import { useParams } from "next/navigation"
import { useAuth } from "@clerk/nextjs";
import { AxiosResponse } from "axios";
import { useTranslations } from "next-intl";

export type {
  ComponentType,
  EmailComponent,
  ViewMode,
  EditorMode,
} from "../_lib/types";

export const EmailBuilder = forwardRef<
  { generateHTML: () => string },
  EmailBuilderProps
>(
  (
    {
      initialComponents = [],
      readOnly = false,
      onHTMLChange,
    }: EmailBuilderProps,
    ref
  ) => {
    const t = useTranslations("emailBuilderPage");
    const { getToken } = useAuth();

    const params = useParams()
    const emailId = params?.id as string

    const [components, setComponents] =
      useState<EmailComponent[]>(initialComponents);
    const [selectedComponent, setSelectedComponent] =
      useState<EmailComponent | null>(null);
    const [viewMode, setViewMode] = useState<ViewMode>("desktop");
    const [editorMode, setEditorMode] = useState<EditorMode>(
      readOnly ? "preview" : "design"
    );
    const [history, setHistory] = useState<EmailComponent[][]>([
      initialComponents.length ? [...initialComponents] : [],
    ]);
    const [historyIndex, setHistoryIndex] = useState(0);

    // Initialize with initial components if provided
    useEffect(() => {
      if (initialComponents.length > 0) {
        setComponents(initialComponents);
        setHistory([initialComponents]);
        setHistoryIndex(0);
      }
    }, [initialComponents]);

    // Expose the generateHTML method to parent components
    useImperativeHandle(ref, () => ({
      generateHTML: () => {
        const html = generateHTML();
        if (onHTMLChange) {
          onHTMLChange(html);
        }
        return html;
      },
    }));

    // Add a component to the canvas
    const addComponent = (type: ComponentType) => {
      const newComponent: EmailComponent = {
        id: uuidv4(),
        type,
        content: getDefaultContent(type),
        settings: getDefaultSettings(type),
      };

      // Add default items for list
      if (type === "list") {
        newComponent.items = ["Item 1", "Item 2", "Item 3"];
        newComponent.content = undefined;
      }

      // Add default headers and rows for table
      if (type === "table") {
        newComponent.headers = ["Header 1", "Header 2", "Header 3"];
        newComponent.rows = [
          ["Row 1, Cell 1", "Row 1, Cell 2", "Row 1, Cell 3"],
          ["Row 2, Cell 1", "Row 2, Cell 2", "Row 2, Cell 3"],
        ];
        newComponent.content = undefined;
      }

      // Add default social links
      if (type === "social") {
        newComponent.socialLinks = [
          { platform: "facebook", url: "https://facebook.com", enabled: true },
          { platform: "twitter", url: "https://twitter.com", enabled: true },
          {
            platform: "instagram",
            url: "https://instagram.com",
            enabled: true,
          },
          { platform: "linkedin", url: "https://linkedin.com", enabled: true },
          { platform: "youtube", url: "https://youtube.com", enabled: false },
        ];
        newComponent.content = undefined;
      }
      // Preserve existing components and add the new one
      setComponents((prevComponents) => {
        const newComponents = [...prevComponents, newComponent];
        addToHistory(newComponents); // Add the new state to history
        return newComponents;
      });      
    };

    // Update a component's settings
    const updateComponent = (id: string, updates: Partial<EmailComponent>) => {
      const newComponents = components.map((component) =>
        component.id === id ? { ...component, ...updates } : component
      );
      setComponents(newComponents);

      // If we're updating the selected component, update that too
      if (selectedComponent && selectedComponent.id === id) {
        setSelectedComponent({ ...selectedComponent, ...updates });
      }

      addToHistory(newComponents);
    };

    // Remove a component from the canvas
    const removeComponent = (id: string) => {
      const newComponents = components.filter(
        (component) => component.id !== id
      );
      setComponents(newComponents);

      if (selectedComponent && selectedComponent.id === id) {
        setSelectedComponent(null);
      }

      addToHistory(newComponents);
    };

    // Reorder components
    const reorderComponents = (startIndex: number, endIndex: number) => {
      const result = Array.from(components);
      const [removed] = result.splice(startIndex, 1);
      result.splice(endIndex, 0, removed);

      setComponents(result);
      addToHistory(result);
    };

    // Add current state to history
    const addToHistory = (newComponents: EmailComponent[]) => {
      // If we're not at the end of the history, truncate it
      const newHistory = history.slice(0, historyIndex + 1);

      // Add the new state
      newHistory.push([...newComponents]);

      // Update history and index
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    };

    // Undo
    const undo = () => {
      if (historyIndex > 0) {
        setHistoryIndex(historyIndex - 1);
        setComponents([...history[historyIndex - 1]]);
      }
    };

    // Redo
    const redo = () => {
      if (historyIndex < history.length - 1) {
        setHistoryIndex(historyIndex + 1);
        setComponents([...history[historyIndex + 1]]);
      }
    };

    // Save template to local storage
    const saveTemplate = async(name: string) => {
      // IN DB
      try {
        // const templates = JSON.parse(
        //   localStorage.getItem("emailTemplates") || "{}"
        // );
        // templates[name] = components;
        // localStorage.setItem("emailTemplates", JSON.stringify(templates));
        const token = await getToken();
        const response = await axiosInstance.put(EMAIL_BUILDER_EDIT(emailId), 
        {
          title: name,
          fields: components,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        toast(t('template-saved'), {
          description: t('template-name-has-been-saved-successfully', { name }),
        });
      } catch (error) {
        console.log(error);
        toast(t('error-saving-template'), {
          description: t('there-was-an-error-saving-your-template'),
        });
      }
    };
    const getEmailById = async (emailId:String) => {
      try {
        console.log(emailId);
        const token = await getToken();
  
        const response: AxiosResponse<GenerateEmailResponse> = await axiosInstance.get(EMAIL_BUILDER_GET_BY_ID(emailId), {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        console.log(response.data.fields);
        // setInitialComponents(response.data.fields)
        return response.data;
      } catch (error: any) {
        console.error("Error fetching email:", error?.response?.data || error?.message);
        throw error;
      }
    };
    // Load template from local storage
    const loadTemplate = async(id: string) => {
      try {
        // const templates = JSON.parse(
        //   localStorage.getItem("emailTemplates") || "{}"
        // );
        // if (templates[name]) {
        //   setComponents(templates[name]);
        //   addToHistory(templates[name]);
        //   toast(
        //     "Template loaded",

        //     { description: `Template "${name}" has been loaded successfully.` }
        //   );
        // }
        const template = await getEmailById(id)
        setComponents(template.fields);
        addToHistory(template.fields);
          // setComponents(templates[name]);
          // addToHistory(templates[name]);
          toast(
            t('template-loaded'),

            { description: t('template-name-has-been-loaded-successfully', { name }) }
          );
      } catch (error) {
        toast(t('error-loading-template'), {
          description: t('there-was-an-error-loading-your-template'),
        });
      }
    };

    // Generate HTML from components
    const generateHTML = () => {
      let html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Template</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
  <table cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">`;

      components.forEach((component) => {
        const { type, content, settings } = component;
        const style = generateComponentStyle(settings);

        switch (type) {
          case "header":
            html += `
    <tr>
      <td style="${style}">
        <h1 style="margin: 0; font-weight: ${
          settings.fontWeight || "bold"
        }; line-height: ${settings.lineHeight || "1.2"};">${content}</h1>
      </td>
    </tr>`;
            break;
          case "text":
            html += `
    <tr>
      <td style="${style}">
        <p style="margin: 0; font-weight: ${
          settings.fontWeight || "normal"
        }; line-height: ${settings.lineHeight || "1.5"};">${content}</p>
      </td>
    </tr>`;
            break;
          case "image":
            const imgHtml = `<img src="${
              settings.url || "/placeholder.svg?height=200&width=600"
            }" alt="${settings.altText || "Image"}" style="width: ${
              settings.width || "100%"
            }; height: ${settings.height || "auto"}; border-radius: ${
              settings.borderRadius || "0px"
            };" />`;

            html += `
    <tr>
      <td style="${style}">
        ${
          settings.linkImage === "true"
            ? `<a href="${
                settings.linkUrl || "#"
              }" target="_blank" rel="noopener noreferrer" style="text-decoration: none;">${imgHtml}</a>`
            : imgHtml
        }
      </td>
    </tr>`;
            break;
          case "button":
            html += `
    <tr>
      <td style="${style}">
        <table cellpadding="0" cellspacing="0" style="margin: ${
          settings.alignment === "center"
            ? "0 auto"
            : settings.alignment === "right"
            ? "0 0 0 auto"
            : "0"
        }; width: ${settings.buttonWidth === "full" ? "100%" : "auto"};">
          <tr>
            <td style="background-color: ${
              settings.backgroundColor || "#0070f3"
            }; border-radius: ${
              settings.borderRadius || "4px"
            }; padding: 12px 24px; border: ${
              settings.borderWidth || "0px"
            } solid ${settings.borderColor || "transparent"};">
              <a href="${
                settings.url || "#"
              }" target="_blank" rel="noopener noreferrer" style="color: ${
              settings.color || "#ffffff"
            }; text-decoration: none; display: inline-block; font-weight: ${
              settings.fontWeight || "normal"
            };">${content}</a>
            </td>
          </tr>
        </table>
      </td>
    </tr>`;
            break;
          case "divider":
            html += `
    <tr>
      <td style="${style}">
        <hr style="border: none; border-top: ${
          settings.dividerWidth || "1px"
        } ${settings.dividerStyle || "solid"} ${
              settings.dividerColor || "#e5e7eb"
            }; margin: 0;" />
      </td>
    </tr>`;
            break;
          case "footer":
            html += `
    <tr>
      <td style="${style}">
        <p style="margin: 0; font-size: ${
          settings.fontSize || "12px"
        }; color: ${settings.color || "#6b7280"}; font-weight: ${
              settings.fontWeight || "normal"
            }; line-height: ${settings.lineHeight || "1.5"};">${content}</p>
      </td>
    </tr>`;
            break;
          case "list":
            const listItems = component.items || [];
            const listType = settings.listType === "number" ? "ol" : "ul";
            const listItemsHtml = listItems
              .map((item) => `<li style="margin-bottom: 8px;">${item}</li>`)
              .join("");

            html += `
<tr>
  <td style="${style}">
    <${listType} style="margin: 0; padding-left: 20px; color: ${
              settings.color || "#333333"
            }; font-size: ${settings.fontSize || "16px"};">
      ${listItemsHtml}
    </${listType}>
  </td>
</tr>`;
            break;

          case "table":
            const headers = component.headers || [];
            const rows = component.rows || [];

            let tableHtml = `<table cellpadding="8" cellspacing="0" width="100%" style="border-collapse: collapse;">`;

            // Add headers
            if (headers.length > 0) {
              tableHtml += `<tr>`;
              headers.forEach((header) => {
                tableHtml += `<th style="text-align: left; background-color: ${
                  settings.headerBackgroundColor || "#f3f4f6"
                }; border: 1px solid ${
                  settings.borderColor || "#e5e7eb"
                }; color: ${settings.color || "#333333"}; font-size: ${
                  settings.fontSize || "14px"
                };">${header}</th>`;
              });
              tableHtml += `</tr>`;
            }

            // Add rows
            rows.forEach((row) => {
              tableHtml += `<tr>`;
              row.forEach((cell) => {
                tableHtml += `<td style="border: 1px solid ${
                  settings.borderColor || "#e5e7eb"
                }; color: ${settings.color || "#333333"}; font-size: ${
                  settings.fontSize || "14px"
                };">${cell}</td>`;
              });
              tableHtml += `</tr>`;
            });

            tableHtml += `</table>`;

            html += `
<tr>
  <td style="${style}">
    ${tableHtml}
  </td>
</tr>`;
            break;

          case "social":
            const socialLinks = component.socialLinks || [];
            const iconSize = settings.iconSize || "32px";
            const iconSpacing = settings.iconSpacing || "10px";
            const iconColor = settings.iconColor || "#333333";

            // Filter only enabled social links
            const enabledSocialLinks = socialLinks.filter(
              (link) => link.enabled
            );

            // Create the social icons HTML
            let socialIconsHtml = `<div style="font-size: 0;">`;
            enabledSocialLinks.forEach((link) => {
              // Get appropriate icon for each platform
              const iconSvg = getSocialIconSvg(link.platform, iconColor);

              socialIconsHtml += `
              <a href="${
                link.url
              }" target="_blank" rel="noopener noreferrer" style="display: inline-block; margin-right: ${iconSpacing}; text-decoration: none;">
                <img src="data:image/svg+xml;base64,${btoa(iconSvg)}" alt="${
                link.platform
              }" width="${iconSize}" height="${iconSize}" style="border: 0;" />
              </a>`;
            });
            socialIconsHtml += `</div>`;

            html += `
<tr>
  <td style="${style}; text-align: ${settings.alignment || "center"};">
    ${socialIconsHtml}
  </td>
</tr>`;
            break;
        }
      });

      html += `
  </table>
</body>
</html>`;

      // After generating the HTML, call onHTMLChange if it exists
      if (onHTMLChange) {
        onHTMLChange(html);
      }

      return html;
    };

    // Function to get SVG icon for social platforms
    const getSocialIconSvg = (platform: string, color: string): string => {
      const svgColor = color.replace("#", "%23"); // URL-encode the color for SVG

      switch (platform.toLowerCase()) {
        case "facebook":
          return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>`;
        case "twitter":
          return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>`;
        case "instagram":
          return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>`;
        case "linkedin":
          return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle></svg>`;
        case "youtube":
          return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon></svg>`;
        default:
          return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="${svgColor}" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>`;
      }
    };

    // Helper function to generate CSS style string from settings
    const generateComponentStyle = (settings: EmailComponent["settings"]) => {
      let style = "padding: " + (settings.padding || "16px") + "; ";

      if (settings.margin) style += "margin: " + settings.margin + "; ";
      if (settings.color) style += "color: " + settings.color + "; ";
      if (settings.backgroundColor)
        style += "background-color: " + settings.backgroundColor + "; ";
      if (settings.fontSize) style += "font-size: " + settings.fontSize + "; ";
      if (settings.alignment)
        style += "text-align: " + settings.alignment + "; ";

      return style;
    };

    // Helper function to get default content based on component type
    const getDefaultContent = (type: ComponentType): string => {
      switch (type) {
        case "header":
          return t('your-email-header');
        case "text":
          return t('this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content');
        case "button":
          return t('click-me');
        case "footer":
          return t('c-2025-your-company-all-rights-reserved');
        default:
          return "";
      }
    };

    // Helper function to get default settings based on component type
    const getDefaultSettings = (
      type: ComponentType
    ): EmailComponent["settings"] => {
      const baseSettings = {
        padding: "16px",
        paddingUnit: "px",
        margin: "0",
        marginUnit: "px",
        alignment: "left" as const,
      };

      switch (type) {
        case "header":
          return {
            ...baseSettings,
            fontSize: "24px",
            fontSizeUnit: "px",
            color: "#000000",
            fontWeight: "bold",
            lineHeight: "1.2",
          };
        case "text":
          return {
            ...baseSettings,
            fontSize: "16px",
            fontSizeUnit: "px",
            color: "#333333",
            fontWeight: "normal",
            lineHeight: "1.5",
          };
        case "image":
          return {
            ...baseSettings,
            width: "100%",
            widthUnit: "%",
            height: "auto",
            heightUnit: "auto",
            url: "/placeholder.svg?height=200&width=600",
            altText: "Email image",
            borderRadius: "0px",
            linkImage: "false",
          };
        case "button":
          return {
            ...baseSettings,
            alignment: "center",
            backgroundColor: "#0070f3",
            color: "#ffffff",
            padding: "12px 24px",
            paddingUnit: "px",
            borderRadius: "4px",
            url: "#",
            fontWeight: "normal",
            buttonWidth: "auto",
            borderColor: "transparent",
            borderWidth: "0px",
          };
        case "divider":
          return {
            ...baseSettings,
            padding: "8px 16px",
            paddingUnit: "px",
            dividerColor: "#e5e7eb",
            dividerWidth: "1px",
            dividerStyle: "solid",
          };
        case "footer":
          return {
            ...baseSettings,
            fontSize: "12px",
            fontSizeUnit: "px",
            color: "#6b7280",
            backgroundColor: "#f9fafb",
            fontWeight: "normal",
            lineHeight: "1.5",
          };
        case "list":
          return {
            ...baseSettings,
            fontSize: "16px",
            fontSizeUnit: "px",
            color: "#333333",
            listType: "bullet",
          };
        case "table":
          return {
            ...baseSettings,
            fontSize: "14px",
            fontSizeUnit: "px",
            color: "#333333",
            headerBackgroundColor: "#f3f4f6",
            borderColor: "#e5e7eb",
          };
        case "social":
          return {
            ...baseSettings,
            alignment: "center",
            iconSize: "32px",
            iconSpacing: "10px",
            iconColor: "#333333",
          };
        default:
          return baseSettings;
      }
    };

    if (readOnly) {
      return (
        <div className="h-full">
          <PreviewPanel components={components} viewMode={viewMode} />
        </div>
      );
    }

    return (
      <DndProvider backend={HTML5Backend}>
        <div className="flex flex-col h-screen">
          <Toolbar
            viewMode={viewMode}
            setViewMode={setViewMode}
            editorMode={editorMode}
            setEditorMode={setEditorMode}
            canUndo={historyIndex > 0}
            canRedo={historyIndex < history.length - 1}
            onUndo={undo}
            onRedo={redo}
            onSave={saveTemplate}
            onLoad={loadTemplate}
            generateHTML={generateHTML}
            setComponents={setComponents}
          />

          <div className="flex flex-1 overflow-hidden">
            {editorMode !== "code" && <Sidebar onAddComponent={addComponent} />}

            <div className="flex-1 overflow-auto">
              {editorMode === "design" && (
                <Canvas
                  components={components}
                  selectedComponent={selectedComponent}
                  setSelectedComponent={setSelectedComponent}
                  onUpdateComponent={updateComponent}
                  onRemoveComponent={removeComponent}
                  onReorderComponents={reorderComponents}
                  onAddComponent={addComponent}
                  viewMode={viewMode}
                />                
              )}

              {editorMode === "preview" && (
                <PreviewPanel components={components} viewMode={viewMode} />
              )}

              {editorMode === "code" && (
                <div className="h-full p-4 bg-muted/20">
                  <pre className="h-full p-4 overflow-auto text-sm bg-muted rounded-md">
                    <code>{generateHTML()}</code>
                  </pre>
                </div>
              )}
            </div>

            {editorMode === "design" && selectedComponent && (
              <SettingsPanel
                component={selectedComponent}
                onUpdateComponent={(updates) =>
                  updateComponent(selectedComponent.id, updates)
                }
                onRemoveComponent={() => removeComponent(selectedComponent.id)}
              />
            )}
          </div>
        </div>
      </DndProvider>
    );
  }
);

EmailBuilder.displayName = "EmailBuilder";
