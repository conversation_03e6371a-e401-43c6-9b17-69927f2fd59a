"use client"

import type React from "react"
import { Facebook, Twitter, Instagram, Linkedin, Youtube } from "lucide-react"
import type { EmailComponent } from "./email-builder"
import { cn } from "@/lib/utils"

interface ComponentPreviewProps {
  component: EmailComponent
  isPreview?: boolean
}

export function ComponentPreview({ component, isPreview = false }: ComponentPreviewProps) {
  const { type, content, settings } = component

  const style: React.CSSProperties = {
    padding: settings.padding || "16px",
    margin: settings.margin || "0",
    color: settings.color,
    backgroundColor: settings.backgroundColor,
    fontSize: settings.fontSize,
    textAlign: settings.alignment as any,
    fontWeight: settings.fontWeight,
    lineHeight: settings.lineHeight,
  }

  switch (type) {
    case "header":
      return (
        <div style={style} className={cn("border-b", !isPreview && "min-h-[60px]")}>
          <h1 className="text-2xl font-bold m-0">{content}</h1>
        </div>
      )

    case "text":
      return (
        <div style={style} className={cn(!isPreview && "min-h-[60px]")}>
          <p className="m-0">{content}</p>
        </div>
      )

    case "image":
      const imageStyle: React.CSSProperties = {
        width: settings.width || "100%",
        height: settings.height || "auto",
        borderRadius: settings.borderRadius || "0px",
      }

      const imageElement = (
        <img
          src={settings.url || "/placeholder.svg?height=200&width=600"}
          alt={settings.altText || "Email image"}
          className="w-full h-auto"
          style={imageStyle}
        />
      )

      return (
        <div style={style} className={cn(!isPreview && "min-h-[60px]")}>
          {settings.linkImage === "true" ? (
            <a
              href={settings.linkUrl || "#"}
              target="_blank"
              rel="noopener noreferrer"
              onClick={isPreview ? undefined : (e) => e.preventDefault()}
            >
              {imageElement}
            </a>
          ) : (
            imageElement
          )}
        </div>
      )

    case "button":
      const buttonStyle: React.CSSProperties = {
        backgroundColor: settings.backgroundColor || "#0070f3",
        color: settings.color || "#ffffff",
        borderRadius: settings.borderRadius || "4px",
        borderWidth: settings.borderWidth || "0px",
        borderStyle: "solid",
        borderColor: settings.borderColor || "transparent",
        width: settings.buttonWidth === "full" ? "100%" : "auto",
      }

      return (
        <div
          style={style}
          className={cn(
            "flex",
            settings.alignment === "center"
              ? "justify-center"
              : settings.alignment === "right"
                ? "justify-end"
                : "justify-start",
            !isPreview && "min-h-[60px]",
          )}
        >
          <a
            href={settings.url || "#"}
            className={cn(
              "inline-block px-6 py-3 rounded text-center no-underline",
              !isPreview && "pointer-events-none",
            )}
            style={buttonStyle}
            onClick={isPreview ? undefined : (e) => e.preventDefault()}
          >
            {content}
          </a>
        </div>
      )

    case "divider":
      return (
        <div style={style} className={cn(!isPreview && "min-h-[20px]")}>
          <hr
            className="m-0"
            style={{
              borderWidth: "0",
              borderTopWidth: settings.dividerWidth || "1px",
              borderTopStyle: settings.dividerStyle || "solid",
              borderTopColor: settings.dividerColor || "#e5e7eb",
            }}
          />
        </div>
      )

    case "footer":
      return (
        <div style={style} className={cn("border-t", !isPreview && "min-h-[60px]")}>
          <p className="text-sm text-gray-500 m-0">{content}</p>
        </div>
      )

    case "list":
      const listItems = component.items || []
      const ListTag = settings.listType === "number" ? "ol" : "ul"

      return (
        <div style={style} className={cn(!isPreview && "min-h-[60px]")}>
          <ListTag className="pl-5 m-0">
            {listItems.map((item, index) => (
              <li key={index} className="mb-2">
                {item}
              </li>
            ))}
          </ListTag>
        </div>
      )

    case "table":
      const headers = component.headers || []
      const rows = component.rows || []

      return (
        <div style={style} className={cn(!isPreview && "min-h-[60px]", "overflow-x-auto")}>
          <table className="w-full border-collapse">
            <thead>
              <tr>
                {headers.map((header, index) => (
                  <th
                    key={index}
                    className="text-left p-2 border"
                    style={{
                      backgroundColor: settings.headerBackgroundColor || "#f3f4f6",
                      borderColor: settings.borderColor || "#e5e7eb",
                      color: settings.color || "#333333",
                      fontSize: settings.fontSize || "14px",
                    }}
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rows.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      className="p-2 border"
                      style={{
                        borderColor: settings.borderColor || "#e5e7eb",
                        color: settings.color || "#333333",
                        fontSize: settings.fontSize || "14px",
                      }}
                    >
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )

    case "social":
      const socialLinks = component.socialLinks || []
      const iconSize = Number.parseInt(settings.iconSize || "32", 10)
      const iconColor = settings.iconColor || "#333333"

      // Filter only enabled social links
      const enabledSocialLinks = socialLinks.filter((link) => link.enabled)

      // Social icons mapping
      const socialIcons: Record<string, React.ReactNode> = {
        facebook: <Facebook size={iconSize} color={iconColor} />,
        twitter: <Twitter size={iconSize} color={iconColor} />,
        instagram: <Instagram size={iconSize} color={iconColor} />,
        linkedin: <Linkedin size={iconSize} color={iconColor} />,
        youtube: <Youtube size={iconSize} color={iconColor} />,
      }

      return (
        <div
          style={style}
          className={cn(
            !isPreview && "min-h-[60px]",
            "flex items-center",
            settings.alignment === "center"
              ? "justify-center"
              : settings.alignment === "right"
                ? "justify-end"
                : "justify-start",
          )}
        >
          <div className="flex gap-2">
            {enabledSocialLinks.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="block hover:opacity-80 transition-opacity"
                onClick={isPreview ? undefined : (e) => e.preventDefault()}
              >
                {socialIcons[link.platform.toLowerCase()] || (
                  <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs">
                    {link.platform.charAt(0).toUpperCase()}
                  </div>
                )}
              </a>
            ))}
          </div>
        </div>
      )

    default:
      return null
  }
}

