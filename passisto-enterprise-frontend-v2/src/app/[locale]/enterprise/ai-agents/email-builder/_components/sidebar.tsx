"use client"

import type React from "react"
import { useDrag } from "react-dnd"
import {
  Type,
  Image,
  BoxIcon as ButtonIcon,
  SeparatorHorizontal,
  FootprintsIcon as FooterIcon,
  Heading,
  ListIcon,
  TableIcon,
  Share2,
} from "lucide-react"
import type { ComponentType } from "../_lib/types"
import { cn } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"
import { useTranslations } from "next-intl"

interface SidebarProps {
  onAddComponent: (type: ComponentType) => void
}

export function Sidebar({ onAddComponent }: SidebarProps) {
  const t  = useTranslations()
  return (
    <div className="w-64 border-r bg-card p-4 overflow-auto">
      <h2 className="text-lg font-semibold mb-4">{t('components')}</h2>

      <div className="space-y-2">
        <DraggableComponent
          type="header"
          label={t('header')}
          icon={<Heading className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="text"
          label={t('text-block')}
          icon={<Type className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="image"
          label={t('image')}
          icon={<Image className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="button"
          label={t('button')}
          icon={<ButtonIcon className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="divider"
          label={t('divider')}
          icon={<SeparatorHorizontal className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="list"
          label={t('list')}
          icon={<ListIcon className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="table"
          label={t('table')}
          icon={<TableIcon className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="social"
          label={t('social-icons')}
          icon={<Share2 className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />

        <DraggableComponent
          type="footer"
          label={t('footer')}
          icon={<FooterIcon className="h-4 w-4" />}
          onAddComponent={onAddComponent}
        />
      </div>

      <Separator className="my-4" />

      <div className="space-y-2">
        <h3 className="text-sm font-medium mb-2">{t('instructions')}</h3>
        <p className="text-xs text-muted-foreground">
          {t('drag-components-to-the-canvas-or-click-to-add-them-to-the-bottom-of-your-email')}
        </p>
      </div>
    </div>
  )
}

interface DraggableComponentProps {
  type: ComponentType
  label: string
  icon: React.ReactNode
  onAddComponent: (type: ComponentType) => void
}

function DraggableComponent({ type, label, icon, onAddComponent }: DraggableComponentProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: "component",
    item: { type },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }))

  return (
    <div
      ref={drag}
      className={cn(
        "flex items-center gap-2 p-2 rounded-md border cursor-move bg-background hover:bg-accent hover:text-accent-foreground transition-colors",
        isDragging && "opacity-50",
      )}
      // onClick={() => onAddComponent(type)}
    >
      {icon}
      <span className="text-sm">{label}</span>
    </div>
  )
}

