"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Mail, Send } from "lucide-react";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { EMAIL_BUILDER_SEND } from "@/utils/routes";
import { useBackendUser } from "@/hooks/useBackendUser";
import { emailBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useTranslations } from "next-intl"; // Import useTranslations

interface SendEmailDialogProps {
  generateHTML: () => string;
  disabled?: boolean;
}

export function SendEmailDialog({
  generateHTML,
  disabled = false,
}: SendEmailDialogProps) {
  const { getToken } = useAuth();
  const t = useTranslations("sendEmailDialog"); // Initialize useTranslations with the namespace

  const [open, setOpen] = useState(false);
  const [recipientEmail, setRecipientEmail] = useState("");
  const [senderEmail, setSenderEmail] = useState("<EMAIL>");
  const [senderName, setSenderName] = useState("");
  const [subject, setSubject] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [html, setHtml] = useState<string>("");
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Update HTML when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      try {
        // Generate fresh HTML when dialog opens
        const freshHTML = generateHTML();
        setHtml(freshHTML);
      } catch (error) {
        console.error("Error generating HTML:", error);
        setHtml("");
        setError(t("htmlGenerationError")); // Use translation
      }
    }
  };

  const handleSendEmail = async () => {
    if (!recipientEmail || !subject) {
      setError(t("recipientSubjectRequiredError")); // Use translation
      return;
    }

    if (!html) {
      setError(t("emailContentError")); // Use translation
      return;
    }

    setIsSending(true);
    setError(null);

    try {
      // Prepare the data to send to the backend
      const emailData = {
        from: senderEmail || undefined,
        to: recipientEmail,
        subject,
        fromName: senderName || undefined,
        htmlBody: html,
      };
      const token = await getToken();

      await axiosInstance.post(EMAIL_BUILDER_SEND, emailData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Show success message
      toast(t("sendSuccessToastTitle"), {
        description: t("sendSuccessToastDescription", { recipientEmail }), // Use translation with interpolation
      });

      // Close the dialog
      setOpen(false);

      // Reset form
      setRecipientEmail("");
      setSenderName("");
      setSubject("");
    } catch (error) {
      console.error("Error sending email:", error);
      setError(
        error instanceof Error ? error.message : t("sendFailToastDescription") // Use translation for generic error
      );
      toast(t("sendFailToastTitle"), {
        description: t("sendFailToastDescription"), // Use translation
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {emailBuilderPermissions.canSendBuilderEmail(
          backendUser?.permissions || []
        ) && (
          <Button variant="outline" className="gap-2" disabled={disabled}>
            <Mail className="h-4 w-4" />
            {t("sendButton")}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="recipient-email" className="text-right">
              {t("toLabel")} <span className="text-destructive">*</span>
            </Label>
            <Input
              id="recipient-email"
              type="email"
              value={recipientEmail}
              onChange={(e) => setRecipientEmail(e.target.value)}
              placeholder={t("toPlaceholder")}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sender-name" className="text-right">
              {t("fromNameLabel")}
            </Label>
            <Input
              id="sender-name"
              value={senderName}
              onChange={(e) => setSenderName(e.target.value)}
              placeholder={t("fromNamePlaceholder")}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sender-email" className="text-right">
              {t("fromEmailLabel")}
            </Label>
            <Input
              id="sender-email"
              type="email"
              value={senderEmail}
              placeholder={t("fromEmailPlaceholder")}
              className="col-span-3"
              disabled
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="subject" className="text-right">
              {t("subjectLabel")} <span className="text-destructive">*</span>
            </Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder={t("subjectPlaceholder")}
              className="col-span-3"
              required
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            {t("cancelButton")}
          </Button>
          {emailBuilderPermissions.canSendBuilderEmail(
            backendUser?.permissions || []
          ) && (
            <Button
              onClick={handleSendEmail}
              disabled={isSending}
              className="gap-2"
            >
              {isSending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t("sendingButton")}
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  {t("sendButton")}
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}