"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { useTranslations } from "next-intl"

interface ColorPickerProps {
  color: string
  onChange: (color: string) => void
}

export function ColorPicker({ color, onChange }: ColorPickerProps) {
  const t  = useTranslations()
  const [localColor, setLocalColor] = useState(color)

  const handleColorChange = (newColor: string) => {
    setLocalColor(newColor)
    onChange(newColor)
  }

  return (
    <div className="flex items-center gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <button
            className="w-8 h-8 rounded-md border shadow-sm"
            style={{ backgroundColor: localColor }}
            aria-label={t('pick-a-color')}
          />
        </PopoverTrigger>
        <PopoverContent className="w-64 p-3">
          <div className="grid gap-2">
            <div className="grid grid-cols-5 gap-1">
              {[
                "#000000",
                "#ffffff",
                "#f44336",
                "#2196f3",
                "#4caf50",
                "#ffeb3b",
                "#ff9800",
                "#9c27b0",
                "#795548",
                "#607d8b",
              ].map((presetColor) => (
                <button
                  key={presetColor}
                  className={cn("w-8 h-8 rounded-md border", localColor === presetColor && "ring-2 ring-primary")}
                  style={{ backgroundColor: presetColor }}
                  onClick={() => handleColorChange(presetColor)}
                />
              ))}
            </div>
            <Input
              type="color"
              value={localColor}
              onChange={(e) => handleColorChange(e.target.value)}
              className="h-8"
            />
            <Input
              type="text"
              value={localColor}
              onChange={(e) => handleColorChange(e.target.value)}
              placeholder="#000000 or rgba(0,0,0,1)"
            />
          </div>
        </PopoverContent>
      </Popover>
      <Input type="text" value={localColor} onChange={(e) => handleColorChange(e.target.value)} className="flex-1" />
    </div>
  )
}

