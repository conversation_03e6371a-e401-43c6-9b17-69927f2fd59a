"use client"

import { useState } from "react"
import type { EmailComponent } from "./email-builder"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Trash2, Check } from "lucide-react"
import { ColorPicker } from "./color-picker"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { useTranslations } from "next-intl"

interface SettingsPanelProps {
  component: EmailComponent
  onUpdateComponent: (updates: Partial<EmailComponent>) => void
  onRemoveComponent: () => void
}

export function SettingsPanel({ component, onUpdateComponent, onRemoveComponent }: SettingsPanelProps) {

  const t = useTranslations()

  const { type, content, settings } = component

  // For social links editing
  const [editingPlatform, setEditingPlatform] = useState<number | null>(null)

  const updateContent = (newContent: string) => {
    onUpdateComponent({ content: newContent })
  }

  const updateSetting = (key: string, value: string) => {
    onUpdateComponent({
      settings: {
        ...settings,
        [key]: value,
      },
    })
  }

  return (
    <div className="w-72 border-l bg-card p-4 overflow-auto">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">{t('settings')}</h2>
        <Button variant="destructive" size="icon" onClick={onRemoveComponent}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-4">
        {/* Content Editor */}
        {(type === "header" || type === "text" || type === "button" || type === "footer") && (
          <div className="space-y-2">
            <Label htmlFor="content">{t('content')}</Label>
            {type === "text" || type === "footer" ? (
              <Textarea
                id="content"
                value={content}
                onChange={(e) => updateContent(e.target.value)}
                className="min-h-[100px]"
              />
            ) : (
              <Input id="content" value={content} onChange={(e) => updateContent(e.target.value)} />
            )}
          </div>
        )}

        <Separator />

        {/* Common Settings */}
        <div className="space-y-4">
          {/* Padding */}
          <div className="space-y-2">
            <Label htmlFor="padding">{t('padding')}</Label>
            <div className="grid grid-cols-2 gap-2">
              <Select
                value={settings.paddingUnit || "px"}
                onValueChange={(value) => updateSetting("paddingUnit", value)}
              >
                <SelectTrigger id="paddingUnit" className="w-full">
                  <SelectValue placeholder={t('unit')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="px">px</SelectItem>
                  <SelectItem value="%">%</SelectItem>
                  <SelectItem value="em">em</SelectItem>
                  <SelectItem value="rem">rem</SelectItem>
                </SelectContent>
              </Select>
              <Input
                id="padding"
                value={settings.padding?.replace(/[^0-9.]/g, "") || "16"}
                onChange={(e) => updateSetting("padding", `${e.target.value}${settings.paddingUnit || "px"}`)}
                type="number"
              />
            </div>
          </div>

          {/* Margin */}
          <div className="space-y-2">
            <Label htmlFor="margin">{t('margin')}</Label>
            <div className="grid grid-cols-2 gap-2">
              <Select value={settings.marginUnit || "px"} onValueChange={(value) => updateSetting("marginUnit", value)}>
                <SelectTrigger id="marginUnit" className="w-full">
                  <SelectValue placeholder={t('unit')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="px">px</SelectItem>
                  <SelectItem value="%">%</SelectItem>
                  <SelectItem value="em">em</SelectItem>
                  <SelectItem value="rem">rem</SelectItem>
                </SelectContent>
              </Select>
              <Input
                id="margin"
                value={settings.margin?.replace(/[^0-9.]/g, "") || "0"}
                onChange={(e) => updateSetting("margin", `${e.target.value}${settings.marginUnit || "px"}`)}
                type="number"
              />
            </div>
          </div>

          {/* Alignment */}
          <div className="space-y-2">
            <Label htmlFor="alignment">{t('alignment')}</Label>
            <Select value={settings.alignment || "left"} onValueChange={(value) => updateSetting("alignment", value)}>
              <SelectTrigger id="alignment">
                <SelectValue placeholder={t('select-alignment')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">{t('left')}</SelectItem>
                <SelectItem value="center">{t('t-center')}</SelectItem>
                <SelectItem value="right">{t('right')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Text Color */}
          {(type === "header" || type === "text" || type === "button" || type === "footer") && (
            <div className="space-y-2">
              <Label htmlFor="color">{t('text-color')}</Label>
              <ColorPicker color={settings.color || "#000000"} onChange={(color) => updateSetting("color", color)} />
            </div>
          )}

          {/* Background Color */}
          <div className="space-y-2">
            <Label htmlFor="backgroundColor">{t('background-color')}</Label>
            <ColorPicker
              color={settings.backgroundColor || "transparent"}
              onChange={(color) => updateSetting("backgroundColor", color)}
            />
          </div>

          {/* Font Size */}
          {(type === "header" || type === "text" || type === "button" || type === "footer") && (
            <div className="space-y-2">
              <Label htmlFor="fontSize">{t('font-size')}</Label>
              <div className="grid grid-cols-2 gap-2">
                <Select
                  value={settings.fontSizeUnit || "px"}
                  onValueChange={(value) => updateSetting("fontSizeUnit", value)}
                >
                  <SelectTrigger id="fontSizeUnit" className="w-full">
                    <SelectValue placeholder={t('unit-0')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="px">px</SelectItem>
                    <SelectItem value="pt">pt</SelectItem>
                    <SelectItem value="em">em</SelectItem>
                    <SelectItem value="rem">rem</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  id="fontSize"
                  value={settings.fontSize?.replace(/[^0-9.]/g, "") || "16"}
                  onChange={(e) => updateSetting("fontSize", `${e.target.value}${settings.fontSizeUnit || "px"}`)}
                  type="number"
                />
              </div>
            </div>
          )}

          {/* Font Weight */}
          {(type === "header" || type === "text" || type === "button" || type === "footer") && (
            <div className="space-y-2">
              <Label htmlFor="fontWeight">{t('font-weight')}</Label>
              <Select
                value={settings.fontWeight || "normal"}
                onValueChange={(value) => updateSetting("fontWeight", value)}
              >
                <SelectTrigger id="fontWeight">
                  <SelectValue placeholder={t('t-select-weight')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">{t('normal')}</SelectItem>
                  <SelectItem value="bold">{t('bold')}</SelectItem>
                  <SelectItem value="lighter">{t('lighter')}</SelectItem>
                  <SelectItem value="bolder">{t('bolder')}</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                  <SelectItem value="300">300</SelectItem>
                  <SelectItem value="400">400</SelectItem>
                  <SelectItem value="500">500</SelectItem>
                  <SelectItem value="600">600</SelectItem>
                  <SelectItem value="700">700</SelectItem>
                  <SelectItem value="800">800</SelectItem>
                  <SelectItem value="900">900</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Line Height */}
          {(type === "header" || type === "text" || type === "button" || type === "footer") && (
            <div className="space-y-2">
              <Label htmlFor="lineHeight">{t('line-height')}</Label>
              <div className="flex items-center gap-2">
                <Slider
                  id="lineHeight"
                  defaultValue={[Number.parseFloat(settings.lineHeight || "1.5")]}
                  min={1}
                  max={3}
                  step={0.1}
                  onValueChange={(value) => updateSetting("lineHeight", value[0].toString())}
                  className="flex-1"
                />
                <span className="w-10 text-center">{settings.lineHeight || "1.5"}</span>
              </div>
            </div>
          )}
        </div>

        {/* Component-specific settings */}
        {type === "image" && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="url">{t('t-image-url')}</Label>
                <Input
                  id="url"
                  value={settings.url || ""}
                  onChange={(e) => updateSetting("url", e.target.value)}
                  placeholder="/placeholder.svg?height=200&width=600"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="altText">{t('alt-text')}</Label>
                <Input
                  id="altText"
                  value={settings.altText || ""}
                  onChange={(e) => updateSetting("altText", e.target.value)}
                  placeholder={t('t-image-description')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="width">{t('width')}</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={settings.widthUnit || "%"}
                    onValueChange={(value) => updateSetting("widthUnit", value)}
                  >
                    <SelectTrigger id="widthUnit" className="w-full">
                      <SelectValue placeholder={t('unit')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="%">%</SelectItem>
                      <SelectItem value="px">px</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="width"
                    value={settings.width?.replace(/[^0-9.]/g, "") || "100"}
                    onChange={(e) => updateSetting("width", `${e.target.value}${settings.widthUnit || "%"}`)}
                    type="number"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="height">{t('height')}</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={settings.heightUnit || "auto"}
                    onValueChange={(value) => updateSetting("heightUnit", value)}
                  >
                    <SelectTrigger id="heightUnit" className="w-full">
                      <SelectValue placeholder={t('unit')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">{t('auto')}</SelectItem>
                      <SelectItem value="px">px</SelectItem>
                    </SelectContent>
                  </Select>
                  {settings.heightUnit !== "auto" && (
                    <Input
                      id="height"
                      value={settings.height?.replace(/[^0-9.]/g, "") || "auto"}
                      onChange={(e) => updateSetting("height", `${e.target.value}${settings.heightUnit || "px"}`)}
                      type="number"
                    />
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="borderRadius">{t('border-radius')}</Label>
                <Input
                  id="borderRadius"
                  value={settings.borderRadius || "0px"}
                  onChange={(e) => updateSetting("borderRadius", e.target.value)}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="linkImage"
                  checked={settings.linkImage === "true"}
                  onCheckedChange={(checked) => updateSetting("linkImage", checked.toString())}
                />
                <Label htmlFor="linkImage">{t('link-image')}</Label>
              </div>

              {settings.linkImage === "true" && (
                <div className="space-y-2">
                  <Label htmlFor="linkUrl">{t('link-url')}</Label>
                  <Input
                    id="linkUrl"
                    value={settings.linkUrl || "#"}
                    onChange={(e) => updateSetting("linkUrl", e.target.value)}
                  />
                </div>
              )}
            </div>
          </>
        )}

        {type === "button" && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="url">{t('button-url')}</Label>
                <Input id="url" value={settings.url || "#"} onChange={(e) => updateSetting("url", e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="borderRadius">{t('border-radius-0')}</Label>
                <Input
                  id="borderRadius"
                  value={settings.borderRadius || "4px"}
                  onChange={(e) => updateSetting("borderRadius", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="buttonWidth">{t('button-width')}</Label>
                <Select
                  value={settings.buttonWidth || "auto"}
                  onValueChange={(value) => updateSetting("buttonWidth", value)}
                >
                  <SelectTrigger id="buttonWidth">
                    <SelectValue placeholder={t('t-t-t-t-select-width')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">{t('auto-0')}</SelectItem>
                    <SelectItem value="full">{t('full-width')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="borderColor">{t('border-color')}</Label>
                <ColorPicker
                  color={settings.borderColor || "transparent"}
                  onChange={(color) => updateSetting("borderColor", color)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="borderWidth">{t('border-width')}</Label>
                <Input
                  id="borderWidth"
                  value={settings.borderWidth || "0px"}
                  onChange={(e) => updateSetting("borderWidth", e.target.value)}
                />
              </div>
            </div>
          </>
        )}

        {type === "divider" && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="dividerColor">{t('t-divider-color')}</Label>
                <ColorPicker
                  color={settings.dividerColor || "#e5e7eb"}
                  onChange={(color) => updateSetting("dividerColor", color)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dividerWidth">{t('divider-width')}</Label>
                <Input
                  id="dividerWidth"
                  value={settings.dividerWidth || "1px"}
                  onChange={(e) => updateSetting("dividerWidth", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dividerStyle">{t('divider-style')}</Label>
                <Select
                  value={settings.dividerStyle || "solid"}
                  onValueChange={(value) => updateSetting("dividerStyle", value)}
                >
                  <SelectTrigger id="dividerStyle">
                    <SelectValue placeholder={t('select-style')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="solid">{t('t-solid')}</SelectItem>
                    <SelectItem value="dashed">{t('dashed')}</SelectItem>
                    <SelectItem value="dotted">{t('dotted')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        )}

        {type === "list" && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="listType">{t('list-type')}</Label>
                <Select
                  value={settings.listType || "bullet"}
                  onValueChange={(value) => updateSetting("listType", value)}
                >
                  <SelectTrigger id="listType">
                    <SelectValue placeholder={t('select-list-type')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bullet">{t('t-bullet-list')}</SelectItem>
                    <SelectItem value="number">{t('numbered-list')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>{t('list-items')}</Label>
                {component.items?.map((item, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      value={item}
                      onChange={(e) => {
                        const newItems = [...(component.items || [])]
                        newItems[index] = e.target.value
                        onUpdateComponent({ items: newItems })
                      }}
                      placeholder={`Item ${index + 1}`}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        const newItems = [...(component.items || [])].filter((_, i) => i !== index)
                        onUpdateComponent({ items: newItems })
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newItems = [...(component.items || []), `Item ${(component.items?.length || 0) + 1}`]
                    onUpdateComponent({ items: newItems })
                  }}
                >
                  {t('add-item')}
                </Button>
              </div>
            </div>
          </>
        )}

        {type === "table" && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="headerBackgroundColor">{t('header-background-color')}</Label>
                <ColorPicker
                  color={settings.headerBackgroundColor || "#f3f4f6"}
                  onChange={(color) => updateSetting("headerBackgroundColor", color)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="borderColor">{t('border-color')}</Label>
                <ColorPicker
                  color={settings.borderColor || "#e5e7eb"}
                  onChange={(color) => updateSetting("borderColor", color)}
                />
              </div>

              <div className="space-y-2">
                <Label>{t('table-headers')}</Label>
                <div className="flex gap-2 mb-2">
                  {component.headers?.map((header, index) => (
                    <Input
                      key={index}
                      value={header}
                      onChange={(e) => {
                        const newHeaders = [...(component.headers || [])]
                        newHeaders[index] = e.target.value
                        onUpdateComponent({ headers: newHeaders })
                      }}
                      placeholder={`Header ${index + 1}`}
                      className="flex-1"
                    />
                  ))}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newHeaders = [
                        ...(component.headers || []),
                        `Header ${(component.headers?.length || 0) + 1}`,
                      ]

                      // Also add a new column to each row
                      const newRows = (component.rows || []).map((row) => [...row, ""])

                      onUpdateComponent({
                        headers: newHeaders,
                        rows: newRows,
                      })
                    }}
                  >
                    {t('add-column')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if ((component.headers?.length || 0) <= 1) return

                      const newHeaders = [...(component.headers || [])]
                      newHeaders.pop()

                      // Also remove the last column from each row
                      const newRows = (component.rows || []).map((row) => row.slice(0, -1))

                      onUpdateComponent({
                        headers: newHeaders,
                        rows: newRows,
                      })
                    }}
                  >
                    {t('remove-column')}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t('table-rows')}</Label>
                {component.rows?.map((row, rowIndex) => (
                  <div key={rowIndex} className="flex gap-2 mb-2">
                    {row.map((cell, cellIndex) => (
                      <Input
                        key={cellIndex}
                        value={cell}
                        onChange={(e) => {
                          const newRows = [...(component.rows || [])]
                          newRows[rowIndex][cellIndex] = e.target.value
                          onUpdateComponent({ rows: newRows })
                        }}
                        placeholder={`Row ${rowIndex + 1}, Cell ${cellIndex + 1}`}
                        className="flex-1"
                      />
                    ))}
                  </div>
                ))}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const columnCount = component.headers?.length || 0
                      const newRow = Array(columnCount)
                        .fill("")
                        .map((_, i) => `Row ${(component.rows?.length || 0) + 1}, Cell ${i + 1}`)
                      const newRows = [...(component.rows || []), newRow]
                      onUpdateComponent({ rows: newRows })
                    }}
                  >
                    {t('add-row')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if ((component.rows?.length || 0) <= 1) return

                      const newRows = [...(component.rows || [])]
                      newRows.pop()
                      onUpdateComponent({ rows: newRows })
                    }}
                  >
                    {t('remove-row')}
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}

        {type === "social" && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="iconSize">{t('icon-size')}</Label>
                <Input
                  id="iconSize"
                  value={settings.iconSize || "32px"}
                  onChange={(e) => updateSetting("iconSize", e.target.value)}
                  placeholder="32px"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="iconSpacing">{t('icon-spacing')}</Label>
                <Input
                  id="iconSpacing"
                  value={settings.iconSpacing || "10px"}
                  onChange={(e) => updateSetting("iconSpacing", e.target.value)}
                  placeholder="10px"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="iconColor">{t('icon-color')}</Label>
                <ColorPicker
                  color={settings.iconColor || "#333333"}
                  onChange={(color) => updateSetting("iconColor", color)}
                />
              </div>

              <div className="space-y-2">
                <Label>{t('t-social-media-links')}</Label>
                {component.socialLinks?.map((link, index) => (
                  <div key={index} className="flex items-center gap-2 mb-2 border rounded-md p-2">
                    {editingPlatform === index ? (
                      <>
                        <div className="grid grid-cols-2 gap-2 flex-1">
                          <Input
                            value={link.platform}
                            onChange={(e) => {
                              const newLinks = [...(component.socialLinks || [])]
                              newLinks[index] = { ...newLinks[index], platform: e.target.value }
                              onUpdateComponent({ socialLinks: newLinks })
                            }}
                            placeholder={t('platform')}
                          />
                          <Input
                            value={link.url}
                            onChange={(e) => {
                              const newLinks = [...(component.socialLinks || [])]
                              newLinks[index] = { ...newLinks[index], url: e.target.value }
                              onUpdateComponent({ socialLinks: newLinks })
                            }}
                            placeholder="URL"
                          />
                        </div>
                        <Button variant="ghost" size="icon" onClick={() => setEditingPlatform(null)}>
                          <Check className="h-4 w-4" />
                        </Button>
                      </>
                    ) : (
                      <>
                        <div className="flex items-center gap-2 flex-1">
                          <Switch
                            checked={link.enabled}
                            onCheckedChange={(checked) => {
                              const newLinks = [...(component.socialLinks || [])]
                              newLinks[index] = { ...newLinks[index], enabled: checked }
                              onUpdateComponent({ socialLinks: newLinks })
                            }}
                          />
                          <span className="text-sm capitalize">{link.platform}</span>
                        </div>
                        <Button variant="ghost" size="icon" onClick={() => setEditingPlatform(index)}>
                          <span className="sr-only">{t('edit')}</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-4 w-4"
                          >
                            <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3Z"></path>
                          </svg>
                        </Button>
                      </>
                    )}
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newLinks = [
                      ...(component.socialLinks || []),
                      { platform: "newplatform", url: "https://example.com", enabled: true },
                    ]
                    onUpdateComponent({ socialLinks: newLinks })
                    setEditingPlatform(newLinks.length - 1)
                  }}
                >
                  {t('add-platform')}
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

