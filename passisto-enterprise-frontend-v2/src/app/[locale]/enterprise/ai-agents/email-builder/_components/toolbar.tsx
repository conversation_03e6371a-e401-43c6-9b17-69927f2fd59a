"use client";

import { useEffect, useState } from "react";
import {
  Monitor,
  Tablet,
  Smartphone,
  Code,
  Eye,
  Paintbrush,
  Undo2,
  Redo2,
  Save,
  FolderOpen,
  FileJson,
  LayoutTemplate,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import type { ViewMode, EditorMode, EmailComponent } from "./email-builder";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { SendEmailDialog } from "./send-email-dialog";
import { Link } from '@/i18n/nvigation';;
import { GenerateEmailResponse } from "../_lib/types";
import { AxiosResponse } from "axios";
import axiosInstance from "@/config/axios";
import { EMAIL_BUILDER_GET_ALL, EMAIL_BUILDER_GET_BY_ID } from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl"; // Import useTranslations

interface ToolbarProps {
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  editorMode: EditorMode;
  setEditorMode: (mode: EditorMode) => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onSave: (name: string) => void;
  onLoad: (name: string) => void;
  generateHTML: () => string;
  setComponents?: (components: EmailComponent[]) => void;
}

export function Toolbar({
  viewMode,
  setViewMode,
  editorMode,
  setEditorMode,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onLoad,
  generateHTML,
  setComponents,
}: ToolbarProps) {
  const { getToken } = useAuth();
  const t = useTranslations("toolbar"); // Initialize useTranslations with the namespace

  const [templateName, setTemplateName] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [templates, setTemplates] = useState<GenerateEmailResponse[]>([]);
  const [jsonInput, setJsonInput] = useState("");
  const params = useParams();
  const emailId = params?.id as string;

  const getEmailById = async () => {
    try {
      console.log(emailId);
      const token = await getToken();

      const response: AxiosResponse<GenerateEmailResponse> =
        await axiosInstance.get(EMAIL_BUILDER_GET_BY_ID(emailId), {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      console.log(response.data.fields);
      setTemplateName(response.data.title);
      // setInitialComponents(response.data.fields)
      // return response.data;
    } catch (error: any) {
      console.error(
        "Error fetching email:",
        error?.response?.data || error?.message
      );
      throw error;
    }
  };

  const loadTemplatesList = async () => {
    try {
      const token = await getToken();

      const response: AxiosResponse<GenerateEmailResponse[]> =
        await axiosInstance.get(EMAIL_BUILDER_GET_ALL, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      console.log(response.data);
      setTemplates(response.data);
    } catch (error: any) {
      console.error(
        "Error fetching email:",
        error?.response?.data || error?.message
      );
      throw error;
    }
  };

  // Copy HTML to clipboard
  const copyHTML = () => {
    const html = generateHTML();
    navigator.clipboard.writeText(html);
    toast(t("htmlCopiedToastTitle"), {
      description: t("htmlCopiedToastDescription"),
    });
  };

  // Import JSON template
  const importJSON = () => {
    if (!jsonInput.trim() || !setComponents) {
      return;
    }

    try {
      const parsedComponents = JSON.parse(jsonInput);
      if (Array.isArray(parsedComponents)) {
        setComponents(parsedComponents);
        toast(t("templateImportedToastTitle"), {
          description: t("templateImportedToastDescription"),
        });
        setJsonInput("");
      } else {
        throw new Error("Invalid format");
      }
    } catch (error) {
      toast(t("invalidJsonToastTitle"), {
        description: t("invalidJsonToastDescription"),
      });
    }
  };

  useEffect(() => {
    getEmailById();
  }, []);

  return (
    <div className="flex items-center p-2 border-b bg-card">
      <div className="flex items-center space-x-1">
        <Button
          variant={editorMode === "design" ? "default" : "outline"}
          size="icon"
          onClick={() => setEditorMode("design")}
          title={t("designMode")}
        >
          <Paintbrush className="h-4 w-4" />
        </Button>

        <Button
          variant={editorMode === "preview" ? "default" : "outline"}
          size="icon"
          onClick={() => setEditorMode("preview")}
          title={t("previewMode")}
        >
          <Eye className="h-4 w-4" />
        </Button>

        <Button
          variant={editorMode === "code" ? "default" : "outline"}
          size="icon"
          onClick={() => setEditorMode("code")}
          title={t("htmlCode")}
        >
          <Code className="h-4 w-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="mx-2 h-6" />

      <div className="flex items-center space-x-1">
        <Button
          variant={viewMode === "desktop" ? "default" : "outline"}
          size="icon"
          onClick={() => setViewMode("desktop")}
          title={t("desktopView")}
          disabled={editorMode === "code"}
        >
          <Monitor className="h-4 w-4" />
        </Button>

        <Button
          variant={viewMode === "tablet" ? "default" : "outline"}
          size="icon"
          onClick={() => setViewMode("tablet")}
          title={t("tabletView")}
          disabled={editorMode === "code"}
        >
          <Tablet className="h-4 w-4" />
        </Button>

        <Button
          variant={viewMode === "mobile" ? "default" : "outline"}
          size="icon"
          onClick={() => setViewMode("mobile")}
          title={t("mobileView")}
          disabled={editorMode === "code"}
        >
          <Smartphone className="h-4 w-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="mx-2 h-6" />

      <div className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="icon"
          onClick={onUndo}
          disabled={!canUndo}
          title={t("undoButton")}
        >
          <Undo2 className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          onClick={onRedo}
          disabled={!canRedo}
          title={t("redoButton")}
        >
          <Redo2 className="h-4 w-4" />
        </Button>
      </div>

      <div className="ml-auto flex items-center space-x-2">
        {/* <ThemeToggle className="mr-2" /> */}
        {/* Templates Link */}
        <Link href="/enterprise/ai-agents/email-builder/templates">
          <Button variant="outline" size="icon" title={t("viewAllTemplatesButton")}>
            <LayoutTemplate className="h-4 w-4" />
          </Button>
        </Link>

        {/* Send Email Button */}
        <SendEmailDialog generateHTML={generateHTML} />

        {editorMode === "code" && (
          <Button variant="outline" onClick={copyHTML} className="text-xs">
            {t("copyHtmlButton")}
          </Button>
        )}

        {setComponents && (
          <Dialog>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                title={t("importJsonTemplateButton")}
              >
                <FileJson className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{t("importJsonDialogTitle")}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="json-input">{t("pasteJsonLabel")}</Label>
                  <Textarea
                    id="json-input"
                    value={jsonInput}
                    onChange={(e) => setJsonInput(e.target.value)}
                    placeholder='[{"id":"1","type":"header","content":"Welcome","settings":{...}}]'
                    className="min-h-[200px] font-mono text-xs"
                  />
                  <p className="text-xs text-muted-foreground">
                    {t("pasteJsonDescription")}
                  </p>
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">{t("cancelButton")}</Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button onClick={importJSON} disabled={!jsonInput.trim()}>
                    {t("importButton")}
                  </Button>
                </DialogClose>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="icon" title={t("saveTemplateButton")}>
              <Save className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("saveTemplateDialogTitle")}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">{t("templateNameLabel")}</Label>
                <Input
                  id="template-name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder={t("templateNamePlaceholder")}
                />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">{t("cancelButton")}</Button>
              </DialogClose>
              <DialogClose asChild>
                <Button
                  onClick={() => {
                    if (templateName) {
                      onSave(templateName);
                      setTemplateName("");
                    }
                  }}
                  disabled={!templateName}
                >
                  {t("saveButton")}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              title={t("loadTemplateButton")}
              onClick={loadTemplatesList}
            >
              <FolderOpen className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("loadTemplateDialogTitle")}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="template-select">{t("selectTemplateLabel")}</Label>
                <Select
                  value={selectedTemplate}
                  onValueChange={setSelectedTemplate}
                >
                  <SelectTrigger id="template-select">
                    <SelectValue placeholder={t("selectTemplatePlaceholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((tmpl) => ( // Changed 't' to 'tmpl' to avoid conflict with 't' from useTranslations
                      <SelectItem key={tmpl.id} value={tmpl.id}>
                        {tmpl.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">{t("cancelButton")}</Button>
              </DialogClose>
              <DialogClose asChild>
                <Button
                  onClick={() => {
                    if (selectedTemplate) {
                      onLoad(selectedTemplate);
                      setSelectedTemplate("");
                    }
                  }}
                  disabled={!selectedTemplate}
                >
                  {t("loadButton")} {/* Assuming you'll add a 'loadButton' to en.json */}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}