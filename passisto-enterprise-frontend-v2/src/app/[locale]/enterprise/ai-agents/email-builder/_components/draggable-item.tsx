"use client"

import { useRef } from "react"
import { useDrag, useDrop } from "react-dnd"
import type { EmailComponent } from "./email-builder"
import { cn } from "@/lib/utils"
import { Trash2, GripVertical } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ComponentPreview } from "./component-preview"

interface DraggableItemProps {
  component: EmailComponent
  index: number
  isSelected: boolean
  onSelect: () => void
  onUpdate: (updates: Partial<EmailComponent>) => void
  onRemove: () => void
  moveItem: (dragIndex: number, hoverIndex: number) => void
}

interface DragItem {
  index: number
  id: string
  type: string
}

export function DraggableItem({
  component,
  index,
  isSelected,
  onSelect,
  onUpdate,
  onRemove,
  moveItem,
}: DraggableItemProps) {
  const ref = useRef<HTMLDivElement>(null)

  const [{ handlerId }, drop] = useDrop({
    accept: "draggableItem",
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect()

      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2

      // Determine mouse position
      const clientOffset = monitor.getClientOffset()

      // Get pixels to the top
      const hoverClientY = clientOffset!.y - hoverBoundingRect.top

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }

      // Time to actually perform the action
      moveItem(dragIndex, hoverIndex)

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
  })

  const [{ isDragging }, drag, preview] = useDrag({
    type: "draggableItem",
    item: () => {
      return { id: component.id, index }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  drag(drop(ref))


  return (
    <div
      ref={preview}
      className={cn("relative transition-opacity", isDragging && "opacity-50")}
      data-handler-id={handlerId}
    >
      <div className={cn("relative group", isSelected && "ring-2 ring-primary")} onClick={onSelect}>
        <ComponentPreview component={component} />

        <div className={cn("absolute inset-0 bg-black/0 transition-colors", "group-hover:bg-black/5")} />

        <div
          ref={ref}
          className={cn(
            "absolute top-0 left-0 p-1 opacity-0 cursor-move",
            "group-hover:opacity-100 transition-opacity",
          )}
        >
          <div className="bg-background/80 backdrop-blur-sm p-1 rounded-md shadow-sm">
            <GripVertical className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>

        <div className={cn("absolute top-0 right-0 p-1 opacity-0", "group-hover:opacity-100 transition-opacity")}>
          <Button
            variant="destructive"
            size="icon"
            className="h-6 w-6 bg-background/80 backdrop-blur-sm hover:bg-destructive"
            onClick={(e) => {
              e.stopPropagation()
              onRemove()
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  )
}

