"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Edit, Eye, MoreVertical, Plus, Trash, Copy, ArrowLeft } from "lucide-react";
import type { GenerateEmailResponse } from "../_lib/types";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { EMAIL_BUILDER_DELETE, EMAIL_BUILDER_GET_ALL } from "@/utils/routes";
import { AxiosResponse } from "axios";
import { checkPermissions, emailBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function TemplatesPage() {
  const { getToken } = useAuth();
  const [templates, setTemplates] = useState<GenerateEmailResponse[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  const router = useRouter();
  const t = useTranslations("templatesPage"); // Initialize useTranslations

  const fetchAllTemplates = async () => {
    try {
      const token = await getToken();

      const response: AxiosResponse<GenerateEmailResponse[]> =
        await axiosInstance.get(EMAIL_BUILDER_GET_ALL, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      console.log(response.data);
      return response.data;
    } catch (error: any) {
      console.error(
        "Error fetching email:",
        error?.response?.data || error?.message
      );
      throw error;
    }
  };

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one email builder-related permission
      const hasAnyEmailBuilderPermission = checkPermissions(permissions, [
        emailBuilderPermissions.canViewTemplate,
        emailBuilderPermissions.canEditBuilder,
        emailBuilderPermissions.canDeleteBuilder,
        emailBuilderPermissions.canSendBuilderEmail,
      ]);

      // Redirect if user doesn't have any email builder-related permissions
      if (!hasAnyEmailBuilderPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        });
        router.back();
      }
    }
  }, [backendUser, backendUserLoading, router, t]); // Add 't' to the dependency array

  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true);
        const data = await fetchAllTemplates();
        setTemplates(data);
        setError(null);
      } catch (err) {
        console.error("Failed to load templates:", err);
        setError(t("failedToLoadTemplates")); // Use translation
        setTemplates([]);
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, [t]); // Add 't' to the dependency array

  const handleEdit = (id: string) => {
    router.push(`/enterprise/ai-agents/email-builder/editor/${id}`);
  };

  const handleView = (id: string) => {
    router.push(`/enterprise/ai-agents/email-builder/preview/${id}`);
  };

  const handleDelete = async () => {
    if (!deleteId) return;

    try {
      const token = await getToken();
      await axiosInstance.delete(`${EMAIL_BUILDER_DELETE(deleteId)}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setTemplates((prev) => (prev ? prev.filter((template) => template.id !== deleteId) : []));
      toast(t("templateDeletedToastTitle"), {
        description: t("templateDeletedToastDescription"),
      });
    } catch (err) {
      console.error("Failed to delete template:", err);
      toast(t("errorToastTitle"), {
        description: t("deleteErrorToastDescription"),
      });
    } finally {
      setDeleteId(null);
      setIsDeleteDialogOpen(false);
    }
  };

  const confirmDelete = (id: string) => {
    setDeleteId(id);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateNew = () => {
    router.push("/enterprise/ai-agents/email-builder/");
  };

  const handleDuplicate = (template: GenerateEmailResponse) => {
    // Store the template data in localStorage to be used in the new template
    localStorage.setItem(
      "duplicateTemplate",
      JSON.stringify({
        title: t("duplicateMenuItem", { title: template.title }), // Use translation for duplicated title
        description: template.description,
        fields: template.fields,
      })
    );
    router.push("/editor/duplicate");
  };

  const handleBackToHome = () => {
    router.push("/enterprise/ai-agents/email-builder/");
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={handleBackToHome}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">{t("emailTemplatesTitle")}</h1>
        </div>
        {emailBuilderPermissions.canGenerateTemplate(
          backendUser?.permissions || []
        ) && (
          <Button onClick={handleCreateNew}>
            <Plus className="mr-2 h-4 w-4" /> {t("createNewTemplateButton")}
          </Button>
        )}
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full" />
              </CardContent>
              <CardFooter className="flex justify-between">
                <Skeleton className="h-10 w-20" />
                <Skeleton className="h-10 w-20" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      ) : templates && templates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <Card key={template.id} className="overflow-hidden">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="line-clamp-1">
                      {template.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-2 mt-1">
                      {template.description || t("noDescription")}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {emailBuilderPermissions.canEditBuilder(
                        backendUser?.permissions || []
                      ) && (
                        <DropdownMenuItem onClick={() => handleEdit(template.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t("editMenuItem")}
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => handleView(template.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t("previewMenuItem")}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicate(template)}>
                        <Copy className="mr-2 h-4 w-4" />
                        {t("duplicateMenuItem")}
                      </DropdownMenuItem>
                      {emailBuilderPermissions.canDeleteBuilder(
                        backendUser?.permissions || []
                      ) && (
                        <DropdownMenuItem
                          onClick={() => confirmDelete(template.id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          {t("deleteMenuItem")}
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-32 bg-gray-100 dark:bg-gray-800 rounded-md flex items-center justify-center overflow-hidden">
                  {/* Template preview thumbnail would go here */}
                  <div className="text-gray-400 text-sm">
                    {t("componentsCount", { count: template.fields.length })}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(template.id)}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        {t("previewMenuItem")}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("previewTooltip")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      {emailBuilderPermissions.canEditBuilder(
                        backendUser?.permissions || []
                      ) && (
                        <Button size="sm" onClick={() => handleEdit(template.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t("editMenuItem")}
                        </Button>
                      )}
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{t("editTooltip")}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
            <div className="text-gray-400">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12l-3-3m0 0l-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                />
              </svg>
            </div>
          </div>
          <h3 className="text-lg font-medium mb-2">
            {t("noTemplatesFoundTitle")}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {t("noTemplatesFoundDescription")}
          </p>
          {emailBuilderPermissions.canGenerateTemplate(
            backendUser?.permissions || []
          ) && (
            <Button onClick={handleCreateNew}>
              <Plus className="mr-2 h-4 w-4" />{" "}
              {t("createFirstTemplateButton")}
            </Button>
          )}
        </div>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("deleteAlertDialogTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteAlertDialogDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("cancelButton")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              {t("deleteButton")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}