import type React from "react"
/**
 * Email Builder Types
 * This file contains all the type definitions used throughout the email builder application.
 */

/**
 * The types of components that can be added to an email
 */
export type ComponentType = "header" | "text" | "image" | "button" | "divider" | "footer" | "list" | "table" | "social"

/**
 * Settings that can be applied to email components
 */
export interface ComponentSettings {
  // Common settings
  fontSize?: string
  fontSizeUnit?: string
  color?: string
  backgroundColor?: string
  padding?: string
  paddingUnit?: string
  margin?: string
  marginUnit?: string
  alignment?: "left" | "center" | "right"
  fontWeight?: string
  lineHeight?: string

  // Image specific settings
  width?: string
  widthUnit?: string
  height?: string
  heightUnit?: string
  borderRadius?: string
  url?: string
  altText?: string
  linkImage?: string
  linkUrl?: string

  // Button specific settings
  buttonWidth?: string
  borderColor?: string
  borderWidth?: string

  // Divider specific settings
  dividerColor?: string
  dividerWidth?: string
  dividerStyle?: string

  // List specific settings
  listType?: "bullet" | "number"

  // Table specific settings
  headerBackgroundColor?: string

  // Social specific settings
  iconSize?: string
  iconSpacing?: string
  iconColor?: string
}

/**
 * Social media link for the social component
 */
export interface SocialLink {
  platform: string
  url: string
  enabled: boolean
}

/**
 * The structure of a component in the email
 */
export interface EmailComponent {
  id: string
  type: ComponentType
  content?: string
  items?: string[]
  headers?: string[]
  rows?: string[][]
  socialLinks?: SocialLink[]
  settings: ComponentSettings
}
// export interface Email {
//   id: String;
//   companyId: String;
//   title: string;
//   description: string;
//   fields: EmailComponent[];
//   html: String;
// }
/**
 * The different view modes for the email preview
 */
export interface GenerateEmailResponse {
  id: string
  companyId: string
  title: string
  description: string
  fields: EmailComponent[]
  html: string
}
export type ViewMode = "desktop" | "tablet" | "mobile"

/**
 * The different editor modes
 */
export type EditorMode = "design" | "preview" | "code"

/**
 * Props for the EmailBuilder component
 */
export interface EmailBuilderProps {
  initialComponents?: EmailComponent[]
  readOnly?: boolean
  onHTMLChange?: (html: string) => void
}

/**
 * Props for the Canvas component
 */
export interface CanvasProps {
  components: EmailComponent[]
  selectedComponent: EmailComponent | null
  setSelectedComponent: (component: EmailComponent | null) => void
  onUpdateComponent: (id: string, updates: Partial<EmailComponent>) => void
  onRemoveComponent: (id: string) => void
  onReorderComponents: (startIndex: number, endIndex: number) => void
  viewMode: ViewMode
}

/**
 * Props for the SettingsPanel component
 */
export interface SettingsPanelProps {
  component: EmailComponent
  onUpdateComponent: (updates: Partial<EmailComponent>) => void
  onRemoveComponent: () => void
}

/**
 * Props for the Toolbar component
 */
export interface ToolbarProps {
  viewMode: ViewMode
  setViewMode: (mode: ViewMode) => void
  editorMode: EditorMode
  setEditorMode: (mode: EditorMode) => void
  canUndo: boolean
  canRedo: boolean
  onUndo: () => void
  onRedo: () => void
  onSave: (name: string) => void
  onLoad: (name: string) => void
  generateHTML: () => string
  setComponents?: (components: EmailComponent[]) => void
}

/**
 * Props for the PreviewPanel component
 */
export interface PreviewPanelProps {
  components: EmailComponent[]
  viewMode: ViewMode
}

/**
 * Props for the ComponentPreview component
 */
export interface ComponentPreviewProps {
  component: EmailComponent
  isPreview?: boolean
}

/**
 * Props for the DraggableItem component
 */
export interface DraggableItemProps {
  component: EmailComponent
  index: number
  isSelected: boolean
  onSelect: () => void
  onUpdate: (updates: Partial<EmailComponent>) => void
  onRemove: () => void
  moveItem: (dragIndex: number, hoverIndex: number) => void
}

/**
 * Props for the ColorPicker component
 */
export interface ColorPickerProps {
  color: string
  onChange: (color: string) => void
}

/**
 * Props for the EmailPreviewDialog component
 */
export interface EmailPreviewDialogProps {
  generateHTML: () => string
}

/**
 * Props for the SendEmailDialog component
 */
export interface SendEmailDialogProps {
  generateHTML: () => string
  disabled?: boolean
}

/**
 * Template data structure for the templates page
 */
export interface TemplateData {
  name: string
  components: EmailComponent[]
}

// Add theme-related types to the existing types file

/**
 * Theme mode options
 */
export type ThemeMode = "light" | "dark" | "system"

/**
 * Theme context interface
 */
export interface ThemeContextType {
  theme: ThemeMode
  setTheme: (theme: ThemeMode) => void
}

/**
 * Theme provider props
 */
export interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: ThemeMode
  storageKey?: string
}

/**
 * Theme toggle props
 */
export interface ThemeToggleProps {
  className?: string
}

