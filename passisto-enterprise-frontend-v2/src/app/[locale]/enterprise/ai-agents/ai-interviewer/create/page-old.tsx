'use client'
import React, { useEffect, useState } from "react";
import Agent from "../_components/agent";
import { Message } from "../interviews/[id]/page";
import { useAuth } from "@clerk/nextjs";
import { AxiosResponse } from "axios";
import axiosInstance from "@/config/axios";
import { GET_CURRENT_USER } from "@/utils/routes";

export interface AgentProps {
  userName: string;
  userId?: string;
  interviewId?: string;
  feedbackId?: string;
  type: "generate" | "interview";
  questions?: string[];
  interviewType: string;
  interviewRole: string;
  interviewLevel: string;
}
export interface Interview {
  id: string;
  role: string;
  level: string;
  questions: string[];
  techstack: string[];
  createdAt: string;
  userId: string;
  type: string;
  finalized: boolean;
  candidateName: string;
  candidateEmail: string;
  transcript: Message[];
  hasFeedback: boolean;
}
interface CurrentUser {
  id: string;
  fullName: string;
  companyId: string
  companyName: string;
}
function page() {
  const { getToken } = useAuth();
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getCurrentUser = async () => {
    try {
      const token = await getToken();
      const response: AxiosResponse<CurrentUser> = await axiosInstance.get(
        GET_CURRENT_USER,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log(response.data);
      setCurrentUser(response.data);
    } catch (err: any) {
      console.log(err);
      if (err.response?.status === 404) {
        setError("User not found.");
      } else {
        setError("Something went wrong.");
      }
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    getCurrentUser();
  }, []);

  return (
    <Agent
      userName={currentUser?.fullName!}
      createdBy={currentUser?.fullName}
      companyId={currentUser?.companyId!}
      companyName={currentUser?.companyName!}
      type="generate"
    />
  );
}

export default page;
