"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslations } from "next-intl"

interface InterviewQuestionsCollapsibleProps {
  questions: string[]
}

export default function InterviewQuestionsCollapsible({ questions }: InterviewQuestionsCollapsibleProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const t  = useTranslations()
  return (
    <Card className="mb-8">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl">{t('interview-questions')}</CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{questions.length} {t('questions')}</Badge>
          <Button variant="ghost" size="sm" onClick={() => setIsExpanded(!isExpanded)} className="h-8 w-8 p-0">
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent>
          <div className="space-y-3">
            {questions.map((question, index) => (
              <div key={index} className="p-3 bg-muted/30 rounded-md border">
                <div className="flex items-start gap-2">
                  <Badge variant="outline" className="mt-0.5 shrink-0">
                    {index + 1}
                  </Badge>
                  <p>{question}</p>
                </div>
              </div>
            ))}

            {questions.length === 0 && (
              <div className="py-4 text-center">
                <p className="text-muted-foreground">{t('no-questions-available-for-this-interview')}</p>
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  )
}
