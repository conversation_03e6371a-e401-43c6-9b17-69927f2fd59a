"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  FileText,
  Mic,
  MicOff,
  PhoneCall,
  PhoneOff,
  Send,
  Video,
  VideoOff,
} from "lucide-react";
import { Link } from '@/i18n/nvigation';;
import { toast } from "sonner";
import { useTranslations } from "next-intl"; // Import useTranslations

interface Message {
  role: "user" | "ai" | string;
  content: string;
  timestamp: string;
}

interface Interview {
  id: string;
  role: string;
  level: string;
  questions: string[];
  techstack: string[];
  createdAt: string;
  userId: string;
  type: string;
  finalized: boolean;
  candidateName: string;
  candidateEmail: string;
  transcript: Message[];
  hasFeedback: boolean;
}

export default function InterviewDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [callActive, setCallActive] = useState(false);
  const [videoEnabled, setVideoEnabled] = useState(true);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [inputMessage, setInputMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [aiLoading, setAiLoading] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const t = useTranslations("InterviewDetailPage"); // Initialize useTranslations

  // Mock AI responses for demo purposes
  const mockAIResponses = [
    "Tell me about your experience with React.",
    "How would you optimize a slow-loading website?",
    "Can you explain how you would implement authentication in a web application?",
    "What's your approach to debugging a complex issue?",
    "How do you stay updated with the latest technologies?",
  ];

  useEffect(() => {
    // In a real app, you would fetch the interview data from an API
    // For this demo, we'll use mock data
    const mockInterview: Interview = {
      id: params.id,
      role: "Frontend Developer",
      level: "senior",
      questions: [
        "Tell me about your experience with React",
        "How do you handle state management?",
        "Explain your approach to responsive design",
        "How do you optimize performance in web applications?",
        "Describe a challenging project you worked on",
      ],
      techstack: ["React", "TypeScript", "Next.js"],
      createdAt: "2023-05-15T10:30:00Z",
      userId: "user1",
      type: "technical",
      finalized: false,
      candidateName: "John Doe",
      candidateEmail: "<EMAIL>",
      transcript: [],
      hasFeedback: false,
    };

    // Simulate API call
    setTimeout(() => {
      setInterview(mockInterview);
      setLoading(false);
    }, 1000);
  }, [params.id]);

  useEffect(() => {
    if (callActive && interview) {
      // Add initial AI message when call starts
      const initialMessage = {
        role: "ai",
        content: `Hello! I'll be conducting your ${
          interview.type
        } interview for the ${interview.role} ${
          interview.level
        } position. Let's start with a few questions about your experience with ${interview.techstack.join(
          ", "
        )}. Are you ready?`,
        timestamp: new Date().toISOString(),
      };
      setMessages([initialMessage]);
    }
  }, [callActive, interview]);

  useEffect(() => {
    if (videoEnabled && callActive) {
      const startVideo = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: true,
          });
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
          }
        } catch (err) {
          console.error("Error accessing webcam:", err);
          toast(t("webcamErrorTitle"), {
            description: t("webcamErrorDescription"),
          });
          setVideoEnabled(false);
        }
      };
      startVideo();

      return () => {
        const stream = videoRef.current?.srcObject as MediaStream;
        if (stream) {
          stream.getTracks().forEach((track) => track.stop());
        }
      };
    }
  }, [videoEnabled, callActive, toast, t]); // Add 't' to dependencies

  const handleStartCall = () => {
    setCallActive(true);
    toast(t("callStartedToastTitle"), {
      description: t("callStartedToastDescription"),
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t("loadingInterview")}</p>
        </div>
      </div>
    );
  }

  if (!interview) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center">
            <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <h2 className="text-xl font-bold mb-2">{t("interviewNotFoundTitle")}</h2>
            <p className="text-center text-muted-foreground mb-6">
              {t("interviewNotFoundDescription")}
            </p>
            <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
              <Button>{t("returnToInterviews")}</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center gap-2 mb-6">
        <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{t("interviewDetailsTitle")}</h1>
      </div>
      <div className="flex flex-col md:flex-row gap-6">
        <div className="flex flex-col gap-6 flex-1">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("roleCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium">{interview.role}</span>
                <Badge>{interview.level}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("interviewTypeCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium capitalize">
                  {interview.type}
                </span>
                <Badge
                  variant={interview.finalized ? "default" : "secondary"}
                >
                  {interview.finalized ? t("completedBadge") : t("notStartedBadge")}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("techStackCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {interview.techstack.map((tech) => (
                  <Badge key={tech} variant="secondary">
                    {tech}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex-1 flex justify-center">
          <Card className="max-w-md w-full">
            <CardContent className="pt-6 flex flex-col items-center">
              <Avatar className="h-24 w-24 mb-4">
                <AvatarImage
                  src="/placeholder.svg?height=96&width=96"
                  alt="AI Interviewer"
                />
                <AvatarFallback>AI</AvatarFallback>
              </Avatar>
              <h2 className="text-xl font-bold mb-2">
                {t("readyToStartInterviewTitle")}
              </h2>
              <p className="text-center text-muted-foreground mb-6">
                {t("readyToStartInterviewDescription", {
                  interviewType: interview.type,
                  interviewRole: interview.role,
                  interviewLevel: interview.level,
                })}
              </p>
              <Button
                size="lg"
                className="font-semibold shadow-md transition-all duration-300"
              >
                <Link href={`/enterprise/ai-agents/ai-interviewer/interviews/${params?.id}/take/start`} >
                {t("startButton")}
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}