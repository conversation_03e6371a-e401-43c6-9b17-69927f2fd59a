"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, FileText } from "lucide-react";
import { Link } from '@/i18n/nvigation';;
import { Candidate, InterviewResponse } from "../../page";
import axiosInstance from "@/config/axios";
import { GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID } from "@/utils/routes";
import { useParams } from "next/navigation";
import { useBackendUser } from "@/hooks/useBackendUser";
import {
  checkPermissions,
  interviewPermissions,
} from "@/utils/ACTION_PERMISSIONS";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function CandidateInterviewPage() {
  const params = useParams<{ id: string; candidateId: string }>();
  const router = useRouter();
  const t = useTranslations("CandidateInterviewPage"); // Initialize useTranslations
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  const [interview, setInterview] = useState<InterviewResponse | null>(null);
  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchCandidate = async (inteviewId: string, candidateId: String) => {
    try {
      const response = await axiosInstance.get<InterviewResponse>(
        GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID(inteviewId, candidateId)
      );
      console.log(response.data);
      setInterview(response.data);
      setCandidate(response?.data?.candidates[0] || null);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching candidate interview:", error);
      toast.error("Failed to load interview details.", {
        description: "There was an issue retrieving the candidate's interview.",
      });
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one interview-related permission
      const hasAnyInterviewPermission = checkPermissions(permissions, [
        interviewPermissions.canView,
        interviewPermissions.canViewFeedback,
      ]);

      // Redirect if user doesn't have any interview-related permissions
      if (!hasAnyInterviewPermission) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        });
        router.back();
      }
    }
  }, [backendUser, backendUserLoading, router, t]); // Add 't' to dependencies

  useEffect(() => {
    console.log(params.id);
    console.log(params.candidateId);
    if (params.id && params.candidateId) {
      fetchCandidate(params.id, params.candidateId);
    }
  }, [params?.id, params.candidateId]);

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">
            {t("loadingInterviewDetails")}
          </p>
        </div>
      </div>
    );
  }

  if (!interview || !candidate) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center">
            <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <h2 className="text-xl font-bold mb-2">
              {t("interviewNotFoundTitle")}
            </h2>
            <p className="text-center text-muted-foreground mb-6">
              {t("interviewNotFoundDescription")}
            </p>
            <Link href="/interviews">
              <Button>{t("returnToInterviews")}</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Format timestamp
  const formatTimestamp = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Link
              href={`/enterprise/ai-agents/ai-interviewer/interviews/${interview._id}`}
            >
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">
              {t("candidateInterviewTitle")}
            </h1>
          </div>
          <p className="text-muted-foreground">
            {candidate.name} ({candidate.email}) -{" "}
            {formatDate(candidate.completedAt || "")}
          </p>
        </div>
        {candidate.hasFeedback && (
          <Link href={`${params.candidateId}/feedback`}>
            {checkPermissions(backendUser?.permissions || [], [
              interviewPermissions.canView,
              interviewPermissions.canViewFeedback,
            ]) && (
              <Button className="gap-2">
                <FileText className="h-4 w-4" />
                {t("viewFeedbackButton")}
              </Button>
            )}
          </Link>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>{t("roleCardTitle")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="text-lg font-medium">{interview.role}</span>
              <Badge>{interview.level}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>{t("interviewTypeCardTitle")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="text-lg font-medium capitalize">
                {interview.type}
              </span>
              <Badge
                variant={
                  candidate.status === "completed" ? "default" : "secondary"
                }
              >
                {candidate.status === "completed"
                  ? t("statusCompleted")
                  : t("statusInProgress")}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>{t("scoreCardTitle")}</CardTitle>
          </CardHeader>
          <CardContent>
            {candidate.score ? (
              <div className="flex items-center gap-2">
                <span
                  className={`text-lg font-medium ${
                    candidate.score >= 80
                      ? "text-green-600"
                      : candidate.score >= 60
                      ? "text-yellow-600"
                      : "text-red-600"
                  }`}
                >
                  {candidate.score}/100
                </span>
                <Badge
                  variant={
                    candidate.score >= 80
                      ? "default"
                      : candidate.score >= 60
                      ? "secondary"
                      : "destructive"
                  }
                >
                  {candidate.score >= 80
                    ? t("scoreExcellent")
                    : candidate.score >= 60
                    ? t("scoreGood")
                    : t("scoreNeedsImprovement")}
                </Badge>
              </div>
            ) : (
              <span className="text-lg text-muted-foreground">
                {t("scoreNotScoredYet")}
              </span>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("transcriptTitle")}</CardTitle>
          <CardDescription>{t("transcriptDescription")}</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px] pr-4">
            <div className="space-y-6">
              {candidate.transcript.map((message, index) => (
                <div key={index} className="flex gap-4">
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                    {message.role.includes("assistant") ? "AI" : "C"}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold">
                        {message.role.includes("assistant")
                          ? t("aiInterviewer")
                          : candidate.name}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(message.timestamp)}
                      </span>
                    </div>
                    <p className="text-muted-foreground">{message.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}