"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Link } from '@/i18n/nvigation';;
import axiosInstance from "@/config/axios";
import {
  GET_INTERVIEW_BY_ID,
  GET_INTERVIEW_BY_ID_NO_CANDIDATES,
} from "@/utils/routes";
import {
  checkPermissions,
  interviewPermissions,
} from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl";

interface InterviewTemplate {
  _id: string;
  role: string;
  level: string;
  techstack: string[];
  createdAt: string;
  createdBy: string;
  type: string;
  questions: string[];
}

export default function EditInterviewTemplate() {
  const t = useTranslations();
  const params = useParams<{ id: string }>();
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  const [template, setTemplate] = useState<InterviewTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState("");
  const [level, setLevel] = useState("");
  const [techStack, setTechStack] = useState<string[]>([]);
  const [techInput, setTechInput] = useState("");
  const [type, setType] = useState("");
  const [questions, setQuestions] = useState<string[]>([]);
  const [newQuestion, setNewQuestion] = useState("");

  const router = useRouter();
  const fetchInterview = async (inteviewId: string) => {
    try {
      const response = await axiosInstance.get<InterviewTemplate>(
        `${GET_INTERVIEW_BY_ID(inteviewId)}?includeCandidates=false`
      );
      console.log(response.data);
      setTemplate(response.data);
      setRole(response.data.role);
      setLevel(response.data.level.toLocaleLowerCase());
      setTechStack([...response.data.techstack]);
      setType(response.data.type.toLocaleLowerCase());
      setQuestions([...response.data.questions]);
      setLoading(false);
    } catch (error) {}
  };

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one interview-related permission
      const hasAnyInterviewPermission = checkPermissions(permissions, [
        interviewPermissions.canUpdate,
      ]);

      // Redirect if user doesn't have any interview-related permissions
      if (!hasAnyInterviewPermission) {
        toast.error(t('permission-denied'), {
          description:
            t('you-dont-have-permission-to-access-the-interviews-page'),
        });
        router.back();
      }
    }
  }, [backendUser, backendUserLoading, router]);

  useEffect(() => {
    console.log(params.id);
    if (params.id) {
      fetchInterview(params.id);
    }
  }, [params?.id]);

  const handleAddTech = () => {
    if (techInput && !techStack.includes(techInput)) {
      setTechStack([...techStack, techInput]);
      setTechInput("");
    }
  };

  const handleRemoveTech = (tech: string) => {
    setTechStack(techStack.filter((t) => t !== tech));
  };

  const handleAddQuestion = () => {
    if (newQuestion.trim()) {
      setQuestions([...questions, newQuestion]);
      setNewQuestion("");
    }
  };

  const handleRemoveQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index));
  };

  const handleSaveTemplate = async () => {
    if (!role || !level || techStack.length === 0 || questions.length === 0) {
      toast(t('missing-information'), {
        description: t('please-fill-in-all-required-fields-before-saving'),
      });
      return;
    }
    console.log(template);
    const response = await axiosInstance.put(GET_INTERVIEW_BY_ID(params.id), {
      role,
      level,
      type,
      techstack: techStack,
      questions,
    });
    if (response.status === 200) {
      toast(t('template-updated'), {
        description: t('your-interview-template-has-been-updated-successfully'),
      });
      router.push("/enterprise/ai-agents/ai-interviewer/interviews");
    } else {
      toast(t('update-failed-0'), {
        description: t('something-went-wrong-please-try-again'),
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t('loading-template')}</p>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center">
            <h2 className="text-xl font-bold mb-2">{t('template-not-found')}</h2>
            <p className="text-center text-muted-foreground mb-6">
              {t('we-couldnt-find-the-template-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist')}
            </p>
            <Link href="/enterprise/ai-agents/ai-interviewerinterview-templates">
              <Button>{t('return-to-templates')}</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center gap-2 mb-6">
        <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{t('edit-interview-template')}</h1>
      </div>

      <div className="grid gap-6 max-w-3xl mx-auto">
        <div className="grid gap-2">
          <Label htmlFor="role">{t('role-0')}</Label>
          <Input
            id="role"
            placeholder={t('e-g-frontend-developer')}
            value={role}
            onChange={(e) => setRole(e.target.value)}
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="level">{t('level')}</Label>
          <Select value={level} onValueChange={setLevel}>
            <SelectTrigger id="level">
              <SelectValue placeholder={t('select-level')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="junior">{t('junior')}</SelectItem>
              <SelectItem value="mid">{t('mid-level')}</SelectItem>
              <SelectItem value="senior">{t('senior')}</SelectItem>
              <SelectItem value="lead">{t('lead')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-2">
          <Label htmlFor="type">{t('interview-type')}</Label>
          <Select value={type} onValueChange={setType}>
            <SelectTrigger id="type">
              <SelectValue placeholder={t('select-type')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="technical">{t('technical')}</SelectItem>
              <SelectItem value="behavioral">{t('behavioral')}</SelectItem>
              <SelectItem value="system-design">{t('system-design')}</SelectItem>
              <SelectItem value="mixed">{t('mixed')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-2">
          <Label htmlFor="tech-stack">{t('tech-stack-0')}</Label>
          <div className="flex gap-2">
            <Input
              id="tech-stack"
              placeholder={t('e-g-react')}
              value={techInput}
              onChange={(e) => setTechInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAddTech();
                }
              }}
            />
            <Button type="button" onClick={handleAddTech}>
              {t('add')}
            </Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {techStack.map((tech) => (
              <Badge
                key={tech}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {tech}
                <button
                  onClick={() => handleRemoveTech(tech)}
                  className="ml-1 rounded-full hover:bg-muted p-1"
                >
                  ×
                </button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="questions">{t('interview-questions')}</Label>
          <div className="flex gap-2">
            <Textarea
              id="questions"
              placeholder={t('enter-a-question')}
              value={newQuestion}
              onChange={(e) => setNewQuestion(e.target.value)}
              className="min-h-[60px]"
            />
            <Button
              type="button"
              onClick={handleAddQuestion}
              className="self-end"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-4 space-y-2">
            {questions.map((question, index) => (
              <div
                key={index}
                className="flex items-start gap-2 p-3 border rounded-md"
              >
                <div className="flex-1">
                  <p>{question}</p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveQuestion(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            {questions.length === 0 && (
              <p className="text-muted-foreground text-center py-4">
                {t('no-questions-added-yet-add-some-questions-to-create-your-interview-template')}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Link href="/enterprise/ai-agents/ai-interviewerinterview-templates">
            <Button variant="outline">{t('cancel')}</Button>
          </Link>
          <Button onClick={handleSaveTemplate}>{t('save-template')}</Button>
        </div>
      </div>
    </div>
  );
}
