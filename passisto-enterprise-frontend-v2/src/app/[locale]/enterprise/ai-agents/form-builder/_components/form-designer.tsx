"use client"

import { useState } from "react"
import { useDrag, useDrop } from "react-dnd"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Trash2, GripVertical, ChevronDown, ChevronUp, Copy } from "lucide-react"
import type { FormField } from "../_lib/types"
import { cn } from "@/lib/utils"

type FormDesignerProps = {
  formTitle: string
  setFormTitle: (title: string) => void
  formDescription: string
  setFormDescription: (description: string) => void
  formFields: FormField[]
  updateField: (id: string, updates: Partial<FormField>) => void
  removeField: (id: string) => void
  reorderFields: (startIndex: number, endIndex: number) => void
  brandColor: string
}

type DragItem = {
  index: number
  id: string
  type: string
}

export function FormDesigner({
  formTitle,
  setFormTitle,
  formDescription,
  setFormDescription,
  formFields,
  updateField,
  removeField,
  reorderFields,
  brandColor,
}: FormDesignerProps) {
  const [expandedField, setExpandedField] = useState<string | null>(null)

  const toggleFieldExpansion = (id: string) => {
    setExpandedField(expandedField === id ? null : id)
  }

  const duplicateField = (field: FormField) => {
    const newField = {
      ...field,
      id: `field-${Date.now()}`,
      label: `${field.label} (Copy)`,
    }
    const index = formFields.findIndex((f) => f.id === field.id)
    const newFields = [...formFields]
    newFields.splice(index + 1, 0, newField)
    return newFields
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Input
          type="text"
          value={formTitle}
          onChange={(e) => setFormTitle(e.target.value)}
          className="text-2xl font-bold border-none px-0 mb-2 focus-visible:ring-0"
          placeholder="Form Title"
        />
        <Textarea
          value={formDescription}
          onChange={(e) => setFormDescription(e.target.value)}
          className="resize-none border-none px-0 focus-visible:ring-0"
          placeholder="Form Description (optional)"
        />
      </div>

      <div className="space-y-4">
        {formFields.length === 0 ? (
          <div className="text-center py-12 border-2 border-dashed rounded-lg">
            <p className="text-gray-500">Your form is empty. Add fields from the sidebar.</p>
          </div>
        ) : (
          formFields.map((field, index) => (
            <FieldItem
              key={field.id}
              field={field}
              index={index}
              updateField={updateField}
              removeField={removeField}
              reorderFields={reorderFields}
              expanded={expandedField === field.id}
              toggleExpansion={toggleFieldExpansion}
              duplicateField={() => duplicateField(field)}
              brandColor={brandColor}
            />
          ))
        )}
      </div>
    </div>
  )
}

type FieldItemProps = {
  field: FormField
  index: number
  updateField: (id: string, updates: Partial<FormField>) => void
  removeField: (id: string) => void
  reorderFields: (startIndex: number, endIndex: number) => void
  expanded: boolean
  toggleExpansion: (id: string) => void
  duplicateField: () => void
  brandColor: string
}

function FieldItem({
  field,
  index,
  updateField,
  removeField,
  reorderFields,
  expanded,
  toggleExpansion,
  duplicateField,
  brandColor,
}: FieldItemProps) {
  const [{ isDragging }, drag, dragPreview] = useDrag({
    type: "FIELD",
    item: { index, id: field.id, type: "FIELD" } as DragItem,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: "FIELD",
    drop: (item: DragItem) => {
      if (item.index !== index) {
        reorderFields(item.index, index)
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  })

  return (
    <div
      ref={(node) => drop(dragPreview(node))}
      className={cn(
        "transition-all duration-200",
        isDragging && "opacity-50",
        isOver && canDrop && "border-2 border-primary border-dashed",
      )}
    >
      <Card className={cn("shadow-sm", expanded && "ring-2 ring-primary")}>
        <CardContent className="p-0">
          <div
            className="flex items-center justify-between p-4 cursor-pointer"
            onClick={() => toggleExpansion(field.id)}
          >
            <div className="flex items-center gap-3 flex-1">
              <div ref={drag} className="cursor-move">
                <GripVertical className="h-5 w-5 text-gray-400" />
              </div>
              <div className="flex-1">
                <div className="font-medium">{field.label}</div>
                <div className="text-sm text-gray-500">{field.type}</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {expanded ? (
                <ChevronUp className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-400" />
              )}
            </div>
          </div>

          {expanded && (
            <div className="p-4 border-t">
              <div className="space-y-4">
                <div>
                  <Label htmlFor={`${field.id}-label`}>Field Label</Label>
                  <Input
                    id={`${field.id}-label`}
                    value={field.label}
                    onChange={(e) => updateField(field.id, { label: e.target.value })}
                    className="mt-1"
                  />
                </div>

                {field.type === "select" && (
                  <div>
                    <Label htmlFor={`${field.id}-options`}>Options (one per line)</Label>
                    <Textarea
                      id={`${field.id}-options`}
                      value={(field.options || []).join("\n")}
                      onChange={(e) =>
                        updateField(field.id, {
                          options: e.target.value.split("\n").filter((opt) => opt.trim() !== ""),
                        })
                      }
                      className="mt-1"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`${field.id}-required`}
                      checked={field.required}
                      onCheckedChange={(checked) => updateField(field.id, { required: checked })}
                    />
                    <Label htmlFor={`${field.id}-required`}>Required</Label>
                  </div>

                  {/* Only show email validation for email fields */}
                  {field.type === "email" && (
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${field.id}-validation`}
                        checked={!!field.validation}
                        onCheckedChange={(checked) =>
                          updateField(field.id, {
                            validation: checked ? "email" : undefined,
                          })
                        }
                      />
                      <Label htmlFor={`${field.id}-validation`}>Email Validation</Label>
                    </div>
                  )}
                </div>

                <div className="pt-2 flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      duplicateField()
                    }}
                  >
                    <Copy className="h-4 w-4 mr-1" /> Duplicate
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      removeField(field.id)
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-1" /> Remove
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

