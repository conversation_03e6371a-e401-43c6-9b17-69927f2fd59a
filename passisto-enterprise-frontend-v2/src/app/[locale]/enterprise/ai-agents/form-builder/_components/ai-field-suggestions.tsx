"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Loader2, Wand2 } from "lucide-react";
import type { FormTemplate } from "../_lib/types";
import { useTranslations } from "next-intl"; // Import useTranslations

type AIFieldSuggestionsProps = {
  formIntent: string;
  setFormIntent: (intent: string) => void;
  isGenerating: boolean;
  setIsGenerating: (isGenerating: boolean) => void;
  addField: (field: any) => void;
  applyTemplate: (template: FormTemplate) => void;
};

async function generateWithOpenRouter(prompt: string) {
  try {
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer 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",
      },
      body: JSON.stringify({
        model: "google/gemini-2.0-flash-lite-001",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error("Error calling OpenRouter API:", error);
    throw error;
  }
}

export function AIFieldSuggestions({
  formIntent,
  setFormIntent,
  isGenerating,
  setIsGenerating,
  addField,
  applyTemplate,
}: AIFieldSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<FormTemplate | null>(null);
  const [error, setError] = useState<string | null>(null);
  const t = useTranslations("aiFieldSuggestions"); // Initialize useTranslations

  const generateSuggestions = async () => {
    if (!formIntent.trim()) {
      setError(t("describeFormPurposeError")); // Use translation
      return;
    }

    setError(null);
    setIsGenerating(true);
    setSuggestions(null);

    try {
      const prompt = `
        Generate a form template for: "${formIntent}".
        
        Return a JSON object with the following structure:
        {
          "title": "Form title based on the intent",
          "description": "Brief description of the form purpose",
          "fields": [
            {
              "id": "field-1",
              "type": "text|textarea|email|select|checkbox|radio|file",
              "label": "Field label",
              "required": true|false,
              "validation": "email" (optional),
              "options": ["Option 1", "Option 2"] (for select and radio types),
              "checkboxLabel": "Label for checkbox" (for checkbox type)
            }
          ]
        }
        
        Include appropriate fields based on the form intent. For example, if it's a contact form, include name, email, and message fields. If it's a job application, include fields for personal information, experience, and file upload for resume.
        
        IMPORTANT: Return ONLY the JSON object, nothing else.
      `;

      const text = await generateWithOpenRouter(prompt);

      // Parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedTemplate = JSON.parse(jsonMatch[0]);
        setSuggestions(parsedTemplate);
      } else {
        setError(t("generateSuggestionsFailedError")); // Use translation
      }
    } catch (err) {
      console.error("Error generating suggestions:", err);
      setError(t("anErrorOccurred")); // Use translation
    } finally {
      setIsGenerating(false);
    }
  };

  const handleApplySuggestions = () => {
    if (suggestions) {
      applyTemplate(suggestions);
      setSuggestions(null);
    }
  };

  return (
    <div className="space-y-3">
      <div>
        <Textarea
          placeholder={t("textareaPlaceholder")} // Use translation
          value={formIntent}
          onChange={(e) => setFormIntent(e.target.value)}
          className="resize-none"
          rows={3}
        />
        {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
      </div>

      <Button onClick={generateSuggestions} disabled={isGenerating || !formIntent.trim()} className="w-full">
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {t("generatingButton")}
          </>
        ) : (
          <>
            <Wand2 className="mr-2 h-4 w-4" />
            {t("generateFieldsButton")}
          </>
        )}
      </Button>

      {suggestions && (
        <Card className="p-3 mt-4 bg-primary/5">
          <div className="mb-2">
            <h4 className="font-medium">{t("suggestionsCardTitle", { title: suggestions.title })}</h4>
            <p className="text-sm text-gray-500">
              {t("suggestionsCardFieldsCount", { count: suggestions.fields.length })}
            </p>
          </div>
          <Button size="sm" className="w-full" onClick={handleApplySuggestions}>
            {t("applySuggestionsButton")}
          </Button>
        </Card>
      )}
    </div>
  );
}