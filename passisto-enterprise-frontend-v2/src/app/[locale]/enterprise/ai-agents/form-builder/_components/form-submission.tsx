"use client";

import type React from "react";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import type { StoredForm } from "../_lib/types";
import { cn } from "@/lib/utils";
import { CheckCircle2 } from "lucide-react";
import { Loader2 } from "lucide-react"; // Add this import for the spinner icon
import { FORM_BUILDER_SUBMIT } from "@/utils/routes";
import axiosInstance from "@/config/axios";
import { useAuth } from "@clerk/nextjs";

type FormSubmissionProps = {
  form: StoredForm;
  companyId: string
};

export function FormSubmission({ form, companyId }: FormSubmissionProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false); // Add loading state
  const { getToken } = useAuth();

  const handleChange = (id: string, value: any) => {
    setFormData((prev) => ({ ...prev, [id]: value }));

    // Clear error when field is filled
    if (errors[id]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    form.fields.forEach((field) => {
      // Check required fields
      if (
        field.required &&
        (!formData[field.id] || formData[field.id] === "")
      ) {
        newErrors[field.id] = "This field is required";
      }

      // Check email validation
      if (
        field.validation === "email" &&
        formData[field.id] &&
        !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData[field.id])
      ) {
        newErrors[field.id] = "Please enter a valid email address";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setLoading(true); // Set loading to true
      const formDataToSend = new FormData();

      // Ajoute tous les champs du formulaire
      Object.entries(formData).forEach(([key, value]) => {
        // Si le champ est un fichier
        if (value instanceof File) {
          const field = form.fields.find((f) => f.id === key);

          // Use the label if found, otherwise fallback to the key
          const fieldLabel = field?.label || key;

          formDataToSend.append(fieldLabel, value); // 'file' doit correspondre à upload.single('file')
        } else {
          formDataToSend.append(key, value);
        }
      });

      formDataToSend.append("formId", form.id);

      try {
        const token = await getToken();
        await axiosInstance.post(FORM_BUILDER_SUBMIT(companyId, form.id), formDataToSend);

        setSubmitted(true);
      } catch (error) {
        alert("Erreur lors de la soumission du formulaire.");
        console.error(error);
      } finally {
        setLoading(false); // Set loading to false after request
      }
    }
  };

  if (submitted) {
    return (
      <div className="min-h-screen flex flex-col w-full">
        {/* Header */}
        <div className="w-full py-8" style={{ backgroundColor: form.brandColor || "#4f46e5" }} />

        <div className="flex-1 flex items-center justify-center p-8">
          <div className="bg-card rounded-lg shadow-lg max-w-md w-full p-8 text-center">
            <div className="mb-6 flex justify-center">
              <div className="rounded-full bg-green-100 dark:bg-green-900 p-3">
                <CheckCircle2 className="h-12 w-12 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <h2 className="text-2xl font-bold mb-2 text-card-foreground">Form Submitted Successfully!</h2>
            <p className="text-muted-foreground mb-6">Thank you for your submission.</p>
            <Button
              onClick={() => setSubmitted(false)}
              style={{ backgroundColor: form.brandColor || "#4f46e5" }}
              className="w-full"
            >
              Submit Another Response
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <div
        className="fixed top-0 left-0 w-full py-4 z-50"
        style={{
          backgroundColor: form.brandColor || "#4f46e5",
        }}
      />

      <div className="w-full mx-auto px-4 py-8 mt-0">
        <div className="bg-card rounded-lg shadow-lg overflow-hidden">
          {/* Form Header */}
          <div className="border-b">
            <div className="px-6 py-4">
              {form.logo && (
                <div className="mb-4 flex justify-center">
                  <img src={form.logo || "/placeholder.svg"} alt="Company Logo" className="h-16 object-contain" />
                </div>
              )}
              <h1 className="text-2xl font-bold mb-2" style={{ color: form.brandColor || "#4f46e5" }}>
                {form.title}
              </h1>
              {form.description && <p className="text-muted-foreground">{form.description}</p>}
            </div>
          </div>

          {/* Form Body */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-6">
              {form.fields.map((field) => (
                <div key={field.id} className="p-4 bg-muted rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor={field.id} className="flex items-center gap-1 text-base">
                      {field.label}
                      {field.required && <span className="text-red-500">*</span>}
                    </Label>

                    {field.type === "text" && (
                      <Input
                        id={field.id}
                        type="text"
                        value={formData[field.id] || ""}
                        onChange={(e) => handleChange(field.id, e.target.value)}
                        className={cn("bg-card", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {field.type === "textarea" && (
                      <Textarea
                        id={field.id}
                        value={formData[field.id] || ""}
                        onChange={(e) => handleChange(field.id, e.target.value)}
                        className={cn("bg-card min-h-[100px]", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {field.type === "email" && (
                      <Input
                        id={field.id}
                        type="email"
                        value={formData[field.id] || ""}
                        onChange={(e) => handleChange(field.id, e.target.value)}
                        className={cn("bg-card", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {field.type === "select" && (
                      <Select value={formData[field.id] || ""} onValueChange={(value) => handleChange(field.id, value)}>
                        <SelectTrigger className={cn("bg-card", errors[field.id] ? "border-red-500" : "")}>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          {(field.options || []).map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}

                    {field.type === "checkbox" && (
                      <div className="flex items-center space-x-2 pt-2">
                        <Checkbox
                          id={field.id}
                          checked={formData[field.id] || false}
                          onCheckedChange={(checked) => handleChange(field.id, checked)}
                        />
                        <label
                          htmlFor={field.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {field.checkboxLabel || "Yes"}
                        </label>
                      </div>
                    )}

                    {field.type === "radio" && (
                      <RadioGroup
                        value={formData[field.id] || ""}
                        onValueChange={(value) => handleChange(field.id, value)}
                      >
                        <div className="space-y-2 pt-2">
                          {(field.options || []).map((option) => (
                            <div key={option} className="flex items-center space-x-2">
                              <RadioGroupItem value={option} id={`${field.id}-${option}`} />
                              <Label htmlFor={`${field.id}-${option}`}>{option}</Label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                    )}

                    {field.type === "file" && (
                      <Input
                        id={field.id}
                        type="file"
                        onChange={(e) => {
                          const files = (e.target as HTMLInputElement).files
                          handleChange(field.id, files ? files[0] : null)
                        }}
                        className={cn("bg-card", errors[field.id] ? "border-red-500" : "")}
                      />
                    )}

                    {errors[field.id] && <p className="text-red-500 text-sm">{errors[field.id]}</p>}
                  </div>
                </div>
              ))}

              {form.fields.length > 0 && (
                <div className="flex justify-between items-center pt-4">
                  <p className="text-sm text-muted-foreground">* Required fields</p>
                  <Button
                    type="submit"
                    className="px-8 flex items-center gap-2"
                    style={{
                      backgroundColor: form.brandColor || "#4f46e5",
                      borderColor: form.brandColor || "#4f46e5",
                    }}
                    disabled={loading}
                  >
                    {loading && <Loader2 className="animate-spin h-5 w-5" />}
                    Submit
                  </Button>
                </div>
              )}

              {form.fields.length === 0 && (
                <div className="text-center py-8 border-2 border-dashed rounded-lg">
                  <p className="text-muted-foreground">This form has no fields.</p>
                </div>
              )}
            </div>
          </form>

          {/* Form Footer */}
          <div className="border-t px-6 py-4">
            <p className="text-xs text-muted-foreground text-center">
              This form was created using Passisto AI-Powered Form Builder
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
