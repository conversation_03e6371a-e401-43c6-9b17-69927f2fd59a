import {
  ENHANC<PERSON>_DESCRIPTION_PROMPT,
  GENERATE_TEMPLATE_PROMPT,
} from "../_data/PROMPTS";
import axiosInstance from "@/config/axios";
import {
  FORM_BUILDER_DELETE,
  FORM_BUILDER_DELETE_RESPONSE,
  FORM_BUILDER_DOWNLOAD_RESPONSE_FILE,
  FORM_BUILDER_EDIT,
  FORM_BUILDER_ENHANCE_DESCRIPTION,
  FORM_BUILDER_EXPORT_FORM,
  FORM_BUILDER_GENERATE_FORM,
  FORM_BUILDER_GET_ALL,
  FORM_BUILDER_GET_BY_ID,
  FORM_BUILDER_GET_RESPONSES,
} from "@/utils/routes";
import type { FormTemplate, StoredForm, FormField } from "./types";
import { FileUpload } from "../responses/[id]/page";

export async function enhanceDescription(formIntent: string, token: string) {
  if (!formIntent.trim()) {
    return { error: "Please enter a form description first" };
  }

  try {
    const prompt = ENHANCE_DESCRIPTION_PROMPT(formIntent);

    const enhanced = await axiosInstance.post(
      FORM_BUILDER_ENHANCE_DESCRIPTION,
      {
        userInput: prompt,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const result = enhanced.data.enhancedDescription;
    console.log(result);

    return result;
  } catch (err) {
    console.error("Error enhancing description:", err);
    return { error: "Failed to enhance description. Please try again." };
  }
}

export async function generateForm(formIntent: string, token: string) {
  if (!formIntent.trim()) {
    return { error: "Please describe your form purpose" };
  }

  try {
    const prompt = GENERATE_TEMPLATE_PROMPT(formIntent);

    const text = await axiosInstance.post(
      FORM_BUILDER_GENERATE_FORM,
      {
        userInput: prompt,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    // Log the full response to debug
    console.log("API Response:", text.data);

    const parsedTemplate = text.data.formTemplate as FormTemplate;
    console.log(text.data.formTemplate);

    // Create a new form with unique ID
    const formId = `form-${Date.now()}`;

    const newForm: StoredForm = {
      id: formId,
      companyId: "company-id",
      title: parsedTemplate.title,
      description: parsedTemplate.description,
      fields: parsedTemplate.fields.map((field) => ({
        ...field,
        id: `field-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      })),
      brandColor: "#4f46e5",
      logo: "",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return { result: { formId, newForm } };
  } catch (err) {
    console.error("Error generating form:", err);
    return {
      error: "An error occurred while generating the form. Please try again.",
    };
  }
}

export async function saveForm(
  formId: string,
  formData: {
    title: string;
    description: string;
    fields: FormField[];
    brandColor: string;
    logo: string;
  },
  getToken: () => Promise<string>
) {
  try {
    const token = await getToken();
    const response = await axiosInstance.put(
      FORM_BUILDER_EDIT(formId),
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response;
  } catch (error) {
    throw error;
  }
}

export async function getFormById(
  formId: string,
  companyId: string,
  getToken: () => Promise<string>
) {
  try {
    console.log("Fetching form with ID:", formId);
    const token = await getToken();

    const response = await axiosInstance.get(
      FORM_BUILDER_GET_BY_ID(formId, companyId),
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    console.log("Form data response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error(
      "Error fetching form:",
      error?.response?.data || error?.message
    );
    throw error;
  }
}

export const downloadFile = async (
  file: FileUpload,
  formId: string,
  token: string
) => {
  try {
    const url = FORM_BUILDER_DOWNLOAD_RESPONSE_FILE(formId, file.filename);
    const response = await axiosInstance.get(url, {
      headers: { Authorization: `Bearer ${token}` },
      responseType: "blob",
    });

    const blobUrl = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = blobUrl;
    link.setAttribute("download", file.originalname);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error("Error downloading file:", error);
    throw new Error("Error downloading file");
  }
};

export const deleteSubmission = async (
  formId: string,
  submissionId: string,
  token: string
): Promise<void> => {
  try {
    const url = FORM_BUILDER_DELETE_RESPONSE(formId);
    await axiosInstance.delete(url, {
      headers: { Authorization: `Bearer ${token}` },
      data: { responseId: submissionId },
    });
  } catch (error) {
    console.error("Erreur lors de la suppression :", error);
    throw new Error("Échec de la suppression de la soumission");
  }
};

export const fetchFormResponses = async (formId: string, token: string) => {
  try {
    const res = await axiosInstance.get(FORM_BUILDER_GET_RESPONSES(formId), {
      headers: { Authorization: `Bearer ${token}` },
    });

    const data = res.data;

    // Extract form
    const form = data.form ?? (data.title ? data : null);

    // Extract responses
    let responses: any[] = [];
    if (Array.isArray(data.responses)) {
      responses = data.responses;
    } else if (data.responses?.responses) {
      responses = data.responses.responses;
    } else if (Array.isArray(data)) {
      responses = data;
    }

    return { form, responses };
  } catch (error: any) {
    console.error("Error loading responses:", error);
    throw new Error(
      error.response?.data?.message ||
        error.message ||
        "Error loading responses"
    );
  }
};

export const exportForm = async (
  formId: string,
  formTitle: string,
  format: "json" | "csv" | "pdf",
  token: string
) => {
  if (format === "json" || format === "csv") {
    try {
      const response = await axiosInstance.get(
        FORM_BUILDER_EXPORT_FORM(formId, format),
        {
          responseType: "blob",
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      const disposition = response.headers["content-disposition"];
      let fileName = `${formTitle
        .toLowerCase()
        .replace(/\s+/g, "-")}.${format}`;
      if (disposition) {
        const match = disposition.match(/filename="?([^"]+)"?/);
        if (match) fileName = match[1];
      }

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      alert("Error exporting the form.");
      console.error("Export error:", error);
    }
  } else {
    alert(
      `Export to ${format.toUpperCase()} will be implemented in a future update.`
    );
  }
};

export const loadForms = async (getToken: () => Promise<string | null>) => {
  const token = await getToken();
  if (!token) {
    throw new Error("Token not available");
  }

  try {
    const response = await axiosInstance.get(FORM_BUILDER_GET_ALL, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error loading forms:", error);
    throw error;
  }
};

// Fonction pour supprimer un form
export const deleteForm = async (
  formId: string,
  getToken: () => Promise<string | null>
) => {
  const token = await getToken();
  if (!token) {
    throw new Error("Token not available");
  }
  try {
    await axiosInstance.delete(`${FORM_BUILDER_DELETE(formId)}/`, {
      headers: { Authorization: `Bearer ${token}` },
    });
  } catch (error) {
    console.error("Error deleting form:", error);
    throw error;
  }
};
