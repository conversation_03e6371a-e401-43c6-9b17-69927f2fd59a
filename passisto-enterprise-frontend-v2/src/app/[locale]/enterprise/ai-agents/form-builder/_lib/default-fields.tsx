import type { FormField } from "./types"
import { Type, AlignLeft, Mail, List, CheckSquare, CircleDot, FileText } from "lucide-react"

export const defaultFormFields: FormField[] = [
  {
    id: "field-text",
    type: "text",
    label: "text-field",
    required: false,
    icon: Type,
  },
  {
    id: "field-textarea",
    type: "textarea",
    label: "text-area",
    required: false,
    icon: AlignLeft,
  },
  {
    id: "field-email",
    type: "email",
    label: "email-field",
    required: false,
    validation: "email",
    icon: Mail,
  },
  {
    id: "field-select",
    type: "select",
    label: "dropdown",
    required: false,
    options: ["Option 1", "Option 2", "Option 3"],
    icon: List,
  },
  {
    id: "field-checkbox",
    type: "checkbox",
    label: "checkbox",
    required: false,
    checkboxLabel: "I agree to the terms",
    icon: CheckSquare,
  },
  {
    id: "field-radio",
    type: "radio",
    label: "radio-group",
    required: false,
    options: ["Option 1", "Option 2", "Option 3"],
    icon: CircleDot,
  },
  {
    id: "field-file",
    type: "file",
    label: "file-upload",
    required: false,
    icon: FileText,
  },
]

