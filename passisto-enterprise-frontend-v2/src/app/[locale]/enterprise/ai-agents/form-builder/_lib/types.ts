import type { LucideIcon } from "lucide-react"

export type FormField = {
  id: string
  type: "text" | "textarea" | "email" | "select" | "checkbox" | "radio" | "file"
  label: string
  required: boolean
  validation?: "email" | "url" | "phone"
  options?: string[]
  checkboxLabel?: string
  icon?: LucideIcon
}

export type FormTemplate = {
  id: string
  title: string
  description: string
  fields: FormField[]
  brandColor: string
  logo: string
}

export type StoredForm = {
  id: string
  title: string
  description: string
  fields: FormField[]
  brandColor: string
  logo: string
  createdAt: string
  updatedAt: string
  companyId:string
  
}

export type FormSubmissionDto = {
  formId: string
  data: Record<string, any>
  
}

export type FormSubmissionData = {
  id: string 
  formId: string
  data: Record<string, any>
  submittedAt: string
}

