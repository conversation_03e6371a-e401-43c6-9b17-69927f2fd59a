"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { FormBuilder } from "../_components/form-builder"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import type { FormTemplate } from "../_lib/types"

export default function EditorPage() {
  const router = useRouter()
  const [formTemplate, setFormTemplate] = useState<FormTemplate | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load form template from localStorage
    const savedTemplate = localStorage.getItem("formTemplate")
    if (savedTemplate) {
      try {
        setFormTemplate(JSON.parse(savedTemplate))
      } catch (e) {
        console.error("Error parsing form template:", e)
      }
    }
    setLoading(false)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (!formTemplate) {
    return (
      <div className="min-h-screen p-4 md:p-8 bg-gray-50">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">No Form Template Found</h1>
          <p className="mb-6">Please go back and create a form first.</p>
          <Button onClick={() => router.push("/")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Form Creation
          </Button>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen p-4 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <Button variant="outline" onClick={() => router.push("/")} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Form Creation
          </Button>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Form Editor</h1>
        </div>
        <FormBuilder initialTemplate={formTemplate} />
      </div>
    </main>
  )
}

