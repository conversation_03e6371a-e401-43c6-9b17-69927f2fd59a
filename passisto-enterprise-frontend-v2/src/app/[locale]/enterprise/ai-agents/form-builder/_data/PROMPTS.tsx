export const ENHANCE_DESCRIPTION_PROMPT = (userInput: string) => `
I need to create a form for: "${userInput}".
        
Please enhance this description by adding more details about what fields might be needed,
what validations would be appropriate, and any conditional logic that might be useful.

Make the description more specific and detailed, but keep it concise (max 3-4 sentences).
     
`;

export const GENERATE_TEMPLATE_PROMPT = (userInput: string) => `
Generate a form template for: "${userInput}".
        
        Return a JSON object with the following structure:
        {
          "title": "Form title based on the intent",
          "description": "Brief description of the form purpose",
          "fields": [
            {
              "id": "field-1",
              "type": "text|textarea|email|select|checkbox|radio|file",
              "label": "Field label",
              "required": true|false,
              "validation": "email" (only for email type),
              "options": ["Option 1", "Option 2"] (for select and radio types),
              "checkboxLabel": "Label for checkbox" (for checkbox type)
            }
          ]
        }
        
        Include appropriate fields based on the form intent. For example, if it's a contact form, include name, email, and message fields. If it's a job application, include fields for personal information, experience, and file upload for resume.
        
        IMPORTANT: Return ONLY the JSON object, nothing else.
`;
