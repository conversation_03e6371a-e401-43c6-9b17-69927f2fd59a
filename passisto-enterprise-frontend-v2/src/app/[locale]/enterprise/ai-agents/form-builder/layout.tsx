"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import axios from "axios";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import { useBackendUser } from "@/hooks/useBackendUser";
import { formBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";




interface UsersLayoutProps {
  children: React.ReactNode;
}

export default function UsersLayout({ children }: UsersLayoutProps) {
   const { backendUser, loading: backendUserLoading } = useBackendUser();
   const router = useRouter();
   const [loading, setLoading] = useState(true);

  useEffect(() => {
  // Only check permissions after backendUser has loaded
  if (!backendUserLoading && backendUser) {
    const permissions = backendUser?.permissions ?? [];
    setLoading(true);


    // Check if user has at least one form builder-related permission
    const hasAnyFormBuilderPermission =
      formBuilderPermissions.canCreateForm(permissions) ||
      formBuilderPermissions.canUpdateForm(permissions) ||
      formBuilderPermissions.canDeleteForm(permissions) ||
      formBuilderPermissions.canViewForm(permissions) ||
      formBuilderPermissions.canPublishForm(permissions) ||
      formBuilderPermissions.canExportFormData(permissions) ||
      formBuilderPermissions.canManageFormBuilder(permissions);

    // Redirect if user doesn't have any form builder-related permissions
    if (!hasAnyFormBuilderPermission) {
      toast.error("Permission denied", {
        description:
          "You don't have permission to access the form builder page.",
      });

      router.push("/enterprise/dashboard");
    }
    setLoading(false);
  }
}, [backendUser, backendUserLoading, router]);
 if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">Loading Form...</p>
        </div>
      </div>
    );
  }

  return (

   <> 
   {children}
    </>
  );
}
