"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ArrowLeft, Download, FileText, Trash2 } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import { useAuth } from "@clerk/nextjs";
import {
  deleteSubmission,
  downloadFile,
  exportForm,
  fetchFormResponses,
} from "../../_lib/actions";
import { formBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { toast } from "sonner";

// ==================== TYPES ET INTERFACES ====================

interface FormField {
  id: string;
  type: string;
  label: string;
  required?: boolean;
  options?: string[];
  placeholder?: string;
}

interface StoredForm {
  id: string;
  title: string;
  description?: string;
  fields: FormField[];
  brandColor: string;
  logo?: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface FileUpload {
  path: string;
  size: number;
  filename: string;
  mimetype: string;
  originalname: string;
  id: string;
  label: string;
}

interface FormSubmissionData {
  id?: string;
  formId?: string;
  submittedAt?: string;
  files?: FileUpload[];
  data?: {
    [key: string]: any;
  };
  [key: string]: any;
}

// ==================== FONCTIONS UTILITAIRES ====================

// Fonction pour formater la taille des fichiers
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (
    Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  );
};

// Fonction pour obtenir le label d'un champ
const getFieldLabel = (fieldId: string, fields: FormField[]): string => {
  const field = fields.find((f) => f.id === fieldId);
  if (field) return field.label;

  // Fallback pour les champs dynamiques
  switch (fieldId) {
    case "field-1":
      return "Name";
    case "field-2":
      return "Email";
    default:
      return fieldId.replace(/^field-/, "Field ").replace(/-/g, " ");
  }
};

// Fonction pour exporter en CSV
const exportResponsesCSV = async (
  form: StoredForm,
  submissions: FormSubmissionData[],
  token: string
) => {
  if (!form || submissions.length === 0) {
    return;
  }

  try {
    await exportForm(form.id, form.title, "csv", token);
  } catch (error) {
    console.error("Erreur exportResponsesCSV:", error);
  }
};
export default function ResponsesPage({
  params,
}: {
  params: { companyId: string; id: string };
}) {
  const router = useRouter();
  const { getToken } = useAuth();
  const [form, setForm] = useState<StoredForm | null>(null);
  const [submissions, setSubmissions] = useState<FormSubmissionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [submissionToDelete, setSubmissionToDelete] = useState<{
    id?: string;
    index: number;
  } | null>(null);

  // Add these state variables to manage pagination at the top of the component, after the existing state declarations
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [totalPages, setTotalPages] = useState(1);
  const [expandedSubmissions, setExpandedSubmissions] = useState<Set<string>>(
    new Set()
  );
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Fonction pour obtenir le label d'un champ (utilise le form state)
  const getFieldLabelForForm = (fieldId: string): string => {
    if (!form || !form.fields) return fieldId;
    return getFieldLabel(fieldId, form.fields);
  };

  // Fonction pour télécharger un fichier (utilise le token)
  const handleDownloadFile = async (file: FileUpload) => {
    try {
      const token = await getToken();
      if (!token) throw new Error("Jeton d'authentification manquant");
      await downloadFile(file, params.id, token);
    } catch (error) {
      console.error("Erreur:", error);
    }
  };

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one form builder-related permission
      const hasAnyFormBuilderPermission =
        formBuilderPermissions.canCreateForm(permissions) ||
        formBuilderPermissions.canUpdateForm(permissions) ||
        formBuilderPermissions.canDeleteForm(permissions) ||
        formBuilderPermissions.canViewForm(permissions) ||
        formBuilderPermissions.canExportFormData(permissions) ||
        formBuilderPermissions.canPublishForm(permissions) ||
        formBuilderPermissions.canDeleteFormResponse(permissions) ||
        formBuilderPermissions.canExportFormData(permissions) ||
        formBuilderPermissions.canViewResponses(permissions) ||
        formBuilderPermissions.canManageFormBuilder(permissions);

      // Redirect if user doesn't have any form builder-related permissions
      if (!hasAnyFormBuilderPermission) {
        toast.error("Permission denied", {
          description:
            "You don't have permission to access the form builder page.",
        });
        router.push("/enterprise/dashboard");
      }
    }
  }, [backendUser, backendUserLoading, router]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const token = await getToken();
        if (!token) throw new Error("Missing authentication token");

        console.log("Loading form responses for form:", params.id);

        const { form, responses } = await fetchFormResponses(params.id, token);

        console.log("Form:", form);
        console.log("Submissions:", responses);

        if (form) setForm(form);
        if (responses) setSubmissions(responses);
      } catch (error: any) {
        console.error("Error loading data:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchData();
    }
  }, [params.id, getToken]);

  // Add this calculation after the useEffect hook that fetches data
  // Calculate pagination values whenever submissions change
  useEffect(() => {
    if (submissions.length > 0) {
      setTotalPages(Math.ceil(submissions.length / itemsPerPage));
      // Reset to first page when submissions change
      setCurrentPage(1);
    } else {
      setTotalPages(1);
    }
  }, [submissions, itemsPerPage]);

  const handleDeleteSubmission = async () => {
    if (!submissionToDelete || !submissionToDelete.id) return;

    try {
      const token = await getToken();
      if (!token) throw new Error("Jeton d'authentification manquant");

      await deleteSubmission(params.id, submissionToDelete.id, token);

      setSubmissions(submissions.filter((s) => s.id !== submissionToDelete.id));
      setDeleteDialogOpen(false);
      setSubmissionToDelete(null);
    } catch (error) {
      console.error("Erreur de suppression :", error);
    }
  };

  const handleExportResponses = async () => {
    if (form) {
      const token = await getToken();
      await exportForm(form.id, form.title, "csv", token!);
    }
  };

  // Add this function to get the current page items
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return submissions.slice(startIndex, endIndex);
  };

  // Add this function to handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const toggleSubmissionExpansion = (submissionId: string) => {
    const newExpanded = new Set(expandedSubmissions);
    if (newExpanded.has(submissionId)) {
      newExpanded.delete(submissionId);
    } else {
      newExpanded.add(submissionId);
    }
    setExpandedSubmissions(newExpanded);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen p-2 md:p-4 bg-gray-50">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-2 text-red-600">Error</h1>
          <p className="mb-4">{error}</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
      </div>
    );
  }

  if (!form) {
    return (
      <div className="min-h-screen p-2 md:p-4 bg-gray-50">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-2">Form Not Found</h1>
          <p className="mb-4">
            The form you are looking for does not exist or has been deleted.
          </p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen p-2 md:p-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="mb-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="mb-3 hover:bg-gray-50 transition-colors"
            size="sm"
          >
            <ArrowLeft className="mr-1 h-3 w-3" />
            Back
          </Button>
          <div className="flex justify-between items-center bg-white p-3 rounded-lg shadow-sm border border-gray-200">
            <div>
              <h1 className="text-xl font-bold tracking-tight text-gray-900 mb-1">
                Form Responses
              </h1>
              <p className="text-sm text-gray-600">{form.title}</p>
            </div>
            {submissions.length > 0 &&
              formBuilderPermissions.canExportFormData(
                backendUser?.permissions || []
              ) && (
                <Button
                  onClick={handleExportResponses}
                  className="bg-green-600 hover:bg-green-700 transition-colors shadow-sm"
                  size="sm"
                >
                  <Download className="mr-1 h-3 w-3" />
                  Export
                </Button>
              )}
          </div>
        </div>

        {submissions.length === 0 ? (
          <Card className="shadow-sm border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg py-3">
              <CardTitle className="text-lg text-gray-800">
                No Submissions
              </CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                This form has not received any responses yet.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3">
              <p className="text-gray-700 mb-2 text-sm">
                Share your form to start collecting responses.
              </p>
              <div className="bg-gray-50 p-2 rounded-lg border">
                <p className="font-medium text-gray-800 mb-1 text-xs">
                  Form URL:
                </p>
                <code className="block p-2 bg-white rounded border text-xs text-gray-700 break-all">
                  {typeof window !== "undefined"
                    ? `${window.location.origin}/public-page/form/${form.id}`
                    : ""}
                </code>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3">
            <div className="bg-white p-2 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600 font-medium text-sm">
                {submissions.length} response(s) received
              </p>
            </div>

            {getCurrentPageItems().map((submission, index) => {
              // Calculate the actual index in the full list for correct numbering
              const actualIndex = (currentPage - 1) * itemsPerPage + index;
              const submissionId = submission.id || `submission-${actualIndex}`;
              const isExpanded = expandedSubmissions.has(submissionId);

              return (
                <Card
                  key={submission.id || actualIndex}
                  className="shadow-sm border-0 hover:shadow-md transition-shadow duration-300"
                >
                  <Collapsible
                    open={isExpanded}
                    onOpenChange={() => toggleSubmissionExpansion(submissionId)}
                  >
                    <CollapsibleTrigger asChild>
                      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg py-2 px-3 cursor-pointer hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-200 transition-colors">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-base text-gray-800">
                              Submission #{actualIndex + 1}
                            </CardTitle>
                            <div className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs font-medium">
                              Response
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">
                              {isExpanded
                                ? "Click to collapse"
                                : "Click to expand"}
                            </span>
                            <div
                              className={`transform transition-transform duration-200 ${
                                isExpanded ? "rotate-180" : ""
                              }`}
                            >
                              <svg
                                className="w-4 h-4 text-gray-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                        <CardDescription className="text-gray-600 text-xs">
                          {submission.submittedAt && (
                            <>
                              Submitted on{" "}
                              {new Date(submission.submittedAt).toLocaleString(
                                "en-US"
                              )}
                            </>
                          )}
                        </CardDescription>
                      </CardHeader>
                    </CollapsibleTrigger>

                    <CollapsibleContent>
                      <CardContent className="p-3">
                        <div className="space-y-3">
                          {/* Champs de formulaire */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                            {Object.entries(submission).map(([key, value]) => {
                              // Ignorer les champs spéciaux
                              if (
                                [
                                  "id",
                                  "formId",
                                  "submittedAt",
                                  "files",
                                  "data",
                                ].includes(key)
                              ) {
                                return null;
                              }

                              return (
                                <div
                                  key={key}
                                  className="bg-gray-50 p-2 rounded-lg border border-gray-200"
                                >
                                  <p className="text-xs font-semibold text-gray-700 mb-1">
                                    {getFieldLabelForForm(key)}
                                  </p>
                                  <p className="break-words text-gray-800 text-sm">
                                    {value !== undefined && value !== null ? (
                                      typeof value === "object" ? (
                                        <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
                                          {JSON.stringify(value, null, 2)}
                                        </pre>
                                      ) : (
                                        <span className="font-medium">
                                          {String(value)}
                                        </span>
                                      )
                                    ) : (
                                      <span className="text-gray-400 italic text-xs">
                                        No response
                                      </span>
                                    )}
                                  </p>
                                </div>
                              );
                            })}

                            {/* Afficher les données du champ data si présent */}
                            {submission.data &&
                              Object.entries(submission.data).map(
                                ([key, value]) => (
                                  <div
                                    key={key}
                                    className="bg-gray-50 p-2 rounded-lg border border-gray-200"
                                  >
                                    <p className="text-xs font-semibold text-gray-700 mb-1">
                                      {getFieldLabelForForm(key)}
                                    </p>
                                    <p className="break-words text-gray-800 text-sm">
                                      {value !== undefined && value !== null ? (
                                        typeof value === "object" ? (
                                          <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
                                            {JSON.stringify(value, null, 2)}
                                          </pre>
                                        ) : (
                                          <span className="font-medium">
                                            {String(value)}
                                          </span>
                                        )
                                      ) : (
                                        <span className="text-gray-400 italic text-xs">
                                          No response
                                        </span>
                                      )}
                                    </p>
                                  </div>
                                )
                              )}
                          </div>

                          {/* Fichiers uploadés */}
                          {submission.files && submission.files.length > 0 && (
                            <div className="bg-blue-50 p-2 rounded-lg border border-blue-200">
                              <p className="text-xs font-semibold text-blue-800 mb-2">
                                📎 Files ({submission.files.length})
                              </p>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {submission.files.map((file, fileIndex) => (
                                  <div
                                    key={fileIndex}
                                    className="flex items-center justify-between p-2 bg-white rounded-lg border border-blue-200 hover:border-blue-300 transition-colors"
                                  >
                                    <div className="flex items-center space-x-2">
                                      <div className="bg-blue-100 p-1 rounded-lg">
                                        <FileText className="h-4 w-4 text-blue-600" />
                                      </div>
                                      <div>
                                        <p className="text-xs font-medium text-gray-800">
                                          {file.label}
                                        </p>
                                        <p className="text-xs font-medium text-gray-800">
                                          {file.originalname}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                          {formatFileSize(file.size)}
                                        </p>
                                      </div>
                                    </div>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleDownloadFile(file)}
                                      className="h-7 px-2 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                    >
                                      <Download className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Bouton de suppression en bas à droite */}
                          <div className="flex justify-end pt-2 border-t border-gray-200">
                            <Dialog
                              open={deleteDialogOpen}
                              onOpenChange={setDeleteDialogOpen}
                            >
                              <DialogTrigger asChild>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => {
                                    setSubmissionToDelete({
                                      id: submission.id,
                                      index: index,
                                    });
                                  }}
                                  className="flex items-center gap-1 hover:bg-red-600 transition-colors shadow-sm h-7 px-2"
                                >
                                  <Trash2 className="h-3 w-3" />
                                  Delete
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[425px]">
                                <DialogHeader>
                                  <DialogTitle className="flex items-center gap-2 text-red-600">
                                    <Trash2 className="h-5 w-5" />
                                    Delete
                                  </DialogTitle>
                                  <DialogDescription className="text-gray-600">
                                    Are you sure you want to delete submission #
                                    {submissionToDelete?.index
                                      ? submissionToDelete.index + 1
                                      : ""}
                                    ? This action cannot be undone.
                                  </DialogDescription>
                                </DialogHeader>
                                <DialogFooter className="gap-2">
                                  <Button
                                    variant="outline"
                                    onClick={() => {
                                      setDeleteDialogOpen(false);
                                      setSubmissionToDelete(null);
                                    }}
                                    className="hover:bg-gray-50"
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    onClick={handleDeleteSubmission}
                                    className="hover:bg-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              );
            })}
            {submissions.length > 0 && (
              <div className="flex justify-between items-center mt-4 bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                <div className="text-sm text-gray-600">
                  Showing{" "}
                  {Math.min(
                    submissions.length,
                    (currentPage - 1) * itemsPerPage + 1
                  )}{" "}
                  to {Math.min(submissions.length, currentPage * itemsPerPage)}{" "}
                  of {submissions.length} responses
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-8 px-2"
                  >
                    Previous
                  </Button>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                      (page) => (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className={`h-8 w-8 p-0 ${
                            currentPage === page
                              ? "bg-primary text-primary-foreground"
                              : ""
                          }`}
                        >
                          {page}
                        </Button>
                      )
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-8 px-2"
                  >
                    Next
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    className="h-8 rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={itemsPerPage}
                    onChange={(e) => setItemsPerPage(Number(e.target.value))}
                  >
                    <option value={5}>5 per page</option>
                    <option value={10}>10 per page</option>
                    <option value={25}>25 per page</option>
                    <option value={50}>50 per page</option>
                  </select>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </main>
  );
}
