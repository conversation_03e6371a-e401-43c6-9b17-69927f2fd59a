"use client";

import { FormDescriptionInput } from "./_components/form-description-input";
import { Button } from "@/components/ui/button";
import { Link } from "@/i18n/nvigation";
import { LayoutDashboard, Sparkles } from "lucide-react";
import { formBuilderPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl";

export default function FormBuilderAgent() {
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const t = useTranslations();
  return (
    <main className="p-4 mt-8 md:mt-8 md:p-8 bg-background">
      <div className="w-full mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl flex items-center justify-center gap-2 md:text-4xl font-bold tracking-tight text-foreground mb-2">
            <Sparkles className="h-6 w-6 text-primary" />
            {t("ai-powered-form-builder")}
          </h1>
          <p className="text-lg text-muted-foreground mb-6">
            {t(
              "create-custom-forms-with-intelligent-field-suggestions-based-on-your-business-needs"
            )}
          </p>
          <div className="flex justify-center mb-8">
            {formBuilderPermissions.canViewForm(
              backendUser?.permissions || []
            ) && (
              <Button asChild variant="outline">
                <Link
                  href="/enterprise/ai-agents/form-builder/dashboard"
                  className="flex items-center gap-2"
                >
                  <LayoutDashboard className="h-4 w-4" />
                  {t("view-your-forms-dashboard")}
                </Link>
              </Button>
            )}
          </div>
        </div>
        <FormDescriptionInput />
      </div>
    </main>
  );
}