"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON> } from "@/i18n/nvigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Edit, Eye, Trash2, BarChart, ArrowLeft, Plus } from "lucide-react"
import type { StoredForm } from "../_lib/types"

import { useAuth } from "@clerk/nextjs"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { deleteForm, loadForms } from "../_lib/actions"
import { formBuilderPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useBackendUser } from "@/hooks/useBackendUser"
import { toast } from "sonner"
import { useTranslations } from "next-intl"

export default function DashboardPage() {
  const router = useRouter()
  const { getToken } = useAuth()
  const [forms, setForms] = useState<StoredForm[]>([])
  const [loading, setLoading] = useState(true)
  const [formToDelete, setFormToDelete] = useState<string | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const { backendUser, loading: backendUserLoading } = useBackendUser()

  const t = useTranslations()

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? []

      // Check if user has at least one form builder-related permission
      const hasAnyFormBuilderPermission =
        formBuilderPermissions.canCreateForm(permissions) ||
        formBuilderPermissions.canUpdateForm(permissions) ||
        formBuilderPermissions.canDeleteForm(permissions) ||
        formBuilderPermissions.canViewForm(permissions) ||
        formBuilderPermissions.canExportFormData(permissions) ||
        formBuilderPermissions.canPublishForm(permissions) ||
        formBuilderPermissions.canExportFormData(permissions) ||
        formBuilderPermissions.canManageFormBuilder(permissions)

      // Redirect if user doesn't have any form builder-related permissions
      if (!hasAnyFormBuilderPermission) {
        toast.error("Permission denied", {
          description: "You don't have permission to access the form builder page.",
        })
        router.push("/enterprise/dashboard")
      }
    }
  }, [backendUser, backendUserLoading, router])

  useEffect(() => {
    const fetchForms = async () => {
      try {
        const data = await loadForms(getToken)
        setForms(data)
      } catch (error) {
      } finally {
        setLoading(false)
      }
    }

    fetchForms()
  }, [getToken])

  const handleEditForm = (id: string) => {
    router.push(`/enterprise/ai-agents/form-builder/editor/${id}`)
  }
  const handleViewForm = (id: string, companyId: string) => {
    router.push(`/public-page/company/${companyId}/form/${id}`)
  }

  const handleViewResponses = (id: string, companyId: string) => {
    router.push(`/enterprise/ai-agents/form-builder/responses/${id}`)
  }

  const openDeleteDialog = (formId: string) => {
    setFormToDelete(formId)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteForm = async () => {
    if (!formToDelete) return

    try {
      await deleteForm(formToDelete, getToken)
      setForms(forms.filter((form) => form.id !== formToDelete))
    } catch (error) {
      alert("Failed to delete the form. Please try again.")
    } finally {
      setIsDeleteDialogOpen(false)
      setFormToDelete(null)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  return (
    <main className="min-h-screen p-4 sm:p-6 md:p-8 bg-background">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div>
              <Button variant="outline" asChild className="mb-2">
                <Link href="/enterprise/ai-agents/form-builder" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  {t("back-to-home")}
                </Link>
              </Button>
              <h1 className="text-3xl font-bold tracking-tight text-foreground">Your Forms Dashboard</h1>
            </div>
            {formBuilderPermissions.canCreateForm(backendUser?.permissions || []) && (
              <Button asChild>
                <Link href="/enterprise/ai-agents/form-builder" className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Create New Form
                </Link>
              </Button>
            )}
          </div>
        </div>

        {forms.length === 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>No Forms Yet</CardTitle>
              <CardDescription>You haven't created any forms yet. Use the form builder to get started.</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-muted-foreground">
                Create your first form by describing what you need, and our AI will generate it for you.
              </p>
              {formBuilderPermissions.canCreateForm(backendUser?.permissions || []) && (
                <Button asChild>
                  <Link href="/enterprise/ai-agents/form-builder">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Form
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {forms.map((form) => (
              <Card key={form.id} className="overflow-hidden flex flex-col h-full">
                <CardHeader className="pb-2">
                  <CardTitle className="truncate">{form.title}</CardTitle>
                  <CardDescription>Created: {new Date(form.createdAt).toLocaleDateString()}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2 flex-1">
                  <p className="text-sm text-muted-foreground line-clamp-2">{form.description || "No description"}</p>
                  <p className="text-sm mt-1">{form.fields.length} fields</p>
                </CardContent>
                <CardFooter className="pt-2 border-t">
                  <div className="flex flex-col gap-2 w-full">
                    {/* First row - Primary actions */}
                    <div className="flex gap-2">
                      {formBuilderPermissions.canUpdateForm(backendUser?.permissions || []) && (
                        <Button size="sm" variant="outline" onClick={() => handleEditForm(form.id)} className="flex-1">
                          <Edit className="h-4 w-4 mr-1" />
                          <span className="text-xs sm:text-sm">Edit</span>
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewForm(form.id, form.companyId)}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        <span className="text-xs sm:text-sm">View</span>
                      </Button>
                    </div>

                    {/* Second row - Secondary actions */}
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewResponses(form.id, form.companyId)}
                        className="flex-1"
                      >
                        <BarChart className="h-4 w-4 mr-1" />
                        <span className="text-xs sm:text-sm">Responses</span>
                      </Button>
                      {formBuilderPermissions.canDeleteForm(backendUser?.permissions || []) && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => openDeleteDialog(form.id)}
                          className="flex-1"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          <span className="text-xs sm:text-sm">Delete</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this form? This action cannot be undone and all responses will be
              permanently deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setFormToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteForm}
              // Remove custom bg-red-600 classes, use destructive variant
              className=""
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  )
}
