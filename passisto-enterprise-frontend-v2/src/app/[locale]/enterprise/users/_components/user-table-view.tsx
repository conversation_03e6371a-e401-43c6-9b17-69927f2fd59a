"use client";

import { useRouter } from "next/navigation";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Pencil, Trash2, Ban, MoreHorizontal, CheckCircle } from "lucide-react";
import { Team, User } from "@/store/slices/userSlice";
import { Role } from "@/store/slices/rolePermissionSlice";
import { getRoleBadgeVariant, getRoleLabel } from "../_lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { userPermissions } from "@/utils/ACTION_PERMISSIONS";
import { BackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"; // Import useTranslations

export function UserTableView({
  users,
  onDeleteUser,
  onToggleStatus,
  backendUser,
}: {
  users: User[];
  onDeleteUser: (user: User) => void;
  onToggleStatus: (user: User) => void;
  backendUser: BackendUser;
}) {
  const router = useRouter();
  const t = useTranslations("UserTableView"); // Initialize useTranslations

  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("nameTableHead")}</TableHead>
            <TableHead>{t("emailTableHead")}</TableHead>
            <TableHead>{t("rolesTableHead")}</TableHead>
            <TableHead>{t("teamsTableHead")}</TableHead>
            <TableHead className="w-[100px]">{t("actionsTableHead")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                {t("noUsersFound")}
              </TableCell>
            </TableRow>
          ) : (
            users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.firstName + " " + user.lastName}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  {user.roles && user.roles.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {user.roles.map((role: Role) => (
                        <Badge key={role.id} variant={getRoleBadgeVariant(role.name)}>
                          {getRoleLabel(role.name)}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">{t("noRoles")}</span>
                  )}
                </TableCell>
                <TableCell>
                  {user.teams && user.teams.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {user.teams.map((team: Team) => (
                        <Badge key={team.id} variant="outline">
                          {team.name}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">{t("noTeams")}</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    {userPermissions.canView(backendUser?.permissions || []) && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => router.push(`/enterprise/users/${user.id}/view`)}
                        title={t("viewDetailsButtonTitle")}
                      >
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">{t("viewDetailsSrOnly")}</span>
                      </Button>
                    )}
                    {userPermissions.canUpdate(backendUser?.permissions || []) && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => router.push(`/enterprise/users/${user.id}`)}
                        title={t("editUserButtonTitle")}
                      >
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">{t("editUserSrOnly")}</span>
                      </Button>
                    )}

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">{t("moreOptionsSrOnly")}</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {userPermissions.canToggleStatus(backendUser?.permissions || []) && (
                        <DropdownMenuItem
                          className={user.isActive ? "text-destructive focus:text-destructive" : "text-green-600 focus:text-green-600"}
                          onClick={() => onToggleStatus(user)}
                        >
                          {user.isActive ? (
                            <>
                              <Ban className="mr-2 h-4 w-4" />
                              {t("banAction")}
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              {t("activateAction")}
                            </>
                          )}
                        </DropdownMenuItem>
                        )}

                        <DropdownMenuSeparator />
                        {userPermissions.canDelete(backendUser?.permissions || []) && (
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => onDeleteUser(user)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t("deleteAction")}
                          </DropdownMenuItem>
                        )}

                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}