"use client"
import * as React from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { permissionCategories } from "@/lib/mock-data"
import { cn } from "@/lib/utils"

interface PermissionSelectorProps {
  selectedPermissions: string[]
  onChange: (permissions: string[]) => void
}

interface PermissionCategory {
  name: string
  permissions: string[]
}

export function PermissionSelector({ selectedPermissions = [], onChange }: PermissionSelectorProps) {
  const handleTogglePermission = (permission: string) => {
    let updatedPermissions: string[]
    if (selectedPermissions.includes(permission)) {
      updatedPermissions = selectedPermissions.filter((p) => p !== permission)
    } else {
      updatedPermissions = [...selectedPermissions, permission]
    }
    onChange(updatedPermissions)
  }

  const handleToggleCategory = (category: string) => {
    const categoryData = permissionCategories.find((c) => c.name === category)
    if (!categoryData) return

    const categoryPermissions = categoryData.permissions
    const allSelected = categoryPermissions.every((p) => selectedPermissions.includes(p))

    let updatedPermissions: string[]
    if (allSelected) {
      // Remove all permissions in this category
      updatedPermissions = selectedPermissions.filter((p) => !categoryPermissions.includes(p))
    } else {
      // Add all permissions in this category that aren't already selected
      const permissionsToAdd = categoryPermissions.filter((p) => !selectedPermissions.includes(p))
      updatedPermissions = [...selectedPermissions, ...permissionsToAdd]
    }

    onChange(updatedPermissions)
  }

  const formatPermissionName = (permission: string): string => {
    return permission
      .replace("CAN_", "")
      .split("_")
      .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ")
  }

  const isCategoryFullySelected = (category: string): boolean => {
    const categoryData = permissionCategories.find((c) => c.name === category)
    if (!categoryData) return false
    return categoryData.permissions.every((p) => selectedPermissions.includes(p))
  }

  const isCategoryPartiallySelected = (category: string): boolean => {
    const categoryData = permissionCategories.find((c) => c.name === category)
    if (!categoryData) return false
    return (
      categoryData.permissions.some((p) => selectedPermissions.includes(p)) && 
      !isCategoryFullySelected(category)
    )
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground mb-2">{selectedPermissions.length} permission(s) selected</div>

      <Accordion type="multiple" className="w-full">
        {permissionCategories.map((category) => (
          <AccordionItem key={category.name} value={category.name}>
            <AccordionTrigger className="hover:no-underline">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={isCategoryFullySelected(category.name)}
                  className={cn(
                    "data-[state=indeterminate]:bg-primary data-[state=indeterminate]:opacity-50",
                    isCategoryPartiallySelected(category.name) && "[&>span]:opacity-50 [&>span]:bg-primary"
                  )}
                  onCheckedChange={() => handleToggleCategory(category.name)}
                  onClick={(e: React.MouseEvent) => e.stopPropagation()}
                  aria-label={`Select all ${category.name} permissions`}
                />
                <span>{category.name}</span>
                <Badge variant="outline" className="ml-2">
                  {category.permissions.filter((p) => selectedPermissions.includes(p)).length} /{" "}
                  {category.permissions.length}
                </Badge>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="pl-6 space-y-2">
                {category.permissions.map((permission) => (
                  <div key={permission} className="flex items-center space-x-2">
                    <Checkbox
                      id={permission}
                      checked={selectedPermissions.includes(permission)}
                      onCheckedChange={() => handleTogglePermission(permission)}
                    />
                    <label htmlFor={permission} className="text-sm cursor-pointer">
                      {formatPermissionName(permission)}
                    </label>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}