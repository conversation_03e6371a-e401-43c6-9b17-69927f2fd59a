import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useAppSelector } from "@/store/hooks"

interface RoleSelectorProps {
  selectedRoles: string[]
  onChange: (roles: string[]) => void
}

export function RoleSelector({ selectedRoles, onChange }: RoleSelectorProps) {
  const [open, setOpen] = React.useState(false)
  
  // Get roles from Redux store
  const { roles } = useAppSelector((state) => state.rolePermission)
  
  // Extract role names for selection
  const roleNames = roles.map(role => role.name)

  const handleSelect = (currentValue: string) => {
    const newSelectedRoles = selectedRoles.includes(currentValue)
      ? selectedRoles.filter((role) => role !== currentValue)
      : [...selectedRoles, currentValue]
    
    onChange(newSelectedRoles)
    // Keep the popover open for multiple selections
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
            {selectedRoles.length > 0 ? `${selectedRoles.length} role(s) selected` : "Select roles..."}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search roles..." />
            <CommandList>
              <CommandEmpty>No roles found.</CommandEmpty>
              <CommandGroup>
                {roleNames.map((role) => (
                  <CommandItem key={role} value={role} onSelect={() => handleSelect(role)}>
                    <Check className={cn("mr-2 h-4 w-4", selectedRoles.includes(role) ? "opacity-100" : "opacity-0")} />
                    {role}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      
      {selectedRoles.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedRoles.map((role) => (
            <div key={role} className="bg-primary/10 text-primary rounded-md px-2 py-1 text-xs flex items-center">
              {role}
              <button
                type="button"
                onClick={() => onChange(selectedRoles.filter((r) => r !== role))}
                className="ml-1 text-primary hover:text-primary/80"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

