"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Pencil,
  Trash2,
  Eye,
  Mail,
  UserCircle,
  Shield,
  Users,
  Calendar,
  CheckCircle,
  XCircle,
  Ban,
} from "lucide-react";
import { Team, User } from "@/store/slices/userSlice";
import { Role } from "@/store/slices/rolePermissionSlice";
import { formatDate, getRoleBadgeVariant, getRoleLabel } from "../_lib/utils";
import { userPermissions } from "@/utils/ACTION_PERMISSIONS";
import { BackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"; // Import useTranslations

export function UserCardView({
  users,
  onDeleteUser,
  onToggleStatus,
  backendUser,
}: {
  users: User[];
  onDeleteUser: (user: User) => void;
  onToggleStatus: (user: User) => void;
  backendUser: BackendUser;
}): React.ReactElement {
  const router = useRouter();
  const t = useTranslations("UserCardView"); // Initialize useTranslations

  if (users.length === 0) {
    return (
      <div className="text-center py-10 border rounded-lg bg-muted/20">
        <UserCircle className="mx-auto h-10 w-10 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">{t("noUsersFoundTitle")}</h3>
        <p className="text-muted-foreground">
          {t("noUsersFoundDescription")}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {users.map((user) => (
        <Card
          key={user.id}
          className={`overflow-hidden ${
            !user.isActive ? "opacity-70 bg-muted/30" : ""
          }`}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div className="flex flex-col">
                <CardTitle className="text-xl flex items-center">
                  {user.firstName + " " + user.lastName}
                  {user.isActive !== undefined && (
                    <span
                      className="ml-2"
                      title={user.isActive ? t("statusActive") : t("statusInactive")}
                    >
                      {user.isActive ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                    </span>
                  )}
                </CardTitle>
                {!user.isActive && (
                  <span className="text-xs text-muted-foreground mt-1">
                    {t("inactiveAccount")}
                  </span>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">{t("actionsSrOnly")}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {userPermissions.canView(backendUser?.permissions || []) && (
                    <DropdownMenuItem
                      onClick={() =>
                        router.push(`/enterprise/users/${user.id}/view`)
                      }
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      {t("viewDetails")}
                    </DropdownMenuItem>
                  )}
                  {userPermissions.canUpdate(
                    backendUser?.permissions || []
                  ) && (
                    <DropdownMenuItem
                      onClick={() =>
                        router.push(`/enterprise/users/${user.id}`)
                      }
                    >
                      <Pencil className="mr-2 h-4 w-4" />
                      {t("edit")}
                    </DropdownMenuItem>
                  )}

                  {userPermissions.canToggleStatus(
                    backendUser?.permissions || []
                  ) && (
                  <DropdownMenuItem
                    className={
                      user.isActive
                        ? "text-destructive focus:text-destructive"
                        : "text-green-600 focus:text-green-600"
                    }
                    onClick={() => onToggleStatus(user)}
                  >
                    {user.isActive ? (
                      <>
                        <Ban className="mr-2 h-4 w-4" />
                        {t("ban")}
                      </>
                    ) : (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        {t("activate")}
                      </>
                    )}
                  </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  {userPermissions.canDelete(backendUser?.permissions || []) && (
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => onDeleteUser(user)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t("delete")}
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <div className="flex items-center text-sm text-muted-foreground mb-3">
              <Mail className="mr-2 h-4 w-4" />
              {t("emailLabel")}: {user.email}
            </div>

            <div className="flex items-center text-sm text-muted-foreground mb-3">
              <Calendar className="mr-2 h-4 w-4" />
              {t("createdLabel")}: {formatDate(user.createdAt)}
            </div>

            <div className="mb-3">
              <p className="text-sm font-medium mb-1 flex items-center">
                <UserCircle className="mr-1 h-4 w-4 text-primary" />
                {t("rolesLabel")}
              </p>
              <div className="flex flex-wrap gap-1">
                {user.roles.length > 0 ? (
                  user.roles.map((role: Role) => (
                    <Badge
                      key={role.id}
                      variant={getRoleBadgeVariant(role.name)}
                    >
                      {getRoleLabel(role.name)}
                    </Badge>
                  ))
                ) : (
                  <span className="text-sm text-muted-foreground">
                    {t("noRolesAssigned")}
                  </span>
                )}
              </div>
            </div>

            <div className="mb-3">
              <div className="flex items-center mb-1">
                <p className="text-sm font-medium flex items-center">
                  <Shield className="mr-1 h-4 w-4 text-purple-500" />
                  {t("permissionsLabel")}
                </p>
                <Badge variant="outline" className="ml-2 text-xs">
                  {user.permissionsCount?.toString() || "0"}
                </Badge>
              </div>
              {user.permissionsCount && user.permissionsCount > 0 ? (
                <div className="text-sm text-muted-foreground flex items-center">
                  <Shield className="h-3 w-3 mr-1" />
                  {t("permissionsCount", { count: user.permissionsCount })}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{t("noPermissions")}</p>
              )}
            </div>

            <div>
              <p className="text-sm font-medium mb-1 flex items-center">
                <Users className="mr-1 h-4 w-4 text-blue-500" />
                {t("teamsLabel")}
              </p>
              {user.teams.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {user.teams.map((team: Team) => (
                    <Badge key={team.id} variant="outline" className="text-xs">
                      {team.name}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">{t("noTeams")}</p>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}