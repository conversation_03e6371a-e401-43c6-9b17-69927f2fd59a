"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { getUserById, Team, User } from "@/store/slices/userSlice";
import { useAuth } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  ArrowLeft,
  Building,
  Calendar,
  Edit,
  Mail,
  Minus,
  Plus,
  Shield,
  UserCircle,
  Globe,
  Users,
  User as UserIcon,
} from "lucide-react";
import {
  fetchRolesPermissions,
  Permission,
  Role,
} from "@/store/slices/rolePermissionSlice";
import { fetchTeams } from "@/store/slices/teamSlice";
import {
  computeEffectivePermissions,
  formatPermissionName,
  getScopeBadgeColor,
  getScopeIcon,
  getScopeLabel,
} from "../../_lib/utils";
import { userHasPermission } from "@/lib/utils";
import { useBackendUser } from "@/hooks/useBackendUser";
import { userPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useTranslations } from "next-intl"; // Import useTranslations

export default function UserViewPage() {
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  const t = useTranslations("UserViewPage"); // Initialize useTranslations
  const { backendUser, loading: backendUserLoading, error } = useBackendUser();

  // Get data from Redux store
  const { currentUser: user, status } = useAppSelector((state) => state.users);
  const { roles, permissionsByCategory, scopes } = useAppSelector(
    (state) => state.rolePermission
  );
  const { teams: availableTeams } = useAppSelector((state) => state.teams);

  const [loading, setLoading] = useState(true);
  const [effectivePermissions, setEffectivePermissions] = useState<any[]>([]);

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to view users
      if (!userPermissions.canView(backendUser?.permissions ?? [])) {
        toast.error(t("permissionDeniedToastTitle"), {
          description: t("permissionDeniedToastDescription"),
        });
        router.push("/enterprise/users");
      }
    }
  }, [backendUser, backendUserLoading, router, t]); // Add 't' to dependencies

  // Helper function to determine if one scopeType is broader than another
  function isBroaderScope(scope1: string, scope2: string): boolean {
    const scopeHierarchy: Record<string, number> = {
      GLOBAL: 3,
      TEAM: 2,
      PROJECT: 1,
      SELF: 0,
    };

    return (scopeHierarchy[scope1] || 0) > (scopeHierarchy[scope2] || 0);
  }

  // Calculate effective permissions
  const calculateEffectivePermissions = useCallback(() => {
    if (!user) return [];

    const effectivePermissions = new Map<
      string,
      { permission: string; scopeType: string; id?: string; scopeId?: string }
    >();

    // Add permissions from roles
    if (user.roles) {
      user.roles.forEach((role: Role) => {
        const roleObj = roles.find((r) => r.id === role.id);
        if (roleObj?.permissions) {
          roleObj.permissions.forEach((permObj) => {
            const permission = permObj.action || permObj.scopeType;
            const scopeType = permObj.scopeType;

            if (!permission) return;

            const key = `${permission}-${scopeType}`;
            if (
              !effectivePermissions.has(key) ||
              isBroaderScope(
                scopeType,
                effectivePermissions.get(key)!.scopeType
              )
            ) {
              effectivePermissions.set(key, {
                permission,
                scopeType,
                id: permObj.id,
                scopeId: permObj.scopeId,
              });
            }
          });
        }
      });
    }

    // Add permissions from teams
    if (user.teams) {
      user.teams.forEach((team: Team) => {
        const teamObj = availableTeams.find((t) => t.id === team.id);
        if (teamObj?.permissions) {
          teamObj.permissions.forEach((permObj) => {
            const permission = permObj.action || permObj.scopeType;
            const scopeType = permObj.scopeType;

            if (!permission) return;

            const key = `${permission}-${scopeType}`;
            if (
              !effectivePermissions.has(key) ||
              isBroaderScope(
                scopeType,
                effectivePermissions.get(key)!.scopeType
              )
            ) {
              effectivePermissions.set(key, {
                permission,
                scopeType,
                id: permObj.id,
                scopeId: permObj.scopeId,
              });
            }
          });
        }
      });
    }

    // Add extra permissions
    if (user.extraPermissions) {
      user.extraPermissions.forEach((permObj: Permission) => {
        const permission = permObj.action || permObj.scopeType;
        const scopeType = permObj.scopeType;

        if (!permission) return;

        const key = `${permission}-${scopeType}`;
        if (
          !effectivePermissions.has(key) ||
          isBroaderScope(scopeType, effectivePermissions.get(key)!.scopeType)
        ) {
          effectivePermissions.set(key, {
            permission,
            scopeType,
            id: permObj.id,
          });
        }
      });
    }

    // Remove revoked permissions
    if (user.revokedPermissions) {
      user.revokedPermissions.forEach((permObj: Permission) => {
        const permission = permObj.action || permObj.scopeType;
        const scopeType = permObj.scopeType;

        if (!permission) return;

        const key = `${permission}-${scopeType}`;
        if (effectivePermissions.has(key)) {
          const currentScope = effectivePermissions.get(key)!.scopeType;
          if (
            scopeType === currentScope ||
            isBroaderScope(scopeType, currentScope)
          ) {
            effectivePermissions.delete(key);
          }
        }
      });
    }

    // Convert Map to array
    return Array.from(effectivePermissions.values());
  }, [user, roles, availableTeams]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const token = await getToken();

        if (!token) {
          toast.error(t("authenticationErrorToastTitle"), {
            description: t("authenticationErrorToastDescription"),
          });
          router.push("/sign-in");
          return;
        }

        // Fetch roles and permissions first
        await dispatch(fetchRolesPermissions(token)).unwrap();

        // Fetch teams
        await dispatch(fetchTeams(token)).unwrap();

        // Fetch user by ID
        await dispatch(
          getUserById({
            userId: params.id,
            token: token,
          })
        ).unwrap();

        console.log("User data fetched successfully");
      } catch (error) {
        console.error("Error fetching user:", error);
        toast.error(t("userNotFoundToastTitle"), {
          description: t("userNotFoundToastDescription"),
        });
        router.push("/enterprise/users");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.id, dispatch, getToken, router, t]); // Add 't' to dependencies

  // Update effective permissions when user data changes
  useEffect(() => {
    if (user) {
      const permissions = calculateEffectivePermissions();
      setEffectivePermissions(permissions);
      console.log("Effective permissions calculated:", permissions);
    }
  }, [user, calculateEffectivePermissions]);

  // Group permissions by category
  const groupedPermissions = () => {
    const result: Record<string, any[]> = {};

    if (!permissionsByCategory || !Array.isArray(permissionsByCategory)) {
      // If categories are not available, group by a default category
      result["Permissions"] = effectivePermissions;
      return result;
    }
    console.log("view", effectivePermissions);
    // First, try to categorize permissions based on permissionsByCategory
    permissionsByCategory.forEach((category) => {
      if (!category || !category.permissions) return;

      const categoryPermissions = effectivePermissions.filter((permObj) =>
        category.permissions.some(
          (p) => (p.action || p.scopeType) === permObj.permission
        )
      );

      if (categoryPermissions.length > 0) {
        result[category.category] = categoryPermissions;
      }
    });

    // Add uncategorized permissions
    const uncategorized = effectivePermissions.filter(
      (permObj) =>
        !Object.values(result)
          .flat()
          .some(
            (p) =>
              p.permission === permObj.permission &&
              p.scopeType === permObj.scopeType
          )
    );

    if (uncategorized.length > 0) {
      result[t("otherCategory")] = uncategorized;
    }

    return result;
  };

  // Check if a permission is from a role, team, or extra
  const getPermissionSource = (permObj: {
    permission: string;
    scopeType: string;
  }) => {
    if (!user) return { type: "unknown", label: t("unknownSource") };

    const { permission, scopeType } = permObj;

    // Check if it's an extra permission
    const extraPerm = user.extraPermissions?.find(
      (p) =>
        (p.action || p.scopeType) === permission && p.scopeType === scopeType
    );
    if (extraPerm) {
      return { type: "extra", label: t("extraPermissionSource") };
    }

    // Check if it's from a role
    for (const role of user.roles || []) {
      const roleObj = roles.find((r) => r.id === role.id);
      if (roleObj && roleObj.permissions) {
        const rolePerm = roleObj.permissions.find(
          (p) =>
            (p.action || p.scopeType) === permission &&
            p.scopeType === scopeType
        );
        if (rolePerm) {
          return { type: "role", label: t("fromRoleSource", { roleName: roleObj.name }) };
        }
      }
    }

    // Check if it's from a team
    for (const team of user.teams || []) {
      const teamObj = availableTeams.find((t) => t.id === team.id);
      if (teamObj && teamObj.permissions) {
        const teamPerm = teamObj.permissions.find(
          (p) =>
            (p.action || p.scopeType) === permission &&
            p.scopeType === scopeType
        );
        if (teamPerm) {
          return { type: "team", label: t("fromTeamSource", { teamName: teamObj.name }) };
        }
      }
    }

    return { type: "unknown", label: t("unknownSource") };
  };

  if (loading || status === "loading") {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t("loadingUserDetails")}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
          <h2 className="mt-2 text-xl font-semibold">{t("userNotFoundTitle")}</h2>
          <p className="mt-1 text-muted-foreground">
            {t("userNotFoundDescription")}
          </p>
          <Button
            className="mt-4"
            onClick={() => router.push("/enterprise/users")}
          >
            {t("backToUsersButton")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      <div className="flex items-center justify-between mb-6">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("backButton")}
        </Button>
        {userHasPermission(backendUser?.permissions ?? [], [
          "CAN_UPDATE_USER",
        ]) && (
          <Button onClick={() => router.push(`/enterprise/users/${params.id}`)}>
            <Edit className="mr-2 h-4 w-4" />
            {t("editUserButton")}
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User Profile Card */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <div className="h-24 w-24 rounded-full bg-primary/10 flex items-center justify-center">
                  <UserCircle className="h-12 w-12 text-primary" />
                </div>
                <div className="absolute bottom-0 right-0">
                  {user.isActive ? (
                    <Badge variant="default" className="bg-green-500">
                      {t("activeBadge")}
                    </Badge>
                  ) : (
                    <Badge
                      variant="outline"
                      className="bg-red-100 text-red-800"
                    >
                      {t("inactiveBadge")}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <CardTitle className="text-center">
              {user.firstName} {user.lastName}
            </CardTitle>
            <div className="flex justify-center mt-1">
              <Badge variant="outline" className="flex items-center">
                <Mail className="mr-1 h-3 w-3" />
                {user.email}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center">
                  <UserCircle className="mr-1 h-4 w-4 text-primary" />
                  {t("rolesHeading")}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {user.roles && user.roles.length > 0 ? (
                    user.roles.map((role) => (
                      <Badge key={role.id} variant="outline">
                        {role.name}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      {t("noRolesAssigned")}
                    </span>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center">
                  <Users className="mr-1 h-4 w-4 text-blue-500" />
                  {t("teamsHeading")}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {user.teams && user.teams.length > 0 ? (
                    user.teams.map((team) => (
                      <Badge key={team.id} variant="outline">
                        {team.name}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-muted-foreground">
                      {t("noTeamsAssigned")}
                    </span>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center">
                  <UserCircle className="mr-1 h-4 w-4 text-primary" />
                  {t("accountDetailsHeading")}
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground flex items-center">
                      <Calendar className="mr-1 h-4 w-4 text-blue-500" />
                      {t("createdLabel")}
                    </span>
                    <span>
                      {user.createdAt
                        ? new Date(user.createdAt).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground flex items-center">
                      <Shield className="mr-1 h-4 w-4 text-purple-500" />
                      {t("permissionsLabel")}
                    </span>
                    <Badge variant="outline" className="rounded-full px-2.5">
                      {user.permissionsCount}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>{t("userDetailsCardTitle")}</CardTitle>
              <CardDescription>
                {t("userDetailsCardDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="permissions">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="permissions">{t("permissionsTab")}</TabsTrigger>
                  <TabsTrigger value="overrides">
                    {t("permissionOverridesTab")}
                  </TabsTrigger>
                  <TabsTrigger value="activity">{t("activityTab")}</TabsTrigger>
                </TabsList>

                <TabsContent value="permissions" className="space-y-6 pt-4">
                  {effectivePermissions.length === 0 ? (
                    <div className="text-center py-8 border rounded-lg bg-muted/20">
                      <Shield className="mx-auto h-8 w-8 text-muted-foreground" />
                      <p className="mt-2 text-muted-foreground">
                        {t("noPermissionsAssigned")}
                      </p>
                    </div>
                  ) : (
                    Object.entries(groupedPermissions()).map(
                      ([category, permissions]) => (
                        <div key={category} className="space-y-2">
                          <h3 className="font-medium">{category}</h3>
                          <Separator />
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-2">
                            {permissions.map((permObj, index) => {
                              const source = getPermissionSource(permObj);
                              const permKey = `${
                                permObj.permission || "unknown"
                              }-${permObj.scopeType || "unknown"}-${index}`;
                              return (
                                <div
                                  key={permKey}
                                  className="flex items-center space-x-2 p-2 rounded-md bg-muted/50"
                                >
                                  <Shield
                                    className={`h-4 w-4 ${
                                      source.type === "extra"
                                        ? "text-green-500"
                                        : source.type === "role"
                                        ? "text-primary"
                                        : "text-blue-500"
                                    }`}
                                  />
                                  <div className="flex-1 min-w-0">
                                    <span className="text-sm">
                                      {formatPermissionName(permObj.permission)}
                                    </span>
                                    <div className="flex items-center mt-1">
                                      <div
                                        className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getScopeBadgeColor(
                                          permObj.scopeType
                                        )}`}
                                      >
                                        {getScopeIcon(permObj.scopeType)}
                                        <span className="ml-1">
                                          {getScopeLabel(permObj.scopeType)}
                                        </span>
                                      </div>
                                    </div>
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {source.label}
                                    </p>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )
                    )
                  )}
                </TabsContent>

                <TabsContent value="overrides" className="pt-4">
                  <div className="space-y-6">
                    {/* Extra Permissions */}
                    <div className="space-y-2">
                      <h3 className="font-medium flex items-center">
                        <Plus className="h-4 w-4 mr-2 text-green-500" />
                        {t("extraPermissionsHeading")}
                      </h3>
                      <Separator />
                      {!user.extraPermissions ||
                      user.extraPermissions.length === 0 ? (
                        <div className="text-center py-4 border rounded-lg bg-muted/20">
                          <p className="text-muted-foreground">
                            {t("noExtraPermissions")}
                          </p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-2">
                          {user.extraPermissions.map((permObj, index) => {
                            const permission =
                              permObj.action || permObj.scopeType;
                            const scopeType = permObj.scopeType;
                            return (
                              <div
                                key={`extra-${permission}-${scopeType}-${index}`}
                                className="flex items-center space-x-2 p-2 rounded-md bg-green-50 border border-green-100"
                              >
                                <Plus className="h-4 w-4 text-green-500" />
                                <div>
                                  <span className="text-sm">
                                    {formatPermissionName(permission)}
                                  </span>
                                  <div className="flex items-center mt-1">
                                    <div
                                      className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getScopeBadgeColor(
                                        scopeType
                                      )}`}
                                    >
                                      {getScopeIcon(scopeType)}
                                      <span className="ml-1">
                                        {getScopeLabel(scopeType)}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>

                    {/* Revoked Permissions */}
                    <div className="space-y-2">
                      <h3 className="font-medium flex items-center">
                        <Minus className="h-4 w-4 mr-2 text-destructive" />
                        {t("revokedPermissionsHeading")}
                      </h3>
                      <Separator />
                      {!user.revokedPermissions ||
                      user.revokedPermissions.length === 0 ? (
                        <div className="text-center py-4 border rounded-lg bg-muted/20">
                          <p className="text-muted-foreground">
                            {t("noRevokedPermissions")}
                          </p>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-2">
                          {user.revokedPermissions.map((permObj, index) => {
                            const permission =
                              permObj.action || permObj.scopeType;
                            const scopeType = permObj.scopeType;
                            return (
                              <div
                                key={`revoked-${permission}-${scopeType}-${index}`}
                                className="flex items-center space-x-2 p-2 rounded-md bg-red-50 border border-red-100"
                              >
                                <Minus className="h-4 w-4 text-destructive" />
                                <div>
                                  <span className="text-sm">
                                    {formatPermissionName(permission)}
                                  </span>
                                  <div className="flex items-center mt-1">
                                    <div
                                      className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getScopeBadgeColor(
                                        scopeType
                                      )}`}
                                    >
                                      {getScopeIcon(scopeType)}
                                      <span className="ml-1">
                                        {getScopeLabel(scopeType)}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="activity" className="pt-4">
                  <div className="text-center py-8 border rounded-lg bg-muted/20">
                    <Calendar className="mx-auto h-8 w-8 text-muted-foreground" />
                    <p className="mt-2 text-muted-foreground">
                      {t("activityNotEnabled")}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {t("enableActivityTracking")}
                    </p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}