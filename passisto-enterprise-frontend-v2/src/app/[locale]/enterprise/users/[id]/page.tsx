"use client";

import { useState, useEffect, useCallback } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchRolesPermissions,
  Role,
  Permission,
  PermissionWithSource,
} from "@/store/slices/rolePermissionSlice";
import { fetchTeams } from "@/store/slices/teamSlice";
import { useAuth } from "@clerk/nextjs";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, Shield, Plus, Info } from "lucide-react";
import { updateUser, getUserById, User, Team } from "@/store/slices/userSlice";

import {
  formatPermissionName,
  getScopeIcon,
  getScopeBadgeColor,
  getScopeLabel,
  groupInheritedPermissionsByCategory,
  getValidScopesForPermission,
  computeInheritedPermissions,
  computeEffectivePermissions,
} from "../_lib/utils";
import { renderScopeSelector } from "../_lib/scope-selectors";
import { useBackendUser } from "@/hooks/useBackendUser";
import { userPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useTranslations } from "next-intl";

export default function NewUserPage() {
  const t = useTranslations("EditUserPage");
  const params = useParams<{ id: string }>();
  const userId = params?.id;
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const [teamSearch, setTeamSearch] = useState("");

  // Get roles and permissions from Redux store
  const {
    roles,
    permissionsByCategory,
    scopes,
    loading: rolesLoading,
  } = useAppSelector((state) => state.rolePermission);

  // Get teams from Redux store
  const {
    teams: availableTeams,
    status: teamsStatus,
    error: teamsError,
  } = useAppSelector((state) => state.teams);

  const [formData, setFormData] = useState<User>({
    id: userId,
    firstName: "",
    lastName: "",
    email: "",
    roles: [],
    teams: [],
    extraPermissions: [],
    revokedPermissions: [],
    permissionsCount: null,
    isActive: null,
    createdAt: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inheritedPermissions, setInheritedPermissions] = useState<
    PermissionWithSource[]
  >([]);
  const [effectivePermissions, setEffectivePermissions] = useState<
    Permission[]
  >([]);
  // Ajoutez cet état pour gérer les erreurs du formulaire
  const [formErrors, setFormErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    roles?: string;
  }>({});

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to create teams
      if (!userPermissions.canUpdate(backendUser?.permissions ?? [])) {
        toast.error("Permission denied", {
          description: "You don't have permission to create teams.",
        });
        router.push("/enterprise/users");
      }
    }
  }, [backendUser, backendUserLoading, router]);
  // Fetch roles, permissions and teams on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken();
        await dispatch(fetchRolesPermissions(token!)).unwrap();
        await dispatch(fetchTeams(token!)).unwrap();
        const userData = await dispatch(
          getUserById({ userId, token: token! })
        ).unwrap();
        
        setFormData({
          id: userId,
          firstName: userData.firstName || "",
          lastName: userData.lastName || "",
          email: userData.email || "",
          roles: userData.roles || [],
          teams: userData.teams || [],
          extraPermissions: userData.extraPermissions || [],
          revokedPermissions: userData.revokedPermissions || [],
          permissionsCount: userData.permissionsCount,
          isActive: userData.isActive,
          createdAt: userData.createdAt,
        });
        console.log(permissionsByCategory);
      } catch (err) {
        console.error("Error fetching data:", err);
        toast.error(t("errors.loadData"));
      }
    };

    fetchData();
  }, [dispatch, getToken, t, userId]);

  useEffect(() => {
    setEffectivePermissions(calculateEffectivePermissions());
    setInheritedPermissions(calculateInheritedPermissions());
  }, [
    formData?.roles,
    formData?.teams,
    formData?.extraPermissions,
    formData?.revokedPermissions,
    roles,
    availableTeams,
  ]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleRolesChange = (selectedRoles: Role[]) => {
    setFormData((prev) => ({
      ...prev,
      roles: selectedRoles,
    }));
  };

  const handleExtraPermissionChange = (
    id: string,
    permission: string,
    scopeType: string,
    scopeId: string
  ) => {
    if (
      formData?.extraPermissions.some(
        (p) =>
          p.action === permission &&
          p.scopeType === scopeType &&
          (scopeId === null || p.scopeId === scopeId)
      )
    ) {
      setFormData((prev) => ({
        ...prev,
        extraPermissions: prev.extraPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    } else {
      const newPermission: Permission = {
        id: id,
        action: permission,
        scopeType,
        scopeId: scopeId!,
      };

      setFormData((prev) => ({
        ...prev,
        extraPermissions: [...prev.extraPermissions, newPermission],
        revokedPermissions: prev.revokedPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    }
  };

  const handleRevokedPermissionChange = (
    permission: string,
    scopeType: string,
    scopeId: string
  ) => {
    if (
      formData?.revokedPermissions.some(
        (p) =>
          p.action === permission &&
          p.scopeType === scopeType &&
          (scopeId === null || p.scopeId === scopeId)
      )
    ) {
      setFormData((prev) => ({
        ...prev,
        revokedPermissions: prev.revokedPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    } else {
      const permissionObj = permissionsByCategory
        .flatMap((category) => category.permissions)
        .find((p) => p.action === permission);

      const newRevocation: Permission = {
        id: permissionObj?.id!,
        action: permission,
        scopeType: scopeType,
        scopeId: scopeId!,
      };

      setFormData((prev) => ({
        ...prev,
        revokedPermissions: [...prev.revokedPermissions, newRevocation],
        extraPermissions: prev.extraPermissions.filter(
          (p) =>
            !(
              p.action === permission &&
              p.scopeType === scopeType &&
              (scopeId === null || p.scopeId === scopeId)
            )
        ),
      }));
    }
  };

  // Calculate permissions inherited from roles and teams
  const calculateInheritedPermissions =
    useCallback((): PermissionWithSource[] => {
      return computeInheritedPermissions(roles, availableTeams, formData);
    }, [roles, formData?.roles, availableTeams, formData?.teams]);

  const calculateEffectivePermissions = useCallback((): Permission[] => {
    return computeEffectivePermissions(
      roles,
      availableTeams,
      formData,
      permissionsByCategory
    );
  }, [
    formData,
    roles,
    formData?.roles,
    availableTeams,
    formData?.teams,
    permissionsByCategory,
  ]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Réinitialiser les erreurs
    setFormErrors({});

    // Vérifier chaque champ individuellement
    const errors: any = {};

    if (!formData.firstName?.trim()) {
      errors.firstName = t("formErrors.firstName");
    }

    if (!formData.lastName?.trim()) {
      errors.lastName = t("formErrors.lastName");
    }

    if (!formData.email?.trim()) {
      errors.email = t("formErrors.emailRequired");
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = t("formErrors.emailInvalid");
    }

    if (!formData.roles || formData.roles.length === 0) {
      errors.roles = t("formErrors.roles");
    }

    // Vérifier si des erreurs existent
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Format the data according to the required structure
      console.log(formData.extraPermissions);
      console.log(formData.revokedPermissions);
      const userData = {
        email: formData?.email,
        firstName: formData?.firstName,
        lastName: formData?.lastName,
        groups: formData?.teams.map((team) => team.id),
        roles: formData?.roles.map((role) => role.id),
        extraPermissions: formData?.extraPermissions.map((perm) => {
          let permissionId = perm.id;
          if (!permissionId) {
            const permissionObj = permissionsByCategory
              .flatMap((category) => category.permissions)
              .find((p) => p.action === perm.action);
            permissionId = permissionObj?.id || perm.action;
          }

          let scopeId = "global";
          if (perm.scopeType === "TEAM" && perm.scopeId) {
            scopeId = `team-id:${perm.scopeId.replace("team-id:", "")}`; // Use ID directly, don't replace
          } else if (perm.scopeType === "PROJECT" && perm.scopeId) {
            scopeId = `project-id:${perm.scopeId}`;
          }

          return {
            permissionId: permissionId,
            scopeType: perm.scopeType,
            scopeId: scopeId,
          };
        }),
        revokedPermissions: formData?.revokedPermissions.map((perm) => {
          let permissionId = perm.id;
          if (!permissionId) {
            const permissionObj = permissionsByCategory
              .flatMap((category) => category.permissions)
              .find((p) => p.action === perm.action);
            permissionId = permissionObj?.id || perm.action;
          }

          let scopeId = "global";
          if (perm.scopeType === "TEAM" && perm.scopeId) {
            scopeId = `team-id:${perm.scopeId?.replace("team-id:", "")}`;
          } else if (perm.scopeType === "PROJECT" && perm.scopeId) {
            scopeId = `project-id:${perm.scopeId}`;
          }

          return {
            permissionId: permissionId,
            scopeType: perm.scopeType,
            scopeId: scopeId,
          };
        }),
      };

      const token = await getToken();
      await dispatch(updateUser({ userData, token: token!, userId })).unwrap();

      toast.success(t("successMessage"));
      router.push("/enterprise/users");
    } catch (error: any) {
      console.error("Error updating user:", error);

      // Check for user already exists error
      if (error.response?.status === 409) {
        // Définir une erreur spécifique pour l'email existant
        setFormErrors({ email: `A user with this email already exists.` });
      } else if (!/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(formData.email)) {
        // Vérifie si l'email est au bon format
        setFormErrors({ email: "Please enter a valid email address." });
      } else {
        setFormErrors({ email: `A user with this email already exists.` });
        // Pour les autres erreurs
        toast.error("Failed to update user");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full p-4">
      <Card>
        <CardHeader>
          <CardTitle>{t("title")}</CardTitle>
          <CardDescription>
            {t("description")}
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {Object.keys(formErrors).length > 0 && (
              <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md mb-4 border border-destructive/30">
                <h4 className="font-medium mb-1">
                  {formErrors.email &&
                  formErrors.email.includes("already exists")
                    ? "The user already exists"
                    : "Please fill in the following fields:"}
                </h4>
                <ul className="list-disc list-inside text-sm">
                  {formErrors.firstName && <li>{formErrors.firstName}</li>}
                  {formErrors.lastName && <li>{formErrors.lastName}</li>}
                  {formErrors.email && <li>{formErrors.email}</li>}
                  {formErrors.roles && <li>{formErrors.roles}</li>}
                </ul>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t("formLabels.firstName")}</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  placeholder={t("placeholders.firstName")}
                  value={formData?.firstName}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">{t("formLabels.lastName")}</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  placeholder={t("placeholders.lastName")}
                  value={formData?.lastName}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t("formLabels.email")}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder={t("placeholders.email")}
                value={formData?.email}
                onChange={handleChange}
              />
            </div>

            <Tabs defaultValue="roles" className="mt-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="roles">{t("tabs.roles")}</TabsTrigger>
                <TabsTrigger value="teams">{t("tabs.teams")}</TabsTrigger>
                <TabsTrigger value="permissions">
                  {t("tabs.permissions")}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="roles" className="pt-4">
                <div className="space-y-2">
                  <Label>{t("roles.title")}</Label>
                  {rolesLoading ? (
                    <div className="text-center py-4">{t("loading.roles")}</div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {roles.map((role) => (
                        <div
                          key={role.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`role-${role.id}`}
                            checked={formData?.roles.some(
                              (r) => r.id === role.id
                            )}
                            onCheckedChange={() => {
                              const newRoles = formData?.roles.some(
                                (r) => r.id === role.id
                              )
                                ? formData?.roles.filter(
                                    (r) => r.id !== role.id
                                  )
                                : [...formData?.roles, role];
                              handleRolesChange(newRoles);
                            }}
                          />
                          <label
                            htmlFor={`role-${role.id}`}
                            className="text-sm"
                          >
                            {role.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="teams" className="pt-4">
                <div className="space-y-2">
                  <Label>Teams</Label>
                  {/* Barre de recherche */}
                  <Input
                    type="text"
                    placeholder="Search teams..."
                    value={teamSearch}
                    onChange={(e) => setTeamSearch(e.target.value)}
                    className="mb-2"
                  />
                  {/* Liste dynamique sous la barre de recherche */}
                  {teamSearch && (
                    <div className="mb-2 border rounded bg-white shadow max-h-40 overflow-auto">
                      {availableTeams
                        .filter((team) =>
                          team.name
                            .toLowerCase()
                            .includes(teamSearch.toLowerCase())
                        )
                        .map((team) => (
                          <div
                            key={team.id}
                            className="flex items-center px-3 py-2 hover:bg-muted cursor-pointer"
                            onClick={() => {
                              if (team.name === "Company") return;
                              const isSelected = formData?.teams.some(
                                (t) => t.id === team.id
                              );
                              const newTeams = isSelected
                                ? formData?.teams.filter(
                                    (t) => t.id !== team.id
                                  )
                                : [
                                    ...formData?.teams,
                                    {
                                      ...team,
                                      permissions: team.permissions ?? [],
                                    } as Team,
                                  ];
                              setFormData((prev) => ({
                                ...prev,
                                teams: newTeams,
                              }));
                              setTeamSearch(""); // Optionnel : vide la recherche après sélection
                            }}
                          >
                            <Checkbox
                              id={`team-search-${team.id}`}
                              checked={
                                team.name === "Company" ||
                                formData?.teams.some(
                                  (t) => t.name === team.name
                                )
                              }
                              disabled={team.name === "Company"}
                              className="mr-2"
                              tabIndex={-1}
                              onChange={() => {}} // Pour éviter le warning React
                            />
                            <span className="text-sm">
                              {team.name}
                              {team.name === "Company" && " (Required)"}
                              {team.memberCount !== undefined &&
                                team.memberCount > 0 && (
                                  <span className="ml-1 text-xs text-gray-500">
                                    ({team.memberCount}{" "}
                                    {team.memberCount === 1
                                      ? "member"
                                      : "members"}
                                    )
                                  </span>
                                )}
                            </span>
                          </div>
                        ))}
                      {/* Si aucun résultat */}
                      {availableTeams.filter((team) =>
                        team.name
                          .toLowerCase()
                          .includes(teamSearch.toLowerCase())
                      ).length === 0 && (
                        <div className="px-3 py-2 text-xs text-muted-foreground">
                          No team found.
                        </div>
                      )}
                    </div>
                  )}
                  {/* Liste classique des teams */}
                  {teamsStatus === "loading" ? (
                    <div className="text-center py-4">{t("loading.teams")}</div>
                  ) : teamsStatus === "failed" ? (
                    <div className="text-center py-4 text-red-500">
                      {t("errors.teamsLoadFailed")}: {teamsError}
                    </div>
                  ) : availableTeams.length === 0 ? (
                    <div className="text-center py-4">{t("teams.noTeams")}</div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {availableTeams.map((team) => (
                        <div
                          key={team.id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`team-${team.id}`}
                            checked={
                              team.name === "Company" ||
                              formData?.teams.some((t) => t.name === team.name)
                            }
                            disabled={team.name === "Company"}
                            onCheckedChange={() => {
                              if (team.name === "Company") return;
                              const isSelected = formData?.teams.some(
                                (t) => t.id === team.id
                              );
                              const newTeams = isSelected
                                ? formData?.teams.filter(
                                    (t) => t.id !== team.id
                                  )
                                : [
                                    ...formData?.teams,
                                    {
                                      ...team,
                                      permissions: team.permissions ?? [],
                                    } as Team,
                                  ];
                              setFormData((prev) => ({
                                ...prev,
                                teams: newTeams,
                              }));
                            }}
                          />
                          <label
                            htmlFor={`team-${team.id}`}
                            className={`text-sm ${
                              team.name === "Company" ? "font-medium" : ""
                            }`}
                          >
                            {team.name}
                            {team.name === "Company" && ` (${t("teams.required")})`}
                            {team.memberCount !== undefined &&
                              team.memberCount > 0 && (
                                <span className="ml-1 text-xs text-gray-500">
                                  ({team.memberCount}{" "}
                                  {team.memberCount === 1
                                    ? t("teams.member")
                                    : t("teams.members")}
                                  )
                                </span>
                              )}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="permissions" className="pt-4">
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-2">
                      <Info className="h-4 w-4 mr-2 text-blue-500" />
                      <h3 className="text-sm font-medium">
                        {t("permissions.inheritedTitle")}
                      </h3>
                    </div>
                    <p className="text-xs text-muted-foreground mb-4">
                      {t("permissions.inheritedDescription")}
                    </p>

                    {inheritedPermissions.length === 0 ? (
                      <div className="text-center py-4 border rounded-lg bg-muted/20">
                        <p className="text-muted-foreground">
                          {t("permissions.noInherited")}
                        </p>
                      </div>
                    ) : (
                      Object.entries(
                        groupInheritedPermissionsByCategory(
                          inheritedPermissions,
                          permissionsByCategory
                        )
                      ).map(([category, permissions]) => (
                        <div key={category} className="mb-4">
                          <h4 className="text-sm font-medium mb-2">
                            {category}
                          </h4>
                          <div className="grid grid-cols-1 gap-2 border rounded-md p-3">
                            {permissions.map((permObj) => (
                              <div
                                key={`${permObj.action}-${permObj.scopeType}`}
                                className="flex items-center justify-between"
                              >
                                <div className="flex items-center">
                                  <Checkbox
                                    id={`inherited-${permObj.action}-${permObj.scopeType}`}
                                    checked={true}
                                    disabled={true}
                                  />
                                  <label
                                    htmlFor={`inherited-${permObj.action}-${permObj.scopeType}`}
                                    className="text-sm ml-2"
                                  >
                                    {formatPermissionName(permObj.action)}
                                  </label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div
                                    className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getScopeBadgeColor(
                                      permObj.scopeType
                                    )}`}
                                  >
                                    {getScopeIcon(permObj.scopeType)}
                                    <span className="ml-1">
                                      {getScopeLabel(permObj.scopeType)}
                                    </span>
                                  </div>
                                  <Badge variant="outline" className="text-xs">
                                    {permObj.source.type === "role"
                                      ? "Role: "
                                      : "Team: "}
                                    {permObj.source.name}
                                  </Badge>
                                  <Checkbox
                                    id={`revoke-${permObj.action}-${permObj.scopeType}`}
                                    checked={formData?.revokedPermissions.some(
                                      (p) =>
                                        p.action === permObj.action &&
                                        p.scopeType === permObj.scopeType
                                    )}
                                    onCheckedChange={() =>
                                      handleRevokedPermissionChange(
                                        permObj.action,
                                        permObj.scopeType,
                                        permObj.scopeId!
                                      )
                                    }
                                  />
                                  <label
                                    htmlFor={`revoke-${permObj.action}-${permObj.scopeType}`}
                                    className="text-xs text-destructive"
                                  >
                                    Revoke
                                  </label>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-sm font-medium flex items-center mb-2">
                      <Plus className="h-4 w-4 mr-2 text-green-500" />
                      {t("permissions.extraTitle")}
                    </h3>
                    <p className="text-xs text-muted-foreground mb-4">
                      {t("permissions.extraDescription")}
                    </p>

                    {rolesLoading ? (
                      <div className="text-center py-4">
                        {t("loading.permissions")}
                      </div>
                    ) : (
                      <Accordion type="multiple" className="w-full">
                        {permissionsByCategory.map((category) => {
                          const availablePermissions = category.permissions;
                          if (availablePermissions.length === 0) return null;

                          return (
                            <AccordionItem
                              key={category.category}
                              value={category.category}
                            >
                              <AccordionTrigger>
                                <h4 className="text-sm font-medium">
                                  {category.category}
                                </h4>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-1 gap-2 border rounded-md p-3">
                                  {availablePermissions.map((permission) => {
                                    const validScopes =
                                      getValidScopesForPermission(
                                        permission.action
                                      );

                                    return (
                                      <div
                                        key={permission.id}
                                        className="flex flex-col space-y-2"
                                      >
                                        <div className="flex items-center">
                                          <label className="text-sm font-medium">
                                            {formatPermissionName(
                                              permission.action
                                            )}
                                          </label>
                                        </div>
                                        <div className="space-y-4">
                                          {scopes
                                            .filter((scopeType) =>
                                              validScopes.includes(scopeType)
                                            )
                                            .map((scopeType) => {
                                              const needsTarget =
                                                scopeType === "TEAM" ||
                                                scopeType === "PROJECT";
                                              if (needsTarget) {
                                                return (
                                                  <Popover
                                                    key={`${permission.action}-${scopeType}`}
                                                  >
                                                    <div className="flex items-center space-x-2 ml-4">
                                                      <Checkbox
                                                        id={`extra-${permission.action}-${scopeType}`}
                                                        checked={formData?.extraPermissions.some(
                                                          (p) =>
                                                            p.action ===
                                                              permission.action &&
                                                            p.scopeType ===
                                                              scopeType
                                                        )}
                                                        onCheckedChange={() => {
                                                          if (
                                                            formData?.extraPermissions.some(
                                                              (p) =>
                                                                p.action ===
                                                                  permission.action &&
                                                                p.scopeType ===
                                                                  scopeType
                                                            )
                                                          ) {
                                                            setFormData(
                                                              (prev) => ({
                                                                ...prev,
                                                                extraPermissions:
                                                                  prev.extraPermissions.filter(
                                                                    (p) =>
                                                                      !(
                                                                        p.action ===
                                                                          permission.action &&
                                                                        p.scopeType ===
                                                                          scopeType
                                                                      )
                                                                  ),
                                                              })
                                                            );
                                                          } else {
                                                            if (
                                                              scopeType ===
                                                                "TEAM" &&
                                                              formData?.teams
                                                                .length === 0
                                                            ) {
                                                              toast.error(
                                                                "No teams selected",
                                                                {
                                                                  description:
                                                                    "Please select teams in the Teams tab first.",
                                                                }
                                                              );
                                                              return;
                                                            }
                                                            if (
                                                              scopeType ===
                                                                "TEAM" &&
                                                              formData?.teams
                                                                .length > 0
                                                            ) {
                                                              handleExtraPermissionChange(
                                                                permission.id,
                                                                permission.action,
                                                                scopeType,
                                                                formData
                                                                  ?.teams[0].id
                                                              );
                                                            } else {
                                                              handleExtraPermissionChange(
                                                                permission.id,
                                                                permission.action,
                                                                scopeType,
                                                                permission.scopeId!
                                                              );
                                                            }
                                                          }
                                                        }}
                                                      />
                                                      <div className="flex items-center">
                                                        {getScopeIcon(
                                                          scopeType
                                                        )}
                                                        <label
                                                          htmlFor={`extra-${permission.action}-${scopeType}`}
                                                          className="text-xs ml-1"
                                                        >
                                                          {getScopeLabel(
                                                            scopeType
                                                          )}
                                                        </label>
                                                      </div>
                                                      <PopoverTrigger asChild>
                                                        <Button
                                                          variant="outline"
                                                          size="sm"
                                                          className="h-6 px-2 text-xs"
                                                          disabled={
                                                            scopeType ===
                                                              "TEAM" &&
                                                            formData?.teams
                                                              .length === 0
                                                          }
                                                        >
                                                          Select{" "}
                                                          {scopeType === "TEAM"
                                                            ? "Teams"
                                                            : "Projects"}
                                                          <ChevronDown className="ml-1 h-3 w-3" />
                                                        </Button>
                                                      </PopoverTrigger>
                                                    </div>
                                                    <PopoverContent className="w-64 p-2">
                                                      {renderScopeSelector(
                                                        permission.action,
                                                        {
                                                          id: scopeType,
                                                          name: getScopeLabel(
                                                            scopeType
                                                          ),
                                                        },
                                                        formData,
                                                        permissionsByCategory,
                                                        setFormData
                                                      )}
                                                    </PopoverContent>
                                                  </Popover>
                                                );
                                              }
                                              // For global and self scopes
                                              return (
                                                <div
                                                  key={`${permission.action}-${scopeType}`}
                                                  className="flex items-center space-x-2 ml-4"
                                                >
                                                  <Checkbox
                                                    id={`extra-${permission.action}-${scopeType}`}
                                                    checked={formData?.extraPermissions.some(
                                                      (p) =>
                                                        p.action ===
                                                          permission.action &&
                                                        p.scopeType ===
                                                          scopeType
                                                    )}
                                                    onCheckedChange={(
                                                      checked
                                                    ) => {
                                                      if (checked) {
                                                        const permissionObj =
                                                          permissionsByCategory
                                                            .flatMap(
                                                              (category) =>
                                                                category.permissions
                                                            )
                                                            .find(
                                                              (p) =>
                                                                p.action ===
                                                                permission.action
                                                            );

                                                        const newPermission = {
                                                          id: permissionObj?.id!,
                                                          action:
                                                            permission.action,
                                                          scopeType: scopeType,
                                                        };

                                                        setFormData((prev) => ({
                                                          ...prev,
                                                          extraPermissions: [
                                                            ...prev.extraPermissions,
                                                            newPermission,
                                                          ],
                                                          revokedPermissions:
                                                            prev.revokedPermissions.filter(
                                                              (p) =>
                                                                !(
                                                                  p.action ===
                                                                    permission.action &&
                                                                  p.scopeType ===
                                                                    scopeType
                                                                )
                                                            ),
                                                        }));
                                                      } else {
                                                        setFormData((prev) => ({
                                                          ...prev,
                                                          extraPermissions:
                                                            prev.extraPermissions.filter(
                                                              (p) =>
                                                                !(
                                                                  p.action ===
                                                                    permission.action &&
                                                                  p.scopeType ===
                                                                    scopeType
                                                                )
                                                            ),
                                                        }));
                                                      }
                                                    }}
                                                  />
                                                  <div className="flex items-center">
                                                    {getScopeIcon(scopeType)}
                                                    <label
                                                      htmlFor={`extra-${permission.action}-${scopeType}`}
                                                      className="text-xs ml-1"
                                                    >
                                                      {getScopeLabel(scopeType)}
                                                    </label>
                                                  </div>
                                                </div>
                                              );
                                            })}
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          );
                        })}
                      </Accordion>
                    )}
                  </div>

                  <Separator />

                  <div className="mt-4 p-4 border rounded-md bg-muted/20">
                    <h3 className="text-sm font-medium mb-2 flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-primary" />
                      {t("permissions.effectiveTitle")}
                    </h3>
                    <p className="text-xs text-muted-foreground mb-3">
                      {t("permissions.effectiveDescription")}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {effectivePermissions.length > 0 ? (
                        effectivePermissions.map((permObj) => (
                          <div
                            key={`${permObj.action}-${permObj.scopeType}`}
                            className="flex items-center space-x-1 border rounded-md px-2 py-1 text-xs"
                          >
                            {getScopeIcon(permObj.scopeType)}
                            <span>{formatPermissionName(permObj.action)}</span>
                          </div>
                        ))
                      ) : (
                        <span className="text-sm text-muted-foreground">
                          {t("permissions.noEffective")}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/enterprise/users")}
            >
              {t("buttons.cancel")}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? t("buttons.saving") : t("buttons.save")}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}