//utils.ts

import {
  PermissionCategory,
  PermissionWithSource,
  Role,
} from "@/store/slices/rolePermissionSlice";
import { Team } from "@/store/slices/teamSlice";
import { User } from "@/store/slices/userSlice";
import { format } from "date-fns";
import {
  Shield,
  Globe,
  Users,
  UserIcon,
} from "lucide-react";

export const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case "ADMIN":
      return "default";
    case "MANAGER":
      return "secondary";
    case "MEMBER":
      return "outline";
    case "GUEST":
      return "destructive";
    default:
      return "outline";
  }
};

export const getRoleLabel = (role: string) => {
  if (!role) return "";

  const lower = role.toLowerCase();
  return lower.charAt(0).toUpperCase() + lower.slice(1);
};

export const formatDate = (dateString: any) => {
  try {
    return format(new Date(dateString), "MMM d, yyyy");
  } catch (error) {
    return "Invalid date";
  }
};

// Helper function to determine if one scopeType is broader than another
export const isBroaderScope = (scope1: string, scope2: string): boolean => {
  const scopeHierarchy: Record<string, number> = {
    GLOBAL: 3,
    TEAM: 2,
    PROJECT: 1,
    SELF: 0,
  };

  return (scopeHierarchy[scope1] || 0) > (scopeHierarchy[scope2] || 0);
};

// Format permission name for display
export const formatPermissionName = (permission: string): string => {
  return permission
    ?.replace("CAN_", "")
    .split("_")
    .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
    .join(" ");
};

// Get scopeType icon based on scopeType
export const getScopeIcon = (scopeType: string) => {
  switch (scopeType) {
    case "GLOBAL":
      return <Globe className="h-4 w-4 text-blue-500" />;
    case "TEAM":
      return <Users className="h-4 w-4 text-green-500" />;
    case "SELF":
      return <UserIcon className="h-4 w-4 text-amber-500" />;
    case "PROJECT":
      return <Shield className="h-4 w-4 text-purple-500" />;
    default:
      return <Shield className="h-4 w-4" />;
  }
};

// Get scopeType badge color based on scopeType
export const getScopeBadgeColor = (scopeType: string): string => {
  switch (scopeType) {
    case "GLOBAL":
      return "bg-blue-50 text-blue-700 border-blue-200";
    case "TEAM":
      return "bg-green-50 text-green-700 border-green-200";
    case "SELF":
      return "bg-amber-50 text-amber-700 border-amber-200";
    case "PROJECT":
      return "bg-purple-50 text-purple-700 border-purple-200";
    default:
      return "";
  }
};

// Get scopeType label based on scopeType
export const getScopeLabel = (scopeType: string): string => {
  const scopeLabels: Record<string, string> = {
    GLOBAL: "Global",
    TEAM: "Team-specific",
    PROJECT: "Project-specific",
    SELF: "Self only",
  };
  return scopeLabels[scopeType] || scopeType;
};

// Group inherited permissions by category
export const groupInheritedPermissionsByCategory = (
  inheritedPermissions: PermissionWithSource[],
  permissionsByCategory: PermissionCategory[]
) => {
  const grouped: Record<string, PermissionWithSource[]> = {};
  inheritedPermissions.forEach((permission) => {
    // Find the category for this permission
    let category = "Uncategorized";

    for (const cat of permissionsByCategory) {
      if (cat.permissions.some((p) => p.action === permission.action)) {
        category = cat.category;
        break;
      }
    }

    if (!grouped[category]) {
      grouped[category] = [];
    }

    grouped[category].push(permission);
  });
  // console.log("grouped", grouped);
  return grouped;
};

//Get valid scopes for a permission
export const getValidScopesForPermission = (permission: string): string[] => {
  return ["GLOBAL", "TEAM", "PROJECT", "SELF"];
};

export const computeInheritedPermissions = (
  roles: Role[],
  availableTeams: Team[],
  formData: User
) => {
  const permissions: PermissionWithSource[] = [];

  // Add permissions from roles
  roles.forEach((role: Role) => {
    const isSelected = formData?.roles?.some((r) => r.id === role.id);
    if (!isSelected || !role.permissions) return;
    if (role.permissions) {
      role.permissions.forEach((permObj) => {
        permissions.push({
          id: permObj.id,
          action: permObj.action,
          scopeType: permObj.scopeType,
          scopeId: permObj.scopeId, // always include
          source: { type: "role", name: role.name },
          category: permObj.category,
        });
      });
    }
  });

  // Add permissions from teams
  availableTeams.forEach((team) => {
    const isSelected = formData.teams.some((t) => t.id === team.id);
    // console.log(team);
    if (!isSelected || !team.permissions) return;
    if (team.permissions) {
      team.permissions.forEach((permObj) => {
        if (true) {
          permissions.push({
            id: permObj.id,
            action: permObj.action,
            scopeType: permObj.scopeType,
            scopeId: permObj.scopeId, // always included
            source: { type: "team", name: team.name },
            category: permObj.category,
          });
        }
      });
    }
  });
  return permissions;
};

export const computeEffectivePermissions = (roles: Role[], availableTeams: Team[], formData: User, permissionsByCategory:PermissionCategory[]) => {
   const effectivePermissions = new Map< string, { scopeType: string; id?: string; scopeId?: string } >();

    // Add permissions from roles

    roles.forEach((role) => {
      const isSelected = formData?.roles?.some((r) => r.id === role.id);
      if (!isSelected || !role.permissions) return;
      if (role.permissions) {
        role.permissions.forEach((permObj) => {
          const permission = permObj.action;
          const scopeType = permObj.scopeType;

          if (
            !effectivePermissions.has(permission) ||
            isBroaderScope(
              scopeType,
              effectivePermissions.get(permission)!.scopeType
            )
          ) {
            effectivePermissions.set(permission, {
              scopeType,
              id: permObj.id,
              scopeId: permObj.scopeId,
            });
          }
        });
      }
    });
    // console.log("effectivePermissions roles", effectivePermissions);

    // Add permissions from teams
    availableTeams.forEach((team) => {
      const isSelected = formData.teams.some((t) => t.id === team.id);
      // console.log(team);
      if (!isSelected || !team.permissions) return;
      if (team.permissions) {
        team.permissions.forEach((permObj) => {
          const permission = permObj.action;
          const scopeType = permObj.scopeType;
          // Only add if the permission doesn't exist or the scopeType is broader
          if (
            !effectivePermissions.has(permission) ||
            isBroaderScope(
              scopeType,
              effectivePermissions.get(permission)!.scopeType
            )
          ) {
            effectivePermissions.set(permission, {
              scopeType,
              id: permObj.id,
              scopeId: permObj.scopeId,
            });
          }
        });
      }
    });
    // console.log("effectivePermissions teams", effectivePermissions);

    // Add extra permissions
    formData?.extraPermissions.forEach((permObj) => {
      const { action, scopeType, id } = permObj;
      // Only add if the permission doesn't exist or the scopeType is broader
      if (
        !effectivePermissions.has(action) ||
        isBroaderScope(scopeType, effectivePermissions.get(action)!.scopeType)
      ) {
        effectivePermissions.set(action, {
          scopeType,
          id,
          scopeId: permObj.scopeId,
        });
      }
    });
    // console.log("effectivePermissions extra", effectivePermissions);

    // Remove revoked permissions
    formData?.revokedPermissions.forEach((permObj) => {
      const { action, scopeType } = permObj;
      // If the permission exists and the scopeType matches or is broader, remove it
      if (effectivePermissions.has(action)) {
        const currentScope = effectivePermissions.get(action)!.scopeType;
        if (
          scopeType === currentScope ||
          isBroaderScope(scopeType, currentScope)
        ) {
          effectivePermissions.delete(action);
        }
      }
    });
    // console.log("effectivePermissions", effectivePermissions);
    // Convert Map to array of Permission objects
    return Array.from(effectivePermissions.entries()).map(
      ([permission, { scopeType, id, scopeId }]) => {
        // Find the permission object to get its ID if not available
        let permissionId = id;
        if (!permissionId) {
          const permissionObj = permissionsByCategory
            .flatMap((category) => category.permissions)
            .find((p) => p.action === permission);
          permissionId = permissionObj?.id;
        }

        return {
          id: permissionId!,
          action: permission,
          scopeType,
          scopeId,
        };
      }
    );
}
// Added by zakia