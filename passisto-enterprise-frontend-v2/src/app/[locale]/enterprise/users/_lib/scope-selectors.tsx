"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Permission } from "@/store/slices/rolePermissionSlice";
import { Team } from "@/store/slices/userSlice";
import { User } from "@/store/slices/userSlice";

/**
 * Renders a scope selector based on the scope type
 */
export function renderScopeSelector(
  permission: string,
  scopeType: { id: string; name: string },
  formData: User,
  permissionsByCategory: any[],
  setFormData: (value: React.SetStateAction<User>) => void
) {
  if (scopeType.id === "TEAM") {
    // Filter selected teams
    const selectedTeams = formData?.teams;

    return (
      <div className="mt-2">
        <p className="text-xs text-muted-foreground mb-1">
          Select specific teams:
        </p>
        <div className="grid grid-cols-1 gap-2">
          {selectedTeams.map((team) => (
            <div
              key={`${permission}-team-${team.id}`}
              className="flex items-center space-x-2"
            >
              <Checkbox
                id={`${permission}-team-${team.id}`}
                checked={formData?.extraPermissions.some(
                  (p) =>
                    p.action === permission &&
                    p.scopeType === scopeType.id &&
                    p.scopeId === team.id
                )}
                onCheckedChange={(checked) => {
                  // Find the permission object
                  const permissionObj = permissionsByCategory
                    .flatMap((category) => category.permissions)
                    .find((p) => p.action === permission);

                  if (checked) {
                    // Add this specific team
                    const newPermission: Permission = {
                      id: permissionObj?.id!,
                      action: permission,
                      scopeType: scopeType.id,
                      scopeId: team.id,
                    };

                    setFormData((prev) => ({
                      ...prev,
                      extraPermissions: [
                        ...prev.extraPermissions,
                        newPermission,
                      ],
                      revokedPermissions: prev.revokedPermissions.filter(
                        (p) =>
                          !(
                            p.action === permission &&
                            p.scopeType === scopeType.id &&
                            p.scopeId === team.id
                          )
                      ),
                    }));
                  } else {
                    // Remove this specific team
                    setFormData((prev) => ({
                      ...prev,
                      extraPermissions: prev.extraPermissions.filter(
                        (p) =>
                          !(
                            p.action === permission &&
                            p.scopeType === scopeType.id &&
                            p.scopeId === team.id
                          )
                      ),
                    }));
                  }
                }}
              />
              <label
                htmlFor={`${permission}-team-${team.id}`}
                className="text-sm"
              >
                {team.name}
              </label>
            </div>
          ))}
        </div>
      </div>
    );
  } else if (scopeType.id === "PROJECT") {
    // Project scope selector implementation would go here
    return (
      <div className="mt-2">
        <p className="text-xs text-muted-foreground mb-1">
          Project scope selector not implemented yet
        </p>
      </div>
    );
  }

  return null;
}