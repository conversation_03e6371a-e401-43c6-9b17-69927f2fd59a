"use client";

import React, { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation';
import axios from "axios";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import Provider from '@/app/[locale]/provider'
import ThemeToggle from '@/components/theme-toggle'
import { CHECK_ONBOARDING, CHECK_STRIPE_SUBSCRIPTION_STATUS } from '@/utils/routes';
import axiosInstance from '@/config/axios';
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Globe } from "lucide-react";
import { routing } from '@/i18n/routing';
import { Link } from '@/i18n/nvigation';
import { useTranslations } from 'next-intl';


function Layout({children}: {children: React.ReactNode}) {
  const t = useTranslations()
  const router = useRouter();
  const pathname = usePathname();
  const { getToken } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [checksComplete, setChecksComplete] = useState(false);
  
  useEffect(() => {
    const performChecks = async () => {
      try {
        const token = await getToken();
        if (!token) {
          router.push('/auth/login');
          return;
        }

        // First check onboarding
        const onboardingResponse = await axiosInstance.post(
          CHECK_ONBOARDING,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
          }
        );

        console.log("Onboarding response:", onboardingResponse.data);

        if (onboardingResponse.data.hasCompletedOnboarding === false) {
          toast(t('please-complete-onboarding-first'));
          router.push('/auth/onboarding');
          return;
        }


        // Then check subscription
        const subscriptionResponse = await axiosInstance.get(CHECK_STRIPE_SUBSCRIPTION_STATUS, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log(subscriptionResponse.data);
        if (!subscriptionResponse.data.active) {
          router.push('/payment');
          return;
        }

        // Both checks passed
        setChecksComplete(true);

      } catch (error: any) {
        console.error('Error during checks:', error);
        if (error?.response?.status === 401) {
          router.push('/auth/login');
        } else {
          toast.error(t('error-verifying-account-status'));
          router.push('/auth/onboarding');
        }
      } finally {
        setIsLoading(false);
      }
    };

    performChecks();
  }, [router, getToken])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t('verifying-account-status')}</p>
        </div>
      </div>
    )
  }

  if (!checksComplete) {
    return null; // Don't render anything if checks haven't completed successfully
  }

  return (
    <Provider>
      <div className="w-full max-w-2xl mx-auto">
        <div className="fixed top-4 right-4 z-50 flex items-center gap-2">
          <LanguageSwitcher currentPath={pathname} />
          <ThemeToggle />
        </div>
      </div>
      {children}
    </Provider>
  )
}

function LanguageSwitcher({ currentPath }: { currentPath: string }) {
  const t = useTranslations()
  // Extract the full path without the locale prefix
  const pathSegments = currentPath.split('/');
  // Remove the empty first segment and locale segment
  pathSegments.splice(0, 2);
  // Reconstruct the path without locale
  const pathWithoutLocale = pathSegments.join('/');
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="rounded-full">
          <Globe className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">{t('switch-language')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {routing.locales.map((locale) => (
          <DropdownMenuItem key={locale} asChild>
            <Link href={`/${pathWithoutLocale}`} locale={locale}>
              {t(locale)}
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default Layout
