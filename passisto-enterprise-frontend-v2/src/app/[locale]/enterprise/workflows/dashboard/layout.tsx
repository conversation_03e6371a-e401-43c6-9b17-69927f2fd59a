"use client"

import { <PERSON>actN<PERSON>, useState, useEffect } from "react"
import { DashboardNav } from "@/components/workflows/dashboard-nav"
import { UserHeader } from "@/components/workflows/user-header"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Menu } from "lucide-react"

interface DashboardLayoutProps {
  readonly children: ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);

  // Load sidebar state from localStorage on component mount
  useEffect(() => {
    setIsMounted(true);
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      setCollapsed(savedState === 'true');
    }
  }, []);

  const toggleSidebar = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    // Only save to localStorage if component is mounted (client-side)
    if (isMounted) {
      localStorage.setItem('sidebarCollapsed', String(newState));
    }
  };

  const toggleMobileSidebar = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
      <div className="flex min-h-screen flex-col">
        {/* <UserHeader /> */}
        <div className="flex flex-1 relative">
          {/* Desktop sidebar */}
          <aside
            className={`hidden md:block ${collapsed ? 'w-16' : 'w-64'} border-r bg-sidebar transition-all duration-300 ease-in-out flex-shrink-0`}
          >
            <div className="flex h-full flex-col">
              <div className="flex h-14 items-center justify-between border-b px-4">
                {!collapsed && <h2 className="text-lg font-semibold">Navigation</h2>}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleSidebar}
                  className="ml-auto"
                  aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
                >
                  {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                </Button>
              </div>
              <DashboardNav collapsed={collapsed} />
            </div>
          </aside>

          {/* Mobile sidebar overlay */}
          <button
            className={`fixed inset-0 bg-black/50 z-40 md:hidden transition-opacity duration-200 ${mobileOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
            onClick={toggleMobileSidebar}
            aria-label="Close sidebar"
            tabIndex={mobileOpen ? 0 : -1}
          />

          {/* Mobile sidebar */}
          <aside
            className={`fixed top-0 bottom-0 left-0 z-50 w-64 border-r bg-sidebar transition-transform duration-300 ease-in-out md:hidden ${mobileOpen ? 'translate-x-0' : '-translate-x-full'}`}
          >
            <div className="flex h-full flex-col">
              <div className="flex h-14 items-center justify-between border-b px-4">
                <h2 className="text-lg font-semibold">Navigation</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMobileSidebar}
                  className="ml-auto"
                  aria-label="Close sidebar"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </div>
              <DashboardNav collapsed={false} />
            </div>
          </aside>

          {/* Mobile sidebar toggle */}
          <Button
            variant="outline"
            size="icon"
            className="fixed left-4 top-20 z-30 md:hidden"
            onClick={toggleMobileSidebar}
          >
            <Menu className="h-4 w-4" />
          </Button>

          <main className="flex-1 overflow-auto">{children}</main>
        </div>
      </div>
  )
}