"use client"

import { useState } from "react"
import { WorkflowsTable, type Workflow } from "@/components/workflows/workflows-table"
import { DashboardShell } from "@/components/workflows/dashboard-shell"
import { DashboardHeader } from "@/components/workflows/dashboard-header"
import { useRouter } from "next/navigation"
import { ScrollArea } from "@/components/ui/scroll-area"

export default function WorkflowsPage() {
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null)
  const router = useRouter()

  const handleCreateNew = () => {
    router.push("/enterprise/workflows/dashboard/workflows/new")
  }

  const handleLoadWorkflow = (workflow: Workflow) => {
    router.push(`/enterprise/workflows/dashboard/workflows/${workflow.id}`)
  }

  return (
    <DashboardShell>
      <ScrollArea className="h-[calc(100vh-120px)]">
        <div className="p-6">
          <DashboardHeader
            heading="Workflows"
            text="Create and manage your automated workflows."
          />
          <div className="grid gap-8">
            <WorkflowsTable
              onCreateNew={handleCreateNew}
              onLoadWorkflow={handleLoadWorkflow}
              currentWorkflow={currentWorkflow}
            />
          </div>
        </div>
      </ScrollArea>
    </DashboardShell>
  )
}
