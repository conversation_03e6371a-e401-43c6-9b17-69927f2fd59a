"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { DashboardActions } from "@/components/workflows/dashboard-actions"
import { ScrollArea } from "@/components/ui/scroll-area"

export default function DashboardPage() {
  return (
    <ScrollArea className="h-[calc(100vh-64px)]">
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflows</CardTitle>
              <CardDescription>
                Create and manage your workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Design and configure automated workflows to streamline your processes.
              </p>
              <DashboardActions type="workflows" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Workflow Runs</CardTitle>
              <CardDescription>
                Monitor and manage your workflow executions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                View the status, results, and performance of your workflow executions.
              </p>
              <DashboardActions type="workflow-runs" />
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  )
}
