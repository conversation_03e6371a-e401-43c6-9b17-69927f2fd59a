'use client';
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from 'next/navigation';
import { Trash, RefreshCw, ChevronLeft, FileText, FileImage, FileCode, File, Search, X } from "lucide-react";
import { toast } from "sonner";

// Interface pour les fichiers
interface FileItem {
  id: string;
  name: string;
  type: string;
  size: string;
  deletedAt: string;
  path?: string;
}

// Données mockées pour les fichiers dans la corbeille
const mockTrashedFiles: FileItem[] = [
  {
    id: "1",
    name: "important-document.pdf",
    type: "pdf",
    size: "2.5 MB",
    deletedAt: "2024-02-28T10:30:00",
    path: "/documents/"
  },
  {
    id: "2",
    name: "profile-picture.jpg",
    type: "image",
    size: "1.2 MB",
    deletedAt: "2024-02-27T15:45:00",
    path: "/images/"
  },
  {
    id: "3",
    name: "script.js",
    type: "code",
    size: "45 KB",
    deletedAt: "2024-02-26T09:15:00",
    path: "/scripts/"
  },
  {
    id: "4",
    name: "backup.zip",
    type: "archive",
    size: "150 MB",
    deletedAt: "2024-02-25T18:20:00",
    path: "/backups/"
  }
];

// Fonction pour obtenir l'icône appropriée selon le type de fichier
const getFileIcon = (type: string) => {
  switch (type) {
    case 'pdf':
      return <FileText className="h-5 w-5 text-red-500" />;
    case 'image':
      return <FileImage className="h-5 w-5 text-blue-500" />;
    case 'code':
      return <FileCode className="h-5 w-5 text-green-500" />;
    default:
      return <File className="h-5 w-5 text-gray-500" />;
  }
};

export default function TrashPage() {
  const router = useRouter();
  const [trashedItems, setTrashedItems] = useState<FileItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Charger les données mockées au lieu du localStorage
    setTrashedItems(mockTrashedFiles);
  }, []);

  const handleRestore = (item: FileItem) => {
    const updatedTrashedItems = trashedItems.filter((i) => i.id !== item.id);
    setTrashedItems(updatedTrashedItems);
    
    toast("File restored", {
      description: `${item.name} has been restored successfully.`
    });
  };

  const handlePermanentDelete = (item: FileItem) => {
    if (confirm("Are you sure you want to permanently delete this item? This action cannot be undone.")) {
      const updatedTrashedItems = trashedItems.filter((i) => i.id !== item.id);
      setTrashedItems(updatedTrashedItems);

      toast("File deleted permanently", {
        description: `${item.name} has been permanently deleted.`
      });
    }
  };

  const handleEmptyTrash = () => {
    if (confirm("Are you sure you want to permanently delete all items? This action cannot be undone.")) {
      setTrashedItems([]);
      toast("Trash emptied", {
        description: "All items have been permanently deleted."
      });
    }
  };

  // Filtrer les éléments en fonction de la recherche
  const filteredItems = trashedItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-4">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => router.back()}>
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl font-bold">Trash</h1>
          </div>
          {trashedItems.length > 0 && (
            <Button
              variant="destructive"
              onClick={handleEmptyTrash}
              className="flex items-center gap-2"
            >
              <Trash className="h-4 w-4" />
              Empty Trash
            </Button>
          )}
        </div>

        {/* Barre de recherche */}
        <div className="relative w-full max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            className="pl-10 pr-10"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {filteredItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[400px] border-2 border-dashed rounded-lg p-8">
            <Trash className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              {searchQuery ? "No files found matching your search" : "Trash is empty"}
            </p>
          </div>
        ) : (
          <div className="grid gap-4 grid-cols-1">
            {filteredItems.map((item) => (
              <div
                key={item.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center gap-4">
                  {getFileIcon(item.type)}
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <div className="flex gap-2 text-sm text-muted-foreground">
                      <span>{item.size}</span>
                      <span>•</span>
                      <span>Deleted: {new Date(item.deletedAt).toLocaleDateString()}</span>
                      <span>•</span>
                      <span className="text-gray-400">{item.path}</span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRestore(item)}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Restore
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handlePermanentDelete(item)}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete Permanently
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
