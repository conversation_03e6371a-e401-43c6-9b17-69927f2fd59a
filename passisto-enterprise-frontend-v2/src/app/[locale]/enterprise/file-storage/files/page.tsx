'use client';
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { 
  ChevronLeft, 
  File, 
  Folder, 
  Grid, 
  List, 
  MoreVertical, 
  Plus, 
  Search, 
  X, 
  FolderSearch, 
  FolderIcon, 
  FolderPlus,
  Upload
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Share, Co<PERSON>, Check, Wand2 } from "lucide-react";

interface FileItem {
  id: string;
  name: string;
  type: "file" | "folder";
  size?: string;
  modified: string;
  parentId: string | null;
  children?: FileItem[];
}

export default function FileStoragePage() {
  const [items, setItems] = useState<FileItem[]>([]);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState("grid");
  const [isNewFolderModalOpen, setIsNewFolderModalOpen] = useState(false);
  const [isNewFileModalOpen, setIsNewFileModalOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [newFileName, setNewFileName] = useState("");
  const [fileExtension, setFileExtension] = useState(".txt");
  const [isRenameModalOpen, setIsRenameModalOpen] = useState(false);
  const [itemToRename, setItemToRename] = useState<FileItem | null>(null);
  const [newItemName, setNewItemName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [selectedItemForShare, setSelectedItemForShare] = useState<FileItem | null>(null);
  const [sharePermission, setSharePermission] = useState("view");
  const [shareLink, setShareLink] = useState("");
  const [hasCopied, setHasCopied] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const commonExtensions = [
    ".txt",
    ".md",
    ".json",
    ".js",
    ".ts",
    ".html",
    ".css",
    ".xml",
    ".csv",
  ];

  // Charger les données au démarrage
  useEffect(() => {
    const savedItems = localStorage.getItem('fileStorage');
    if (savedItems) {
      setItems(JSON.parse(savedItems));
    }
  }, []);

  // Fonction pour filtrer les éléments
  const filterItems = (items: FileItem[]) => {
    return items.filter((item) => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Rechercher aussi dans les sous-dossiers
      if (item.children && item.children.length > 0) {
        item.children = filterItems(item.children);
        return matchesSearch || item.children.length > 0;
      }
      
      return matchesSearch;
    });
  };

  const getCurrentItems = () => {
    if (!currentFolder) {
      const rootItems = items.filter(item => !item.parentId);
      return searchQuery ? filterItems(rootItems) : rootItems;
    }

    const folder = findItemById(currentFolder);
    if (!folder || !folder.children) return [];
    
    return searchQuery ? filterItems(folder.children) : folder.children;
  };

  const findItemById = (id: string): FileItem | undefined => {
    const findInItems = (items: FileItem[]): FileItem | undefined => {
      for (const item of items) {
        if (item.id === id) return item;
        if (item.children) {
          const found = findInItems(item.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    return findInItems(items);
  };

  const createFolder = () => {
    setIsNewFolderModalOpen(true);
  };

  const handleCreateFolder = () => {
    const newFolder = {
      id: Date.now().toString(),
      name: newFolderName,
      type: "folder",
      modified: new Date().toISOString().split("T")[0],
      parentId: currentFolder,
      children: [],
    };

    setItems((prevItems) => {
      let updatedItems;
      if (!currentFolder) {
        updatedItems = [...prevItems, newFolder];
      } else {
        updatedItems = prevItems.map((item) => {
          if (item.id === currentFolder) {
            return {
              ...item,
              children: [...(item.children || []), newFolder],
            };
          }
          return item;
        });
      }
      localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
      return updatedItems;
    });

    setIsNewFolderModalOpen(false);
    setNewFolderName("");
  };

  const createFile = () => {
    setIsNewFileModalOpen(true);
  };

  const handleCreateFile = () => {
    const hasExtension = /\.[a-zA-Z0-9]+$/.test(newFileName);
    const newFile: FileItem = {
      id: Date.now().toString(),
      name: hasExtension ? newFileName : newFileName + fileExtension,
      type: "file",
      size: "0 KB",
      modified: new Date().toISOString().split("T")[0],
      parentId: currentFolder,
    };

    setItems((prevItems) => {
      let updatedItems;
      if (!currentFolder) {
        updatedItems = [...prevItems, newFile];
      } else {
        updatedItems = prevItems.map((item) => {
          if (item.id === currentFolder) {
            return {
              ...item,
              children: [...(item.children || []), newFile],
            };
          }
          return item;
        });
      }
      localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
      return updatedItems;
    });

    setIsNewFileModalOpen(false);
    setNewFileName("");
    setFileExtension(".txt");
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const newFile: FileItem = {
        id: Date.now().toString(),
        name: file.name,
        type: "file",
        size: `${(file.size / 1024).toFixed(2)} KB`,
        modified: new Date().toISOString().split("T")[0],
        parentId: currentFolder,
      };

      setItems((prevItems) => {
        let updatedItems;
        if (!currentFolder) {
          updatedItems = [...prevItems, newFile];
        } else {
          updatedItems = prevItems.map((item) => {
            if (item.id === currentFolder) {
              return {
                ...item,
                children: [...(item.children || []), newFile],
              };
            }
            return item;
          });
        }
        localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
        return updatedItems;
      });
    }
  };

  const handleItemClick = (item: FileItem) => {
    if (item.type === "folder") {
      setCurrentFolder(item.id);
    }
  };

  const handleBackClick = () => {
    const currentItem = findItemById(currentFolder);
    setCurrentFolder(currentItem?.parentId || null);
  };

  const handleDragStart = (e: React.DragEvent, id: string) => {
    e.stopPropagation();
    e.dataTransfer.setData("text/plain", id);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Retirer le style visuel
    const dropTarget = e.currentTarget;
    dropTarget.classList.remove("bg-gray-100");
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    e.stopPropagation();
    const dropTarget = e.currentTarget;
    dropTarget.classList.remove("bg-gray-100");

    const sourceId = e.dataTransfer.getData("text/plain");
    
    if (sourceId === targetId) {
      return; // Éviter de déplacer un élément sur lui-même
    }

    setItems((prevItems) => {
      // Fonction pour trouver et retirer un élément de l'arborescence
      const findAndRemoveItem = (items: FileItem[], id: string): [FileItem[], FileItem | undefined] => {
        let removedItem: FileItem | undefined;
        
        const filterItems = (items: FileItem[]): FileItem[] => {
          return items.filter(item => {
            if (item.id === id) {
              removedItem = item;
              return false;
            }
            if (item.children) {
              item.children = filterItems(item.children);
            }
            return true;
          });
        };

        const newItems = filterItems(items);
        return [newItems, removedItem];
      };

      // Fonction pour ajouter un élément à un dossier cible
      const addItemToTarget = (items: FileItem[], targetId: string, itemToAdd: FileItem): FileItem[] => {
        return items.map(item => {
          if (item.id === targetId) {
            return {
              ...item,
              children: [...(item.children || []), itemToAdd]
            };
          }
          if (item.children) {
            return {
              ...item,
              children: addItemToTarget(item.children, targetId, itemToAdd)
            };
          }
          return item;
        });
      };

      // Trouver et retirer l'élément source
      const [newItems, removedItem] = findAndRemoveItem(prevItems, sourceId);
      
      if (!removedItem) return prevItems;

      // Si la cible est un dossier, ajouter l'élément dans ce dossier
      const targetItem = findItemById(targetId);
      if (targetItem && targetItem.type === "folder") {
        const updatedItems = addItemToTarget(newItems, targetId, removedItem);
        localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
        return updatedItems;
      }

      // Si la cible n'est pas un dossier, ajouter l'élément au même niveau
      const targetParentId = findItemById(targetId)?.parentId;
      const itemToAdd = { ...removedItem, parentId: targetParentId };

      if (!targetParentId) {
        // Ajouter à la racine
        const updatedItems = [...newItems, itemToAdd];
        localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
        return updatedItems;
      }

      // Ajouter dans le dossier parent
      const updatedItems = addItemToTarget(newItems, targetParentId, itemToAdd);
      localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
      return updatedItems;
    });
  };

  const handleRename = (item: FileItem) => {
    setItemToRename(item);
    setNewItemName(item.name);
    setIsRenameModalOpen(true);
  };

  const confirmRename = () => {
    if (itemToRename) {
      setItems((prevItems) => {
        const updateItem = (items: FileItem[]) => {
          return items.map((item) => {
            if (item.id === itemToRename.id) {
              return { ...item, name: newItemName };
            }
            if (item.children) {
              return { ...item, children: updateItem(item.children) };
            }
            return item;
          });
        };
        const updatedItems = updateItem(prevItems);
        // Sauvegarder dans localStorage
        localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
        return updatedItems;
      });
    }
    setIsRenameModalOpen(false);
    setItemToRename(null);
    setNewItemName("");
  };
  const onDelete = (id: string) => {
    console.log(`Deleting file with ID: ${id}`);
  };
  const handleDelete = (item: FileItem) => {
    onDelete(item);
    setItems((prevItems) => {
      const deleteItem = (items: FileItem[]) => {
        return items.filter((i) => {
          if (i.id === item.id) {
            return false;
          }
          if (i.children) {
            i.children = deleteItem(i.children);
          }
          return true;
        });
      };
      const updatedItems = deleteItem(prevItems);
      // Sauvegarder dans localStorage
      localStorage.setItem('fileStorage', JSON.stringify(updatedItems));
      return updatedItems;
    });
  };

  const handleShare = (item: FileItem) => {
    setSelectedItemForShare(item);
    setShareLink(""); // Initialiser avec une chaîne vide
    setIsShareModalOpen(true);
  };

  const copyShareLink = async () => {
    try {
      await navigator.clipboard.writeText(shareLink);
      setHasCopied(true);
      setTimeout(() => setHasCopied(false), 2000); // Reset après 2 secondes
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  const currentItems = getCurrentItems();
  const isEmpty = currentItems.length === 0 && !searchQuery;

  return (
    <div className="p-4">
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            {currentFolder && (
              <Button variant="ghost" onClick={handleBackClick}>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            )}
            <h2 className="text-2xl font-bold">
              {currentFolder ? findItemById(currentFolder)?.name : "My Files"}
            </h2>
          </div>
          <div className="flex gap-2">
            <Button onClick={createFolder}>
              <FolderPlus className="mr-2 h-4 w-4" />
              New Folder
            </Button>
            <div className="relative">
              <input
                type="file"
                className="hidden"
                id="file-upload-empty"
                onChange={handleFileUpload}
              />
              <Button onClick={() => document.getElementById('file-upload-empty')?.click()}>
                <Upload className="mr-2 h-4 w-4" />
                Upload File
              </Button>
            </div>
            <Button
              variant="outline"
              onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
            >
              {viewMode === "grid" ? (
                <List className="h-4 w-4" />
              ) : (
                <Grid className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Ajouter la barre de recherche */}
        <div className="relative w-full max-w-md mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            className="pl-10"
            placeholder="Search files and folders..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {isEmpty ? (
        <div className="flex flex-col items-center justify-center min-h-[400px] border-2 border-dashed rounded-lg p-8">
          <div className="h-20 w-20 rounded-full bg-muted flex items-center justify-center mb-4">
            <FolderIcon className="h-10 w-10 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No files or folders</h3>
          <p className="text-muted-foreground text-center mb-6">
            Get started by creating a new folder or files
          </p>
          <div className="flex gap-4">
            <Button onClick={createFolder}>
              <FolderPlus className="mr-2 h-4 w-4" />
              New Folder
            </Button>
            <div className="relative">
              <input
                type="file"
                className="hidden"
                id="file-upload-empty"
                onChange={handleFileUpload}
              />
              <Button onClick={() => document.getElementById('file-upload-empty')?.click()}>
                <Upload className="mr-2 h-4 w-4" />
                Upload File
              </Button>
            </div>
          </div>
        </div>
      ) : searchQuery && currentItems.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
          <FolderSearch className="h-8 w-8 mb-2" />
          <p>No files or folders found matching "{searchQuery}"</p>
        </div>
      ) : (
        <div
          className={cn(
            "grid gap-4 mt-4",
            viewMode === "grid"
              ? "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6"
              : "grid-cols-1"
          )}
        >
          {currentItems.map((item) => (
            <div
              key={item.id}
              className={cn(
                "p-4 border rounded-lg cursor-pointer relative group",
                viewMode === "list" && "flex items-center justify-between",
                item.type === "folder" && "hover:bg-gray-50"
              )}
              draggable
              onDragStart={(e) => handleDragStart(e, item.id)}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, item.id)}
              onClick={() => handleItemClick(item)}
            >
              <div
                className={cn(
                  "flex flex-col items-center",
                  viewMode === "list" && "flex-row gap-4"
                )}
              >
                {item.type === "folder" ? (
                  <Folder className="h-12 w-12 text-blue-500" />
                ) : (
                  <File className="h-12 w-12 text-gray-500" />
                )}
                <span className="mt-2 text-center truncate w-full">
                  {item.name}
                </span>
              </div>
              {viewMode === "list" && (
                <div className="flex items-center gap-4">
                  <span>{item.size || "-"}</span>
                  <span>{item.modified}</span>
                </div>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRename(item);
                    }}
                  >
                    Rename
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShare(item);
                    }}
                  >
                    <Share className="h-4 w-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(item);
                    }}
                  >
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>
      )}
      <Dialog
        open={isNewFolderModalOpen}
        onOpenChange={setIsNewFolderModalOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
          </DialogHeader>
          <Input
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            placeholder="Enter folder name"
          />
          <DialogFooter>
            <Button onClick={handleCreateFolder}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog open={isRenameModalOpen} onOpenChange={setIsRenameModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Item</DialogTitle>
          </DialogHeader>
          <Input
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
            placeholder="Enter new name"
          />
          <DialogFooter>
            <Button onClick={confirmRename}>Rename</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={isNewFileModalOpen}
        onOpenChange={setIsNewFileModalOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New File</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                File Name
              </label>
              <Input
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                placeholder="Enter file name"
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">
                Extension
              </label>
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-2"
                value={fileExtension}
                onChange={(e) => setFileExtension(e.target.value)}
              >
                {commonExtensions.map((ext) => (
                  <option key={ext} value={ext}>
                    {ext}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleCreateFile}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={isShareModalOpen}
        onOpenChange={setIsShareModalOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Share {selectedItemForShare?.name}</DialogTitle>
            <DialogDescription>
              Generate a link to share this {selectedItemForShare?.type}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col gap-4 py-4">
            <div className="flex flex-col gap-2">
              <Label>Share Link</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={shareLink}
                  readOnly
                  placeholder="Click Generate Link to create a shareable link"
                  className="flex-1"
                />
                <Button 
                  onClick={copyShareLink} 
                  size="icon" 
                  variant="outline"
                  disabled={!shareLink}
                >
                  {hasCopied ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="default"
                  onClick={() => {
                    const uniqueId = Math.random().toString(36).substring(2, 15);
                    setShareLink(`${window.location.origin}/share/${selectedItemForShare?.id}-${uniqueId}`);
                  }}
                  className="bg-black hover:bg-black/90 flex-1"
                >
                  Generate Link
                </Button>
                <div className="w-10"></div> {/* Espace pour aligner avec le bouton de copie */}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Label>Access Permissions</Label>
              <RadioGroup 
                defaultValue="view" 
                onValueChange={(value) => setSharePermission(value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="view" id="view" />
                  <Label htmlFor="view">View</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="edit" id="edit" />
                  <Label htmlFor="edit">Edit</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="full" id="full" />
                  <Label htmlFor="full">Full access</Label>
                </div>
              </RadioGroup>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsShareModalOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
