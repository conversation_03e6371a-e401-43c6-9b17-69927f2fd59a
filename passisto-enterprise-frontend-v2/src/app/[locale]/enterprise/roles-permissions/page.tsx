"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Dialog, // Keeping Dialog imports just in case you re-add functionality later
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Shield, Plus, Pencil, Trash2, Search, Users, AlertTriangle, Info } from "lucide-react"
import { toast } from "sonner"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import {
  fetchRolesPermissions,
  PermissionCategory
} from "@/store/slices/rolePermissionSlice"
import { useAuth } from "@clerk/nextjs"
import { useTranslations } from "next-intl" // Import useTranslations

export default function RolesPermissionsPage() {
  const t = useTranslations("RolesPermissionsPage") // Initialize useTranslations
  const router = useRouter()
  const dispatch = useAppDispatch()
  const { getToken } = useAuth()

  // Get roles and permissions from Redux store
  const {
    roles,
    permissionsByCategory,
    loading: rolesLoading,
    error: rolesError
  } = useAppSelector((state) => state.rolePermission)

  // State for roles management - simplified
  const [rolesList, setRolesList] = useState<string[]>([])
  const [roleSearchQuery, setRoleSearchQuery] = useState("")

  // State for permissions management - simplified
  const [permissionsList, setPermissionsList] = useState<string[]>([])
  const [permissionSearchQuery, setPermissionSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")

  // Loading state
  const [loading, setLoading] = useState(true)

  // State for permissionCategories - properly typed with the imported interface
  const [permissionCategories, setPermissionCategories] = useState<PermissionCategory[]>([])

  // Fetch roles and permissions from API using Redux
  useEffect(() => {
    const loadRolesAndPermissions = async () => {
      try {
        setLoading(true)
        const token = await getToken()

        if (token) {
          // Dispatch the fetchRolesPermissions action with the token
          await dispatch(fetchRolesPermissions(token)).unwrap()
          console.log("Roles and permissions loaded successfully")
        }
      } catch (error) {
        console.error("Error loading roles and permissions:", error)
        toast.error(t("errorLoadingDataDescription")) // Use translation for error message
      } finally {
        setLoading(false)
      }
    }

    loadRolesAndPermissions()
  }, [dispatch, getToken, t]) // Add 't' to the dependency array

  // Update local state when Redux data changes
  useEffect(() => {
    if (roles) {
      setRolesList(roles.map(role => role.name))
    }

    if (permissionsByCategory) {
      // Extract all permissions for your existing component
      const allPermissions: string[] = []
      permissionsByCategory.forEach(category => {
        category.permissions.forEach(permission => {
          allPermissions.push(permission.action)
        })
      })
      setPermissionsList(allPermissions)

      // We can directly use permissionsByCategory since it already matches the interface
      setPermissionCategories(permissionsByCategory)
    }
  }, [roles, permissionsByCategory])

  // Filter roles based on search query
  const filteredRoles = rolesList.filter((role) =>
    role.toLowerCase().includes(roleSearchQuery.toLowerCase())
  )

  // Get category for a permission
  const getPermissionCategory = (permission: string): string => {
    if (!permissionsByCategory) return t("uncategorized") // Use translation
    
    for (const category of permissionsByCategory) {
      const hasPermission = category.permissions.some(p => p.action === permission)
      if (hasPermission) {
        return category.category
      }
    }
    return t("uncategorized") // Use translation
  }

  // Filter permissions based on search query and category
  const filteredPermissions = permissionsList.filter((permission) => {
    const matchesSearch = permission.toLowerCase().includes(permissionSearchQuery.toLowerCase())
    const permissionCategory = getPermissionCategory(permission)
    const matchesCategory = categoryFilter === "all" || permissionCategory === categoryFilter
    return matchesSearch && matchesCategory
  })

  // Format permission name for display
  const formatPermissionName = (permission: string): string => {
    return permission
      .replace("CAN_", "")
      .split("_")
      .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ")
  }

  // Debug category filter changes
  useEffect(() => {
    console.log("Category filter changed to:", categoryFilter)
    console.log(
      "Available categories:",
      permissionCategories.map((c) => c.category),
    )
  }, [categoryFilter, permissionCategories])

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {loading ? (
        <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <p className="mt-4 text-muted-foreground">{t("loadingPermissions")}</p>
          </div>
        </div>
      ) : rolesError ? (
        <div className="bg-destructive/10 border-destructive/20 border rounded-lg p-4 flex items-start space-x-3">
          <AlertTriangle className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-destructive">{t("errorLoadingData")}</h3>
            <p className="text-sm text-muted-foreground">{rolesError}</p>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("pageTitle")}</h1>
            <p className="text-muted-foreground mt-2">{t("pageDescription")}</p>
          </div>

          {/* <div className="bg-muted/50 border rounded-lg p-4 flex items-start space-x-3"> */}
            {/* <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" /> */}
            
              {/* <h3 className="font-medium">Direct Permission Assignment</h3>
              <p className="text-sm text-muted-foreground">
                {t("infoBoxDescription1")}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                On this page, you can view all available roles and permissions. Use the tabs to switch between views,
                the search box to find specific items, and the category filter to organize permissions by type.
              </p> */}
              <div className="mt-3">
                <Button
                  onClick={() => window.location.href = "/enterprise/roles-permissions/info"}
                  variant="outline"
                  size="sm"
                >
                  <Shield className="mr-2 h-4 w-4" />
                  {t("permissionSystemInfoButton")}
                </Button>
              </div>
            
          {/* </div> */}

          <Tabs defaultValue="roles" className="space-y-4">
            <TabsList className="w-full sm:w-auto">
              <TabsTrigger value="roles" className="flex items-center">
                <Users className="mr-2 h-4 w-4" />
                {t("rolesTab")}
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center">
                <Shield className="mr-2 h-4 w-4" />
                {t("permissionsTab")}
              </TabsTrigger>
            </TabsList>

            {/* Roles Tab */}
            <TabsContent value="roles" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 pb-2">
                  <div>
                    <CardTitle>{t("rolesManagementTitle")}</CardTitle>
                    <CardDescription className="mt-1">{t("rolesManagementDescription")}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Search className="h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t("searchRolesPlaceholder")}
                        value={roleSearchQuery}
                        onChange={(e) => setRoleSearchQuery(e.target.value)}
                        className="h-9"
                      />
                    </div>

                    <div className="border rounded-md overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>{t("roleNameTableHead")}</TableHead>
                            <TableHead>{t("systemNameTableHead")}</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredRoles.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={2} className="text-center py-6 text-muted-foreground">
                                {t("noRolesFound")}
                              </TableCell>
                            </TableRow>
                          ) : (
                            filteredRoles.map((role) => (
                              <TableRow key={role}>
                                <TableCell className="font-medium">
                                  {role === "ADMIN"
                                    ? t("roleAdministrator")
                                    : role === "MANAGER"
                                      ? t("roleManager")
                                      : role === "MEMBER"
                                        ? t("roleMember")
                                        : role === "GUEST"
                                          ? t("roleGuest")
                                          : role.replace(/_/g, " ").toLowerCase()}
                                </TableCell>
                                <TableCell>
                                  <code className="bg-muted px-1 py-0.5 rounded text-sm">{role}</code>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Permissions Tab */}
            <TabsContent value="permissions" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 pb-2">
                  <div>
                    <CardTitle>{t("permissionsManagementTitle")}</CardTitle>
                    <CardDescription className="mt-1">{t("permissionsManagementDescription")}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex items-center space-x-2 flex-1">
                        <Search className="h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder={t("searchPermissionsPlaceholder")}
                          value={permissionSearchQuery}
                          onChange={(e) => setPermissionSearchQuery(e.target.value)}
                          className="h-9"
                        />
                      </div>
                      <select
                        className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        value={categoryFilter}
                        onChange={(e) => {
                          console.log("Selected category:", e.target.value)
                          setCategoryFilter(e.target.value)
                        }}
                      >
                        <option value="all">{t("allCategoriesOption")}</option>
                        {permissionCategories.map((category) => (
                          <option key={category.category} value={category.category}>
                            {category.category}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="border rounded-md overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>{t("permissionNameTableHead")}</TableHead>
                            <TableHead>{t("systemNameTableHead")}</TableHead>
                            <TableHead>{t("categoryTableHead")}</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredPermissions.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                                {t("noPermissionsFound")}
                              </TableCell>
                            </TableRow>
                          ) : (
                            filteredPermissions.map((permission) => (
                              <TableRow key={permission}>
                                <TableCell className="font-medium">{formatPermissionName(permission)}</TableCell>
                                <TableCell>
                                  <code className="bg-muted px-1 py-0.5 rounded text-sm">{permission}</code>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="outline">{getPermissionCategory(permission)}</Badge>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  )
}