"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Mail, Bot, HeadsetIcon, Loader2, Sparkles, Zap, Clock, Shield, Check, LayoutDashboard, CreditCard } from "lucide-react"
import { toast } from "sonner"
import { Switch } from "@/components/ui/switch"
import axiosInstance from "@/config/axios"
import { CREATE_STRIPE_CHECKOUT_SESSION, GET_STRIPE_PLANS, SUBSCRIPTION_NAME } from "@/utils/routes"
import { useAuth } from "@clerk/nextjs"
import { UserButton } from "@clerk/nextjs"
import { Link } from '@/i18n/nvigation';
import Image from "next/image"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

import { useTranslations } from "next-intl"

// Define feature icons mapping here in the frontend
const FEATURE_ICONS = {
  email_builder: Mail,
  ai_interviewer: <PERSON><PERSON>,
  support: HeadsetIcon,
} as const

interface PlanFeature {
  id: string
  name: string
  limit: string
  description: string
}

interface Plan {
  name: string
  monthlyPrice: number
  annualDiscount: number
  monthlyPriceId: string
  yearlyPriceId: string
  features: PlanFeature[]
}

export default function PaymentPage() {
  const t = useTranslations()
  const { getToken } = useAuth()
  const [plans, setPlans] = useState<Plan[]>([])
  const [isAnnual, setIsAnnual] = useState(false)
  const [pageLoading, setPageLoading] = useState(true)
  const [loadingStates, setLoadingStates] = useState({})
  const [currentSubscription, setCurrentSubscription] = useState("")
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false)
  const commonFeatures = [
    { id: "ai_powered", name: t('ai-powered'), description: t('advanced-ai-technology'), icon: Sparkles },
    { id: "real_time", name: t('real-time-analysis'), description: t('instant-feedback'), icon: Zap },
    { id: "available", name: t('24-7-available'), description: t('access-anytime'), icon: Clock },
    { id: "security", name: t('enterprise-security'), description: t('bank-grade-encryption'), icon: Shield },
  ]
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken()
        
        // Fetch subscription name
        const subscriptionResponse = await axiosInstance.get(SUBSCRIPTION_NAME, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        console.log(subscriptionResponse.data.subscriptionName);
        setCurrentSubscription(subscriptionResponse.data.subscriptionName)

        // Fetch plans
        const plansResponse = await axiosInstance.get(GET_STRIPE_PLANS)
        setPlans(plansResponse.data)
      } catch (error) {
        toast.error(t('failed-to-load-subscription-information'))
        console.error("Error fetching data:", error)
      } finally {
        setPageLoading(false)
      }
    }

    fetchData()
  }, [getToken])

  const handleSubscribe = async (planId: string, planName: string) => {
    // Check if trying to subscribe to Free plan when already having any subscription
    if (planName === 'Free' && currentSubscription !== 'No Active Subscription') {
      setShowSubscriptionDialog(true)
      return
    }
    
    setLoadingStates(prev => ({ ...prev, [planId]: true }))
    
    try {
      const token = await getToken()
      const response = await axiosInstance.post(
        CREATE_STRIPE_CHECKOUT_SESSION,
        {
          priceId: planId,
          planName: planName,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )
      
      if (response.data.url) {
        window.location.href = response.data.url
      }

      toast.success(t('redirecting-to-checkout'))
    } catch (error) {
      toast.error(t('failed-to-initialize-checkout'))
      console.error("Checkout error:", error)
    } finally {
      setLoadingStates(prev => ({ ...prev, [planId]: false }))
    }
  }

  const SubscriptionDialog = () => (
    <Dialog open={showSubscriptionDialog} onOpenChange={setShowSubscriptionDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('free-plan-subscription-limit-reached')}</DialogTitle>
          <DialogDescription>
            {t('you-can-only-subscribe-to-the-free-plan-once-even-if-your-previous-free-plan-subscription-is-no-longer-active-you-cannot-subscribe-to-the-free-plan-again-to-continue-using-the-service-please-choose-a-paid-plan')} </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
  

  if (pageLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!plans.length) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-600 font-medium mb-4">{t('failed-to-load-plans')}</p>
          <Button onClick={() => window.location.reload()}>{t('try-again')}</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
      {/* Navigation Bar */}
      <nav className="border-b bg-white py-4 px-6 flex justify-between items-center sticky top-0 z-10 shadow-sm">
        <Link 
          href="/enterprise/dashboard" 
          className="hover:opacity-90 transition-opacity"
        >
          <Image
            src="/passisto_logo.png"
            alt="Passisto Logo"
            width={150}
            height={40}
            priority
          />
        </Link>
        <UserButton afterSignOutUrl="/auth/login">
          <UserButton.MenuItems>
            <UserButton.Action
              label={t('dashboard')}
              labelIcon={<LayoutDashboard size={16} />}
              onClick={() => window.location.href = "/enterprise/dashboard"}
            />
            {/* <UserButton.Action
              label="Billing Settings"
              labelIcon={<CreditCard size={16} />}
              onClick={() => window.location.href = "/payment"}
            /> */}
          </UserButton.MenuItems>
        </UserButton>
      </nav>

      <div className="container max-w-7xl px-4 py-16 mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            {t('choose-your-plan')}
          </h1>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {t('access-powerful-ai-tools-to-streamline-your-recruitment-process-and-find-the-perfect-candidates-faster')}
          </p>
        </div>

        <div className="flex items-center justify-center gap-3 mb-12 bg-white p-4 rounded-full shadow-sm max-w-xs mx-auto">
          <span className={!isAnnual ? "font-medium text-primary" : "text-slate-500"}>{t('monthly')}</span>
          <Switch checked={isAnnual} onCheckedChange={setIsAnnual} className="data-[state=checked]:bg-primary" />
          <span className={isAnnual ? "font-medium text-primary" : "text-slate-500"}>
            {t('annual')} <span className="text-xs text-emerald-600 font-bold">{t('save-20')}</span>
          </span>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
          {plans.map((plan, index) => {
            const annualMonthlyPrice = Number.parseFloat(
              (plan.monthlyPrice * (1 - plan.annualDiscount / 100)).toFixed(2),
            )
            const annualPrice = Math.round(annualMonthlyPrice * 12)
            const isPopular = plan.name !== 'Free' && plan.name !== 'Enterprise'

            return (
              <Card
                key={plan.name}
                className={`overflow-hidden border-2 relative flex flex-col transition-all duration-300 hover:shadow-xl ${
                  isPopular ? "border-primary/30 shadow-lg" : "border-slate-200 hover:border-slate-300"
                }`}
              >
                {isPopular && (
                  <div className="absolute top-0 right-0 bg-primary text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                    {t('popular')}
                  </div>
                )}

                <CardHeader className={`pb-6 pt-10 text-center ${isPopular ? "bg-primary/5" : ""}`}>
                  <CardTitle className="text-2xl font-bold mb-2">{plan.name}</CardTitle>
                  <CardDescription>{t('all-in-one-ai-recruitment-suite')}</CardDescription>
                  <div className="mt-6">
                    {isAnnual ? (
                      <div>
                        <span className="text-5xl font-bold text-slate-800">
                          {plan.name === 'Enterprise' ? 'Custom' : `$${annualMonthlyPrice}`}
                        </span>
                        {plan.name !== 'Enterprise' && (
                          <>
                            <span className="text-slate-500">/month</span>
                            <div className="text-sm text-slate-500 mt-2">Billed annually (${annualPrice}/year)</div>
                          </>
                        )}
                      </div>
                    ) : (
                      <div>
                        <span className="text-5xl font-bold text-slate-800">
                          {plan.name === 'Enterprise' ? 'Custom'  : `$${plan.monthlyPrice}`}
                        </span>
                        {plan.name !== 'Enterprise' && (
                          <>
                            <span className="text-slate-500">/month</span>
                            <div className="text-sm text-slate-500 mt-2">
                              Switch to annual for ${annualMonthlyPrice}/month
                            </div>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="pb-6 flex-grow">
                  <div className="grid gap-4">
                    <h3 className="font-semibold text-sm uppercase text-slate-500 tracking-wider mt-2">
                      {t('plan-features')}
                    </h3>
                    <div className="space-y-3">
                      {plan.features.map((feature) => {
                        const FeatureIcon = FEATURE_ICONS[feature.id as keyof typeof FEATURE_ICONS] || Mail
                        return (
                          <div
                            key={feature.id}
                            className="flex items-center gap-3 p-3 rounded-lg bg-slate-50 border border-slate-100 transition-all hover:bg-slate-100"
                          >
                            <div className="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                              <FeatureIcon className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <div className="font-medium">{feature.name}</div>
                              <div className="text-xs text-slate-500">
                                {feature.description} • <span className="font-semibold">{feature.limit}</span>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    <h3 className="font-semibold text-sm uppercase text-slate-500 tracking-wider mt-4">
                      {t('included-for-all-plans')}
                    </h3>

                    {/* Common features */}
                    <div className="space-y-2 mt-1">
                      {commonFeatures.map((feature) => (
                        <div key={feature.id} className="flex items-center gap-2 p-2">
                          <div className="h-5 w-5 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                            <Check className="h-3 w-3 text-emerald-600" />
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">{feature.name}</span>
                            <span className="text-xs text-slate-500 ml-1">({feature.description})</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>

                <CardFooter className="pt-2 mt-auto">
                  <div className="w-full space-y-3">
                  {plan.name === "Free" && (
                      <div className="bg-emerald-50 text-emerald-700 px-3 py-2 rounded-lg text-sm text-center font-medium border border-emerald-100">
                        {t('no-credit-card-required')}
                        <br />{t('cancel-anytime')}
                      </div>
                    )}
                    <Button
                      size="lg"
                      className={`w-full ${
                        isPopular
                          ? "bg-primary hover:bg-primary/90"
                          : plan.name === 'Enterprise'
                            ? "bg-slate-800 hover:bg-slate-700"
                            : ""
                      }`}
                      onClick={() => handleSubscribe(isAnnual ? plan.yearlyPriceId : plan.monthlyPriceId, plan.name)}
                      disabled={
                        loadingStates[isAnnual ? plan.yearlyPriceId : plan.monthlyPriceId] ||
                        (plan.name === 'Free' && currentSubscription !== t('no-active-subscription-0'))
                      }
                    >
                      {loadingStates[isAnnual ? plan.yearlyPriceId : plan.monthlyPriceId] ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Processing...
                        </div>
                      ) : plan.name === 'Free' ? (
                        currentSubscription !== t('no-active-subscription') ? 
                          t('currently-subscribed') : 
                          t('start-free-trial')
                      ) : plan.name === 'Enterprise' ? (
                        t('contact-sales')
                      ) : (
                        t('subscribe-now')
                      )}
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            )
          })}

          {/* Contact Us Card with different styling */}
          <Card className="overflow-hidden border-2 border-slate-800/20 bg-slate-800/5 flex flex-col relative transition-all duration-300 hover:shadow-xl">
            <CardHeader className="pb-6 pt-10 text-center">
              <CardTitle className="text-2xl font-bold mb-2">{t('enterprise')}</CardTitle>
              <CardDescription>{t('custom-solutions-for-large-teams')}</CardDescription>
              <div className="mt-6">
                <span className="text-5xl font-bold text-slate-800">{t('custom')}</span>
                <div className="text-sm text-slate-500 mt-2">{t('tailored-pricing-for-your-specific-needs')}</div>
              </div>
            </CardHeader>

            <CardContent className="pb-6 flex-grow">
              <div className="grid gap-4">
                <h3 className="font-semibold text-sm uppercase text-slate-500 tracking-wider mt-2">
                  {t('enterprise-features')}
                </h3>
                <div className="space-y-3">
                  {[
                    {
                      id: "custom_1",
                      name: t('custom-integration'),
                      description: t('tailored-to-your-needs'),
                      icon: Sparkles,
                    },
                    {
                      id: "custom_2",
                      name: t('dedicated-support'),
                      description: t('priority-24-7-support'),
                      icon: HeadsetIcon,
                    },
                    { id: "custom_3", name: t('custom-features'), description: t('built-for-your-workflow'), icon: Bot },
                  ].map((feature) => (
                    <div
                      key={feature.id}
                      className="flex items-center gap-3 p-3 rounded-lg bg-slate-50 border border-slate-100 transition-all hover:bg-slate-100"
                    >
                      <div className="h-9 w-9 rounded-full bg-slate-800/10 flex items-center justify-center flex-shrink-0">
                        <feature.icon className="h-5 w-5 text-slate-800" />
                      </div>
                      <div>
                        <div className="font-medium">{feature.name}</div>
                        <div className="text-xs text-slate-500">{feature.description}</div>
                      </div>
                    </div>
                  ))}
                </div>

                <h3 className="font-semibold text-sm uppercase text-slate-500 tracking-wider mt-4">
                  {t('included-for-all-plans')}
                </h3>

                {/* Common features */}
                <div className="space-y-2 mt-1">
                  {commonFeatures.map((feature) => (
                    <div key={feature.id} className="flex items-center gap-2 p-2">
                      <div className="h-5 w-5 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                        <Check className="h-3 w-3 text-emerald-600" />
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">{feature.name}</span>
                        <span className="text-xs text-slate-500 ml-1">({feature.description})</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>

            <CardFooter className="pt-2 mt-auto">
              <Button
                size="lg"
                className="w-full bg-slate-800 hover:bg-slate-700"
                onClick={() => {
                  window.location.href = "mailto:<EMAIL>?subject=Enterprise Plan Inquiry"
                }}
              >
                {t('contact-sales')}
              </Button>
            </CardFooter>
          </Card>
        </div>
        <SubscriptionDialog />

        <div className="mt-16 text-center">
          <p className="text-slate-500 max-w-2xl mx-auto">
            {t('have-questions-about-which-plan-is-right-for-you-our-team-is-happy-to-help')}
            <br />
            <a href="mailto:<EMAIL>" className="text-primary font-medium hover:underline">
              {t('contact-support')}
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
