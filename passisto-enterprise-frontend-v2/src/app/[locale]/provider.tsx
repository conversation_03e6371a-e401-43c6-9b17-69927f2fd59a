"use client";
import React, { useEffect } from "react";
import { usePathname } from "next/navigation";
import { AppSidebar } from "@/components/sidebar/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import { UserDetailsContext } from "@/context/UserDetailsContext";
import { ChatSessionProvider } from "@/context/ChatSessionContext";
import { ChatSettingsProvider } from "@/context/ChatSettingsContext";

import { useUser } from "@clerk/nextjs";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { Slash } from "lucide-react";
function Provider({ children }: any) {
  const pathname = usePathname();
  const pathNames = pathname.split("/").filter((path) => path);
  const { user } = useUser();

  return (
    <NextThemesProvider attribute="class" defaultTheme="system" enableSystem>
      <UserDetailsContext.Provider value={user}>
        <ChatSettingsProvider>
          <ChatSessionProvider>
            <SidebarProvider>
              <AppSidebar />
              <SidebarInset>
                <header className="flex h-16 fixed z-50 w-full shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 px-4 text-black dark:text-white">
                    <SidebarTrigger className="-ml-1" />
                    <Separator
                      orientation="vertical"
                      className="mr-2 h-4 bg-gray-300 dark:bg-gray-600"
                    />
                    <Breadcrumb>
                      <BreadcrumbList>
                        {pathNames.map((part, index) => {
                          const href = `/${pathNames
                            .slice(0, index + 1)
                            .join("/")}`;
                          const isLast = index === pathNames.length - 1;

                          return (
                            <React.Fragment key={index}>
                              <BreadcrumbItem>
                                <BreadcrumbLink
                                  // href={href}
                                  className="text-sm max-w-[120px] truncate text-gray-700 dark:text-gray-300 hover:text-black dark:hover:text-white"
                                >
                                  {part.replace(/-/g, " ").toUpperCase()}
                                </BreadcrumbLink>
                              </BreadcrumbItem>
                              {!isLast && (
                                <BreadcrumbSeparator className="text-gray-500 dark:text-gray-400">
                                  <Slash />
                                </BreadcrumbSeparator>
                              )}
                            </React.Fragment>
                          );
                        })}
                      </BreadcrumbList>
                    </Breadcrumb>
                  </div>
                </header>
                <main className="mt-14">
                  {children}
                </main>
              </SidebarInset>
            </SidebarProvider>
          </ChatSessionProvider>
        </ChatSettingsProvider>
      </UserDetailsContext.Provider>
    </NextThemesProvider>
  );
}

export default Provider;
