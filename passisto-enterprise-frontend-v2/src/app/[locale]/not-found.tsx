"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Home, MoveLeft } from "lucide-react"
import { Link } from '@/i18n/nvigation';
import Image from "next/image"
import { motion } from "framer-motion"
import { useTranslations } from "next-intl"; // Import useTranslations

export default function GlobalNotFound() {
  const t = useTranslations("NotFoundPage"); // Initialize useTranslations

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white flex flex-col">
      {/* Navigation Bar */}
      <nav className="border-b bg-white py-4 px-6 flex justify-between items-center sticky top-0 z-10 shadow-sm">
        <Link href="/enterprise/dashboard" className="hover:opacity-90 transition-opacity">
          <Image src="/passisto_logo.png" alt="Passisto Logo" width={150} height={40} priority />
        </Link>
      </nav>

      {/* Not Found Content */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="max-w-3xl w-full mx-auto">
          <motion.div
            className="flex flex-col md:flex-row items-center gap-8 md:gap-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {/* 404 Illustration */}
            <div className="w-full md:w-1/2 flex justify-center">
              <motion.div
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                transition={{
                  duration: 0.5,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "reverse",
                  repeatDelay: 5,
                }}
              >
                <Image
                  src="/404.png?height=400&width=400"
                  alt="404 Illustration"
                  width={400}
                  height={400}
                  className="drop-shadow-xl"
                  priority
                />
              </motion.div>
            </div>

            {/* Content */}
            <div className="w-full md:w-1/2 text-center md:text-left">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h1 className="text-7xl font-bold text-primary mb-2">{t("pageTitle")}</h1>
                <h2 className="text-3xl font-bold mb-4">{t("pageHeading")}</h2>
                <p className="text-muted-foreground mb-8 text-lg">
                  {t("pageDescription")}
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <Link href="/enterprise/dashboard">
                    <Button size="lg" className="w-full sm:w-auto">
                      <Home className="mr-2 h-5 w-5" />
                      {t("dashboardButton")}
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.history.back()}
                    className="w-full sm:w-auto"
                  >
                    <MoveLeft className="mr-2 h-5 w-5" />
                    {t("goBackButton")}
                  </Button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Footer */}
      <footer className="py-6 text-center text-sm text-muted-foreground border-t">
        <p>{t("footerText", { year: new Date().getFullYear() })}</p>
      </footer>
    </div>
  )
}