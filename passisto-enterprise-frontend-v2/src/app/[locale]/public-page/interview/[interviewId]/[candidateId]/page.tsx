"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { Lock, AlertCircle } from "lucide-react"
import { Link } from '@/i18n/nvigation';
import axiosInstance from "@/config/axios"
import { CHECK_CANDIDATE_ACCESS } from "@/utils/routes"
import { AxiosError } from "axios"
import { useTranslations } from "next-intl"

export default function PublicInterviewPage() {

  const t = useTranslations()
  const params = useParams<{ interviewId: string; candidateId: string }>()
  const [accessCode, setAccessCode] = useState("")
  const [email, setEmail] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!email || !accessCode) {
      setError(t('please-enter-both-email-and-access-code'))
      return
    }

    setLoading(true)

    try {
      const response = await axiosInstance.post(CHECK_CANDIDATE_ACCESS(params.interviewId, params.candidateId), {
        email: email,
        password: accessCode
      })
      if(response.status == 200 ){
        toast(t('access-granted'),{
          description: t('redirecting-you-to-your-interview'),
        })
        router.push(`/public-page/interview/${params.interviewId}/${params.candidateId}/take`)

      }else {
        setError(t('invalid-email-or-access-code-please-check-your-credentials-and-try-again'))
      }
    } catch (error: any) {
      console.log(error.response.status);
      if(error.response.status==401){
        setError(t('invalid-email-or-access-code-please-check-your-credentials-and-try-again-0'))
      }else{
      setError(t('an-error-occurred-please-try-again-later'))
      }
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Lock className="h-6 w-6 text-primary" />
              </div>
            </div>
            <CardTitle className="text-2xl text-center">{t('interview-access')}</CardTitle>
            <CardDescription className="text-center">
              {t('enter-your-email-and-access-code-to-start-your-interview')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="bg-destructive/10 text-destructive rounded-md p-3 flex items-start mb-4">
                <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-sm">{error}</p>
              </div>
            )}
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="email">{t('email')}</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={loading}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="accessCode">{t('access-code')}</Label>
                  <Input
                    id="accessCode"
                    type="text"
                    placeholder={t('enter-the-code-from-your-invitation')}
                    value={accessCode}
                    onChange={(e) => setAccessCode(e.target.value)}
                    disabled={loading}
                    required
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Verifying..." : t('access-interview')}
                </Button>
              </div>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col items-center">
            <p className="text-sm text-muted-foreground mt-2">
              {t('having-trouble-contact-support-at')}{" "}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
              </a>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
