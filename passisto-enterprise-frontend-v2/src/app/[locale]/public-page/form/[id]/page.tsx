"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Sun, Moon, Laptop2 } from "lucide-react";
import { FormSubmission } from "@/app/[locale]/enterprise/ai-agents/form-builder/_components/form-submission";
import { StoredForm } from "@/app/[locale]/enterprise/ai-agents/form-builder/_lib/types";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import ThemeToggle from "@/components/theme-toggle";

export default function PublicFormPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [form, setForm] = useState<StoredForm | null>(null);
  const [loading, setLoading] = useState(true);
  const t = useTranslations();
  const { theme, setTheme, systemTheme } = useTheme();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    try {
      const storedForms = localStorage.getItem("forms");
      if (storedForms) {
        const forms = JSON.parse(storedForms) as StoredForm[];
        const foundForm = forms.find((f) => f.id === params.id);
        setForm(foundForm || null);
      }
    } catch (error) {
      console.error("Error loading form:", error);
    } finally {
      setLoading(false);
    }
  }, [params]);

  // Helper for showing the current theme icon
  const currentIcon =
    theme === "dark"
      ? <Moon className="h-5 w-5 text-gray-800 dark:text-yellow-400" />
      : theme === "light"
      ? <Sun className="h-5 w-5 text-yellow-400" />
      : <Laptop2 className="h-5 w-5 text-primary" />;

  return (
    <main className="bg-background min-h-screen">
      {/* Theme Toggle Button */}
      <div className="fixed top-4 right-4 z-50 mt-10">
        <ThemeToggle />
      </div>

      {loading ? (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      ) : !form ? (
        <div className="min-h-screen p-4 md:p-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-2xl font-bold mb-4 text-foreground">
              {t("form-not-found")}
            </h1>
            <p className="mb-6 text-muted-foreground">
              {t("the-form-youre-looking-for-doesnt-exist-or-has-been-deleted")}
            </p>
            <Button onClick={() => router.push("/enterprise/integrations/new")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("back-to-home")}
            </Button>
          </div>
        </div>
      ) : (
        <div className="w-full mx-auto">
          <FormSubmission form={form} />
        </div>
      )}
    </main>
  );
}
