"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, AlertCircle } from "lucide-react"
import axiosInstance from "@/config/axios"
import { FORM_BUILDER_GET_BY_ID } from "@/utils/routes"
import { useAuth } from "@clerk/nextjs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import Link from "next/link"
import { FormSubmission } from "@/app/[locale]/enterprise/ai-agents/form-builder/_components/form-submission"
import { StoredForm } from "@/app/[locale]/enterprise/ai-agents/form-builder/_lib/types"
import ThemeToggle from "@/components/theme-toggle"

export default function PublicFormPage({
  params,
}: {
  params: { companyId: string; id: string }
}) {
  const router = useRouter()
  const { getToken } = useAuth()
  const [form, setForm] = useState<StoredForm | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchForm = async () => {
      setLoading(true)
      setError(null)
      try {
        const token = await getToken?.()
        const response = await axiosInstance.get(FORM_BUILDER_GET_BY_ID(params.id, params.companyId), {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        setForm(response.data)
      } catch (err: any) {
        setError("Failed to load form. It may not exist or you don't have access.")
        setForm(null)
      } finally {
        setLoading(false)
      }
    }
    fetchForm()
  }, [params, getToken])

  const handleBack = () => {
    router.push("/enterprise/ai-agents/form-builder")
  }

  if (loading) {
    return <LoadingState />
  }

  if (error) {
    return <ErrorState error={error} handleBack={handleBack} />
  }

  if (!form) {
    return <NotFoundState handleBack={handleBack} />
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Theme Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>
      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Form Header */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-foreground mb-2">{form.title}</h1>
          {form.description && <p className="text-lg text-muted-foreground max-w-2xl mx-auto">{form.description}</p>}
        </div>

        {/* Form Content */}
        <div className="bg-card rounded-lg shadow-sm border border-border p-8">
          <FormSubmission form={form} companyId={params.companyId} />
        </div>
      </div>

      {/* Powered By Footer */}
      <footer className="mt-16 py-8 border-t border-border bg-background">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <p className="text-sm text-muted-foreground">
            Powered by{" "}
            <Link
              href="https://www.passisto.com/enterprise/en"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary font-medium hover:underline underline-offset-2"
            >
              Passisto Enterprise Solutions
            </Link>
          </p>
        </div>
      </footer>
    </div>
  )
}

function LoadingState() {
  return (
    <div className="min-h-screen bg-background">
      {/* Theme Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Form Header Skeleton */}
        <div className="mb-8 text-center">
          <Skeleton className="h-9 w-64 mx-auto mb-2" />
          <Skeleton className="h-6 w-96 mx-auto" />
        </div>

        {/* Form Content Skeleton */}
        <div className="bg-card rounded-lg shadow-sm border border-border p-8">
          <div className="space-y-6">
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-16 py-8 border-t border-border bg-background">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <Skeleton className="h-4 w-48 mx-auto" />
        </div>
      </footer>
    </div>
  )
}

function ErrorState({ error, handleBack }: { error: string; handleBack: () => void }) {
  return (
    <div className="min-h-screen bg-background">
      {/* Theme Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Error Header */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-destructive mb-2">Error</h1>
          <p className="text-lg text-muted-foreground">Something went wrong while loading the form</p>
        </div>

        {/* Error Content */}
        <div className="bg-card rounded-lg shadow-sm border border-border p-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error Loading Form</AlertTitle>
            <AlertDescription className="mt-2">
              {error}
              <br />
              <Button
                variant="link"
                onClick={handleBack}
                className="p-0 h-auto mt-2 text-destructive underline"
              >
                Go back to forms
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-16 py-8 border-t border-border bg-background">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <p className="text-sm text-muted-foreground">
            Powered by{" "}
            <Link
              href="https://www.passisto.com/enterprise/en"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary font-medium hover:underline underline-offset-2"
            >
              Passisto Enterprise Solutions
            </Link>
          </p>
        </div>
      </footer>
    </div>
  )
}

function NotFoundState({ handleBack }: { handleBack: () => void }) {
  return (
    <div className="min-h-screen bg-background">
      {/* Theme Toggle */}
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Not Found Header */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">Form Not Found</h1>
          <p className="text-lg text-muted-foreground">The form you're looking for doesn't exist</p>
        </div>

        {/* Not Found Content */}
        <div className="bg-card rounded-lg shadow-sm border border-border p-8">
          <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800">
            <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            <AlertTitle className="text-yellow-800 dark:text-yellow-200">Form Not Found</AlertTitle>
            <AlertDescription className="text-yellow-700 dark:text-yellow-300 mt-2">
              The form you are looking for does not exist or has been deleted.
              <br />
              <Button
                variant="link"
                onClick={handleBack}
                className="p-0 h-auto mt-2 text-yellow-700 dark:text-yellow-300 underline"
              >
                Go back to forms
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-16 py-8 border-t border-border bg-background">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <p className="text-sm text-muted-foreground">
            Powered by{" "}
            <Link
              href="https://www.passisto.com/enterprise/en"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary font-medium hover:underline underline-offset-2"
            >
              Passisto Enterprise Solutions
            </Link>
          </p>
        </div>
      </footer>
    </div>
  )
}
