"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import axios from "axios";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";

interface TeamsLayoutProps {
  children: React.ReactNode;
}

export default function FormLayout({ children }: TeamsLayoutProps) {
  

  return (
    <div className="">
      <div className="">
        {children}
      </div>
    </div>
  );
}