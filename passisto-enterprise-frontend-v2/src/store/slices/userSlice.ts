import { createSlice, createAsyncThunk, PayloadAction, ActionReducerMapBuilder } from '@reduxjs/toolkit';
import axiosInstance from '@/config/axios';
import { GET_ALL_USERS, USER_CREATE ,USER_GET_BY_ID, USER_TOGGLE_STATUS, USER_UPDATE, USER_DELETE} from '@/utils/routes';
import { Permission, Role } from './rolePermissionSlice';


// User interface
export interface User {
  id: string
  // name: string
  firstName: string
  lastName: string
  email: string
  roles: Role[]
  teams: Team[]
  extraPermissions: Permission[];
  revokedPermissions: Permission[];
  permissionsCount: number | null;
  isActive: boolean | null;
  createdAt: string
}

// export interface CreateUser {
//   firstName: string;
//   lastName: string;
//   email: string;
//   roles: Role[];
//   teams: Team[];
//   extraPermissions: Permission[];
//   revokedPermissions: Permission[];
// }


// export interface Role{
//   id:string
//   name:string
//   permissions:Permission[]
// }
// export interface Permission {
//   id: string;
//   permission: string;
//   scopeType: string;
//   scopeTarget?: string;
// }

export interface User {
  id: string
  // name: string
  firstName: string
  lastName: string
  email: string
  roles: Role[]
  teams: Team[]
  extraPermissions: Permission[];
  revokedPermissions: Permission[];
  permissionsCount: number | null;
  isActive: boolean | null;
  createdAt: string
}

export interface Team{
  id:string
  name:string
  permissions:Permission[]
}

interface UserState {
  users: User[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  currentUser: User | null;
}

const initialState: UserState = {
  users: [],
  status: 'idle',
  error: null,
  currentUser: null,
};

// Async thunks for CRUD operations using Axios
export const fetchUsers = createAsyncThunk<
  User[],
  string,
  { rejectValue: string }
>('users/fetchUsers', async (token, { rejectWithValue }) => {
  try {
    // Get token from Clerk
  
    
    // Make API request with token in headers
    const response = await axiosInstance.get<User[]>(GET_ALL_USERS, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log(response.data);
    return response.data;
  } catch (error: any) {
    // Fallback to localStorage if API fails
    try {
      
      return  [];
    } catch (localError) {
      return rejectWithValue('Failed to fetch users from API and localStorage');
    }
  }
});

export const addUser = createAsyncThunk(
  'users/addUser', 
  async ({ userData, token }: { userData: any, token: string }) => {
    try {
      const response = await axiosInstance.post(USER_CREATE, userData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      console.log(response.data);
      return response.data;
    } catch (error) {
      console.error("Error in addUser:", error);
      throw new Error('Failed to add user');
    }
  }
);

export const updateUser = createAsyncThunk(
  'users/updateUser', 
  async ({ userData, token, userId }: { userData: any, token: string, userId: string }) => {
    try {
      const response = await axiosInstance.put(`${USER_UPDATE(userId)}`, userData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      console.log(response.data);
      return response.data;
    } catch (error) {
      console.error("Error in updateUser:", error);
      throw new Error('Failed to update user');
    }
  }
);

export const deleteUser = createAsyncThunk(
  'users/deleteUser', 
  async ({ userId, token }: { userId: string, token: string }, { rejectWithValue }) => {
    try {
      await axiosInstance.delete(`${USER_DELETE(userId)}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      return userId;
    } catch (error) {
      console.error("Error deleting user:", error);
      return rejectWithValue('Failed to delete user');
    }
  }
);

// Définir la route pour obtenir un utilisateur par ID


// Thunk pour récupérer un utilisateur par ID
export const getUserById = createAsyncThunk<
  User,
  { userId: string, token: string },
  { rejectValue: string }
>('users/getUserById', async ({ userId, token }, { rejectWithValue }) => {
  try {
    // Faire la requête API avec le token dans les headers
    const response = await axiosInstance.get<User>(USER_GET_BY_ID(userId), {
      headers: {
        Authorization: `Bearer ${token}`
      }
      
    });
    
    console.log('User fetched:', response.data);
    return response.data;
  } catch (error: any) {
    // Essayer de récupérer depuis localStorage si l'API échoue
      return rejectWithValue('Failed to fetch user from API and localStorage');
  }
});

// Add the toggle status thunk
export const toggleUserStatus = createAsyncThunk(
  'users/toggleStatus',
  async ({ userId, token }: { userId: string, token: string }) => {
    try {
      const response = await axiosInstance.patch(`${USER_TOGGLE_STATUS(userId)}`, {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      return response.data;
    } catch (error) {
      console.error("Error toggling user status:", error);
      throw new Error('Failed to toggle user status');
    }
  }
);

const userSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setCurrentUser: (state: UserState, action: PayloadAction<User | null>) => {
      state.currentUser = action.payload;
    },
  },
  extraReducers: (builder: ActionReducerMapBuilder<UserState>) => {
    builder
      // Fetch users
      .addCase(fetchUsers.pending, (state: UserState) => {
        state.status = 'loading';
      })
      .addCase(fetchUsers.fulfilled, (state: UserState, action: PayloadAction<User[]>) => {
        state.status = 'succeeded';
        state.users = action.payload;
      })
      .addCase(fetchUsers.rejected, (state: UserState, action:any) => {
        state.status = 'failed';
        state.error = action.error.message || 'Failed to fetch users';
      })
      // Add user
      .addCase(addUser.fulfilled, (state: UserState, action: PayloadAction<User>) => {
        state.users.push(action.payload);
      })
      // Update user
      .addCase(updateUser.fulfilled, (state: UserState, action: PayloadAction<User>) => {
        const index = state.users.findIndex((user: User) => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
      })
      // Delete user
      .addCase(deleteUser.fulfilled, (state: UserState, action: PayloadAction<string>) => {
        state.users = state.users.filter((user: User) => user.id !== action.payload);
      })
      // Get user by ID
      .addCase(getUserById.pending, (state: UserState) => {
        state.status = 'loading';
      })
      .addCase(getUserById.fulfilled, (state: UserState, action: PayloadAction<User>) => {
        state.status = 'succeeded';
        state.currentUser = action.payload;
        // Mettre à jour l'utilisateur dans le tableau des utilisateurs si nécessaire
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        } else {
          state.users.push(action.payload);
        }
      })
      .addCase(getUserById.rejected, (state: UserState, action: any) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to fetch user';
      })
      // Toggle user status
      .addCase(toggleUserStatus.fulfilled, (state, action) => {
        // Update the user in the state
        if (state.users) {
          state.users = state.users.map(user => 
            user.id === action.payload.id ? action.payload : user
          );
        }
        // Update currentUser if it's the same user
        if (state.currentUser && state.currentUser.id === action.payload.id) {
          state.currentUser = action.payload;
        }
      });
  },
});

export const { setCurrentUser } = userSlice.actions;
export default userSlice.reducer;




