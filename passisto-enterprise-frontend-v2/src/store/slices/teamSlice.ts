import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import axiosInstance from "@/config/axios";
import {
  GET_ALL_TEAMS,
  GET_TEAM_WITH_MEMBERS,
  CREATE_TEAM,
  UPDATE_TEAM,
  DELETE_TEAM,
  ALL_INTEGRATIONS,
  ASSIGN_INTEGRATION_TO_TEAM
} from "@/utils/routes";
import { Role } from "./rolePermissionSlice";

// Définir l'interface Team
export interface Team {
  id: string;
  name: string;
  description: string;
  memberCount?: number;
  createdAt?: string;
  updatedAt?: string;
  permissions?: Permission[];
}

export interface TeamMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: Role[];
}

// Interface for team with members
export interface TeamWithMembers extends Team {
  members: TeamMember[];
}

export interface Permission {
  id: string;
  permissionId: string;
  action: string;
  category: string;
  scopeType: string;
  scopeId: string;
}

// Add this new interface for team creation
export interface CreateTeamDTO {
  name: string;
  description: string;
  permissions: string[]; // Array of permission IDs
}

// Add this new interface for adding members to a team
export interface AddTeamMembersDTO {
  userIds: string[]; // Array of user IDs to add to the team
}

// Define the Integration interfaces
export interface BaseIntegration {
  id: string;
  name: string;
  updateTime: number;
  status: "loading" | "completed" | "failed" | "refreshing" | "not updated";
  opensearchIndexId: string;
  celeryTaskId: string;
  providerType: "jira" | "ftp" | "web";
  firstLoad: boolean;
  createdBy: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

export interface JiraIntegration extends BaseIntegration {
  providerType: "jira";
  jira: {
    id: string;
    domain: string;
    email: string;
    project: string;
  };
  ftp: null;
  web: null;
}

export interface FtpIntegration extends BaseIntegration {
  providerType: "ftp";
  jira: null;
  ftp: {
    id: string;
    isSecure: boolean;
    server: string;
    port: number;
    username: string;
  };
  web: null;
}

export interface WebIntegration extends BaseIntegration {
  providerType: "web";
  jira: null;
  ftp: null;
  web: {
    id: string;
    url: string;
  };
}

export type Integration = JiraIntegration | FtpIntegration | WebIntegration;

// Add interface for assigning integration to team
export interface AssignIntegrationDTO {
  integrationId: string;
  teamId: string;
}

// Update TeamState to include integrations
interface TeamState {
  teams: Team[];
  currentTeam: TeamWithMembers | null;
  integrations: Integration[];
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: TeamState = {
  teams: [],
  currentTeam: null,
  integrations: [],
  status: "idle",
  error: null,
};

// Créer un thunk asynchrone pour récupérer toutes les équipes
export const fetchTeams = createAsyncThunk<
  Team[],
  string | undefined,
  { rejectValue: string }
>("teams/fetchTeams", async (token, { rejectWithValue }) => {
  try {
    // Configurer les en-têtes avec le token si disponible

    const headers = token ? { Authorization: `Bearer ${token}` } : {};
    // Faire la requête API
    const response = await axiosInstance.get<Team[]>(GET_ALL_TEAMS, {
      headers,
    });
    console.log(response.data);
    return response.data;
  } catch (error: any) {
    // Essayer de récupérer depuis localStorage si l'API échoue

      return rejectWithValue("Failed to fetch teams from API");
  }
});

// Thunk pour récupérer une équipe avec ses membres
export const fetchTeamWithMembers = createAsyncThunk<
  TeamWithMembers,
  { teamId: string; token: string | undefined },
  { rejectValue: string }
>(
  "teams/fetchTeamWithMembers",
  async ({ teamId, token }, { rejectWithValue }) => {
    try {
      // Configurer les en-têtes avec le token si disponible
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      // Faire la requête API
      const response = await axiosInstance.get<TeamWithMembers>(
        GET_TEAM_WITH_MEMBERS(teamId),
        {
          headers,
        }
      );

      console.log("Team with members:", response.data);
      return response.data;
    } catch (error: any) {
      console.error("Error fetching team with members:", error);
      return rejectWithValue("Team not found");
    }
  }
);

// Add this new thunk for creating a team
export const createTeam = createAsyncThunk<
  Team,
  { teamData: CreateTeamDTO; token: string },
  { rejectValue: string }
>("teams/createTeam", async ({ teamData, token }, { rejectWithValue }) => {
  try {
    // Configure headers with the token
    const headers = { Authorization: `Bearer ${token}` };

    // Make the API request
    const response = await axiosInstance.post<Team>(CREATE_TEAM, teamData, {
      headers,
    });

    console.log("Team created:", response.data);

    return response.data;
  } catch (error: any) {
    console.error("Error creating team:", error);
    return rejectWithValue(
      error.response?.data?.message || "Failed to create team"
    );
  }
});

// Add this new thunk for updating a team
export const updateTeam = createAsyncThunk<
  Team,
  { teamId: string; teamData: CreateTeamDTO; token: string },
  { rejectValue: string }
>(
  "teams/updateTeam",
  async ({ teamId, teamData, token }, { rejectWithValue }) => {
    try {
      // Configure headers with the token
      const headers = { Authorization: `Bearer ${token}` };

      // Make the API request
      const response = await axiosInstance.put<Team>(
        UPDATE_TEAM(teamId),
        teamData,
        { headers }
      );

      console.log("Team updated:", response.data);

      return response.data;
    } catch (error: any) {
      console.error("Error updating team:", error);
      return rejectWithValue(
        error.response?.data?.message || "Failed to update team"
      );
    }
  }
);

// Add this new thunk for adding members to a team
export const addTeamMembers = createAsyncThunk<
  TeamWithMembers,
  { teamId: string; membersData: AddTeamMembersDTO; token: string },
  { rejectValue: string }
>(
  "teams/addTeamMembers",
  async ({ teamId, membersData, token }, { rejectWithValue }) => {
    try {
      // Configure headers with the token
      const headers = { Authorization: `Bearer ${token}` };

      // Make the API request
      const response = await axiosInstance.post<TeamWithMembers>(
        `${GET_TEAM_WITH_MEMBERS(teamId)}/members`,
        membersData,
        { headers }
      );

      console.log("Team members added:", response.data);

      return response.data;
    } catch (error: any) {
      console.error("Error adding team members:", error);
      return rejectWithValue(
        error.response?.data?.message || "Failed to add team members"
      );
    }
  }
);

// Add this new thunk for removing a member from a team
export const removeTeamMember = createAsyncThunk<
  TeamWithMembers,
  { teamId: string; userId: string; token: string },
  { rejectValue: string }
>(
  "teams/removeTeamMember",
  async ({ teamId, userId, token }, { rejectWithValue }) => {
    try {
      // Configure headers with the token
      const headers = { Authorization: `Bearer ${token}` };

      // Make the API request
      const response = await axiosInstance.delete<TeamWithMembers>(
        `${GET_TEAM_WITH_MEMBERS(teamId)}/members/${userId}`,
        { headers }
      );

      console.log("Team member removed:", response.data);

      return response.data;
    } catch (error: any) {
      console.error("Error removing team member:", error);
      return rejectWithValue(
        error.response?.data?.message || "Failed to remove team member"
      );
    }
  }
);

// Add this new thunk for deleting a team
export const deleteTeam = createAsyncThunk<
  string, // Return the team ID on success
  { teamId: string; token: string },
  { rejectValue: string }
>("teams/deleteTeam", async ({ teamId, token }, { rejectWithValue }) => {
  try {
    // Configure headers with the token
    const headers = { Authorization: `Bearer ${token}` };

    // Make the API request
    await axiosInstance.delete(DELETE_TEAM(teamId), { headers });

    console.log("Team deleted:", teamId);

    return teamId;
  } catch (error: any) {
    console.error("Error deleting team:", error);
    return rejectWithValue(
      error.response?.data?.message || "Failed to delete team"
    );
  }
});

// Add thunk to fetch all integrations
export const fetchIntegrations = createAsyncThunk<
  Integration[],
  string,
  { rejectValue: string }
>("teams/fetchIntegrations", async (token, { rejectWithValue }) => {
  try {
    // Configure headers with the token
    const headers = { Authorization: `Bearer ${token}` };

    // Make the API request
    const response = await axiosInstance.get<Integration[]>(
      ALL_INTEGRATIONS,
      { headers }
    );

    console.log("Integrations fetched:", response.data);
    
    return response.data;
  } catch (error: any) {
    console.error("Error fetching integrations:", error);
    return rejectWithValue(error.response?.data?.message || "Failed to fetch integrations");
  }
});

// Add thunk to assign integration to team
export const assignIntegrationToTeam = createAsyncThunk<
  { success: boolean },
  { integrationId: string; teamId: string; token: string },
  { rejectValue: string }
>(
  "teams/assignIntegrationToTeam",
  async ({ integrationId, teamId, token }, { rejectWithValue }) => {
    try {
      // Configure headers with the token
      const headers = { Authorization: `Bearer ${token}` };

      // Prepare the request body with both integrationId and teamId
      const requestData = {
        integrationId,
        teamId,
      };

      // Make the API request
      const response = await axiosInstance.post(ASSIGN_INTEGRATION_TO_TEAM,
        {
          integrationId: requestData.integrationId,
          groupId: requestData.teamId,
        },
        { headers }
      );

      console.log("Integration assigned to team:", response.data);

      return { success: true };
    } catch (error: any) {
      console.error("Error assigning integration to team:", error);
      return rejectWithValue(
        error.response?.data?.message || "Failed to assign integration to team"
      );
    }
  }
);

// Créer le slice
const teamSlice = createSlice({
  name: "teams",
  initialState,
  reducers: {
    clearCurrentTeam: (state) => {
      state.currentTeam = null;
    },
    updateCurrentTeam: (
      state,
      action: PayloadAction<Partial<TeamWithMembers>>
    ) => {
      if (state.currentTeam) {
        state.currentTeam = {
          ...state.currentTeam,
          ...action.payload,
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Gestion de fetchTeams
      .addCase(fetchTeams.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchTeams.fulfilled, (state, action: PayloadAction<Team[]>) => {
        state.status = "succeeded";
        state.teams = action.payload;
      })
      .addCase(fetchTeams.rejected, (state, action) => {
        state.status = "failed";
        state.error = (action.payload as string) || "Failed to fetch teams";
      })

      // Gestion de fetchTeamWithMembers
      .addCase(fetchTeamWithMembers.pending, (state) => {
        state.status = "loading";
      })
      .addCase(
        fetchTeamWithMembers.fulfilled,
        (state, action: PayloadAction<TeamWithMembers>) => {
          state.status = "succeeded";
          state.currentTeam = action.payload;
        }
      )
      .addCase(fetchTeamWithMembers.rejected, (state, action) => {
        state.status = "failed";
        state.error =
          (action.payload as string) || "Failed to fetch team with members";
      })

      // Add cases for createTeam
      .addCase(createTeam.pending, (state) => {
        state.status = "loading";
      })
      .addCase(createTeam.fulfilled, (state, action: PayloadAction<Team>) => {
        state.status = "succeeded";
        state.teams.push(action.payload);
      })
      .addCase(createTeam.rejected, (state, action) => {
        state.status = "failed";
        state.error = (action.payload as string) || "Failed to create team";
      })

      // Add cases for updateTeam
      .addCase(updateTeam.pending, (state) => {
        state.status = "loading";
      })
      .addCase(updateTeam.fulfilled, (state, action: PayloadAction<Team>) => {
        state.status = "succeeded";
        // Update the team in the teams array
        const index = state.teams.findIndex(
          (team) => team.id === action.payload.id
        );
        if (index !== -1) {
          state.teams[index] = action.payload;
        }
        // Also update currentTeam if it matches
        if (state.currentTeam && state.currentTeam.id === action.payload.id) {
          state.currentTeam = {
            ...state.currentTeam,
            ...action.payload,
          };
        }
      })
      .addCase(updateTeam.rejected, (state, action) => {
        state.status = "failed";
        state.error = (action.payload as string) || "Failed to update team";
      })

      // Add cases for addTeamMembers
      .addCase(addTeamMembers.pending, (state) => {
        state.status = "loading";
      })
      .addCase(
        addTeamMembers.fulfilled,
        (state, action: PayloadAction<TeamWithMembers>) => {
          state.status = "succeeded";
          state.currentTeam = action.payload;

          // Update the member count in the teams array
          const index = state.teams.findIndex(
            (team) => team.id === action.payload.id
          );
          if (index !== -1) {
            state.teams[index] = {
              ...state.teams[index],
              memberCount: action.payload.members.length,
            };
          }
        }
      )
      .addCase(addTeamMembers.rejected, (state, action) => {
        state.status = "failed";
        state.error =
          (action.payload as string) || "Failed to add team members";
      })

      // Add cases for removeTeamMember
      .addCase(removeTeamMember.pending, (state) => {
        state.status = "loading";
      })
      .addCase(
        removeTeamMember.fulfilled,
        (state, action: PayloadAction<TeamWithMembers>) => {
          state.status = "succeeded";
          state.currentTeam = action.payload;

          // Update the member count in the teams array
          const index = state.teams.findIndex(
            (team) => team.id === action.payload.id
          );
          if (index !== -1) {
            state.teams[index] = {
              ...state.teams[index],
              memberCount: action.payload.members.length,
            };
          }
        }
      )
      .addCase(removeTeamMember.rejected, (state, action) => {
        state.status = "failed";
        state.error =
          (action.payload as string) || "Failed to remove team member";
      })

      // Add cases for deleteTeam
      .addCase(deleteTeam.pending, (state) => {
        state.status = "loading";
      })
      .addCase(deleteTeam.fulfilled, (state, action: PayloadAction<string>) => {
        state.status = "succeeded";
        // Remove the team from the teams array
        state.teams = state.teams.filter((team) => team.id !== action.payload);
        // Clear currentTeam if it matches the deleted team
        if (state.currentTeam && state.currentTeam.id === action.payload) {
          state.currentTeam = null;
        }
      })
      .addCase(deleteTeam.rejected, (state, action) => {
        state.status = "failed";
        state.error = (action.payload as string) || "Failed to delete team";
      })

      // Add cases for fetchIntegrations
      .addCase(fetchIntegrations.pending, (state) => {
        state.status = "loading";
      })
      .addCase(
        fetchIntegrations.fulfilled,
        (state, action: PayloadAction<Integration[]>) => {
          state.status = "succeeded";
          state.integrations = action.payload;
        }
      )
      .addCase(fetchIntegrations.rejected, (state, action) => {
        state.status = "failed";
        state.error =
          (action.payload as string) || "Failed to fetch integrations";
      })

      // Add cases for assignIntegrationToTeam
      .addCase(assignIntegrationToTeam.pending, (state) => {
        state.status = "loading";
      })
      .addCase(assignIntegrationToTeam.fulfilled, (state) => {
        state.status = "succeeded";
        // No need to update state as we're just assigning an integration to a team
      })
      .addCase(assignIntegrationToTeam.rejected, (state, action) => {
        state.status = "failed";
        state.error =
          (action.payload as string) || "Failed to assign integration to team";
      });
  },
});

export const { clearCurrentTeam, updateCurrentTeam } = teamSlice.actions;
export default teamSlice.reducer;
