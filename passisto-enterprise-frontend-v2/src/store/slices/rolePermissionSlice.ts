import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosInstance from '@/config/axios';
import { GET_ROLES_AND_PERMISSIONS } from '@/utils/routes';

// Types
// export interface Permission {
//   id: string;
//   name: string;
// }

export interface Permission {
  id: string;
  action: string;
  category?: string | null;
  scopeType: string;
  scopeId?: string;
}
 export interface PermissionWithSource extends Permission {
  source: {
    type: "role" | "team" | "extra";
    name: string;
  };
}
export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}

export interface PermissionCategory {
  category: string;
  permissions: Permission[];
}

export interface RolePermissionState {
  roles: Role[];
  permissionsByCategory: PermissionCategory[];
  scopes: string[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: RolePermissionState = {
  roles: [],
  permissionsByCategory: [],
  scopes: [],
  loading: false,
  error: null,
};

// Async thunk to fetch roles and permissions
export const fetchRolesPermissions = createAsyncThunk<
  { roles: Role[]; permissionsByCategory: PermissionCategory[]; scopes: string[] },
  string | undefined,
  { rejectValue: string }
>('rolePermission/fetchRolesPermissions', async (token, { rejectWithValue }) => {
  try {
    // Make API request with token in headers if available
    const headers = token ? { Authorization: `Bearer ${token}` } : {};
    
    const response = await axiosInstance.get(GET_ROLES_AND_PERMISSIONS, {
      headers
    });
    console.log(response.data);
    if (response.data.success) {
      return {
        roles: response.data.roles,
        permissionsByCategory: response.data.permissionsByCategory,
        scopes: response.data.scopes
      };
    } else {
      return rejectWithValue('Failed to fetch roles and permissions');
    }
  } catch (error: any) {
    return rejectWithValue(error.message || 'Failed to fetch roles and permissions');
  }
});

// Create slice
const rolePermissionSlice = createSlice({
  name: 'rolePermission',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchRolesPermissions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRolesPermissions.fulfilled, (state, action) => {
        state.roles = action.payload.roles;
        state.permissionsByCategory = action.payload.permissionsByCategory;
        state.scopes = action.payload.scopes;
        state.loading = false;
      })
      .addCase(fetchRolesPermissions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch roles and permissions';
      });
  },
});

export default rolePermissionSlice.reducer;
