import { configureStore } from '@reduxjs/toolkit';
import userReducer from './slices/userSlice';
import rolePermissionReducer from './slices/rolePermissionSlice';
import teamReducer from './slices/teamSlice';

export const store = configureStore({
  reducer: {
    users: userReducer,
    rolePermission: rolePermissionReducer,
    teams: teamReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
