"use client";

import { useState } from "react";
import { Trash2 } from "lucide-react";
import type { Integration } from "./integrations-table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ToastContainer, toast } from "react-toastify";
import axiosInstance from "@/config/axios";
import {
  WEB_INTEGRATION,
  FTP_INTEGRATION,
  JIRA_INTEGRATION,
} from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import { integrationPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl";

interface DeleteIntegrationDialogProps {
  integration: Integration;
  onDelete: (id: string) => void;
}

export function DeleteIntegrationDialog({
  integration,
  onDelete,
}: DeleteIntegrationDialogProps) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { getToken } = useAuth();
  const { backendUser } = useBackendUser();

  const canDelete =
    (integration.providerType === "ftp" &&
      integrationPermissions.canDeleteFTP(backendUser?.permissions || [])) ||
    (integration.providerType === "jira" &&
      integrationPermissions.canDeleteJira(backendUser?.permissions || [])) ||
    (integration.providerType === "web" &&
      integrationPermissions.canDeleteWeb(backendUser?.permissions || []));

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const token = await getToken();
      let apiEndpoint = "";

      switch (integration.providerType) {
        case "ftp":
          apiEndpoint = `${FTP_INTEGRATION}/${integration.id}`;
          break;
        case "jira":
          apiEndpoint = `${JIRA_INTEGRATION}/${integration.id}`;
          break;
        case "web":
          apiEndpoint = `${WEB_INTEGRATION}/${integration.id}`;
          break;
        default:
          throw new Error(
            `Unsupported provider type: ${integration.providerType}`
          );
      }

      await axiosInstance.delete(apiEndpoint, {
        headers: { Authorization: `Bearer ${token}` },
      });

      toast.success(
        `Integration "${integration.name}" has been successfully deleted.`
      );
      onDelete(integration.id);
      setOpen(false);
    } catch (error) {
      toast.error(`Failed to delete integration "${integration.name}".`);
      console.error("Error deleting integration:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!canDelete) return null;

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline" size="sm"
            className=" justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('delete-integration')}</DialogTitle>
            <DialogDescription>
              {t('are-you-sure-you-want-to-delete-the-integration')}
              {integration.name}?" {t('this-action-cannot-be-undone')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              {t('cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? t('deleting') : t('delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <ToastContainer />
    </>
  );
}
