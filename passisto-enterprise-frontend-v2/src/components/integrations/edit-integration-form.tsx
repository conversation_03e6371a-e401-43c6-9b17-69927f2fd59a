"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import axios from "axios"
import { Loader2, Save, XCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { ToastContainer, toast } from "react-toastify"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import axiosInstance from "@/config/axios";
import { FTP_INTEGRATION, JIRA_INTEGRATION, 
  WEB_INTEGRATION } from "@/utils/routes";
import { useAuth } from "@clerk/nextjs";
import { useTranslations } from "next-intl"


interface EditIntegrationFormProps {
  integration: {
    id: string
    name: string
    providerType: "jira" | "ftp" | "web"
    status: string
    updateTime: number
    createdBy: string
    createdAt: string
    updatedAt: string
    jira?: {
      domain: string
      project: string
      email: string
    }
    ftp?: {
      server: string
      port: number
      username: string
      isSecure: boolean
    }
    web?: {
      url: string
    }
  }
}

export function EditIntegrationForm({ integration }: EditIntegrationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()
  const { getToken } = useAuth();
  const t = useTranslations()

    // Form schemas for different integration types
const jiraSchema = z.object({
  name: z.string().min(2, { message: t('name-must-be-at-least-2-characters') }),
  project: z.string().min(1, { message: t('project-is-required') }),
  domain: z.string().min(1, { message: t('domain-name-is-required') }),
  email: z.string().email({ message: t('please-enter-a-valid-email-address') }),
  token: z.string().optional(),
  updateTime: z.coerce.number().min(1, { message: t('update-time-must-be-at-least-1-day') }),
})

const ftpSchema = z.object({
  name: z.string().min(2, { message: t('name-must-be-at-least-2-characters') }),
  server: z.string().min(1, { message: t('server-ip-is-required') }),
  port: z.coerce.number().min(1, { message: t('port-is-required') }),
  username: z.string().min(1, { message: t('username-is-required') }),
  password: z.string().optional(),
  isSecure: z.boolean(),
  updateTime: z.coerce.number().min(1, { message: t('update-time-must-be-at-least-1-day') }),
})

const webSchema = z.object({
  name: z.string().min(2, { message: t('name-must-be-at-least-2-characters') }),
  url: z.string().url({ message: t('please-enter-a-valid-url') }),
  updateTime: z.coerce.number().min(1, { message: t('update-time-must-be-at-least-1-day') }),
})

  // Prepare form values based on integration type
  const defaultValues: any = {
    name: integration.name,
    updateTime: integration.updateTime,
    ...(integration.jira || integration.ftp || integration.web),
  }

  // Select the appropriate schema based on integration type
  let schema: any
  switch (integration.providerType) {
    case "jira":
      schema = jiraSchema
      break
    case "ftp":
      schema = ftpSchema
      break
    case "web":
      schema = webSchema
      break
    default:
      schema = jiraSchema
  }

  // Create form
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues,
  })

  const onSubmit = async (data: any) => {
    setIsSubmitting(true)

    try {
      let apiUrl = ""
      let payload = {}
      const token = await getToken();
      const headers = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
      // Prepare API URL and payload based on the integration type
      if (integration.providerType === "jira") {
        apiUrl = `${JIRA_INTEGRATION}/${integration.id}`
        payload = {
          ...data,
          providerType: "jira",
        }
      } else if (integration.providerType === "ftp") {
        apiUrl = `${FTP_INTEGRATION}/${integration.id}`
        payload = {
          ...data,
          providerType: "ftp",
        }
      } else if (integration.providerType === "web") {
        apiUrl = `${WEB_INTEGRATION}/${integration.id}`
        payload = {
          ...data,
          providerType: "web",
        }
      }

      // Make API call
      await axiosInstance.put(apiUrl, payload, headers)

      // Show success notification
      toast.success(`"${data.name}" integration updated successfully!`, {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      })

      // Redirect after successful update
      router.push(`/enterprise/integrations/${integration.id}`)
    } catch (error) {
      // Show error notification with specific message if available
      const errorMessage =
        axios.isAxiosError(error) && error.response?.status === 400 && error.response?.data?.error
          ? error.response.data.error
          : `Failed to update "${data.name}" integration. Please try again.`
      toast.error(errorMessage, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      })

      console.error("Integration update failed:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderForm = () => {
    switch (integration.providerType) {
      case "jira":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('integration-name')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="project"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('project-key')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="domain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('domain-name')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('email')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="token"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('api-token')}</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder={t('leave-blank-to-keep-current-token')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="updateTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('update-frequency-days')}</FormLabel>
                  <FormControl>
                    <Input type="number" min={1} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      case "ftp":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="server"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('server')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="port"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('port')}</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('username')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('password')}</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder={t('leave-blank-to-keep-current-password')} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isSecure"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('secure-connection')}</FormLabel>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="updateTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('update-frequency-days-0')}</FormLabel>
                  <FormControl>
                    <Input type="number" min={1} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      case "web":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="updateTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('update-frequency-days')}</FormLabel>
                  <FormControl>
                    <Input type="number" min={1} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold">{t('edit-integration')}</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {renderForm()}
                <div className="flex justify-end gap-4">
                  <Button type="button" variant="outline" onClick={() => router.push(`/enterprise/integrations/${integration.id}`)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    {t('cancel')}
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Save className="mr-2 h-4 w-4" />
                    {t('save-changes')}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
      <ToastContainer />
    </>
  )
}

