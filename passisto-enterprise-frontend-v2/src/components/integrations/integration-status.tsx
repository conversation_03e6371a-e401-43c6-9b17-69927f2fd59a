import { Badge } from "@/components/ui/badge"
import { useTranslations } from "next-intl";

interface IntegrationStatusProps {
  status: string
}

export function IntegrationStatus({ status }: IntegrationStatusProps) {

  const t = useTranslations()

  const statusMap: Record<
    string,
    { label: string; variant: "default" | "secondary" | "destructive" | "outline" | null; customClass?: string }
  > = {
    loading: {
      label: t('loading'),
      variant: null,
      customClass: "bg-blue-500 hover:bg-blue-600 text-white",
    },
    completed: {
      label: t('completed'),
      variant: null,
      customClass: "bg-green-500 hover:bg-green-600 text-white", // Green for completed
    },
    failed: {
      label: t('failed'),
      variant: "destructive",
    },
    refreshing: {
      label: t('refreshing'),
      variant: null,
      customClass: "bg-cyan-500 hover:bg-cyan-600 text-white", // Light blue for refreshing
    },
    not_updated: {
      label: t('not-updated-0'),
      variant: null,
      customClass: "bg-yellow-500 hover:bg-yellow-600 text-white", // Yellow for not updated
    },
  }

  const { label, variant, customClass } = statusMap[status] || {
    label: status,
    variant: "outline",
  }

  return (
    <Badge variant={variant} className={customClass || ""}>
      {label}
    </Badge>
  )
}

