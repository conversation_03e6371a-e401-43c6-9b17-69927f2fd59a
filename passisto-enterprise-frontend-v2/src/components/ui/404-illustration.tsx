"use client"

import { motion } from "framer-motion"
import { useTheme } from "next-themes"

export function NotFoundIllustration() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  const primaryColor = isDark ? "#a78bfa" : "#6d28d9"
  const secondaryColor = isDark ? "#4c1d95" : "#8b5cf6"
  const backgroundColor = isDark ? "#1e1b4b" : "#f5f3ff"
  const textColor = isDark ? "#f5f3ff" : "#1e1b4b"

  return (
    <svg
      width="400"
      height="400"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-auto max-w-md"
    >
      <motion.circle
        cx="200"
        cy="200"
        r="150"
        fill={backgroundColor}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
      />

      <motion.g
        initial={{ y: 10 }}
        animate={{ y: 0 }}
        transition={{
          duration: 2,
          repeat: Number.POSITIVE_INFINITY,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
      >
        <motion.path
          d="M130 180H170V260H130V180Z"
          fill={primaryColor}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        />
        <motion.path
          d="M230 180H270V260H230V180Z"
          fill={primaryColor}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        />
        <motion.path
          d="M170 180L230 180L200 140L170 180Z"
          fill={secondaryColor}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        />
      </motion.g>

      <motion.text
        x="200"
        y="300"
        textAnchor="middle"
        fill={textColor}
        fontWeight="bold"
        fontSize="48"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        404
      </motion.text>
    </svg>
  )
}
