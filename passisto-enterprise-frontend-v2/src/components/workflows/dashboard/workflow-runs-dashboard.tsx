"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { WorkflowRunsStats } from "./workflow-runs-stats"
import { WorkflowRunsChart } from "./workflow-runs-chart"
import { WorkflowRunsTable, type WorkflowRun } from "@/components/workflows/workflow-runs-table"
import { WorkflowRunDetail } from "@/components/workflows/workflow-run-detail"
import { workflowRunApi } from "@/services/workflowRunApi"
import { toast } from "@/hooks/use-toast"
import { HistoryIcon, SearchIcon, RefreshCwIcon } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface WorkflowRunsDashboardProps {
  readonly initialRun?: WorkflowRun | null
  readonly isInitiallyLoading?: boolean
}

export function WorkflowRunsDashboard({ initialRun = null, isInitiallyLoading = false }: WorkflowRunsDashboardProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [allWorkflowRuns, setAllWorkflowRuns] = useState<WorkflowRun[]>([])
  const [filteredWorkflowRuns, setFilteredWorkflowRuns] = useState<WorkflowRun[]>([])
  const [selectedWorkflowRun, setSelectedWorkflowRun] = useState<WorkflowRun | null>(null)
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  // Set initial run if provided
  useEffect(() => {
    if (initialRun) {
      setSelectedWorkflowRun(initialRun)
    }
  }, [initialRun])

  // Load workflow runs on page load
  useEffect(() => {
    setIsLoading(true)
    fetchWorkflowRuns()
  }, [isInitiallyLoading])

  // Filter workflow runs when tab or search changes
  useEffect(() => {
    filterWorkflowRuns()
  }, [allWorkflowRuns, activeTab, searchQuery])

  const fetchWorkflowRuns = async () => {
    try {
      setIsLoading(true)
      const runs = await workflowRunApi.getAllWorkflowRuns()
      setAllWorkflowRuns(runs)
    } catch (error) {
      console.error("Error fetching workflow runs:", error)
      toast({
        title: "Error",
        description: "Failed to load workflow runs. Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const filterWorkflowRuns = () => {
    let filtered = [...allWorkflowRuns]

    // Filter by tab
    if (activeTab === "running") {
      filtered = filtered.filter(run => run.status === "RUNNING" || run.status === "PENDING")
    } else if (activeTab === "completed") {
      filtered = filtered.filter(run => run.status === "SUCCESS")
    } else if (activeTab === "failed") {
      filtered = filtered.filter(run => run.status === "FAILED")
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(run =>
        (run.workflow?.name?.toLowerCase().includes(query)) ||
        run.id.toLowerCase().includes(query)
      )
    }

    setFilteredWorkflowRuns(filtered)
  }

  // Handle run selection for viewing details
  const handleViewRun = async (run: WorkflowRun) => {
    try {
      // Get full details with assignee information
      const fullRunDetails = await workflowRunApi.getWorkflowRunWithDetails(run.id)
      setSelectedWorkflowRun(fullRunDetails)
    } catch (error) {
      console.error("Error fetching workflow run details:", error)
      toast({
        title: "Error",
        description: "Failed to load workflow run details. Please try again later.",
        variant: "destructive",
      })
    }
  }

  // Handle re-running a workflow
  const handleRerunWorkflow = async (workflowId: string) => {
    try {
      const workflowRun = await workflowRunApi.createWorkflowRun(workflowId)
      toast({
        title: "Success",
        description: "Workflow execution started successfully.",
      })

      // Refresh the list
      await fetchWorkflowRuns()

      // View the new run
      handleViewRun(workflowRun)
    } catch (error) {
      console.error("Error starting workflow execution:", error)
      toast({
        title: "Error",
        description: "Failed to start workflow execution. Please try again later.",
        variant: "destructive",
      })
    }
  }

  // Reset selected workflow run to show the list
  const handleBackToList = () => {
    setSelectedWorkflowRun(null)
  }

  // Determine what content to render based on state
  const renderContent = () => {
    // Loading state
    if (isLoading && !selectedWorkflowRun) {
      return (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
          <Skeleton className="h-[200px] w-full" />
          <Skeleton className="h-[400px] w-full" />
        </div>
      );
    }

    // Selected workflow run detail view
    if (selectedWorkflowRun) {
      return <WorkflowRunDetail workflowRun={selectedWorkflowRun} onBack={handleBackToList} />;
    }

    // Default dashboard view
    return (
      <>
        {/* Stats Cards */}
        <WorkflowRunsStats workflowRuns={allWorkflowRuns} />

        {/* Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Workflow Runs Over Time</CardTitle>
            <CardDescription>Success and failure rates over the past 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <WorkflowRunsChart workflowRuns={allWorkflowRuns} />
          </CardContent>
        </Card>

        {/* Runs Table */}
        <Card>
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <CardTitle>Recent Workflow Runs</CardTitle>
              <div className="relative w-full md:w-64">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search workflows..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="running">Running</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
                <TabsTrigger value="failed">Failed</TabsTrigger>
              </TabsList>
              <TabsContent value="all"></TabsContent>
              <TabsContent value="running"></TabsContent>
              <TabsContent value="completed"></TabsContent>
              <TabsContent value="failed"></TabsContent>
            </Tabs>
          </CardHeader>
          <CardContent>
            <WorkflowRunsTable
              workflowRuns={filteredWorkflowRuns}
              onViewRun={handleViewRun}
              onRerunWorkflow={handleRerunWorkflow}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>
      </>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <HistoryIcon className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Workflow Runs</h1>
        </div>
        {selectedWorkflowRun ? (
          <Button variant="outline" onClick={handleBackToList}>
            Back to Dashboard
          </Button>
        ) : (
          <Button variant="outline" onClick={fetchWorkflowRuns}>
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      {renderContent()}
    </div>
  )
}
