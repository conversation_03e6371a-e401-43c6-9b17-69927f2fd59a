"use client"

import { useState, useEffect } from "react"
import { type WorkflowRun } from "@/components/workflow-runs-table"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { addDays, format, startOfDay, subDays } from "date-fns"

interface WorkflowRunsChartProps {
  workflowRuns: WorkflowRun[]
}

interface ChartData {
  date: string
  successful: number
  failed: number
  running: number
}

export function WorkflowRunsChart({ workflowRuns }: WorkflowRunsChartProps) {
  const [chartData, setChartData] = useState<ChartData[]>([])
  
  useEffect(() => {
    // Generate data for the last 7 days
    const data: ChartData[] = []
    const today = startOfDay(new Date())
    
    // Create entries for the last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = subDays(today, i)
      const dateStr = format(date, "MMM dd")
      
      data.push({
        date: dateStr,
        successful: 0,
        failed: 0,
        running: 0
      })
    }
    
    // Count workflow runs by date and status
    workflowRuns.forEach(run => {
      const runDate = new Date(run.createdAt)
      const dayIndex = 6 - Math.max(0, Math.min(6, Math.floor((today.getTime() - runDate.getTime()) / (1000 * 60 * 60 * 24))))
      
      if (dayIndex >= 0 && dayIndex < 7) {
        if (run.status === "SUCCESS") {
          data[dayIndex].successful += 1
        } else if (run.status === "FAILED") {
          data[dayIndex].failed += 1
        } else if (run.status === "RUNNING" || run.status === "PENDING") {
          data[dayIndex].running += 1
        }
      }
    })
    
    setChartData(data)
  }, [workflowRuns])

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="successful" name="Successful" fill="#10b981" />
          <Bar dataKey="failed" name="Failed" fill="#ef4444" />
          <Bar dataKey="running" name="Running" fill="#3b82f6" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
