"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { type WorkflowRun } from "@/components/workflow-runs-table"
import { CheckCircle, XCircle, Clock } from "lucide-react"

interface WorkflowRunsStatsProps {
  workflowRuns: WorkflowRun[]
}

export function WorkflowRunsStats({ workflowRuns }: WorkflowRunsStatsProps) {
  // Calculate statistics
  const totalRuns = workflowRuns.length
  const successfulRuns = workflowRuns.filter(run => run.status === "SUCCESS").length
  const failedRuns = workflowRuns.filter(run => run.status === "FAILED").length
  const runningRuns = workflowRuns.filter(run => run.status === "RUNNING" || run.status === "PENDING").length
  
  const successRate = totalRuns > 0 ? Math.round((successfulRuns / totalRuns) * 100) : 0
  const failureRate = totalRuns > 0 ? Math.round((failedRuns / totalRuns) * 100) : 0
  
  // Calculate average duration for completed runs
  const completedRuns = workflowRuns.filter(run => run.status === "SUCCESS" || run.status === "FAILED")
  let avgDuration = "N/A"
  
  if (completedRuns.length > 0) {
    const durations = completedRuns.map(run => {
      if (run.startedAt && run.finishedAt) {
        return new Date(run.finishedAt).getTime() - new Date(run.startedAt).getTime()
      }
      return 0
    }).filter(duration => duration > 0)
    
    if (durations.length > 0) {
      const avgMs = durations.reduce((sum, duration) => sum + duration, 0) / durations.length
      const avgMinutes = Math.floor(avgMs / 60000)
      const avgSeconds = Math.floor((avgMs % 60000) / 1000)
      avgDuration = `${avgMinutes}m ${avgSeconds}s`
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{successRate}%</div>
          <p className="text-xs text-muted-foreground">
            {successfulRuns} successful out of {totalRuns} total runs
          </p>
          <div className="mt-3 h-2 w-full rounded-full bg-gray-100">
            <div 
              className="h-2 rounded-full bg-green-500" 
              style={{ width: `${successRate}%` }}
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Failure Rate</CardTitle>
          <XCircle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{failureRate}%</div>
          <p className="text-xs text-muted-foreground">
            {failedRuns} failed out of {totalRuns} total runs
          </p>
          <div className="mt-3 h-2 w-full rounded-full bg-gray-100">
            <div 
              className="h-2 rounded-full bg-red-500" 
              style={{ width: `${failureRate}%` }}
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Current Status</CardTitle>
          <Clock className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{runningRuns} Running</div>
          <p className="text-xs text-muted-foreground">
            Average duration: {avgDuration}
          </p>
          <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
            <div className="flex flex-col items-center">
              <span className="font-medium text-green-500">{successfulRuns}</span>
              <span className="text-muted-foreground">Successful</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="font-medium text-red-500">{failedRuns}</span>
              <span className="text-muted-foreground">Failed</span>
            </div>
            <div className="flex flex-col items-center">
              <span className="font-medium text-blue-500">{runningRuns}</span>
              <span className="text-muted-foreground">Running</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
