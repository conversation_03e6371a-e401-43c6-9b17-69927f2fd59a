"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Upload, X, FileText, FileImage, FileAudio, FileVideo, File as FileIcon } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { useAuth } from "@clerk/nextjs"
import axiosInstance from "@/config/axios"

interface FileUploadProps {
  onFileUploaded: (fileData: any) => void
  nodeRunId: string
  accept?: string
  maxSize?: number // in MB
  className?: string
}

export function FileUpload({ onFileUploaded, nodeRunId, accept, maxSize = 50, className }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [uploadComplete, setUploadComplete] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { getToken } = useAuth()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0]

      // Check file size
      if (selectedFile.size > maxSize * 1024 * 1024) {
        toast({
          title: "File too large",
          description: `Maximum file size is ${maxSize}MB`,
          variant: "destructive"
        })
        return
      }

      // Reset upload complete state when a new file is selected
      setUploadComplete(false)
      setFile(selectedFile)
    }
  }

  const handleUpload = async (selectedFile?: File) => {
    const fileToUpload = selectedFile || file
    if (!fileToUpload || !nodeRunId) return

    setUploading(true)
    setProgress(0)

    try {
      const formData = new FormData()
      formData.append("file", fileToUpload)
      formData.append("nodeRunId", nodeRunId)

      const token = await getToken()
      
      const response = await axiosInstance.post("/files/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentComplete = Math.round((progressEvent.loaded / progressEvent.total) * 100)
            setProgress(percentComplete)
          }
        }
      })

      onFileUploaded(response.data)
      setUploadComplete(true)
      toast({
        title: "File uploaded",
        description: "File has been uploaded successfully"
      })
      setUploading(false)
    } catch (error) {
      console.error("Upload error:", error)
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file",
        variant: "destructive"
      })
      setUploading(false)
    }
  }

  const cancelUpload = () => {
    setFile(null)
    setUploadComplete(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const getFileIcon = () => {
    if (!file) return <Upload className="h-5 w-5" />

    const type = file.type
    if (type.startsWith("image/")) return <FileImage className="h-5 w-5" />
    if (type.startsWith("audio/")) return <FileAudio className="h-5 w-5" />
    if (type.startsWith("video/")) return <FileVideo className="h-5 w-5" />
    if (type.includes("pdf")) return <FileText className="h-5 w-5" />
    return <FileIcon className="h-5 w-5" />
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-col space-y-2">
        <Label htmlFor="file-upload">Upload File</Label>
        <div className="flex items-center gap-2">
          <Input
            ref={fileInputRef}
            id="file-upload"
            type="file"
            accept={accept}
            onChange={handleFileChange}
            disabled={uploading}
            className="flex-1"
          />
          {file && !uploading && (
            <Button variant="outline" size="icon" onClick={cancelUpload} title="Cancel">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {file && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm">
              {getFileIcon()}
              <span className="truncate max-w-[200px]">{file.name}</span>
              <span className="text-xs text-muted-foreground">
                ({(file.size / (1024 * 1024)).toFixed(2)} MB)
              </span>
            </div>
            {!uploading && (
              <Button onClick={() => handleUpload()} size="sm">
                Upload
              </Button>
            )}
          </div>

          {uploading && (
            <div className="space-y-1">
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-center text-muted-foreground">{progress}% uploaded</p>
            </div>
          )}

          {uploadComplete && !uploading && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-green-700 text-xs flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>File uploaded successfully! You can now proceed with the workflow.</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
