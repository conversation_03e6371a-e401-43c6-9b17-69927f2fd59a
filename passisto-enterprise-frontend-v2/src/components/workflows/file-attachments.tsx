"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Paperclip, FileText, FileImage, FileAudio, FileVideo, File as FileIcon, Download, Trash2 } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { useAuth } from "@clerk/nextjs"
import { formatDistanceToNow } from "date-fns"
import api from "@/config/axios-workflows"

interface FileAttachmentsProps {
  nodeRunId: string
  onFileDeleted?: () => void
  className?: string
  readOnly?: boolean
}

interface FileData {
  id: string
  filename: string
  originalName: string
  mimetype: string
  size: number
  url: string
  createdAt: string
}

export function FileAttachments({ nodeRunId, onFileDeleted, className, readOnly = false }: FileAttachmentsProps) {
  const [files, setFiles] = useState<FileData[]>([])
  const [loading, setLoading] = useState(true)
  const { getToken } = useAuth()

  useEffect(() => {
    if (nodeRunId) {
      fetchFiles()
    }
  }, [nodeRunId])

  const fetchFiles = async () => {
    try {
      setLoading(true)
      const token = await getToken()

      const response = await api.get(`/files/node-run/${nodeRunId}`, {
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      })

      setFiles(response.data)
    } catch (error) {
      console.error("Error fetching files:", error)
      toast({
        title: "Error",
        description: "Failed to load file attachments",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDownload = (file: FileData) => {
    window.open(`/files/download/${file.filename}`, '_blank')
  }

  const handleDelete = async (fileId: string) => {
    try {
      const token = await getToken()

      await api.delete(`/files/${fileId}`, {
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      })

      setFiles(files.filter(f => f.id !== fileId))
      toast({
        title: "File deleted",
        description: "File has been deleted successfully"
      })

      if (onFileDeleted) {
        onFileDeleted()
      }
    } catch (error) {
      console.error("Error deleting file:", error)
      toast({
        title: "Error",
        description: "Failed to delete file",
        variant: "destructive"
      })
    }
  }

  const getFileIcon = (mimetype: string) => {
    if (mimetype.startsWith("image/")) return <FileImage className="h-5 w-5" />
    if (mimetype.startsWith("audio/")) return <FileAudio className="h-5 w-5" />
    if (mimetype.startsWith("video/")) return <FileVideo className="h-5 w-5" />
    if (mimetype.includes("pdf")) return <FileText className="h-5 w-5" />
    return <FileIcon className="h-5 w-5" />
  }

  if (loading) {
    return <div className="text-sm text-muted-foreground">Loading attachments...</div>
  }

  if (files.length === 0) {
    return <div className="text-sm text-muted-foreground">No attachments</div>
  }

  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-2">
        <Paperclip className="h-4 w-4" />
        <h4 className="text-sm font-medium">Attachments ({files.length})</h4>
      </div>

      <div className="space-y-2">
        {files.map(file => (
          <Card key={file.id} className="overflow-hidden">
            <CardContent className="p-3">
              <div className="grid grid-cols-[1fr,auto] gap-2">
                <div className="flex items-center gap-2 min-w-0">
                  {getFileIcon(file.mimetype)}
                  <div className="min-w-0">
                    <p className="text-sm font-medium truncate">{file.originalName}</p>
                    <p className="text-xs text-muted-foreground truncate">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB • {formatDistanceToNow(new Date(file.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>

                <div className="flex gap-1 shrink-0">
                  <Button variant="ghost" size="icon" onClick={() => handleDownload(file)} title="Download">
                    <Download className="h-4 w-4" />
                  </Button>

                  {!readOnly && (
                    <Button variant="destructive" size="icon" onClick={() => handleDelete(file.id)} title="Delete">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
