import { <PERSON><PERSON>, <PERSON>si<PERSON> } from "@xyflow/react"
import { Play } from "lucide-react"

function StartNode({ data, isConnectable }) {
  const isActive = data.isActive || false

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-full border-2 transition-all duration-300
        ${isActive ? "bg-green-100 border-green-500 shadow-green-200" : "bg-white border-green-500"}`}
    >
      <div className="flex items-center">
        <Play className={`mr-2 h-5 w-5 ${isActive ? "text-green-600" : "text-green-500"}`} />
        <div className="font-bold">{data.label}</div>
      </div>
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-green-600" : "bg-green-500"}`}
      />
    </div>
  )
}

export { StartNode }

