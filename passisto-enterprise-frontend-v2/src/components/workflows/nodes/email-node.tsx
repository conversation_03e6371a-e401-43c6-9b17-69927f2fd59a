import { <PERSON><PERSON>, Position } from "@xyflow/react"
import { Mail, Database } from "lucide-react"

function EmailNode({ data, isConnectable }) {
  const isActive = data.isActive || false

  // Check if specific outputs are selected
  const hasSelectedOutputs = data.selectedOutputs && data.selectedOutputs.length > 0

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-md border-2 transition-all duration-300 min-w-[200px]
        ${isActive ? "bg-purple-100 border-purple-500 shadow-purple-200" : "bg-white border-purple-500"}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-purple-600" : "bg-purple-500"}`}
      />

      <div className="flex items-center">
        <Mail className={`mr-2 h-5 w-5 ${isActive ? "text-purple-600" : "text-purple-500"}`} />
        <div className="font-bold">{data.label}</div>
      </div>

      <div className="mt-2">
        <div className="text-xs font-semibold text-gray-600">Email Details:</div>
        <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
          {data.to ? (
            <>
              <span className="font-medium">To:</span> {data.to}
            </>
          ) : (
            <span className="text-gray-400 italic">No recipient configured</span>
          )}
        </div>

        {data.subject && (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            <span className="font-medium">Subject:</span> {data.subject.substring(0, 30)}
            {data.subject.length > 30 ? "..." : ""}
          </div>
        )}

        {data.body && (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            <span className="font-medium">Body:</span> {data.body.substring(0, 30)}
            {data.body.length > 30 ? "..." : ""}
          </div>
        )}
      </div>

      {isActive && (
        <div className="mt-2 text-xs text-center py-1 bg-green-100 rounded text-green-600 animate-pulse">
          Email sent successfully
        </div>
      )}

      {/* Display selected outputs indicator if present */}
      {hasSelectedOutputs && (
        <div className="mt-1 flex items-center gap-1 text-xs text-purple-600">
          <Database className="h-3 w-3" />
          <span>{data.selectedOutputs.length} selected {data.selectedOutputs.length === 1 ? 'output' : 'outputs'}</span>
        </div>
      )}

      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-purple-600" : "bg-purple-500"}`}
      />
    </div>
  )
}

export { EmailNode }

