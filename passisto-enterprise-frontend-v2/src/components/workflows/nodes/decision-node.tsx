import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@xyflow/react"
import { GitBranch, CheckCircle2, XCircle, Database } from "lucide-react"

function DecisionNode({ data, isConnectable }) {
  const isActive = data.isActive ?? false
  const activePath = data.output?.path ?? null

  return (
    <div className="relative">
      {/* Main rectangular shape */}
      <div
        className={`shadow-md border-2 rounded-md transition-all duration-300 w-[220px] h-[100px] relative
          ${isActive ? "bg-yellow-100 border-yellow-500 shadow-yellow-200" : "bg-white border-yellow-500"}`}
      >
        <div
          className="flex flex-col items-center justify-center h-full p-4 overflow-hidden"
        >
          <GitBranch className={`h-5 w-5 mb-2 ${isActive ? "text-yellow-600" : "text-yellow-500"}`} />
          <div className="font-bold text-center">{data.label}</div>
          {data.condition && (
            <div className="mt-1 text-xs text-gray-500 text-center overflow-hidden text-ellipsis max-w-full">
              {data.condition.length > 25 ? `${data.condition.substring(0, 25)}...` : data.condition}
            </div>
          )}

          {/* Display selected inputs indicator if present */}
          {data.selectedOutputs && data.selectedOutputs.length > 0 && (
            <div className="mt-1 flex items-center gap-1 text-xs text-emerald-600">
              <Database className="h-3 w-3" />
              <span>{data.selectedOutputs.length} {data.selectedOutputs.length === 1 ? 'input' : 'inputs'}</span>
            </div>
          )}
        </div>
      </div>

      {/* Handle labels */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-6 text-xs font-medium text-gray-500">
        Input
      </div>

      {/* Yes label (bottom right) */}
      <div className="absolute bottom-0 left-3/4 translate-y-6 -translate-x-1/2 flex items-center flex-col">
        <div className={`text-xs font-medium ${activePath === 'yes' ? 'text-green-600 font-bold' : 'text-gray-500'}`}>
          Yes
        </div>
      </div>

      {/* No label (bottom left) */}
      <div className="absolute bottom-0 left-1/4 translate-y-6 -translate-x-1/2 flex items-center flex-col">
        <div className={`text-xs font-medium ${activePath === 'no' ? 'text-red-600 font-bold' : 'text-gray-500'}`}>
          No
        </div>
      </div>

      {/* Handles positioned on the rectangle sides */}
      <Handle
        type="target"
        position={Position.Top}
        style={{
          top: "-8px",
          left: "50%",
          transform: "translateX(-50%)",
          background: isActive ? "#EAB308" : "#F59E0B",
          width: "12px",
          height: "12px",
          zIndex: 10
        }}
        isConnectable={isConnectable}
      />
      {/* Yes handle (bottom right) */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="yes"
        style={{
          bottom: "-8px",
          left: "75%", // Position at 75% from the left (right side)
          transform: "translateX(-50%)", // Center the handle on this point
          background: activePath === 'yes' ? "#059669" : "#10B981",
          width: "12px",
          height: "12px",
          zIndex: 10
        }}
        isConnectable={isConnectable}
      />
      {/* No handle (bottom left) */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="no"
        style={{
          bottom: "-8px",
          left: "25%", // Position at 25% from the left (left side)
          transform: "translateX(-50%)", // Center the handle on this point
          background: activePath === 'no' ? "#B91C1C" : "#EF4444",
          width: "12px",
          height: "12px",
          zIndex: 10
        }}
        isConnectable={isConnectable}
      />
    </div>
  )
}

export { DecisionNode }

