import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@xyflow/react"
import { Mic, Upload, FileAudio, FileVideo, Paperclip, AlertCircle, RefreshCw } from "lucide-react"
import { useEffect, useState } from "react"
import { useAuth } from "@clerk/nextjs"

function SpeechToTextNode({ data, isConnectable }) {
  const isActive = data.isActive || false
  const [fileCount, setFileCount] = useState(0)
  const [hasFiles, setHasFiles] = useState(false)
  const { getToken } = useAuth()
  const token = getToken()

  useEffect(() => {
    if (data.id) {
      fetchFiles()
    }
  }, [data.id])

  // Set up an interval to refresh the file count periodically
  useEffect(() => {
    if (data.id) {
      const intervalId = setInterval(() => {
        fetchFiles()
      }, 5000) // Refresh every 5 seconds

      return () => clearInterval(intervalId)
    }
  }, [data.id])

  const fetchFiles = async () => {
    try {
      const response = await fetch(`/files/node/${data.id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.ok) {
        const files = await response.json()
        setFileCount(files.length)
        setHasFiles(files.length > 0)
      }
    } catch (error) {
      console.error("Error fetching files:", error)
    }
  }

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-md border-2 transition-all duration-300 min-w-[200px]
        ${isActive ? "bg-pink-100 border-pink-500 shadow-pink-200" : "bg-white border-pink-500"}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-pink-600" : "bg-pink-500"}`}
      />

      <div className="flex items-center">
        <Mic className={`mr-2 h-5 w-5 ${isActive ? "text-pink-600" : "text-pink-500"}`} />
        <div className="font-bold">{data.label}</div>
      </div>

      <div className="mt-2">
        <div className="text-xs font-semibold text-gray-600">Inputs:</div>
        <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
          <span className="font-medium">Type:</span> {data.sourceType || "Audio"}
        </div>

        <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
          {data.source ? (
            <>
              <span className="font-medium">Source:</span> {data.source.substring(0, 30)}
              {data.source.length > 30 ? "..." : ""}
            </>
          ) : (
            <div className="flex items-center gap-1 text-gray-400 italic">
              {fileCount > 0 ? (
                <div className="flex items-center gap-1">
                  <Paperclip className="h-3 w-3" />
                  <span>{fileCount} file{fileCount !== 1 ? 's' : ''} attached</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      fetchFiles();
                    }}
                    className="ml-1 p-0.5 hover:bg-gray-100 rounded-full"
                    title="Refresh file count"
                  >
                    <RefreshCw className="h-2 w-2 text-gray-400" />
                  </button>
                </div>
              ) : data.hasUpload ? (
                <>
                  {data.sourceType === "video" ? (
                    <FileVideo className="h-3 w-3" />
                  ) : (
                    <FileAudio className="h-3 w-3" />
                  )}
                  <span>File will be uploaded during execution</span>
                </>
              ) : (
                <>
                  <Upload className="h-3 w-3" />
                  <span>No source configured</span>
                </>
              )}
            </div>
          )}
        </div>

        {data.service === "revai" && (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            <span className="font-medium">Service:</span> Rev.ai
          </div>
        )}

        {!data.source && !hasFiles && !data.hasUpload && !data.demoMode && (
          <div className="text-xs mt-1 pl-2 border-l-2 border-red-200 bg-red-50 p-1 rounded flex items-center gap-1">
            <AlertCircle className="h-3 w-3 text-red-500" />
            <span className="text-red-500"><strong>Error:</strong> File required before running. Workflow will fail without a file.</span>
          </div>
        )}
      </div>

      {data.transcription && (
        <div className="mt-2">
          <div className="text-xs font-semibold text-gray-600">Output:</div>
          <div className="text-xs mt-1 pl-2 border-l-2 border-pink-200 bg-pink-50 p-1 rounded">
            {data.transcription.substring(0, 60)}
            {data.transcription.length > 60 ? "..." : ""}
          </div>
        </div>
      )}

      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-pink-600" : "bg-pink-500"}`}
      />
    </div>
  )
}

export { SpeechToTextNode }

