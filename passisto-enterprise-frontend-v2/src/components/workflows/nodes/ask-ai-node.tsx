import { <PERSON><PERSON>, Posi<PERSON> } from "@xyflow/react"
import { Bot, Database } from "lucide-react"

function AskAINode({ data, isConnectable }) {
  const isActive = data.isActive ?? false

  // Format model name for display
  const getModelDisplayName = () => {
    if (!data.model) return "Gemini 2.0 Flash Exp";
    if (data.model.includes('gemini-2.5-pro-exp')) return 'Gemini 2.0 Flash Exp';
    return data.model.split("/").pop();
  }

  // Check if specific outputs are selected
  const hasSelectedOutputs = data.selectedOutputs && data.selectedOutputs.length > 0;

  // Check if variables are used in the prompt
  const hasVariablesInPrompt = data.prompt && /\{[^}]+\}/.test(data.prompt);

  // Count variables in prompt
  const variableCount = data.prompt ? (data.prompt.match(/\{[^}]+\}/g) || []).length : 0;

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-md border-2 transition-all duration-300 min-w-[200px]
        ${isActive ? "bg-violet-100 border-violet-500 shadow-violet-200" : "bg-white border-violet-500"}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-violet-600" : "bg-violet-500"}`}
      />

      <div className="flex items-center">
        <Bot className={`mr-2 h-5 w-5 ${isActive ? "text-violet-600" : "text-violet-500"}`} />
        <div className="font-bold">{data.label}</div>
      </div>

      <div className="mt-2">
        <div className="text-xs font-semibold text-gray-600">Inputs:</div>
        <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
          {data.prompt ? (
            <>
              <span className="font-medium">Prompt:</span> {data.prompt.substring(0, 50)}
              {data.prompt.length > 50 ? "..." : ""}
            </>
          ) : (
            <span className="text-gray-400 italic">No prompt configured</span>
          )}
        </div>

        {data.context && (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            <span className="font-medium">Context:</span> {data.context.substring(0, 30)}
            {data.context.length > 30 ? "..." : ""}
          </div>
        )}

        <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
          <span className="font-medium">Model:</span> {getModelDisplayName()}
        </div>
        {data.temperature && (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            <span className="font-medium">Temperature:</span> {data.temperature}
          </div>
        )}

        {/* Display selected outputs indicator if present */}
        {hasSelectedOutputs && (
          <div className="mt-1 flex items-center gap-1 text-xs text-violet-600">
            <Database className="h-3 w-3" />
            <span>{data.selectedOutputs.length} selected {data.selectedOutputs.length === 1 ? 'output' : 'outputs'}</span>
          </div>
        )}

        {/* Display variables indicator if present in prompt */}
        {hasVariablesInPrompt && (
          <div className="mt-1 flex items-center gap-1 text-xs text-emerald-600">
            <Database className="h-3 w-3" />
            <span>{variableCount} {variableCount === 1 ? 'variable' : 'variables'} in prompt</span>
          </div>
        )}
      </div>

      {data.response && (
        <div className="mt-2">
          <div className="text-xs font-semibold text-gray-600">Output:</div>
          <div className="text-xs mt-1 pl-2 border-l-2 border-green-200 bg-green-50 p-1 rounded">
            {data.response.substring(0, 60)}
            {data.response.length > 60 ? "..." : ""}
          </div>
        </div>
      )}

      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-violet-600" : "bg-violet-500"}`}
      />
    </div>
  )
}

export { AskAINode }

