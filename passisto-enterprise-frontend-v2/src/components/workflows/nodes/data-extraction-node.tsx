import { <PERSON><PERSON>, <PERSON>si<PERSON> } from "@xyflow/react"
import { FileSearch } from "lucide-react"

function DataExtractionNode({ data, isConnectable }) {
  const isActive = data.isActive || false
  const fields = data.fields || []

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-md border-2 transition-all duration-300 min-w-[200px]
        ${isActive ? "bg-cyan-100 border-cyan-500 shadow-cyan-200" : "bg-white border-cyan-500"}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-cyan-600" : "bg-cyan-500"}`}
      />

      <div className="flex items-center">
        <FileSearch className={`mr-2 h-5 w-5 ${isActive ? "text-cyan-600" : "text-cyan-500"}`} />
        <div className="font-bold">{data.label}</div>
      </div>

      <div className="mt-2">
        <div className="text-xs font-semibold text-gray-600">Fields to Extract:</div>
        {fields.length > 0 ? (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            {fields.slice(0, 3).map((field, index) => (
              <div key={index} className="mb-1">
                <span className="font-medium">{field.name}:</span> {field.description.substring(0, 20)}
                {field.description.length > 20 ? "..." : ""}
              </div>
            ))}
            {fields.length > 3 && <div className="text-gray-400">+ {fields.length - 3} more fields</div>}
          </div>
        ) : (
          <div className="text-xs mt-1 pl-2 border-l-2 border-gray-200">
            <span className="text-gray-400 italic">No fields configured</span>
          </div>
        )}
      </div>

      {data.extractedData && Object.keys(data.extractedData).length > 0 && (
        <div className="mt-2">
          <div className="text-xs font-semibold text-gray-600">Extracted Data:</div>
          <div className="text-xs mt-1 pl-2 border-l-2 border-cyan-200 bg-cyan-50 p-1 rounded">
            {Object.entries(data.extractedData)
              .slice(0, 2)
              .map(([key, value], index) => (
                <div key={index}>
                  <span className="font-medium">{key}:</span> {String(value).substring(0, 20)}
                  {String(value).length > 20 ? "..." : ""}
                </div>
              ))}
            {Object.keys(data.extractedData).length > 2 && (
              <div className="text-gray-400">+ {Object.keys(data.extractedData).length - 2} more items</div>
            )}
          </div>
        </div>
      )}

      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-cyan-600" : "bg-cyan-500"}`}
      />
    </div>
  )
}

export { DataExtractionNode }

