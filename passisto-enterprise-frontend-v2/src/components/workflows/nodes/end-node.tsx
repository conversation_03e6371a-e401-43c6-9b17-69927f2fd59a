import { <PERSON><PERSON>, <PERSON>si<PERSON> } from "@xyflow/react"
import { Square } from "lucide-react"

function EndNode({ data, isConnectable }) {
  const isActive = data.isActive || false

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-full border-2 transition-all duration-300
        ${isActive ? "bg-red-100 border-red-500 shadow-red-200" : "bg-white border-red-500"}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isActive ? "bg-red-600" : "bg-red-500"}`}
      />
      <div className="flex items-center">
        <Square className={`mr-2 h-5 w-5 ${isActive ? "text-red-600" : "text-red-500"}`} />
        <div className="font-bold">{data.label}</div>
      </div>
    </div>
  )
}

export { EndNode }

