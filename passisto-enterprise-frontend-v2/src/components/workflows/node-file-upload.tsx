"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X, FileText, FileImage, FileAudio, FileVideo, File as FileIcon, Download, Trash2, Paperclip } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { useAuth } from "@clerk/nextjs"
import { formatDistanceToNow } from "date-fns"
import api from "@/config/axios-workflows"

interface NodeFileUploadProps {
  nodeId: string
  accept?: string
  maxSize?: number // in MB
  className?: string
  onFilesChanged?: () => void
}

interface FileData {
  id: string
  filename: string
  originalName: string
  mimetype: string
  size: number
  url: string
  createdAt: string
}

export function NodeFileUpload({ nodeId, accept, maxSize = 50, className, onFilesChanged }: NodeFileUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [files, setFiles] = useState<FileData[]>([])
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(true)
  const [progress, setProgress] = useState(0)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { getToken } = useAuth()

  useEffect(() => {
    if (nodeId) {
      fetchFiles()
    }
  }, [nodeId])

  const fetchFiles = async () => {
    try {
      setLoading(true)
      const token = await getToken()

      const response = await api.get(`/files/node/${nodeId}`, {
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      })

      setFiles(response.data)
    } catch (error) {
      console.error("Error fetching files:", error)
      toast({
        title: "Error",
        description: "Failed to load file attachments",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {

      // Check file size
      if (selectedFile.size > maxSize * 1024 * 1024) {
        toast({
          title: "File too large",
          description: `Maximum file size is ${maxSize}MB`,
          variant: "destructive"
        })
        return
      }

      setFile(selectedFile)

      // Auto-upload the file when selected
      await handleUpload(selectedFile)
    }
  }

  const handleUpload = async (selectedFile?: File) => {
    const fileToUpload = selectedFile || file
    if (!fileToUpload || !nodeId) return

    setUploading(true)
    setProgress(0)

    try {
      const formData = new FormData()
      formData.append("file", fileToUpload)
      formData.append("nodeId", nodeId)

      const token = await getToken()

      const response = await api.post("/files/upload", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentComplete = Math.round((progressEvent.loaded / progressEvent.total) * 100)
            setProgress(percentComplete)
          }
        }
      })

      if (response.status >= 200 && response.status < 300) {
        setFile(null)
        setUploadSuccess(true)
        fetchFiles() // Refresh the file list

        // Reset success message after 5 seconds
        setTimeout(() => {
          setUploadSuccess(false)
        }, 5000)

        if (onFilesChanged) {
          onFilesChanged()
        }

        toast({
          title: "File uploaded",
          description: "File has been uploaded successfully"
        })
      }
      setUploading(false)
    } catch (error) {
      console.error("Upload error:", error)
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file",
        variant: "destructive"
      })
      setUploading(false)
    }
  }

  const cancelUpload = () => {
    setFile(null)
    setUploadSuccess(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleDownload = (file: FileData) => {
    window.open(`/files/download/${file.filename}`, '_blank')
  }

  const handleDelete = async (fileId: string) => {
    try {
      const token = await getToken()

      await api.delete(`/files/${fileId}`, {
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {})
        }
      })

      setFiles(files.filter(f => f.id !== fileId))

      if (onFilesChanged) {
        onFilesChanged()
      }

      toast({
        title: "File deleted",
        description: "File has been deleted successfully"
      })
    } catch (error) {
      console.error("Error deleting file:", error)
      toast({
        title: "Error",
        description: "Failed to delete file",
        variant: "destructive"
      })
    }
  }

  const getFileIcon = (file: File | FileData) => {
    if (!file) return <Upload className="h-5 w-5" />

    const type = 'mimetype' in file ? file.mimetype : file.type
    if (type.startsWith("image/")) return <FileImage className="h-5 w-5" />
    if (type.startsWith("audio/")) return <FileAudio className="h-5 w-5" />
    if (type.startsWith("video/")) return <FileVideo className="h-5 w-5" />
    if (type.includes("pdf")) return <FileText className="h-5 w-5" />
    return <FileIcon className="h-5 w-5" />
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-col space-y-2">
        <Label htmlFor="file-upload">Upload File</Label>
        <div className="flex items-center gap-2">
          <Input
            ref={fileInputRef}
            id="file-upload"
            type="file"
            accept={accept}
            onChange={handleFileChange}
            disabled={uploading}
            className="flex-1"
          />
          {file && !uploading && (
            <Button variant="outline" size="icon" onClick={cancelUpload} title="Cancel">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {file && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm">
              {getFileIcon(file)}
              <span className="truncate max-w-[200px]">{file.name}</span>
              <span className="text-xs text-muted-foreground">
                ({(file.size / (1024 * 1024)).toFixed(2)} MB)
              </span>
            </div>
            {!uploading && (
              <div className="text-xs text-blue-600 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Uploading automatically...</span>
              </div>
            )}
          </div>

          {uploading && (
            <div className="space-y-1">
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-center text-muted-foreground">{progress}% uploaded</p>
            </div>
          )}
        </div>
      )}

      {uploadSuccess && !file && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-green-700 text-xs flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span>File uploaded successfully!</span>
        </div>
      )}

      {/* Existing files */}
      {files.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center gap-2 mb-2">
            <Paperclip className="h-4 w-4" />
            <h4 className="text-sm font-medium">Attached Files ({files.length})</h4>
          </div>

          <div className="space-y-2">
            {files.map(file => (
              <Card key={file.id} className="overflow-hidden">
                <CardContent className="p-3">
                  <div className="grid grid-cols-[1fr,auto] gap-2">
                    <div className="flex items-center gap-2 min-w-0">
                      {getFileIcon(file)}
                      <div className="min-w-0">
                        <p className="text-sm font-medium truncate">{file.originalName}</p>
                        <p className="text-xs text-muted-foreground truncate">
                          {(file.size / (1024 * 1024)).toFixed(2)} MB • {formatDistanceToNow(new Date(file.createdAt), { addSuffix: true })}
                        </p>
                      </div>
                    </div>

                    <div className="flex gap-1 shrink-0">
                      <Button variant="ghost" size="icon" onClick={() => handleDownload(file)} title="Download">
                        <Download className="h-4 w-4" />
                      </Button>

                      <Button variant="destructive" size="icon" onClick={() => handleDelete(file.id)} title="Delete">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {loading && (
        <div className="text-sm text-muted-foreground">Loading attachments...</div>
      )}
    </div>
  )
}
