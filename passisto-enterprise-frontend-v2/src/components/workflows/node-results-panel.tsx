"use client"

import { useState, useMemo } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, Clock, AlertCircle, ArrowRightCircle } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

export interface NodeResult {
  nodeId: string
  nodeType: string
  nodeName: string
  status: 'SUCCESS' | 'FAILED' | 'RUNNING' | 'PENDING' | 'WAITING_FOR_USER' | 'SKIPPED'
  executionTime?: string
  output?: any
  error?: string
  order?: number // Position in the execution order
}

interface NodeResultsPanelProps {
  results: NodeResult[]
  currentNodeId: string | null
  isRunning: boolean
}

export function NodeResultsPanel({ results, currentNodeId, isRunning }: NodeResultsPanelProps) {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null)

  // Sort results by order if available
  const sortedResults = useMemo(() => {
    return [...results].sort((a, b) => {
      // If order is available, use it
      if (a.order !== undefined && b.order !== undefined) {
        return a.order - b.order;
      }
      // If only one has order, prioritize the one with order
      if (a.order !== undefined) return -1;
      if (b.order !== undefined) return 1;
      // Otherwise, keep original order
      return 0;
    });
  }, [results]);

  // If no node is selected, default to the current node or the last completed node
  const effectiveSelectedNodeId = selectedNodeId ?? currentNodeId ?? (sortedResults.length > 0 ? sortedResults[0].nodeId : null)

  // Get the selected node result
  const selectedResult = sortedResults.find(r => r.nodeId === effectiveSelectedNodeId)

  // Format the execution time
  const formatTime = (timeString?: string) => {
    if (!timeString) return 'N/A'
    try {
      return formatDistanceToNow(new Date(timeString), { addSuffix: true })
    } catch (e) {
      return timeString
    }
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <Badge className="bg-green-100 text-green-800 border-green-300">Success</Badge>
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800 border-red-300">Failed</Badge>
      case 'RUNNING':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">Running</Badge>
      case 'WAITING_FOR_USER':
        return <Badge className="bg-amber-100 text-amber-800 border-amber-300">Waiting for User</Badge>
      case 'SKIPPED':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">Skipped</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">Pending</Badge>
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'RUNNING':
        return <Clock className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'WAITING_FOR_USER':
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      case 'SKIPPED':
        return <ArrowRightCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  if (!isRunning && results.length === 0) {
    return null
  }

  return (
    <Card className="mt-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <span>Node Execution Results</span>
          {isRunning && <Badge className="ml-2 bg-blue-100 text-blue-800 border-blue-300">Workflow Running</Badge>}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1 border rounded-md p-2">
            <h3 className="text-sm font-medium mb-2">Executed Nodes</h3>
            <ScrollArea className="h-[300px]">
              <div className="space-y-1">
                {sortedResults.length === 0 ? (
                  <div className="text-sm text-gray-500 p-2">No nodes executed yet</div>
                ) : (
                  sortedResults.map((result) => (
                    <div
                      key={result.nodeId}
                      className={`flex items-center p-2 rounded-md cursor-pointer hover:bg-gray-100 ${
                        result.nodeId === effectiveSelectedNodeId ? 'bg-gray-100' : ''
                      }`}
                      onClick={() => setSelectedNodeId(result.nodeId)}
                    >
                      {getStatusIcon(result.status)}
                      <div className="ml-2 flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{result.nodeName}</div>
                        <div className="text-xs text-gray-500 truncate">{result.nodeType}</div>
                      </div>
                      {getStatusBadge(result.status)}
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>

          <div className="md:col-span-2 border rounded-md p-2">
            <h3 className="text-sm font-medium mb-2">Node Details</h3>
            {selectedResult ? (
              <div>
                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div>
                    <div className="text-xs text-gray-500">Node ID</div>
                    <div className="text-sm font-mono">{selectedResult.nodeId}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Status</div>
                    <div className="text-sm">{getStatusBadge(selectedResult.status)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Type</div>
                    <div className="text-sm">{selectedResult.nodeType}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Execution Time</div>
                    <div className="text-sm">{formatTime(selectedResult.executionTime)}</div>
                  </div>
                </div>

                <Tabs defaultValue="output">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="output">Output</TabsTrigger>
                    <TabsTrigger value="error" disabled={!selectedResult.error && !selectedResult.output?.error}>Error</TabsTrigger>
                  </TabsList>
                  <TabsContent value="output">
                    <ScrollArea className="h-[200px] w-full border rounded-md">
                      {selectedResult.output ? (
                        <pre className="p-2 text-xs whitespace-pre-wrap">
                          {typeof selectedResult.output === 'object'
                            ? JSON.stringify(selectedResult.output, null, 2)
                            : selectedResult.output}
                        </pre>
                      ) : (
                        <div className="p-2 text-sm text-gray-500">No output available</div>
                      )}
                    </ScrollArea>
                  </TabsContent>
                  <TabsContent value="error">
                    <ScrollArea className="h-[200px] w-full border rounded-md">
                      {selectedResult.error ? (
                        <pre className="p-2 text-xs text-red-600 whitespace-pre-wrap">
                          {selectedResult.error}
                        </pre>
                      ) : selectedResult.output?.error ? (
                        <pre className="p-2 text-xs text-red-600 whitespace-pre-wrap">
                          {selectedResult.output.error}
                        </pre>
                      ) : (
                        <div className="p-2 text-sm text-gray-500">No errors</div>
                      )}
                    </ScrollArea>
                  </TabsContent>
                </Tabs>
              </div>
            ) : (
              <div className="text-sm text-gray-500 p-2">Select a node to view details</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
