"use client";

import { type Node, type Edge } from "@xyflow/react";

// Suppress ResizeObserver errors
export const suppressResizeObserverErrors = () => {
  const originalError = window.console.error;
  window.console.error = (...args) => {
    if (
      args[0]?.includes?.("ResizeObserver loop") ||
      args[0]?.includes?.("ResizeObserver loop completed with undelivered notifications")
    ) {
      // Suppress the specific ResizeObserver error
      return;
    }
    originalError.apply(window.console, args);
  };
};

// Create styled nodes with selection highlighting
export const createStyledNodes = (nodes: Node[], selectedNodes: Node[]) => {
  return nodes.map((node) => {
    if (selectedNodes.find(n => n.id === node.id)) {
      return {
        ...node,
        style: {
          ...node.style,
          boxShadow: "0 0 0 2px #ff0072",
          border: "2px solid #ff0072",
          borderRadius: "8px",
        },
        // Add a flag to indicate this node is selected (for UI only)
        selected: true,
      };
    }
    return node;
  });
};

// Clean nodes of selection styling before saving
export const cleanNodesForSaving = (nodes: Node[]) => {
  return nodes.map((node) => {
    // Create a new node without the selection styling
    const { style, selected, ...cleanNode } = node;

    // If the node had custom styling (not selection-related), preserve it
    if (style) {
      const { boxShadow, border, borderRadius, ...restStyle } = style;

      // Only add back the style if there are other style properties
      if (Object.keys(restStyle).length > 0) {
        return {
          ...cleanNode,
          style: restStyle,
        };
      }
    }

    return cleanNode;
  });
};

// Create styled edges with selection highlighting
export const createStyledEdges = (edges: Edge[], selectedEdge: Edge | null) => {
  return edges.map((edge) => {
    if (selectedEdge && edge.id === selectedEdge.id) {
      return {
        ...edge,
        style: {
          ...edge.style,
          stroke: "#ff0072",
          strokeWidth: 3,
          strokeDasharray: "5,5",
        },
        animated: true,
      };
    }
    return edge;
  });
};

// Define node types mapping
export const nodeTypes = {
  task: "TaskNode",
  decision: "DecisionNode",
  start: "StartNode",
  end: "EndNode",
  "ask-ai": "AskAINode",
  "speech-to-text": "SpeechToTextNode",
  "data-extraction": "DataExtractionNode",
  email: "EmailNode",
};
