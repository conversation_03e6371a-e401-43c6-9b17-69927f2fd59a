"use client";

import { useCallback } from "react";
import { useReactFlow, type Node } from "@xyflow/react";

export function useNodeOperations(setNodes: React.Dispatch<React.SetStateAction<Node[]>>) {
  const reactFlowInstance = useReactFlow();

  // Handle dropping a new node onto the canvas
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      // We don't need to check for reactFlowWrapper.current anymore since we're using
      // screenToFlowPosition directly with client coordinates
      const type = event.dataTransfer.getData("application/reactflow/type");
      const label = event.dataTransfer.getData("application/reactflow/label");

      // Check if the dropped element is valid
      if (!type) return;

      // Get the position where the node was dropped
      // Use screenToFlowPosition directly with client coordinates
      // This properly accounts for the viewport's zoom and pan state
      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      // Create default data based on node type
      let defaultData: Record<string, any> = { label: label || `${type} node` };

      // Add type-specific default data
      switch (type) {
        case "ask-ai":
          defaultData = {
            ...defaultData,
            prompt: "",
            context: "",
            model: "google/gemini-2.0-flash-exp:free",
            maxTokens: "4096",
            temperature: "0.7",
            response: "",
            selectedOutputs: [] // Array for selected outputs from previous nodes
          };
          break;
        case "speech-to-text":
          defaultData = {
            ...defaultData,
            sourceType: "audio",
            source: "",
            transcription: "",
          };
          break;
        case "data-extraction":
          defaultData = {
            ...defaultData,
            fields: [],
            extractedData: {},
          };
          break;
        case "email":
          defaultData = {
            ...defaultData,
            to: "",
            subject: "",
            body: "",
            selectedOutputs: [], // Add empty array for selected outputs
          };
          break;
      }

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: defaultData,
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes],
  );

  // Handle drag over for drop target
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  return {
    onDrop,
    onDragOver,
  };
}
