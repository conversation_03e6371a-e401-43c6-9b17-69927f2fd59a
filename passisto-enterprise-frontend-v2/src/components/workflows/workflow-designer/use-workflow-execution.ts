"use client";

import { useState, useCallback, useEffect } from "react";
import { useReactFlow, type Node, type Edge } from "@xyflow/react";
import { toast } from "@/hooks/use-toast";
import { workflowRunA<PERSON> } from "@/services/workflowRunApi";
import { Workflow } from "../workflows-table";
import { NodeResult, WorkflowExecutionState } from "./types";

export function useWorkflowExecution(
  currentWorkflow: Workflow | null,
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>
) {
  const reactFlowInstance = useReactFlow();
  const [executionState, setExecutionState] = useState<WorkflowExecutionState>({
    isRunning: false,
    currentNodeId: null,
    executionPath: [],
    nodeResults: [],
  });

  // Clean up effect
  useEffect(() => {
    return () => {
      // Clean up any resources when component unmounts
    };
  }, []);

  // Helper function to determine the execution order of nodes based on the workflow structure
  const determineExecutionOrder = useCallback((nodes: Node[], edges: Edge[]): string[] => {
    // Find start nodes (nodes with no incoming edges)
    const startNodeIds = nodes
      .filter(node => !edges.some(edge => edge.target === node.id))
      .map(node => node.id);

    // Initialize the execution order with start nodes
    const executionOrder: string[] = [...startNodeIds];

    // Build a map of node dependencies (source -> targets)
    const nodeTargets: Record<string, string[]> = {};
    edges.forEach(edge => {
      if (!nodeTargets[edge.source]) {
        nodeTargets[edge.source] = [];
      }
      nodeTargets[edge.source].push(edge.target);
    });

    // Process nodes in topological order
    const processedNodes = new Set(startNodeIds);
    let lastSize = 0;

    // Continue until we can't add any more nodes or we've processed all nodes
    while (processedNodes.size > lastSize && processedNodes.size < nodes.length) {
      lastSize = processedNodes.size;

      // Find nodes whose dependencies have all been processed
      nodes.forEach(node => {
        if (processedNodes.has(node.id)) return; // Skip already processed nodes

        // Check if all incoming edges are from processed nodes
        const incomingEdges = edges.filter(edge => edge.target === node.id);
        const allDependenciesProcessed = incomingEdges.every(edge =>
          processedNodes.has(edge.source)
        );

        if (allDependenciesProcessed) {
          processedNodes.add(node.id);
          executionOrder.push(node.id);
        }
      });
    }

    // Add any remaining nodes that couldn't be ordered (in case of cycles)
    nodes.forEach(node => {
      if (!processedNodes.has(node.id)) {
        executionOrder.push(node.id);
      }
    });

    return executionOrder;
  }, []);

  // Execute workflow
  const executeWorkflow = useCallback(async () => {
    if (!reactFlowInstance || !currentWorkflow?.id) {
      toast({
        title: "Error",
        description: "Cannot execute workflow without a saved workflow",
        variant: "destructive",
      });
      return;
    }

    try {
      // Reset execution state before starting a new workflow
      setExecutionState({
        isRunning: true,  // Set to true immediately for the new run
        currentNodeId: null,
        executionPath: [],
        nodeResults: [], // Clear all previous node results
      });

      // Reset node styling
      setNodes((nodes) =>
        nodes.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isActive: false,
            isCompleted: false,
            isWaiting: false,
          },
        }))
      );

      // Start the new workflow run
      await workflowRunApi.createWorkflowRun(currentWorkflow.id);

      toast({
        title: "Success",
        description: "Workflow execution started successfully",
      });
    } catch (error: any) {
      console.error("Error executing workflow:", error);
      toast({
        title: "Error",
        description: `Failed to execute workflow: ${error.message}`,
        variant: "destructive",
      });
      setExecutionState(prev => ({ ...prev, isRunning: false }));
    }
  }, [reactFlowInstance, currentWorkflow, toast, setNodes]);

  // Stop workflow execution
  const stopExecution = useCallback(async () => {
    if (!executionState.isRunning || !currentWorkflow?.id) return;

    try {
      // Get the latest workflow runs for this workflow
      const runs = await workflowRunApi.getWorkflowRunsByWorkflowId(currentWorkflow.id);

      // Find the most recent running workflow run
      const latestRunningRun = runs.find(run => {
        // Use type assertion to handle all possible status values
        const status = run.status as string;
        return status === 'RUNNING' || status === 'PENDING' || status === 'WAITING_FOR_USER';
      });

      if (!latestRunningRun) {
        toast({
          title: "Info",
          description: "No running workflow found to stop.",
        });
        return;
      }

      // Stop the workflow run
      await workflowRunApi.stopWorkflowRun(latestRunningRun.id);

      toast({
        title: "Success",
        description: "Workflow execution stopped successfully.",
      });

      // Update UI state
      setExecutionState({
        isRunning: false,
        currentNodeId: null,
        executionPath: [],
        nodeResults: [],
      });

      // Reset node styling
      setNodes((nodes) =>
        nodes.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isActive: false,
            isCompleted: false,
            isWaiting: false,
          },
        }))
      );
    } catch (error: any) {
      console.error("Error stopping workflow:", error);
      toast({
        title: "Error",
        description: `Failed to stop workflow: ${error.message}`,
        variant: "destructive",
      });
    }
  }, [executionState.isRunning, currentWorkflow, toast, setNodes]);

  // Update node results
  const updateNodeResults = useCallback((newResult: NodeResult) => {
    setExecutionState(prev => {
      const existingIndex = prev.nodeResults.findIndex(r => r.nodeId === newResult.nodeId);

      if (existingIndex >= 0) {
        const updatedResults = [...prev.nodeResults];
        updatedResults[existingIndex] = { ...updatedResults[existingIndex], ...newResult };
        return { ...prev, nodeResults: updatedResults };
      } else {
        return { ...prev, nodeResults: [...prev.nodeResults, newResult] };
      }
    });
  }, []);

  // Set current node
  const setCurrentNode = useCallback((nodeId: string | null) => {
    setExecutionState(prev => ({ ...prev, currentNodeId: nodeId }));

    if (nodeId) {
      // Update node styling
      setNodes((nodes) =>
        nodes.map((node) => ({
          ...node,
          data: {
            ...node.data,
            isActive: node.id === nodeId,
          },
        }))
      );
    }
  }, [setNodes]);

  // Add node to execution path
  const addToExecutionPath = useCallback((nodeId: string) => {
    setExecutionState(prev => {
      if (!prev.executionPath.includes(nodeId)) {
        return {
          ...prev,
          executionPath: [...prev.executionPath, nodeId]
        };
      }
      return prev;
    });

    // Mark node as completed
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isActive: false,
          isCompleted: node.id === nodeId || node.data.isCompleted,
        },
      }))
    );
  }, [setNodes]);

  // Check if workflow is already running
  const checkWorkflowRunningStatus = useCallback(async () => {
    if (!currentWorkflow?.id) return;

    try {
      // Get the latest workflow runs for this workflow
      const runs = await workflowRunApi.getWorkflowRunsByWorkflowId(currentWorkflow.id);

      // Find the most recent running workflow run
      const latestRunningRun = runs.find(run => {
        // Use type assertion to handle all possible status values
        const status = run.status as string;
        return status === 'RUNNING' || status === 'PENDING' || status === 'WAITING_FOR_USER';
      });

      // Get the most recent workflow run (running or completed)
      const latestRun = runs.length > 0 ? runs[0] : null;

      if (latestRunningRun) {
        console.log('Found running workflow:', latestRunningRun);

        // Get detailed run information
        const runDetails = await workflowRunApi.getWorkflowRunWithDetails(latestRunningRun.id);
        console.log('Run details:', runDetails);

        // Find the current active node
        const activeNodeRun = runDetails.nodeRuns?.find(nodeRun => {
          // Use type assertion to handle all possible status values
          const status = nodeRun.status as string;
          return status === 'RUNNING' || status === 'WAITING_FOR_USER';
        });

        // Find completed nodes for execution path
        const completedNodeIds = runDetails.nodeRuns
          ?.filter(nodeRun => nodeRun.status === 'SUCCESS')
          .map(nodeRun => nodeRun.nodeId) || [];

        // Get the current workflow nodes
        const currentNodes = reactFlowInstance.getNodes();

        // Update execution state
        setExecutionState({
          isRunning: true,
          currentNodeId: activeNodeRun?.nodeId ?? null,
          executionPath: completedNodeIds,
          nodeResults: runDetails.nodeRuns?.map(nodeRun => {
            // Get the node from the workflow to get its type and name
            const node = currentNodes.find((n: Node) => n.id === nodeRun.nodeId);

            return {
              nodeId: nodeRun.nodeId,
              nodeType: node?.type ?? 'unknown',
              nodeName: typeof node?.data?.label === 'string' ? node.data.label : nodeRun.nodeId,
              status: nodeRun.status as any,
              executionTime: nodeRun.startedAt ?? new Date().toISOString(),
              output: nodeRun.output,
              // Use type assertion for error property
              error: (nodeRun as any).error ?? null,
              order: completedNodeIds.indexOf(nodeRun.nodeId),
            };
          }) || [],
        });

        // Update node styling
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: node.id === activeNodeRun?.nodeId,
              isCompleted: completedNodeIds.includes(node.id),
              // Use type assertion for status
              isWaiting: (activeNodeRun?.status as string) === 'WAITING_FOR_USER' && node.id === activeNodeRun?.nodeId,
            },
          }))
        );

        // Show a toast notification
        toast({
          title: "Workflow Running",
          description: "This workflow is currently running.",
        });
      } else if (latestRun) {
        // If there's no running workflow but there is a completed one,
        // make sure all nodes are reset to their proper state
        console.log('No running workflow found, but found completed workflow:', latestRun);

        // Reset all node states to ensure no nodes are stuck in 'waiting' state
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              isActive: false,
              isWaiting: false,
              // We can keep completed status if needed
              // isCompleted: false,
            },
          }))
        );
      }
    } catch (error: any) {
      console.error("Error checking workflow running status:", error);
    }
  }, [currentWorkflow?.id, reactFlowInstance, setNodes, toast]);

  // Reset execution state
  const resetExecutionState = useCallback(() => {
    setExecutionState({
      isRunning: false,
      currentNodeId: null,
      executionPath: [],
      nodeResults: [],
    });

    // Reset node styling
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isActive: false,
          isCompleted: false,
          isWaiting: false,
        },
      }))
    );
  }, [setNodes]);

  // Check for running workflow when component mounts or workflow changes
  useEffect(() => {
    if (currentWorkflow?.id) {
      checkWorkflowRunningStatus();
    }
  }, [currentWorkflow?.id, checkWorkflowRunningStatus]);

  return {
    executionState,
    setExecutionState,
    executeWorkflow,
    stopExecution,
    determineExecutionOrder,
    updateNodeResults,
    setCurrentNode,
    addToExecutionPath,
    resetExecutionState,
    checkWorkflowRunningStatus,
  };
}
