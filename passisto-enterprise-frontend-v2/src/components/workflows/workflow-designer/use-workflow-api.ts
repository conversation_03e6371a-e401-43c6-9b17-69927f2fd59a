"use client";

import { useCallback } from "react";
import { useReactFlow } from "@xyflow/react";
import { toast } from "@/hooks/use-toast";
import { workflowApi } from "@/services/workflowApi";
import { Workflow } from "../workflows-table";
import { cleanNodesForSaving } from "./utils";


export function useWorkflowApi(
  currentWorkflow: Workflow | null,
  workflowTitle: string,
  workflowDescription: string,
  onWorkflowSaved: (workflow: Workflow) => void
) {
  const reactFlowInstance = useReactFlow();

  // Save workflow
  const saveWorkflow = useCallback(async () => {
    console.log("Saving workflow...");
    try {
      // Get the current viewport information
      const viewport = reactFlowInstance.getViewport();

      // Get nodes and clean them of selection styling
      const nodes = cleanNodesForSaving(reactFlowInstance.getNodes());

      const workflow: Partial<Workflow> = {
        name: workflowTitle,
        description: workflowDescription,
        status: 'draft',
        viewport: viewport,
        nodes: nodes,
        edges: reactFlowInstance.getEdges()
      };

      // Log the workflow data being sent
      console.log("Workflow data being sent:", JSON.stringify(workflow, null, 2));

      let savedWorkflow;
      if (currentWorkflow?.id) {
        console.log("Updating workflow with ID:", currentWorkflow.id);
        savedWorkflow = await workflowApi.updateWorkflow(currentWorkflow.id, workflow);
      } else {
        console.log("Creating new workflow");
        savedWorkflow = await workflowApi.createWorkflow(workflow);
      }

      console.log("Workflow saved:", savedWorkflow);

      toast({
        title: "Workflow saved",
        description: "Your workflow has been saved successfully.",
      });

      // Trigger the onWorkflowSaved callback
      onWorkflowSaved(savedWorkflow);

      return savedWorkflow;
    } catch (error: any) {
      console.error("Error saving workflow:", error);
      // Extract error message from response if available
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || "There was a problem saving your workflow.";
      toast({
        title: "Error saving workflow",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  }, [reactFlowInstance, workflowTitle, workflowDescription, currentWorkflow, onWorkflowSaved, cleanNodesForSaving]);

  // Load workflow
  const loadWorkflow = useCallback(async () => {
    try {
      if (!currentWorkflow?.id) {
        toast({
          title: "No workflow selected",
          description: "Please select a workflow to load.",
          variant: "destructive",
        });
        return null;
      }

      const workflow = await workflowApi.getWorkflow(currentWorkflow.id);

      toast({
        title: "Workflow loaded",
        description: "Your workflow has been loaded successfully.",
      });

      return workflow;
    } catch (error: any) {
      console.error("Error loading workflow:", error);
      // Extract error message from response if available
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || "There was a problem loading your workflow.";
      toast({
        title: "Error loading workflow",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    }
  }, [currentWorkflow]);

  // Export workflow as JSON file
  const exportWorkflow = useCallback(() => {
    try {
      // Get nodes and clean them of selection styling
      const nodes = cleanNodesForSaving(reactFlowInstance.getNodes());

      const flow = {
        nodes: nodes,
        edges: reactFlowInstance.getEdges(),
      };
      const json = JSON.stringify(flow, null, 2);
      const blob = new Blob([json], { type: "application/json" });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = "workflow.json";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Workflow exported",
        description: "Your workflow has been exported as JSON.",
      });
    } catch (error) {
      toast({
        title: "Error exporting workflow",
        description: "There was a problem exporting your workflow.",
        variant: "destructive",
      });
    }
  }, [reactFlowInstance, cleanNodesForSaving]);

  return {
    saveWorkflow,
    loadWorkflow,
    exportWorkflow,
  };
}
