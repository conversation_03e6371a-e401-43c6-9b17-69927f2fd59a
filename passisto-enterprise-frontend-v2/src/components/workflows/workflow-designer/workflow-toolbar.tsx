"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Save, Upload, ZoomIn, ZoomOut, Play, Pause, Download, Trash2 } from "lucide-react";
import { Panel } from "@xyflow/react";
import { type Edge, type Node } from "@xyflow/react";

interface WorkflowToolbarProps {
  isRunning: boolean;
  selectedEdge: Edge | null;
  selectedNodes: Node[];
  executeWorkflow: () => void;
  stopExecution: () => void;
  saveWorkflow: () => void;
  exportWorkflow: () => void;
  loadWorkflow: () => void;
  deleteSelectedEdge: () => void;
  deleteSelectedNodes: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  openSaveDialog: () => void;
}

export function WorkflowToolbar({
  isRunning,
  selectedEdge,
  selectedNodes,
  executeWorkflow,
  stopExecution,
  saveWorkflow,
  exportWorkflow,
  loadWorkflow,
  deleteSelectedEdge,
  deleteSelectedNodes,
  zoomIn,
  zoomOut,
  openSaveDialog,
}: WorkflowToolbarProps) {
  return (
    <>
      {/* Top toolbar */}
      <Panel position="top-center" className="flex gap-2 bg-white p-2 rounded-md shadow-md">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="default"
                size="sm"
                onClick={isRunning ? stopExecution : executeWorkflow}
                className={isRunning ? "bg-red-500 hover:bg-red-600" : "bg-green-500 hover:bg-green-600"}
              >
                {isRunning ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" /> Stop
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" /> Run
                  </>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>{isRunning ? "Stop workflow execution" : "Run workflow"}</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="default"
                size="sm"
                onClick={saveWorkflow}
                className="bg-blue-500 hover:bg-blue-600"
              >
                <Save className="h-4 w-4 mr-2" /> Save
              </Button>
            </TooltipTrigger>
            <TooltipContent>Save workflow</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={openSaveDialog}>
                <Save className="h-4 w-4 mr-2" /> Save As...
              </Button>
            </TooltipTrigger>
            <TooltipContent>Save workflow with a new name</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={exportWorkflow}>
                <Download className="h-4 w-4 mr-2" /> Export
              </Button>
            </TooltipTrigger>
            <TooltipContent>Export workflow as JSON file</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Delete edge button - only show when an edge is selected */}
        {selectedEdge && !isRunning && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={deleteSelectedEdge}
                  className="bg-red-50 text-red-500 border-red-200 hover:bg-red-100"
                >
                  <Trash2 className="h-4 w-4 mr-2" /> Delete Connection
                </Button>
              </TooltipTrigger>
              <TooltipContent>Delete selected connection (or press Delete)</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {selectedNodes.length > 0 && !isRunning && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={deleteSelectedNodes}
                  className="bg-red-50 text-red-500 border-red-200 hover:bg-red-100"
                >
                  <Trash2 className="h-4 w-4 mr-2" /> Delete {selectedNodes.length > 1 ? 'Nodes' : 'Node'}
                </Button>
              </TooltipTrigger>
              <TooltipContent>Delete selected {selectedNodes.length > 1 ? 'nodes' : 'node'} (or press Delete)</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </Panel>

      {/* Right side controls */}
      <Panel position="top-right" className="flex gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={zoomIn}>
                <ZoomIn className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Zoom in</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={zoomOut}>
                <ZoomOut className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Zoom out</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={loadWorkflow}>
                <Upload className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Load saved workflow</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </Panel>
    </>
  );
}
