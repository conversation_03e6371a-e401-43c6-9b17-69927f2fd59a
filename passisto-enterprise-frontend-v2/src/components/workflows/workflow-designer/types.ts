import type { Node, <PERSON> } from "@xyflow/react";
import type { Workflow } from "../workflows-table";

export interface NodeResult {
  nodeId: string;
  nodeType: string;
  nodeName: string;
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'WAITING_FOR_USER';
  executionTime: string;
  output?: any;
  error?: any;
  order?: number;
}

export interface WorkflowDesignerProps {
  currentWorkflow: Workflow | null;
  onWorkflowSaved: (workflow: Workflow) => void;
}

export interface NodeUpdateData {
  [key: string]: any;
}

export interface WorkflowExecutionState {
  isRunning: boolean;
  currentNodeId: string | null;
  executionPath: string[];
  nodeResults: NodeResult[];
}

export interface WorkflowProgress {
  workflowId?: string;
  workflowRunId?: string;
  nodeId?: string;
  type?: string;
  status?: string;
  message?: string;
  output?: any;
}

export interface NodeExecutionDetails {
  workflowRunId: string;
  nodeId: string;
  executionDetails: any;
}

export interface NodeRunProgress {
  workflowRunId: string;
  nodeId: string;
  status: string;
  message?: string;
  output?: any;
  assigneeId?: string;
  assigneeName?: string;
}
