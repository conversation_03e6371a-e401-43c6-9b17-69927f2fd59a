"use client";

import { ReactFlowProvider } from "@xyflow/react";
import { WorkflowDesignerInner } from "./workflow-designer-inner";
import { WorkflowDesignerProps } from "./types";
import { NodePalette } from "../node-palette";

function WorkflowDesigner({ currentWorkflow, onWorkflowSaved }: Readonly<WorkflowDesignerProps>) {
  return (
    <div className="h-full w-full flex overflow-hidden">
      <NodePalette />
      <div className="flex-1 h-full overflow-hidden">
        <ReactFlowProvider>
          <WorkflowDesignerInner
            currentWorkflow={currentWorkflow}
            onWorkflowSaved={onWorkflowSaved}
          />
        </ReactFlowProvider>
      </div>
    </div>
  );
}

export default WorkflowDesigner;
