import { useState, useEffect } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Clock,
  Play,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  ArrowLeft,
  RotateCw,
  StopCircle,
  UserCircle,
  ArrowRightCircle
} from "lucide-react"
import { formatDistanceToNow, format } from "date-fns"
import { type WorkflowRun } from "./workflow-runs-table"
import { ReactFlow, <PERSON>s, Background, BackgroundVariant } from "@xyflow/react"
import "@xyflow/react/dist/style.css"
import { ScrollArea } from "@/components/ui/scroll-area"
import { workflowRunApi } from "@/services/workflowRunApi"
import { toast } from "@/hooks/use-toast"
import socketService from "@/services/socket"
import { NodeResultsPanel, type NodeResult } from "./node-results-panel"

interface WorkflowRunDetailProps {
  workflowRun: WorkflowRun
  onBack?: () => void
}

export function WorkflowRunDetail({ workflowRun, onBack }: WorkflowRunDetailProps) {
  const [selectedTab, setSelectedTab] = useState("overview")
  const [flowNodes, setFlowNodes] = useState([])
  const [flowEdges, setFlowEdges] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [nodeResults, setNodeResults] = useState<NodeResult[]>([])
  const [currentNodeId, setCurrentNodeId] = useState<string | null>(null)

  // Set up WebSocket connection for real-time updates
  useEffect(() => {
    if (!workflowRun?.id) return;

    // Connect to socket
    socketService.connect();

    // Listen for workflow run updates
    const handleWorkflowRunUpdate = (data: any) => {
      if (data.id === workflowRun.id) {
        console.log('Received real-time workflow run update:', data);
        // Trigger a refresh when we receive an update for this workflow run
        if (onBack) {
          onBack(); // This will refresh the parent component with the latest data
        }
      }
    };

    // Listen for node run updates
    const handleNodeRunUpdate = (data: any) => {
      if (data.workflowRunId === workflowRun.id) {
        console.log('Received node run update:', data);
        // Trigger a refresh when we receive a node update for this workflow run
        if (onBack) {
          onBack(); // This will refresh the parent component with the latest data
        }
      }
    };

    // Register socket event listeners
    socketService.addListener('workflowRun:updated', handleWorkflowRunUpdate);
    socketService.addListener('nodeRun:updated', handleNodeRunUpdate);

    // Clean up socket listeners on unmount
    return () => {
      socketService.removeListener('workflowRun:updated', handleWorkflowRunUpdate);
      socketService.removeListener('nodeRun:updated', handleNodeRunUpdate);
    };
  }, [workflowRun?.id, onBack])

  // Parse workflow data and convert nodeRuns to NodeResult format
  useEffect(() => {
    if (workflowRun?.workflow?.nodes && workflowRun?.workflow?.edges) {
      try {
        // Parse nodes and edges from the workflow
        let nodes = typeof workflowRun.workflow.nodes === 'string'
          ? JSON.parse(workflowRun.workflow.nodes)
          : workflowRun.workflow.nodes

        let edges = typeof workflowRun.workflow.edges === 'string'
          ? JSON.parse(workflowRun.workflow.edges)
          : workflowRun.workflow.edges

        // Add status to the nodes based on nodeRuns
        nodes = nodes.map(node => {
          const nodeRun = workflowRun.nodeRuns.find(nr => nr.nodeId === node.id)
          return {
            ...node,
            data: {
              ...node.data,
              status: nodeRun?.status ?? 'PENDING'
            },
            className: getNodeStatusClass(nodeRun?.status ?? 'PENDING')
          }
        })

        setFlowNodes(nodes)
        setFlowEdges(edges)

        // Convert nodeRuns to NodeResult format for the results panel
        if (workflowRun.nodeRuns && workflowRun.nodeRuns.length > 0) {
          // First, determine the execution order of nodes based on the workflow structure
          const executionOrder = determineExecutionOrder(nodes, edges);

          // Map nodeRuns to NodeResult format
          const results = workflowRun.nodeRuns.map(nodeRun => {
            // Find the corresponding node to get its name and type
            const node = nodes.find(n => n.id === nodeRun.nodeId);
            return {
              nodeId: nodeRun.nodeId,
              nodeType: node?.type ?? 'unknown',
              nodeName: node?.data?.label ?? nodeRun.nodeId,
              status: nodeRun.status as any,
              executionTime: nodeRun.startedAt ?? undefined,
              output: nodeRun.output ?? undefined,
              error: nodeRun.output?.error ?? undefined,
              // Add an order property based on the execution order
              order: executionOrder.indexOf(nodeRun.nodeId)
            };
          });

          // Sort the results based on the execution order
          results.sort((a, b) => {
            // If a node is not in the execution order, put it at the end
            if (a.order === -1) return 1;
            if (b.order === -1) return -1;
            return a.order - b.order;
          });

          setNodeResults(results);

          // Set current node ID to the most recently active node
          const runningNode = workflowRun.nodeRuns.find(nr => nr.status === 'RUNNING');
          if (runningNode) {
            setCurrentNodeId(runningNode.nodeId);
          } else {
            // If no running node, set to the last completed node
            const sortedNodes = [...workflowRun.nodeRuns]
              .filter(nr => nr.status === 'SUCCESS' || nr.status === 'FAILED')
              .sort((a, b) => {
                if (!a.finishedAt || !b.finishedAt) return 0;
                return new Date(b.finishedAt).getTime() - new Date(a.finishedAt).getTime();
              });

            if (sortedNodes.length > 0) {
              setCurrentNodeId(sortedNodes[0].nodeId);
            }
          }
        }
      } catch (error) {
        console.error("Error parsing workflow data:", error)
      }
    }
  }, [workflowRun])

  // Function to refresh workflow run data
  const refreshWorkflowRun = async () => {
    if (!workflowRun?.id) return

    try {
      setIsLoading(true)
      const updatedRun = await workflowRunApi.getWorkflowRunWithDetails(workflowRun.id)

      // If the component is still mounted and the run ID matches
      if (updatedRun.id === workflowRun.id) {
        // Update the parent component with the new data
        if (onBack) {
          // This will trigger a re-render with the new data
          onBack()
          // Then immediately view the updated run
          setTimeout(() => {
            workflowRunApi.getWorkflowRunWithDetails(workflowRun.id).then(run => {
              // This assumes the parent component has a way to set the selected run
              // We'll need to implement this in the parent
            })
          }, 100)
        }
      }
    } catch (error) {
      console.error("Error refreshing workflow run:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Function to cancel a running workflow
  const cancelWorkflow = async () => {
    if (!workflowRun?.id) return

    try {
      setIsLoading(true)
      // Use the proper stopWorkflowRun method instead of just updating status
      await workflowRunApi.stopWorkflowRun(workflowRun.id)
      toast({
        title: "Workflow Cancelled",
        description: "The workflow run has been cancelled.",
      })
      refreshWorkflowRun()
    } catch (error) {
      console.error("Error cancelling workflow:", error)
      toast({
        title: "Error",
        description: "Failed to cancel workflow run. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Function to rerun the workflow
  const rerunWorkflow = async () => {
    if (!workflowRun?.workflowId) return

    try {
      setIsLoading(true)
      await workflowRunApi.createWorkflowRun(workflowRun.workflowId)
      toast({
        title: "Workflow Started",
        description: "A new workflow run has been started.",
      })

      // Go back to the list to see the new run
      if (onBack) {
        onBack()
      }
    } catch (error) {
      console.error("Error rerunning workflow:", error)
      toast({
        title: "Error",
        description: "Failed to start new workflow run. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>
      case 'RUNNING':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Running</Badge>
      case 'WAITING_FOR_USER':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Waiting for User</Badge>
      case 'SUCCESS':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Success</Badge>
      case 'FAILED':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>
      case 'SKIPPED':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Skipped</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'RUNNING':
        return <Play className="h-4 w-4 text-blue-500" />
      case 'WAITING_FOR_USER':
        return <UserCircle className="h-4 w-4 text-amber-500" />
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'SKIPPED':
        return <ArrowRightCircle className="h-4 w-4 text-gray-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  // Helper function to get node status class
  const getNodeStatusClass = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'node-status-pending'
      case 'RUNNING':
        return 'node-status-running'
      case 'WAITING_FOR_USER':
        return 'node-status-waiting'
      case 'SUCCESS':
        return 'node-status-success'
      case 'FAILED':
        return 'node-status-failed'
      default:
        return ''
    }
  }

  // Helper function to format time
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'N/A'
    return format(new Date(timeString), 'PPpp')
  }

  // Helper function to format relative time
  const formatRelativeTime = (timeString: string | null) => {
    if (!timeString) return 'N/A'
    return formatDistanceToNow(new Date(timeString), { addSuffix: true })
  }

  // Helper function to calculate duration
  const calculateDuration = (startTime: string | null, endTime: string | null) => {
    if (!startTime) return 'Not started'
    if (!endTime) return 'In progress'

    const start = new Date(startTime).getTime()
    const end = new Date(endTime).getTime()
    const durationMs = end - start

    if (durationMs < 1000) return `${durationMs}ms`
    if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`
    return `${Math.round(durationMs / 60000)}m ${Math.round((durationMs % 60000) / 1000)}s`
  }

  // Helper function to determine the execution order of nodes based on the workflow structure
  const determineExecutionOrder = (nodes: any[], edges: any[]): string[] => {
    // Find start nodes (nodes with no incoming edges)
    const startNodeIds = nodes
      .filter(node => !edges.some(edge => edge.target === node.id))
      .map(node => node.id);

    // Initialize the execution order with start nodes
    const executionOrder: string[] = [...startNodeIds];

    // Build a map of node dependencies (source -> targets)
    const nodeTargets: Record<string, string[]> = {};
    edges.forEach(edge => {
      if (!nodeTargets[edge.source]) {
        nodeTargets[edge.source] = [];
      }
      nodeTargets[edge.source].push(edge.target);
    });

    // Process nodes in topological order
    const processedNodes = new Set(startNodeIds);
    let lastSize = 0;

    // Continue until we can't add any more nodes or we've processed all nodes
    while (processedNodes.size > lastSize && processedNodes.size < nodes.length) {
      lastSize = processedNodes.size;

      // Find nodes whose dependencies have all been processed
      nodes.forEach(node => {
        if (processedNodes.has(node.id)) return; // Skip already processed nodes

        // Check if all incoming edges are from processed nodes
        const incomingEdges = edges.filter(edge => edge.target === node.id);
        const allDependenciesProcessed = incomingEdges.every(edge =>
          processedNodes.has(edge.source)
        );

        if (allDependenciesProcessed) {
          processedNodes.add(node.id);
          executionOrder.push(node.id);
        }
      });
    }

    // Add any remaining nodes that couldn't be ordered (in case of cycles)
    nodes.forEach(node => {
      if (!processedNodes.has(node.id)) {
        executionOrder.push(node.id);
      }
    });

    return executionOrder;
  };

  // Sort node runs by execution order (started time) as a fallback
  const sortedNodeRuns = [...(workflowRun?.nodeRuns || [])].sort((a, b) => {
    if (!a.startedAt && !b.startedAt) return 0
    if (!a.startedAt) return 1
    if (!b.startedAt) return -1
    return new Date(a.startedAt).getTime() - new Date(b.startedAt).getTime()
  })

  // Calculate progress percentage
  const calculateProgress = () => {
    if (!workflowRun?.nodeRuns?.length) return 0

    const totalNodes = workflowRun.nodeRuns.length
    const completedNodes = workflowRun.nodeRuns.filter(
      node => node.status === 'SUCCESS' || node.status === 'FAILED'
    ).length

    return Math.round((completedNodes / totalNodes) * 100)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {onBack && (
              <Button variant="ghost" size="icon" onClick={onBack} className="mr-2">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <div>
              <CardTitle className="text-xl">
                Workflow Run {workflowRun.id.substring(0, 8)}
              </CardTitle>
              <CardDescription>
                {workflowRun.workflow?.name || 'Unknown Workflow'}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {isLoading && <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />}
            {getStatusIcon(workflowRun.status)}
            {getStatusBadge(workflowRun.status)}

            <div className="flex space-x-2 ml-4">
              {(workflowRun.status === 'RUNNING' || workflowRun.status === 'PENDING' || workflowRun.status === 'WAITING_FOR_USER') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={cancelWorkflow}
                  disabled={isLoading}
                >
                  <StopCircle className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={refreshWorkflowRun}
                disabled={isLoading}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={rerunWorkflow}
                disabled={isLoading}
              >
                <RotateCw className="h-4 w-4 mr-1" />
                Rerun
              </Button>
            </div>
          </div>
        </div>

        {(workflowRun.status === 'RUNNING' || workflowRun.status === 'PENDING') && (
          <div className="mt-4">
            <div className="flex justify-between text-sm mb-1">
              <span>Progress</span>
              <span>{calculateProgress()}%</span>
            </div>
            <Progress value={calculateProgress()} className="h-2" />
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="nodes">Node Runs</TabsTrigger>
            <TabsTrigger value="results">Node Results</TabsTrigger>
            <TabsTrigger value="visualization">Visualization</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(workflowRun.status)}
                    <span className="font-medium">{workflowRun.status}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Duration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="font-medium">{calculateDuration(workflowRun.startedAt, workflowRun.finishedAt)}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Completion</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="font-medium">{calculateProgress()}%</div>
                  <Progress value={calculateProgress()} className="h-2 mt-2" />
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                      <Clock className="h-4 w-4 text-gray-500" />
                    </div>
                    <div>
                      <p className="font-medium">Created</p>
                      <p className="text-sm text-muted-foreground" title={formatTime(workflowRun.createdAt)}>
                        {formatRelativeTime(workflowRun.createdAt)}
                      </p>
                    </div>
                  </div>

                  <Separator className="my-2" />

                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <Play className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <p className="font-medium">Started</p>
                      <p className="text-sm text-muted-foreground" title={formatTime(workflowRun.startedAt)}>
                        {workflowRun.startedAt ? formatRelativeTime(workflowRun.startedAt) : 'Not started yet'}
                      </p>
                    </div>
                  </div>

                  <Separator className="my-2" />

                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${workflowRun.finishedAt ? (workflowRun.status === 'SUCCESS' ? 'bg-green-100' : 'bg-red-100') : 'bg-gray-100'}`}>
                      {workflowRun.finishedAt ? (
                        workflowRun.status === 'SUCCESS' ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )
                      ) : (
                        <Clock className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">Finished</p>
                      <p className="text-sm text-muted-foreground" title={formatTime(workflowRun.finishedAt)}>
                        {workflowRun.finishedAt ? formatRelativeTime(workflowRun.finishedAt) : 'Not finished yet'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Node Run Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex flex-col items-center p-3 bg-green-50 rounded-md">
                    <CheckCircle className="h-5 w-5 text-green-500 mb-1" />
                    <span className="text-xl font-bold">
                      {workflowRun.nodeRuns?.filter(n => n.status === 'SUCCESS').length || 0}
                    </span>
                    <span className="text-sm text-muted-foreground">Success</span>
                  </div>

                  <div className="flex flex-col items-center p-3 bg-red-50 rounded-md">
                    <XCircle className="h-5 w-5 text-red-500 mb-1" />
                    <span className="text-xl font-bold">
                      {workflowRun.nodeRuns?.filter(n => n.status === 'FAILED').length || 0}
                    </span>
                    <span className="text-sm text-muted-foreground">Failed</span>
                  </div>

                  <div className="flex flex-col items-center p-3 bg-blue-50 rounded-md">
                    <Play className="h-5 w-5 text-blue-500 mb-1" />
                    <span className="text-xl font-bold">
                      {workflowRun.nodeRuns?.filter(n => n.status === 'RUNNING').length || 0}
                    </span>
                    <span className="text-sm text-muted-foreground">Running</span>
                  </div>

                  <div className="flex flex-col items-center p-3 bg-yellow-50 rounded-md">
                    <Clock className="h-5 w-5 text-yellow-500 mb-1" />
                    <span className="text-xl font-bold">
                      {workflowRun.nodeRuns?.filter(n => n.status === 'PENDING').length || 0}
                    </span>
                    <span className="text-sm text-muted-foreground">Pending</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Workflow Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Workflow ID</p>
                    <p className="font-mono text-sm">{workflowRun.workflowId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Run ID</p>
                    <p className="font-mono text-sm">{workflowRun.id}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="nodes">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">Status</TableHead>
                    <TableHead>Node ID</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Assigned To</TableHead>
                    <TableHead>Output</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedNodeRuns.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No node runs found
                      </TableCell>
                    </TableRow>
                  ) : (
                    sortedNodeRuns.map((nodeRun) => (
                      <TableRow key={nodeRun.id}>
                        <TableCell>{getStatusIcon(nodeRun.status)}</TableCell>
                        <TableCell className="font-mono text-xs">
                          {nodeRun.nodeId}
                        </TableCell>
                        <TableCell>
                          {nodeRun.startedAt ? formatRelativeTime(nodeRun.startedAt) : 'Not started'}
                        </TableCell>
                        <TableCell>
                          {calculateDuration(nodeRun.startedAt, nodeRun.finishedAt)}
                        </TableCell>
                        <TableCell>
                          {nodeRun.assigneeId ? (
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6">
                                <AvatarFallback className="text-xs">
                                  {nodeRun.assignee?.firstName?.charAt(0) ?? '?'}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">{nodeRun.assignee?.firstName ?? 'Assigned User'}</span>
                              {nodeRun.status === 'WAITING_FOR_USER' && (
                                <Badge variant="outline" className="ml-2 bg-amber-50 text-amber-700 border-amber-200 text-xs">
                                  Waiting
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <span className="text-gray-500 text-sm">Not assigned</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {nodeRun.output ? (
                            <details>
                              <summary className="cursor-pointer text-sm text-blue-600">View Output</summary>
                              <ScrollArea className="h-[100px] mt-2">
                                <pre className="text-xs p-2 bg-gray-50 rounded-md">
                                  {JSON.stringify(nodeRun.output, null, 2)}
                                </pre>
                              </ScrollArea>
                            </details>
                          ) : (
                            <span className="text-gray-500 text-sm">No output</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="results">
            {nodeResults.length > 0 ? (
              <NodeResultsPanel
                results={nodeResults}
                currentNodeId={currentNodeId}
                isRunning={workflowRun.status === 'RUNNING'}
              />
            ) : (
              <div className="p-4 text-center text-gray-500">
                No node execution results available
              </div>
            )}
          </TabsContent>

          <TabsContent value="visualization">
            <div style={{ height: '500px' }} className="border rounded-md">
              <ReactFlow
                nodes={flowNodes}
                edges={flowEdges}
                fitView
                attributionPosition="bottom-right"
              >
                <Controls />
                <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
              </ReactFlow>
            </div>
            <style>
              {`
              .node-status-pending {
                background-color: rgba(250, 240, 137, 0.1);
                border-color: rgba(234, 179, 8, 0.5) !important;
              }
              .node-status-running {
                background-color: rgba(147, 197, 253, 0.1);
                border-color: rgba(59, 130, 246, 0.5) !important;
              }
              .node-status-waiting {
                background-color: rgba(251, 191, 36, 0.1);
                border-color: rgba(251, 191, 36, 0.5) !important;
              }
              .node-status-success {
                background-color: rgba(134, 239, 172, 0.1);
                border-color: rgba(34, 197, 94, 0.5) !important;
              }
              .node-status-failed {
                background-color: rgba(252, 165, 165, 0.1);
                border-color: rgba(239, 68, 68, 0.5) !important;
              }
              `}
            </style>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}