"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON>alog<PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"
import type { Workflow } from "./workflows-table"

interface SaveWorkflowDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (name: string, description: string) => void
  currentWorkflow: Workflow | null
}

export function SaveWorkflowDialog({ open, onOpenChange, onSave, currentWorkflow }: SaveWorkflowDialogProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")

  // Update form when currentWorkflow changes or dialog opens
  useEffect(() => {
    if (open) {
      if (currentWorkflow) {
        setName(currentWorkflow.name)
        setDescription(currentWorkflow.description)
      } else {
        setName("")
        setDescription("")
      }
    }
  }, [currentWorkflow, open])

  const handleSave = () => {
    if (!name.trim()) {
      toast({
        title: "Name required",
        description: "Please enter a name for your workflow.",
        variant: "destructive",
      })
      return
    }

    onSave(name, description)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{currentWorkflow ? "Update Workflow" : "Save Workflow"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Name
            </label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} placeholder="Enter workflow name" />
          </div>
          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter workflow description (optional)"
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>{currentWorkflow ? "Update" : "Save"}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

