"use client"

import { useState, useEffect } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Edit, Trash2, Plus, FileText, Calendar, Search, ArrowUpDown, Play } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { workflowApi } from "@/services/workflowApi"
import { Node, Edge } from '@xyflow/react'
import { useRouter } from "next/navigation"

export interface Workflow {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  nodes: Node[]
  edges: Edge[]
  viewport: {
    x: number
    y: number
    zoom: number
  }
  status: 'draft' | 'active' | 'archived'
  runs?: {
    id: string
    status: 'PENDING' | 'RUNNING' | 'WAITING_FOR_USER' | 'SUCCESS' | 'FAILED'
    startedAt: string | null
    finishedAt: string | null
    createdAt: string
  }[]
  isExecuting?: boolean
  executionData?: {
    currentNodeId?: string
    executionPath?: string[]
  }
}

interface WorkflowsTableProps {
  readonly onCreateNew: () => void
  readonly onLoadWorkflow: (workflow: Workflow) => void
  readonly currentWorkflow: Workflow | null
}

export function WorkflowsTable({ onCreateNew, onLoadWorkflow, currentWorkflow }: WorkflowsTableProps) {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<Workflow[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [sortField, setSortField] = useState<keyof Workflow>("updatedAt")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingWorkflow, setEditingWorkflow] = useState<Workflow | null>(null)
  const [newName, setNewName] = useState("")
  const [newDescription, setNewDescription] = useState("")
  const [isLoading, setIsLoading] = useState(true)

  // Load workflows from backend on component mount
  useEffect(() => {
    loadWorkflows()
  }, [])

  const loadWorkflows = async () => {
    try {
      setIsLoading(true)
      const workflows = await workflowApi.getAllWorkflows()
      setWorkflows(workflows)
    } catch (error: any) {
      console.error("Error loading workflows:", error)
      // Extract detailed error information
      const errorMessage = error.response?.data?.message || 'Failed to load workflows'
      const errorDetails = error.response?.data?.error || error.message
      const errorStack = error.response?.data?.stack

      toast({
        title: "Error loading workflows",
        description: (
          <div className="space-y-2">
            <p>{errorMessage}</p>
            <p className="text-sm text-red-500">{errorDetails}</p>
            {errorStack && (
              <pre className="text-xs bg-red-50 p-2 rounded mt-2 overflow-auto">
                {errorStack}
              </pre>
            )}
          </div>
        ),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSort = (field: keyof Workflow) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const filteredAndSortedWorkflows = workflows
    .filter(
      (workflow) =>
        workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.description.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .sort((a, b) => {
      if (sortField === "createdAt" || sortField === "updatedAt") {
        const dateA = new Date(a[sortField]).getTime()
        const dateB = new Date(b[sortField]).getTime()
        return sortDirection === "asc" ? dateA - dateB : dateB - dateA
      } else if (sortField === "nodes" || sortField === "edges") {
        // Skip sorting for these fields as they're not string values
        return 0
      } else {
        const valueA = String(a[sortField]).toLowerCase()
        const valueB = String(b[sortField]).toLowerCase()
        return sortDirection === "asc" ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA)
      }
    })

  const handleDeleteWorkflow = async (id: string) => {
    try {
      await workflowApi.deleteWorkflow(id);
      const updatedWorkflows = workflows.filter((workflow) => workflow.id !== id);
      setWorkflows(updatedWorkflows);
      toast({
        title: "Workflow deleted",
        description: "The workflow has been deleted successfully.",
      });
    } catch (error: any) {
      console.error("Error deleting workflow:", error);
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || "There was a problem deleting the workflow.";
      toast({
        title: "Error deleting workflow",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }

  const handleEditWorkflow = (workflow: Workflow) => {
    setEditingWorkflow(workflow)
    setNewName(workflow.name)
    setNewDescription(workflow.description)
    setIsEditDialogOpen(true)
  }

  const saveEditedWorkflow = async () => {
    if (!editingWorkflow) return

    try {
      const updatedWorkflow = {
        ...editingWorkflow,
        name: newName,
        description: newDescription,
      }

      const savedWorkflow = await workflowApi.updateWorkflow(editingWorkflow.id, updatedWorkflow)

      // Update local state
      setWorkflows(workflows.map((workflow) =>
        workflow.id === savedWorkflow.id ? savedWorkflow : workflow
      ))
      setIsEditDialogOpen(false)

      toast({
        title: "Workflow updated",
        description: "The workflow details have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating workflow:", error)
      toast({
        title: "Error updating workflow",
        description: "There was a problem updating the workflow details.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Workflows</h1>
        <Button onClick={onCreateNew} className="bg-green-500 hover:bg-green-600">
          <Plus className="h-4 w-4 mr-2" /> New Workflow
        </Button>
      </div>

      <div className="flex items-center mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">
                <Button variant="ghost" onClick={() => handleSort("name")} className="flex items-center">
                  Name <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="hidden md:table-cell">
                <Button variant="ghost" onClick={() => handleSort("description")} className="flex items-center">
                  Description <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="hidden md:table-cell">
                <Button variant="ghost" onClick={() => handleSort("updatedAt")} className="flex items-center">
                  Last Modified <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary mr-2"></div>
                    Loading workflows...
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredAndSortedWorkflows.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                  {searchTerm ? (
                    <div>
                      <FileText className="mx-auto h-8 w-8 mb-2" />
                      No workflows found matching "{searchTerm}"
                    </div>
                  ) : (
                    <div>
                      <FileText className="mx-auto h-8 w-8 mb-2" />
                      No workflows yet. Create your first workflow!
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedWorkflows.map((workflow) => {
                // Check if workflow is running
                const isRunning = workflow.runs?.some(run =>
                  run.status === 'RUNNING' || run.status === 'PENDING' || run.status === 'WAITING_FOR_USER'
                ) || workflow.isExecuting;

                // Get the current node ID if workflow is running
                const currentNodeId = workflow.executionData?.currentNodeId;

                // Get the latest run status
                const latestRunStatus = workflow.runs?.[0]?.status;

                return (
                <TableRow
                  key={workflow.id}
                  className={`
                    ${currentWorkflow?.id === workflow.id ? "bg-blue-50" : ""}
                    ${isRunning ? "border-l-4 border-l-yellow-400" : ""}
                  `}
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      {isRunning ? (
                        <Play className="h-4 w-4 mr-2 text-yellow-500 animate-pulse" />
                      ) : (
                        <FileText className="h-4 w-4 mr-2 text-blue-500" />
                      )}
                      {workflow.name}
                      {currentWorkflow?.id === workflow.id && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">Current</span>
                      )}
                      {isRunning && (
                        <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded flex items-center">
                          <span className="mr-1">Running</span>
                          {currentNodeId && <span className="text-xs">• Node: {currentNodeId}</span>}
                        </span>
                      )}
                      {latestRunStatus === 'WAITING_FOR_USER' && (
                        <span className="ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded">
                          Waiting for user
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-muted-foreground">
                    {workflow.description || "No description"}
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                      {formatDate(workflow.updatedAt)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onLoadWorkflow(workflow)}
                        className="bg-blue-50 border-blue-200 hover:bg-blue-100"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      {/* <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/dashboard/workflows/designer?id=${workflow.id}`)}
                        className="bg-green-50 border-green-200 hover:bg-green-100"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Designer
                      </Button> */}
                      <Button variant="outline" size="sm" onClick={() => handleEditWorkflow(workflow)}>
                        <FileText className="h-4 w-4 mr-2" />
                        Rename
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteWorkflow(workflow.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
              })
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Workflow</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Name
              </label>
              <Input
                id="name"
                value={newName}
                onChange={(e) => setNewName(e.target.value)}
                placeholder="Enter workflow name"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              <Input
                id="description"
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
                placeholder="Enter workflow description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveEditedWorkflow}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

