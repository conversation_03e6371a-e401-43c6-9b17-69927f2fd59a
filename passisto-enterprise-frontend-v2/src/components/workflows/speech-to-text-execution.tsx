"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { FileUpload } from "@/components/workflows/file-upload"
import { FileAttachments } from "@/components/workflows/file-attachments"
import { toast } from "@/hooks/use-toast"
import { useAuth } from "@clerk/nextjs"
import { Mic, Loader2, Paperclip, AlertCircle } from "lucide-react"

interface SpeechToTextExecutionProps {
  workflowRunId: string
  nodeId: string
  nodeData: any
  onComplete: () => void
}

export function SpeechToTextExecution({ workflowRunId, nodeId, nodeData, onComplete }: SpeechToTextExecutionProps) {
  const [sourceType, setSourceType] = useState(nodeData.sourceType || "audio")
  const [source, setSource] = useState(nodeData.source || "")
  const [transcription, setTranscription] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [hasUploaded, setHasUploaded] = useState(false)
  const [fileCount, setFileCount] = useState(0)
  const isDemoMode = nodeData.demoMode || false
  const { getToken } = useAuth()
  const token = getToken()

  // Fetch files on component mount to check if files already exist
  useEffect(() => {
    fetchFiles()
  }, [])

  const fetchFiles = async () => {
    try {
      const response = await fetch(`/files/node-run/${workflowRunId}-${nodeId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      if (response.ok) {
        const files = await response.json()
        setFileCount(files.length)
        setHasUploaded(files.length > 0)
      }
    } catch (error) {
      console.error("Error fetching files:", error)
    }
  }

  const handleFileUploaded = (fileData: any) => {
    setHasUploaded(true)
    setFileCount(prev => prev + 1)
  }

  const handleFileDeleted = () => {
    fetchFiles()
    setFileCount(prev => Math.max(0, prev - 1))
    if (fileCount <= 1) {
      setHasUploaded(false)
    }
  }

  const handleProcess = async () => {
    if (!source && !hasUploaded && !isDemoMode) {
      toast({
        title: "Error: File Required",
        description: "You must upload a file before running this node. The workflow will fail without a file.",
        variant: "destructive"
      })
      return
    }

    setIsProcessing(true)

    try {
      // In a real implementation, we would:
      // 1. Process the file or URL using a transcription service
      // 2. Get the transcription text

      // For now, we'll simulate a transcription
      let simulatedTranscription = "This is a simulated transcription. In a real implementation, we would process the audio file and return the actual transcription."

      setTranscription(simulatedTranscription)

      // Send the transcription to the backend
      const response = await fetch("/speech-to-text/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          workflowRunId,
          nodeId,
          transcription: simulatedTranscription,
          source,
          sourceType
        })
      })

      if (!response.ok) {
        throw new Error("Failed to process transcription")
      }

      toast({
        title: "Transcription complete",
        description: "The audio has been successfully transcribed"
      })

      // Complete the node
      onComplete()
    } catch (error) {
      console.error("Transcription error:", error)
      toast({
        title: "Transcription failed",
        description: "There was an error processing the transcription",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mic className="h-5 w-5 text-pink-500" />
          <span>{nodeData.label || "Speech to Text"}</span>
        </CardTitle>
        <CardDescription>
          Convert speech to text by uploading an audio file or providing a URL
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="sourceType">Source Type</Label>
              <Select
                value={sourceType}
                onValueChange={setSourceType}
                disabled={isProcessing}
              >
                <SelectTrigger id="sourceType">
                  <SelectValue placeholder="Select source type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="audio">Audio File</SelectItem>
                  <SelectItem value="video">Video File</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="source">Source URL (Optional)</Label>
              <Input
                id="source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                placeholder="Enter URL to audio/video file"
                disabled={isProcessing}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="service">Transcription Service</Label>
              <Select
                value={nodeData.service || "simulated"}
                disabled={true}
              >
                <SelectTrigger id="service">
                  <SelectValue placeholder="Select transcription service" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simulated">Simulated (Demo)</SelectItem>
                  <SelectItem value="revai">Rev.ai</SelectItem>
                  <SelectItem value="azure-whisper">Azure Whisper</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {nodeData.service === "revai" ?
                  "Rev.ai provides professional-grade speech-to-text transcription" :
                  nodeData.service === "azure-whisper" ?
                  "Azure Whisper provides AI-powered speech-to-text with speaker diarization" :
                  "Simulated service for demonstration purposes only"}
              </p>
            </div>

            {isDemoMode && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2 p-2 bg-blue-50 rounded border border-blue-200">
                  <span className="text-sm text-blue-700">Running in demo mode - no file required</span>
                </div>
              </div>
            )}

            {!isDemoMode && !source && !hasUploaded && fileCount === 0 && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2 p-2 bg-red-50 rounded border border-red-200">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-700"><strong>Warning:</strong> A file must be uploaded or the workflow will fail</span>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>File Upload</Label>
                {fileCount > 0 && (
                  <div className="text-sm text-green-600 flex items-center gap-1">
                    <Paperclip className="h-3 w-3" />
                    <span>{fileCount} file{fileCount !== 1 ? 's' : ''} attached</span>
                  </div>
                )}
              </div>

              <FileUpload
                nodeRunId={workflowRunId + "-" + nodeId}
                onFileUploaded={handleFileUploaded}
                accept={sourceType === "audio" ? "audio/*" : "video/*"}
                maxSize={50}
              />

              <p className="text-xs text-muted-foreground">
                Select a file and click "Upload" to upload it.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center">
                <Label>Attached Files</Label>
              </div>

              <FileAttachments
                nodeRunId={workflowRunId + "-" + nodeId}
                className="mt-2"
                onFileDeleted={handleFileDeleted}
              />
            </div>
          </div>
        </div>

        {transcription && (
          <div className="space-y-2 mt-4">
            <Label htmlFor="transcription">Transcription</Label>
            <Textarea
              id="transcription"
              value={transcription}
              readOnly
              className="min-h-[100px]"
            />
          </div>
        )}
      </CardContent>

      <CardFooter className="flex justify-end gap-2">
        <Button
          onClick={handleProcess}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            "Process Transcription"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
