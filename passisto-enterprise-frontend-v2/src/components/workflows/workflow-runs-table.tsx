import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Play,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  AlertTriangle,
  RotateCw,
  StopCircle,
  ArrowUpDown,
  UserCircle,
  ArrowRightCircle
} from "lucide-react"
import { formatDistanceToNow, format } from "date-fns"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { workflowRunApi } from "@/services/workflowRunApi"
import { toast } from "@/hooks/use-toast"

export interface NodeRun {
  id: string
  nodeId: string
  status: 'PENDING' | 'RUNNING' | 'WAITING_FOR_USER' | 'SUCCESS' | 'FAILED' | 'SKIPPED'
  startedAt: string | null
  finishedAt: string | null
  output: any
}

export interface WorkflowRun {
  id: string
  workflowId: string
  status: 'PENDING' | 'RUNNING' | 'WAITING_FOR_USER' | 'SUCCESS' | 'FAILED' | 'SKIPPED'
  startedAt: string | null
  finishedAt: string | null
  createdAt: string
  nodeRuns: NodeRun[]
  workflow?: {
    name: string
  }
}

interface WorkflowRunsTableProps {
  workflowRuns: WorkflowRun[]
  onViewRun: (run: WorkflowRun) => void
  onRerunWorkflow: (workflowId: string) => void
  isLoading?: boolean
  showHeader?: boolean
}

export function WorkflowRunsTable({
  workflowRuns,
  onViewRun,
  onRerunWorkflow,
  isLoading = false,
  showHeader = false,
}: WorkflowRunsTableProps) {
  const [expandedRunId, setExpandedRunId] = useState<string | null>(null)
  const [sortField, setSortField] = useState<string>("createdAt")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")

  // Helper function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>
      case 'RUNNING':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Running</Badge>
      case 'WAITING_FOR_USER':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Waiting for User</Badge>
      case 'SUCCESS':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Success</Badge>
      case 'FAILED':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>
      case 'SKIPPED':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Skipped</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'RUNNING':
        return <Play className="h-4 w-4 text-blue-500" />
      case 'WAITING_FOR_USER':
        return <UserCircle className="h-4 w-4 text-purple-500" />
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'SKIPPED':
        return <ArrowRightCircle className="h-4 w-4 text-gray-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  // Helper function to format time
  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'N/A'
    return formatDistanceToNow(new Date(timeString), { addSuffix: true })
  }

  // Helper function to format absolute time
  const formatAbsoluteTime = (timeString: string | null) => {
    if (!timeString) return 'N/A'
    return format(new Date(timeString), 'MMM dd, yyyy HH:mm:ss')
  }

  // Helper function to calculate duration
  const calculateDuration = (startTime: string | null, endTime: string | null) => {
    if (!startTime) return 'Not started'
    if (!endTime) return 'In progress'

    const start = new Date(startTime).getTime()
    const end = new Date(endTime).getTime()
    const durationMs = end - start

    if (durationMs < 1000) return `${durationMs}ms`
    if (durationMs < 60000) return `${Math.round(durationMs / 1000)}s`
    return `${Math.round(durationMs / 60000)}m ${Math.round((durationMs % 60000) / 1000)}s`
  }

  // Sort workflow runs
  const sortedWorkflowRuns = [...workflowRuns].sort((a, b) => {
    let aValue: any = null
    let bValue: any = null

    // Get values based on sort field
    switch (sortField) {
      case "status":
        aValue = a.status
        bValue = b.status
        break
      case "workflow":
        aValue = a.workflow?.name || ''
        bValue = b.workflow?.name || ''
        break
      case "started":
        aValue = a.startedAt ? new Date(a.startedAt).getTime() : 0
        bValue = b.startedAt ? new Date(b.startedAt).getTime() : 0
        break
      case "duration":
        const aDuration = a.startedAt && a.finishedAt ?
          new Date(a.finishedAt).getTime() - new Date(a.startedAt).getTime() : 0
        const bDuration = b.startedAt && b.finishedAt ?
          new Date(b.finishedAt).getTime() - new Date(b.startedAt).getTime() : 0
        aValue = aDuration
        bValue = bDuration
        break
      case "createdAt":
      default:
        aValue = new Date(a.createdAt).getTime()
        bValue = new Date(b.createdAt).getTime()
    }

    // Compare values based on sort direction
    if (sortDirection === "asc") {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Handle sorting
  const handleSort = (field: string) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      // Set new field and default to descending
      setSortField(field)
      setSortDirection("desc")
    }
  }

  // Handle cancelling a workflow run
  const handleCancelWorkflow = async (runId: string) => {
    try {
      // Use the proper stopWorkflowRun method instead of just updating status
      await workflowRunApi.stopWorkflowRun(runId)
      toast({
        title: "Workflow Cancelled",
        description: "The workflow run has been cancelled.",
      })
      // Refresh the list through the parent component
      onRerunWorkflow("refresh") // This is a hack to trigger a refresh
    } catch (error) {
      console.error("Error cancelling workflow:", error)
      toast({
        title: "Error",
        description: "Failed to cancel workflow run. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">Status</TableHead>
            <TableHead>
              <div className="flex items-center cursor-pointer" onClick={() => handleSort("workflow")}>
                Workflow
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center cursor-pointer" onClick={() => handleSort("started")}>
                Started
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </div>
            </TableHead>
            <TableHead>
              <div className="flex items-center cursor-pointer" onClick={() => handleSort("duration")}>
                Duration
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </div>
            </TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    Loading workflow runs...
                  </TableCell>
                </TableRow>
              ) : sortedWorkflowRuns.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No workflow runs found
                  </TableCell>
                </TableRow>
              ) : (
                sortedWorkflowRuns.map((run) => (
                  <TableRow
                    key={run.id}
                    className={
                      run.status === 'RUNNING' ? 'bg-blue-50 dark:bg-blue-950/30' :
                      run.status === 'WAITING_FOR_USER' ? 'bg-purple-50 dark:bg-purple-950/30' :
                      ''
                    }
                  >
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(run.status)}
                        <span className="hidden md:inline">{getStatusBadge(run.status)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {run.workflow?.name || 'Unknown workflow'}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span title={formatAbsoluteTime(run.startedAt || run.createdAt)}>
                          {formatTime(run.startedAt || run.createdAt)}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatAbsoluteTime(run.startedAt || run.createdAt)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {calculateDuration(run.startedAt, run.finishedAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onViewRun(run)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>

                          {(run.status === 'RUNNING' || run.status === 'PENDING' || run.status === 'WAITING_FOR_USER') && (
                            <DropdownMenuItem onClick={() => handleCancelWorkflow(run.id)}>
                              <StopCircle className="mr-2 h-4 w-4" />
                              Cancel Run
                            </DropdownMenuItem>
                          )}

                          <DropdownMenuSeparator />

                          <DropdownMenuItem onClick={() => onRerunWorkflow(run.workflowId)}>
                            <RotateCw className="mr-2 h-4 w-4" />
                            Run Again
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
      </div>
  )
}