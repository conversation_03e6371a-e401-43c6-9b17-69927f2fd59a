"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Trash2, GripVertical } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export interface FormField {
  id: string
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'date'
  label: string
  placeholder?: string
  required?: boolean
  options?: { value: string; label: string }[]
}

interface FormFieldsEditorProps {
  fields: FormField[]
  onChange: (fields: FormField[]) => void
}

export function FormFieldsEditor({ fields = [], onChange }: FormFieldsEditorProps) {
  const [newField, setNewField] = useState<Partial<FormField>>({
    id: '',
    type: 'text',
    label: '',
    placeholder: '',
    required: false
  })
  const [newOption, setNewOption] = useState({ value: '', label: '' })
  const [expandedField, setExpandedField] = useState<string | null>(null)

  // Generate a unique ID for a field
  const generateFieldId = (label: string) => {
    const baseId = label.toLowerCase().replace(/[^a-z0-9]/g, '_')
    const existingIds = fields.map(f => f.id)
    let id = baseId
    let counter = 1
    
    while (existingIds.includes(id)) {
      id = `${baseId}_${counter}`
      counter++
    }
    
    return id
  }

  // Add a new field
  const addField = () => {
    if (!newField.label) return
    
    const fieldId = newField.id || generateFieldId(newField.label)
    const fieldToAdd: FormField = {
      id: fieldId,
      type: newField.type || 'text',
      label: newField.label,
      placeholder: newField.placeholder,
      required: newField.required || false,
      ...(newField.type === 'select' ? { options: [] } : {})
    }
    
    onChange([...fields, fieldToAdd])
    setNewField({
      id: '',
      type: 'text',
      label: '',
      placeholder: '',
      required: false
    })
    
    // Expand the newly added field if it's a select field
    if (newField.type === 'select') {
      setExpandedField(fieldId)
    }
  }

  // Remove a field
  const removeField = (index: number) => {
    const updatedFields = [...fields]
    updatedFields.splice(index, 1)
    onChange(updatedFields)
  }

  // Update a field
  const updateField = (index: number, updates: Partial<FormField>) => {
    const updatedFields = [...fields]
    updatedFields[index] = { ...updatedFields[index], ...updates }
    onChange(updatedFields)
  }

  // Add an option to a select field
  const addOption = (fieldIndex: number) => {
    if (!newOption.value || !newOption.label) return
    
    const updatedFields = [...fields]
    const field = updatedFields[fieldIndex]
    
    if (field.type === 'select') {
      field.options = [...(field.options || []), { ...newOption }]
      onChange(updatedFields)
      setNewOption({ value: '', label: '' })
    }
  }

  // Remove an option from a select field
  const removeOption = (fieldIndex: number, optionIndex: number) => {
    const updatedFields = [...fields]
    const field = updatedFields[fieldIndex]
    
    if (field.type === 'select' && field.options) {
      field.options.splice(optionIndex, 1)
      onChange(updatedFields)
    }
  }

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Form Fields</h3>
      
      {/* Existing fields */}
      {fields.length > 0 ? (
        <Accordion
          type="single"
          collapsible
          value={expandedField || undefined}
          onValueChange={(value) => setExpandedField(value)}
          className="space-y-2"
        >
          {fields.map((field, index) => (
            <AccordionItem 
              key={field.id} 
              value={field.id}
              className="border rounded-md overflow-hidden"
            >
              <AccordionTrigger className="px-3 py-2 hover:bg-gray-50">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">{field.label}</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span className="px-2 py-0.5 bg-gray-100 rounded">{field.type}</span>
                    {field.required && <span className="px-2 py-0.5 bg-red-100 text-red-800 rounded">Required</span>}
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-3 py-2 bg-gray-50">
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor={`field-${index}-id`} className="text-xs">ID</Label>
                      <Input
                        id={`field-${index}-id`}
                        value={field.id}
                        onChange={(e) => updateField(index, { id: e.target.value })}
                        className="h-8 text-sm"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor={`field-${index}-type`} className="text-xs">Type</Label>
                      <Select
                        value={field.type}
                        onValueChange={(value) => updateField(index, { 
                          type: value as FormField['type'],
                          ...(value === 'select' && !field.options ? { options: [] } : {})
                        })}
                      >
                        <SelectTrigger id={`field-${index}-type`} className="h-8 text-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="text">Text</SelectItem>
                          <SelectItem value="textarea">Textarea</SelectItem>
                          <SelectItem value="select">Select</SelectItem>
                          <SelectItem value="checkbox">Checkbox</SelectItem>
                          <SelectItem value="date">Date</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <Label htmlFor={`field-${index}-label`} className="text-xs">Label</Label>
                    <Input
                      id={`field-${index}-label`}
                      value={field.label}
                      onChange={(e) => updateField(index, { label: e.target.value })}
                      className="h-8 text-sm"
                    />
                  </div>
                  
                  {(field.type === 'text' || field.type === 'textarea') && (
                    <div className="space-y-1">
                      <Label htmlFor={`field-${index}-placeholder`} className="text-xs">Placeholder</Label>
                      <Input
                        id={`field-${index}-placeholder`}
                        value={field.placeholder || ''}
                        onChange={(e) => updateField(index, { placeholder: e.target.value })}
                        className="h-8 text-sm"
                      />
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`field-${index}-required`}
                      checked={field.required || false}
                      onCheckedChange={(checked) => updateField(index, { required: checked })}
                    />
                    <Label htmlFor={`field-${index}-required`} className="text-xs">Required field</Label>
                  </div>
                  
                  {/* Options for select fields */}
                  {field.type === 'select' && (
                    <div className="space-y-2 mt-2 p-2 border rounded bg-white">
                      <Label className="text-xs font-medium">Options</Label>
                      
                      {/* Existing options */}
                      {field.options && field.options.length > 0 ? (
                        <div className="space-y-2 mb-2">
                          {field.options.map((option, optionIndex) => (
                            <div key={optionIndex} className="flex items-center gap-2">
                              <Input
                                value={option.value}
                                onChange={(e) => {
                                  const updatedFields = [...fields]
                                  if (updatedFields[index].options) {
                                    updatedFields[index].options![optionIndex].value = e.target.value
                                    onChange(updatedFields)
                                  }
                                }}
                                placeholder="Value"
                                className="h-7 text-xs flex-1"
                              />
                              <Input
                                value={option.label}
                                onChange={(e) => {
                                  const updatedFields = [...fields]
                                  if (updatedFields[index].options) {
                                    updatedFields[index].options![optionIndex].label = e.target.value
                                    onChange(updatedFields)
                                  }
                                }}
                                placeholder="Label"
                                className="h-7 text-xs flex-1"
                              />
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => removeOption(index, optionIndex)}
                                className="h-7 w-7"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500 italic mb-2">No options defined</div>
                      )}
                      
                      {/* Add new option */}
                      <div className="flex items-center gap-2">
                        <Input
                          value={newOption.value}
                          onChange={(e) => setNewOption({ ...newOption, value: e.target.value })}
                          placeholder="Value"
                          className="h-7 text-xs flex-1"
                        />
                        <Input
                          value={newOption.label}
                          onChange={(e) => setNewOption({ ...newOption, label: e.target.value })}
                          placeholder="Label"
                          className="h-7 text-xs flex-1"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => addOption(index)}
                          disabled={!newOption.value || !newOption.label}
                          className="h-7 w-7"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => removeField(index)}
                    className="w-full mt-2"
                  >
                    <Trash2 className="h-4 w-4 mr-2" /> Remove Field
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      ) : (
        <div className="text-sm text-gray-500 italic p-3 border rounded-md">
          No form fields defined. Add fields below.
        </div>
      )}
      
      {/* Add new field form */}
      <div className="space-y-3 p-3 border rounded-md bg-gray-50">
        <h4 className="text-sm font-medium">Add New Field</h4>
        
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-1">
            <Label htmlFor="new-field-type" className="text-xs">Type</Label>
            <Select
              value={newField.type}
              onValueChange={(value) => setNewField({ ...newField, type: value as FormField['type'] })}
            >
              <SelectTrigger id="new-field-type" className="h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Text</SelectItem>
                <SelectItem value="textarea">Textarea</SelectItem>
                <SelectItem value="select">Select</SelectItem>
                <SelectItem value="checkbox">Checkbox</SelectItem>
                <SelectItem value="date">Date</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="new-field-label" className="text-xs">Label</Label>
            <Input
              id="new-field-label"
              value={newField.label}
              onChange={(e) => setNewField({ ...newField, label: e.target.value })}
              placeholder="Field label"
              className="h-8 text-sm"
            />
          </div>
        </div>
        
        {(newField.type === 'text' || newField.type === 'textarea') && (
          <div className="space-y-1">
            <Label htmlFor="new-field-placeholder" className="text-xs">Placeholder</Label>
            <Input
              id="new-field-placeholder"
              value={newField.placeholder}
              onChange={(e) => setNewField({ ...newField, placeholder: e.target.value })}
              placeholder="Field placeholder"
              className="h-8 text-sm"
            />
          </div>
        )}
        
        <div className="flex items-center space-x-2">
          <Switch
            id="new-field-required"
            checked={newField.required || false}
            onCheckedChange={(checked) => setNewField({ ...newField, required: checked })}
          />
          <Label htmlFor="new-field-required" className="text-xs">Required field</Label>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={addField}
          className="w-full"
          disabled={!newField.label}
        >
          <Plus className="h-4 w-4 mr-2" /> Add Field
        </Button>
      </div>
    </div>
  )
}
