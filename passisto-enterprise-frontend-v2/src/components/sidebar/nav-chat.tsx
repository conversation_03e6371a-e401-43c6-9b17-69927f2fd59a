"use client";

import { ChevronRight, LucideIcon } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { Link } from '@/i18n/nvigation';;

interface NavItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: NavItem[];
}

interface NavChatProps {
  readonly chatItems: NavItem[];
}

export function NavChat({ chatItems }: NavChatProps) {
  const renderSubItems = (subItems: NavItem[], level = 1) => {
    const getItemClass = (level: number): string => {
      switch (level) {
        case 1:
          return "text-sm opacity-100";
        case 2:
          return "text-base opacity-90";
        case 3:
          return "text-lg opacity-80";
        default:
          return "text-base opacity-80";
      }
    };

    return subItems.map((subItem) => (
      <SidebarMenuSubItem key={subItem.title}>
        <SidebarMenuSubButton asChild>
          <a href={subItem.url} className={getItemClass(level)}>
            {subItem.icon && <subItem.icon className="mr-2" />}
            <span>{subItem.title}</span>
          </a>
        </SidebarMenuSubButton>
        {subItem.items && (
          <SidebarMenuSub>
            {renderSubItems(subItem.items, level + 1)}
          </SidebarMenuSub>
        )}
      </SidebarMenuSubItem>
    ));
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Chat</SidebarGroupLabel>
      <SidebarMenu>
        {chatItems.map((item) => (
          <SidebarMenuItem key={item.title}>
            {item.items ? (
              <Collapsible asChild defaultOpen={item.isActive} className="group/collapsible">
                <div>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton tooltip={item.title}>
                      {item.icon && <item.icon className="mr-2" />}
                      <span className="text-sm opacity-100">{item.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <a href={subItem.url} className="text-base opacity-90">
                              {subItem.icon && <subItem.icon className="mr-2" />}
                              <span>{subItem.title}</span>
                            </a>
                          </SidebarMenuSubButton>
                          {subItem.items && (
                            <SidebarMenuSub>{renderSubItems(subItem.items, 2)}</SidebarMenuSub>
                          )}
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            ) : (
              <Link href={item.url} passHref>
                <SidebarMenuButton tooltip={item.title}>
                  {item.icon && <item.icon className="mr-2" />}
                  <span className="text-sm opacity-100">{item.title}</span>
                </SidebarMenuButton>
              </Link>
            )}
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
