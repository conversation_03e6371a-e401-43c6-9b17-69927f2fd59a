"use client";

import React from 'react';
import { Link } from '@/i18n/nvigation';; 
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarGroupContent,
} from "@/components/ui/sidebar";
import { Folder, ChevronRight, MessageCircle } from 'lucide-react'; 
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from '@/components/ui/collapsible';

export default function Tree({ item }) {
  const { title, url, icon: Icon, items } = item;

  // If no sub-items exist, render a link button
  if (!items) {
    return (
      <SidebarMenuButton className="data-[active=true]:bg-transparent w-full overflow-hidden text-ellipsis whitespace-nowrap">
        <Link href={url} className="flex items-center">
          {Icon && <Icon className="mr-2" />} {title}
        </Link>
      </SidebarMenuButton>
    );
  }

  return (
    <SidebarGroup>
      {/* <SidebarGroupLabel>Chat</SidebarGroupLabel> */}
      <SidebarGroupContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <Collapsible
              className="group/collapsible [&[data-state=open]>button>svg:first-child]:rotate-90"
              defaultOpen={title === "Chat"}
            >
              <CollapsibleTrigger asChild>
                <SidebarMenuButton className="w-full overflow-hidden text-ellipsis whitespace-nowrap">
                  <ChevronRight className="transition-transform" />
                  <Folder />
                  {title}
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  {items.map((subItem, index) => (
                    <Tree key={index} item={subItem} />
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            </Collapsible>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
