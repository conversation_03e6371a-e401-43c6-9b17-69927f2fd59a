"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>2 } from "lucide-react";
import { Use<PERSON><PERSON><PERSON><PERSON>, useUser, useAuth } from "@clerk/nextjs";
import { Avatar } from "@/components/ui/avatar";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { CREATE_STRIPE_PORTAL_SESSION } from "@/utils/routes";
import axiosInstance from "@/config/axios";
import { toast } from "sonner";
import { useBackendUser } from "@/hooks/useBackendUser";
import { billingPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useTranslations } from "next-intl"; // Import useTranslations

export function NavUser() {
  const { user } = useUser();
  const { getToken } = useAuth();
  const { backendUser, loading, error } = useBackendUser();
  const t = useTranslations("NavUser"); // Initialize useTranslations

  const handleBillingPortal = async () => {
    try {
      const token = await getToken();
      const response = await axiosInstance.post(
        CREATE_STRIPE_PORTAL_SESSION,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.url) {
        window.location.href = response.data.url;
      }
    } catch (error) {
      console.error("Failed to create portal session:", error);
      toast.error(t("failedToAccessBillingPortalToast")); // Use translation
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div className="relative w-full">
          {/* This is the visible button with all content */}
          <SidebarMenuButton
            size="lg"
            className="flex items-center gap-3 w-full pointer-events-none"
          >
            <Avatar className="h-8 w-8 rounded-lg">
              <div className="h-full w-full rounded-lg overflow-hidden">
                {user?.imageUrl && (
                  <img
                    src={user.imageUrl || "/placeholder.svg"}
                    alt={user?.fullName || t("userAltText")} // Use translation
                    className="h-full w-full object-cover"
                  />
                )}
              </div>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">
                {user?.primaryEmailAddress?.emailAddress}
              </span>
              <span className="truncate text-xs">{user?.fullName}</span>
            </div>
          </SidebarMenuButton>

          {/* This is the actual UserButton that will handle clicks */}
          <UserButton
            appearance={{
              elements: {
                avatarBox: "rounded-lg",
                userButtonTrigger:
                  "absolute inset-0 w-full h-full opacity-0 cursor-pointer",
              },
            }}
          >
            {!loading &&
              billingPermissions.canManage(backendUser?.permissions || []) && (
                <UserButton.MenuItems>
                  <UserButton.Action
                    label={t("usageInformation")} // Use translation
                    labelIcon={<BarChart2 size={16}/>}
                    onClick={() => (window.location.href = "/enterprise/usage")}
                  />
                  <UserButton.Action
                    label={t("upgradeToPro")} // Use translation
                    labelIcon={<Sparkles size={16} />}
                    onClick={handleBillingPortal}
                  />
                  <UserButton.Action
                    label={t("billingSettings")} // Use translation
                    labelIcon={<CreditCard size={16} />}
                    onClick={handleBillingPortal}
                  />
                </UserButton.MenuItems>
              )}
          </UserButton>
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}