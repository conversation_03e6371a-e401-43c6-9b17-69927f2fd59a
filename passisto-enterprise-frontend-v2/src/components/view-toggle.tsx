"use client"

import { LayoutGrid, LayoutList } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

export function ViewToggle({ view, setView }) {
  return (
    <div className="flex items-center border rounded-md overflow-hidden">
      <Button
        variant="ghost"
        size="sm"
        className={cn("rounded-none px-3 h-9", view === "table" && "bg-muted")}
        onClick={() => setView("table")}
        aria-label="Table view"
      >
        <LayoutList className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Table</span>
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn("rounded-none px-3 h-9", view === "card" && "bg-muted")}
        onClick={() => setView("card")}
        aria-label="Card view"
      >
        <LayoutGrid className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Cards</span>
      </Button>
    </div>
  )
}

