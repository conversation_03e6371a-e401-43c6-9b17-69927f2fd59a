"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, Loader2 } from "lucide-react";
import { useAuth } from "@clerk/nextjs";
import { useState } from "react";
import { toast } from "sonner";
import axiosInstance from "@/config/axios";
import { CREATE_STRIPE_PORTAL_SESSION } from "@/utils/routes";
import { usePathname } from "next/navigation";

interface UsageLimitDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "interview" | "email";
  limit: number;
}

export function UsageLimitDialog({
  open,
  onOpenChange,
  type,
  limit,
}: UsageLimitDialogProps) {
  const { getToken } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  
  // Check if the user is a candidate based on the URL path
  const isCandidate = pathname?.includes('/public-page/interview/');

  const handleUpgrade = async () => {
    setIsLoading(true);
    try {
      const token = await getToken();
      const response = await axiosInstance.post(
        CREATE_STRIPE_PORTAL_SESSION,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.url) {
        window.location.href = response.data.url;
      }
    } catch (error) {
      console.error("Failed to create portal session:", error);
      toast.error("Failed to access billing portal");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            {isCandidate ? "Link Not Available" : "Usage Limit Exceeded"}
          </DialogTitle>
          <DialogDescription className="pt-2">
            {isCandidate ? (
              <div className="space-y-2">
                <p>This interview link is no longer active.</p>
                <p>Please contact the company that sent you this interview invitation for assistance.</p>
              </div>
            ) : (
              <p>
                You have reached your {type} limit ({limit} {type === "interview" ? "Minutes" : "emails"}). 
                To continue using this feature, please upgrade your plan.
              </p>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          {!isCandidate && (
            <Button onClick={handleUpgrade} disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Processing...
                </div>
              ) : (
                "Upgrade Plan"
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
