import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

const isProtectedRoute = createRouteMatcher([
  '/(.*)/enterprise(.*)',
  '/(.*)/auth/onboarding',
  '/(.*)/workflows/(.*)',
  '/enterprise(.*)',
  '/auth/onboarding',
  '/workflows/(.*)'
])

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware(routing);

// Main middleware function - single export default
export default clerkMiddleware(async (auth, req) => {
  // For Socket.IO paths and API routes, bypass i18n middleware
  if (req.nextUrl.pathname.startsWith('/socket.io') ||
      req.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Handle internationalization for non-API routes only
  const intlResponse = intlMiddleware(req);
  
  // If intl middleware wants to redirect, let it
  if (intlResponse && intlResponse.status !== 200) {
    return intlResponse;
  }

  // Otherwise, proceed with Clerk auth
  const { userId, redirectToSignIn } = await auth();
  console.log("userID: " + userId);
  
  if (!userId && isProtectedRoute(req)) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }

  return intlResponse || NextResponse.next();
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
    // Add Socket.IO routes
    '/socket.io/:path*',
  ],
};