import axiosInstance from "@/config/axios";
import { CHECK_USAGE_LIMIT, CHECK_USAGE_LIMIT_BY_COMPANY_ID } from "../utils/routes";
import { toast } from "sonner";

interface UsageResponse {
  success: boolean;
  isExceeded: boolean;
  remaining: number;
  currentUsage: number;
  limit: number;
}

interface UsageLimitResult {
  canProceed: boolean;
  limit?: number;
  remaining?: number;
}

export async function checkUsageLimit(
  type: "interview" | "email",
  token: string
): Promise<UsageLimitResult> {
  try {
    const response = await axiosInstance.post<UsageResponse>(
      CHECK_USAGE_LIMIT,
      { type },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (response.data.isExceeded) {
      console.log("form exceeded");
      return {
        canProceed: false,
        limit: response.data.limit,
      };
    }

    if (response.data.remaining <= 60) {
      // Warning when close to limit
      toast.warning(
        `Usage limit warning: You have ${response.data.remaining} ${
          type === "interview" ? "Minutes" : "emails"
        } remaining in your current plan.`
      );
    }

    return {
      canProceed: true,
      remaining: response.data.remaining,
      limit: response.data.limit,
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to check usage limit";
    toast.error("Error", { description: message });
    return { canProceed: false };
  }
}

export async function checkUsageLimitByCompanyId(
  type: "interview" | "email",
  companyId: string
): Promise<UsageLimitResult> {
  try {
    const response = await axiosInstance.post<UsageResponse>(
      CHECK_USAGE_LIMIT_BY_COMPANY_ID,
      { type, companyId },
    );
    if (response.data.isExceeded) {
      console.log("form exceeded");
      return {
        canProceed: false,
        limit: response.data.limit,
      };
    }

    return {
      canProceed: true,
      remaining: response.data.remaining,
      limit: response.data.limit,
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to check usage limit";
    toast.error("Error", { description: message });
    return { canProceed: false };
  }
}
