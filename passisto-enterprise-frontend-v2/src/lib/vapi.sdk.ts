// this lib not longer work i hard code the vapi logic inside agent component to fixe vapi single instant 


import Vapi from "@vapi-ai/web";

let currentVapi: Vapi | null = null;

const configs = [
  {
    workflow: process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W1!,
    token: process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T1!,
  },
  {
    workflow: process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W2!,
    token: process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T2!,
  },
  {
    workflow: process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W3!,
    token: process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T3!,
  },
];

export function getRandomVapiWithWorkflow() {
  const index = Math.floor(Math.random() * configs.length);
  const { token, workflow } = configs[index];

  if (!token || !workflow) {
    throw new Error(`Invalid config: token or workflow is undefined.
      Index: ${index}
      Token: ${token}
      Workflow: ${workflow}`
    );
  }

  // Stop any existing active session before creating a new one
  if (currentVapi) {
    try {
      currentVapi.stop();  // Cleanly stop the previous Vapi session
    } catch (err) {
      console.error("Error stopping the previous Vapi session:", err);
    }
    currentVapi = null;
  }
console.log(token);
console.log(workflow);
  currentVapi = new Vapi(token);
  // currentVapi.start(workflow);
  return { instance: currentVapi, workflow, token };
}
