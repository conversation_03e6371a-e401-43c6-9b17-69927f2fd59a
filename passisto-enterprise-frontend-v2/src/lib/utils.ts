import { Role } from "@/store/slices/rolePermissionSlice";
import { Team } from "@/store/slices/teamSlice";
import { User } from "@/store/slices/userSlice";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function hasPermission(
  permissions: string[],
  required: string | string[]
) {
  if (!permissions) return false;
  if (Array.isArray(required)) {
    return required.some((p) => permissions.includes(p));
  }
  return permissions.includes(required);
}

export function userHasPermission(
  permissions: string[],
  requiredPermission: string | string[]
): boolean {
  return hasPermission(permissions, requiredPermission);
}
