import axios from "axios";

// Use the chatbot container URL with port 5921
const CHATBOT_API_URL = process.env.NEXT_PUBLIC_CHATBOT_API_URL ?? "http://localhost:5921";

const chatbotAxiosInstance = axios.create({
  baseURL: CHATBOT_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  // Don't include credentials to avoid CORS issues with wildcard origin
  withCredentials: false,
});

export default chatbotAxiosInstance;
