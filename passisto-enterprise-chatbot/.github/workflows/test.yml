name: Test CI/CD

on:
  push:
    branches: [dev]
  pull_request:
    branches: [dev]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKER_USERNAME }}/pe-chatbot:dev
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Ubuntu server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_TEST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-test
            docker pull ${{ secrets.DOCKER_USERNAME }}/pe-chatbot:dev

            mkdir -p passisto-enterprise-chatbot

            infisical export --env=test --path=/passisto-enterprise-chatbot > passisto-enterprise-chatbot/.env
            
            docker compose up -d chatbot --force-recreate
 