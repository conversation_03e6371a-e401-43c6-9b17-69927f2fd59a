{"verbose": true, "ignore": ["*.test.js", "*.spec.js"], "watch": ["src/**/*", "index.js", "nodemon.json", ".env", "package.json"], "ext": "js,json", "exec": "node -r dotenv/config src/app.js", "events": {"crash": "echo \"App crashed - waiting for file changes before starting...\"", "restart": "echo \"App restarted due to changes\""}, "env": {"NODE_ENV": "development"}, "delay": "1500", "colours": true, "stdout": true, "stderr": true, "legacyWatch": true}