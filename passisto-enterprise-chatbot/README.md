# Passisto Enterprise Chatbot

A chatbot application built with Express.js, LangChain, and OpenSearch with MongoDB for chat history storage.

## Requirements

- Node.js version: 21.61.1+
- npm version: 10.4.0+

## Getting Started

### Installation

```bash
npm install
```

### Running the Server

```bash
npm run dev     # Run with node<PERSON> for development
npm start       # Run in production mode
```

Or directly:

```bash
node src/app.js
```

## OpenSearch Data Loading

The project includes scripts for loading data into OpenSearch:

### 1. Load Sample Data

```bash
node src/load-opensearch-data.js [index-name]
```

If no index name is provided, a random UUID will be generated and used as the index name.

### 2. Load Data from JSON File

```bash
node src/load-opensearch-from-file.js <path-to-json-file> [index-name]
```

Parameters:
- `<path-to-json-file>`: Path to a JSON file containing an array of documents
- `[index-name]`: (Optional) Name of the OpenSearch index. If not provided, a random UUID will be generated.

### 3. Check OpenSearch Status

```bash
node src/check-opensearch.js [index-name]
```

This script checks the OpenSearch connection and lists available indices. If an index name is provided, it will show sample documents from that index.

For more details, see the [OpenSearch Data Loading documentation](./data/README.md).

## API Endpoints

The application provides the following API endpoints:

### Chat History Management

#### Get Session History

```
GET /history/:sessionId?user_id=<userId>
```

Retrieves the chat history for a specific session. If `user_id` is provided, it will only return the history if it belongs to that user.

#### Delete Session History

```
DELETE /history/:sessionId?user_id=<userId>
```

Deletes a chat session by its ID. The `user_id` parameter is required for security reasons to ensure only the owner can delete their sessions.

#### Bulk Delete Sessions

```
POST /history/bulk-delete
```

Deletes multiple chat sessions at once.

Request body:
```json
{
  "user_id": "user123",
  "session_ids": [
    "uuid1",
    "uuid2",
    "uuid3"
  ]
}
```

Response:
```json
{
  "message": "2 session(s) deleted successfully",
  "deleted_count": 2,
  "failed_sessions": [
    {
      "session_id": "uuid3",
      "reason": "Session not found"
    }
  ]
}
```

### User Sessions

#### Get User Sessions

```
GET /user-sessions/:userId?limit=10&skip=0
```

Retrieves all chat sessions for a specific user with pagination support.

### Session Messages

#### Get Session Messages

```
GET /session-messages/:sessionId?user_id=<userId>&message_type=<type>
```

Retrieves messages from a specific session. The `message_type` parameter can be `user`, `ai`, or `all` to filter the types of messages returned.

### Chatbot

#### Send Message to Chatbot

```
POST /chatbot
```

Sends a message to the chatbot and receives a response.

Request body:
```json
{
  "message": "What is the weather like?",
  "opensearch_alias": "weather-data",
  "session_id": "optional-session-id",
  "user_id": "optional-user-id",
  "chatbot_name": "weather-bot"
}
```

For more details on the API endpoints, visit the Swagger documentation at `/api-docs` when the server is running.