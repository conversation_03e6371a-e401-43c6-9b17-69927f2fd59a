# OpenSearch Data Loading

This directory contains scripts and sample data for loading documents into OpenSearch.

## Available Scripts

### 1. Load Sample Data

The `load-opensearch-data.js` script loads a predefined set of sample documents into OpenSearch.

```bash
node src/load-opensearch-data.js [index-name]
```

If no index name is provided, a random UUID will be generated and used as the index name.

### 2. Load Data from JSON File

The `load-opensearch-from-file.js` script loads documents from a JSON file into OpenSearch.

```bash
node src/load-opensearch-from-file.js <path-to-json-file> [index-name]
```

Parameters:
- `<path-to-json-file>`: Path to a JSON file containing an array of documents
- `[index-name]`: (Optional) Name of the OpenSearch index. If not provided, a random UUID will be generated.

## JSON File Format

The JSON file should contain an array of document objects. Each document object should have:

- `text` (or `content` or `pageContent`): The text content of the document
- `metadata` (optional): An object containing metadata for the document

Example:

```json
[
  {
    "text": "This is the content of the first document",
    "metadata": {
      "source": "example",
      "category": "documentation",
      "date": "2023-10-15"
    }
  },
  {
    "text": "This is the content of the second document",
    "metadata": {
      "source": "example",
      "category": "tutorial",
      "date": "2023-10-16"
    }
  }
]
```

## Environment Variables

Make sure your `.env` file contains the necessary OpenSearch configuration:

```
OPENSEARCH_URL="http://localhost:9200"
OPENSEARCH_USERNAME="admin"
OPENSEARCH_PASSWORD="your-password"
EMBEDDING_MODEL="text-embedding-3-small"
EMBEDDING_DIMENSION=1024
AZURE_OPENAI_EMBEDDING_API_KEY="your-api-key"
AZURE_OPENAI_EMBEDDING_API_VERSION="2023-05-15"
AZUR_OPENAI_EMBEDDING_INSTANCE_NAME="your-instance-name"
AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME="text-embedding-3-small"
```

## Using the Index in Your Application

After loading data, you'll receive an index name (either the one you provided or a generated UUID). Use this index name in your application to query the loaded data.
