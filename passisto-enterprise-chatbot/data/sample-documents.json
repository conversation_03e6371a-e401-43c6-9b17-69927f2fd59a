[{"text": "OpenSearch is a community-driven, open source search and analytics suite derived from Apache 2.0 licensed Elasticsearch 7.10.2 & Kibana 7.10.2.", "metadata": {"source": "documentation", "category": "overview", "date": "2023-10-15"}}, {"text": "OpenSearch provides a distributed, multitenant-capable full-text search engine with an HTTP web interface and schema-free JSON documents.", "metadata": {"source": "documentation", "category": "features", "date": "2023-10-15"}}, {"text": "OpenSearch Dashboards provides visualization capabilities on top of the content indexed on an OpenSearch cluster.", "metadata": {"source": "documentation", "category": "dashboards", "date": "2023-10-15"}}, {"text": "Vector search in OpenSearch allows you to search for similar vectors using approximate nearest neighbor (ANN) algorithms.", "metadata": {"source": "documentation", "category": "vector-search", "date": "2023-10-15"}}, {"text": "k-NN (k-nearest neighbors) is a method that can be used for classification and regression problems in machine learning.", "metadata": {"source": "documentation", "category": "vector-search", "date": "2023-10-15"}}, {"text": "OpenSearch allows you to store, search, and analyze large volumes of data quickly and in near real-time.", "metadata": {"source": "documentation", "category": "features", "date": "2023-10-15"}}, {"text": "Security features in OpenSearch include authentication, access control, encryption, audit logging, and compliance with security standards.", "metadata": {"source": "documentation", "category": "security", "date": "2023-10-15"}}, {"text": "OpenSearch Dashboards is a user interface that lets you visualize your OpenSearch data and navigate the OpenSearch Suite.", "metadata": {"source": "documentation", "category": "dashboards", "date": "2023-10-15"}}, {"text": "The OpenSearch API is RESTful, using HTTP and JSON. You can add, retrieve, and modify data using standard HTTP methods like GET, POST, PUT, and DELETE.", "metadata": {"source": "documentation", "category": "api", "date": "2023-10-15"}}, {"text": "OpenSearch clusters can scale horizontally by adding more nodes, allowing you to handle larger volumes of data and higher query loads.", "metadata": {"source": "documentation", "category": "scaling", "date": "2023-10-15"}}]