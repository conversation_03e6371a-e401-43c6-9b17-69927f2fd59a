import { incrementQuestionsCounter } from "../passisto-config/helpers.cjs";

/**
 * Middleware to increment the questions counter for a specific alias
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const incrementalMiddleware = (req, res, next) => {
  incrementQuestionsCounter(req.body.opensearch_alias);
  next();
};
