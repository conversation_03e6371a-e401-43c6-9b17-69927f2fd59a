import { z } from 'zod';

// Define validation schema for chatbot requests
export const chatbotSchema = z.object({
  message: z.string().max(500),
  indices: z.array(z.string().uuid()).min(1),
  session_id: z.union([z.string().uuid(), z.literal('')]).nullable().optional(),
  user_id: z.string().max(100).nullable().optional(),
  chatbot_name: z.string().max(100).optional(),
  user_location: z.object({
    extra_id: z.string().max(100).optional(),
    _extra_ci: z.string().max(100).optional(),
    _extra_rg: z.string().max(100).optional(),
    _extra_co: z.string().max(100).optional(),
  }).optional(),
});

// Define validation schema for search requests
export const searchSchema = z.object({
  query: z.string().max(500),
  indices: z.array(z.string().uuid()).min(1),
  user_id: z.string().max(100).nullable().optional(),
});

/**
 * Middleware to validate request body against a Zod schema
 * @param {z.ZodSchema} schema - Zod schema to validate against
 * @returns {Function} Express middleware function
 */
export const validateRequest = (schema) => {
  return (req, res, next) => {
    try {
      schema.parse(req.body);
      next();
    } catch (error) {
      console.error('Validation error:', error.errors);
      return res.status(400).json({
        message: 'Invalid request data',
        errors: error.errors,
      });
    }
  };
};
