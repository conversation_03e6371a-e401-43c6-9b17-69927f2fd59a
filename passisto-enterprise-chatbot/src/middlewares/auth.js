/**
 * Authentication middleware that checks for a valid API token in the request headers
 * 
 * This middleware verifies that the request includes a valid API token in the
 * 'x-api-key' header. If the token is missing or invalid, the request is rejected
 * with a 401 Unauthorized response.
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const authMiddleware = (req, res, next) => {
  // Hard-coded API token (in a real app, this would be stored securely)
  const VALID_API_TOKEN = 'passisto-chatbot-api-token-2025';
  
  // Get the token from the request headers
  const apiKey = req.headers['x-api-key'];
  
  // Check if the token is present and valid
  if (!apiKey || apiKey !== VALID_API_TOKEN) {
    return res.status(401).json({
      message: 'Unauthorized: Invalid or missing API key',
    });
  }
  
  // If the token is valid, proceed to the next middleware
  next();
};
