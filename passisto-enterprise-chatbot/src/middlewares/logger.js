import morgan from 'morgan';

/**
 * Configure request logging middleware
 * @returns {Function} Configured Morgan middleware
 */
export const requestLogger = () => {
  // Use a simple format that will definitely show in the console
  return morgan('dev');
};

/**
 * Error logging middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const errorLogger = (err, req, res, next) => {
  // Log the error to console
  console.error(`ERROR: ${err.message}`);
  console.error(err.stack);
  
  // Pass to next error handler
  next(err);
};
