import { pushUserData2DW } from "../passisto-config/helpers.cjs";

/**
 * Middleware that stores user data in the data warehouse
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const Push2DWMiddleware = (req, res, next) => {
  let user_data = {
    question: req.body.message ?? '',
    alias: req.body.opensearch_alias ?? '',
    user_location: req.body.user_location ?? {}
  };
  
  // Listen for the 'finish' event on the response object
  res.on('finish', () => {
    // Perform actions after the response has been sent
    user_data.response = res.locals.response ?? '';
    pushUserData2DW(user_data);
  });
  
  next();
};
