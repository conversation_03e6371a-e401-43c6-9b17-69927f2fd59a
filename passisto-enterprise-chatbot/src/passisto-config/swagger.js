import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Passisto online chatbot JS API',
      version: '1.0.0',
      description: 'Documentation of chatbot express',
    },
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'x-api-key'
        }
      }
    },
    security: [
      {
        ApiKeyAuth: []
      }
    ]
  },
  apis: ['./src/routes/*.js'], // Update to look for JSDoc in route files
};

const specs = swaggerJsdoc(options);

export { specs, swaggerUi };
