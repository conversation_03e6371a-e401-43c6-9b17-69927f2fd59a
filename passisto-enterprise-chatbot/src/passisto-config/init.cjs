// const apm = require('elastic-apm-node').start({
//     serviceName: 'CHATBOT_API',
//     serverUrl: process.env.APM_URL,
//     environment:  'PROD'
// });

import { AzureOpenAIEmbeddings } from "@langchain/openai";
// const { CacheBackedEmbeddings } = require("langchain/embeddings/cache_backed");
// const { InMemoryStore } = require("langchain/storage/in_memory");
const rateLimit = require('express-rate-limit');

require('dotenv').config();

// Define rate limit rules
const limiter = rateLimit(
    {
        windowMs: 60 * 1000, // 1 minutes
        max: 100, // Limit each IP to 100 requests per windowMs
        message: "Too many requests from this IP, please try again later."
    }
);

// class InvalidEnvironmentError extends Error {
//     constructor(message) {
//         super(message); // Pass the message to the Error base class
//         this.name = "InvalidEnvironmentError"; // Set a custom error name
//     }
// }

// Init Embedding Model with Cache
export const underlyingEmbeddings = new AzureOpenAIEmbeddings({
    azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY,
    azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME,
    azureOpenAIApiEmbeddingsDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME, 
    azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION,
    maxRetries: 5,
    dimensions: parseInt(process.env.EMBEDDING_DIMENSION),
    model: process.env.EMBEDDING_MODEL
  });


module.exports = {
    underlyingEmbeddings,
    limiter
}
