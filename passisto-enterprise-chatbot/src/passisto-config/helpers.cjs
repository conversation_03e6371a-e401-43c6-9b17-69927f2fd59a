"use strict";
const { Kafka } = require('kafkajs')

require('dotenv').config();

const incrementQuestionsCounter = async (alias) => {
    try {
        const response = await fetch(process.env.INCREMENTAL_COUNTER_QUESTION_URL, {
            method: 'POST',
            headers: {
                Authorization: process.env.EXPRESS_APP_ACCESS_TOKEN,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                aliasName: alias,
            })
        });

        if (response.status === 200) {
            console.log("Question is incremented.");
        } else {
            throw new Error(`${response.status}: ${response.statusText}`);
        }
    } catch (e) {
        console.error(`Error occurred when incrementing the question counter: `, e);
    }
}


const kafka_broker = new Kafka({
    clientId: 'chatbot-api',
    brokers: [`${process.env.KAFKA_BROKER}:${process.env.KAFKA_PORT}`],
});
const producer = kafka_broker.producer();

const pushUserData2DW = async (data) => {
    try {
        await producer.connect();
        await producer.send({
            topic: process.env.KAFKA_TOPIC,
            messages: [
                { 
                    value: JSON.stringify({
                        question: data.question,
                        response: data.response,
                        alias: data.alias,
                        user_location: data.user_location,
                    })
                }
            ],
        });
        console.log('Message successfully sent!');
    } catch (e) {
        console.error('Error occurred when storing user data:', e);
    } finally {
        await producer.disconnect();
    }
};


const updateClientTokensConsumption = (alias, totalTokens) => {
    try {
        const response = fetch(process.env.INCREMENTAL_COUNTER_CONSUMED_TOKEN_URL, {
            method: 'POST',
            headers: {
                Authorization: process.env.EXPRESS_APP_ACCESS_TOKEN,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                promptTokens: totalTokens,
                completionTokens: 0,
                aliasName: alias,
            }),
        })
    } catch (e) {
        console.log("Error: ", e);
    }

}

module.exports = {incrementQuestionsCounter, pushUserData2DW, updateClientTokensConsumption}