"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepInfraLLM = exports.ENV_VARIABLE = exports.DEFAULT_MODEL_NAME = exports.DEEPINFRA_API_BASE = void 0;
const llms_1 = require("@langchain/core/language_models/llms");
const env_1 = require("@langchain/core/utils/env");
exports.DEEPINFRA_API_BASE = "https://api.deepinfra.com/v1/openai/completions";
exports.DEFAULT_MODEL_NAME = "mistralai/Mixtral-8x22B-Instruct-v0.1";
exports.ENV_VARIABLE = "DEEPINFRA_API_TOKEN";
require('dotenv').config();
class DeepInfraLLM extends llms_1.LLM {
    static lc_name() {
        return "DeepInfraLLM";
    }
    constructor(fields = {}) {
        super(fields);
        Object.defineProperty(this, "lc_serializable", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        Object.defineProperty(this, "apiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "maxTokens", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "temperature", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "alias", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.apiKey = fields.apiKey ?? (0, env_1.getEnvironmentVariable)(exports.ENV_VARIABLE);
        this.model = fields.model ?? exports.DEFAULT_MODEL_NAME;
        this.maxTokens = fields.maxTokens;
        this.temperature = fields.temperature;
        this.alias = fields.alias;
    }
    _llmType() {
        return "DeepInfra";
    }
    async _call(prompt, options) {

        console.log("\n\n");
        console.log("================ PROMPT =====================");
        console.log(prompt);
        console.log("=====================================");
        console.log("\n\n");

        const body = {
            temperature: this.temperature,
            max_tokens: this.maxTokens,
            ...options,
            prompt,
            model: this.model,
        };
        const response = await this.caller.call(() => fetch(exports.DEEPINFRA_API_BASE, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${this.apiKey}`,
                "Content-Type": "application/json",
            },
            body: JSON.stringify(body),
        }).then((res) => res.json())
            .then((res) => {
                console.log("\n\n");
                console.log("================ RESPONSE =====================");
                console.log(res.choices[0].text);
                console.log("=====================================");
                console.log("\n\n");
                // update client tokens consumption
                // this.updateClientTokensConsumption(res.usage.prompt_tokens, res.usage.completion_tokens, res.usage.estimated_cost);
                return res.choices[0].text.trim();
            })
        );
        return response;
    }
    // backend url to env file
}
exports.DeepInfraLLM = DeepInfraLLM;
