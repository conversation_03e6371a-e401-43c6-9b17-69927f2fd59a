import { Document } from "@langchain/core/documents";
import getVectorStore from "./getVectoreStore.js";
import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";

dotenv.config();

/**
 * Load sample data into OpenSearch
 * @param {string} indexName - The name of the OpenSearch index (alias)
 * @param {Array} documents - Array of document objects with text and metadata
 */
async function loadDataToOpenSearch(indexName, documents) {
  try {
    console.log(`Loading ${documents.length} documents to OpenSearch index: ${indexName}`);
    
    // Get the vector store for the specified index
    const vectorStore = await getVectorStore(indexName);
    
    // Convert to LangChain Document objects
    const langchainDocs = documents.map(doc => 
      new Document({
        pageContent: doc.text,
        metadata: doc.metadata || {}
      })
    );
    
    // Add documents to OpenSearch
    await vectorStore.addDocuments(langchainDocs);
    
    console.log(`Successfully loaded ${documents.length} documents to OpenSearch index: ${indexName}`);
  } catch (error) {
    console.error("Error loading data to OpenSearch:", error);
    throw error;
  }
}

// Sample data to load
const sampleData = [
  {
    text: "OpenSearch is a community-driven, open source search and analytics suite derived from Apache 2.0 licensed Elasticsearch 7.10.2 & Kibana 7.10.2.",
    metadata: {
      source: "documentation",
      category: "overview",
      date: "2023-10-15"
    }
  },
  {
    text: "OpenSearch provides a distributed, multitenant-capable full-text search engine with an HTTP web interface and schema-free JSON documents.",
    metadata: {
      source: "documentation",
      category: "features",
      date: "2023-10-15"
    }
  },
  {
    text: "OpenSearch Dashboards provides visualization capabilities on top of the content indexed on an OpenSearch cluster.",
    metadata: {
      source: "documentation",
      category: "dashboards",
      date: "2023-10-15"
    }
  },
  {
    text: "Vector search in OpenSearch allows you to search for similar vectors using approximate nearest neighbor (ANN) algorithms.",
    metadata: {
      source: "documentation",
      category: "vector-search",
      date: "2023-10-15"
    }
  },
  {
    text: "k-NN (k-nearest neighbors) is a method that can be used for classification and regression problems in machine learning.",
    metadata: {
      source: "documentation",
      category: "vector-search",
      date: "2023-10-15"
    }
  }
];

// Generate a random index name if not provided as command line argument
const indexName = process.argv[2] || uuidv4();

// Execute the function
loadDataToOpenSearch(indexName, sampleData)
  .then(() => {
    console.log(`Data loaded successfully to index: ${indexName}`);
    console.log("Use this index name in your application to query the loaded data.");
  })
  .catch(error => {
    console.error("Failed to load data:", error);
    process.exit(1);
  });
