import pg from 'pg';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

// Create the PostgreSQL connection URI
const connectionString = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_DATABASE}?options=-c%20search_path%3D${process.env.DB_SCHEMA}`;

// Configure the PostgreSQL connection
let ssl = false;
if (process.env.ENVIRONMENT === 'PROD') {
  ssl = {
    rejectUnauthorized: false,
    ca: fs.readFileSync(process.env.DB_SSL_CERT_PATH).toString(),
  };
}

export const pgPool = new pg.Pool({
  connectionString: connectionString,
  ssl: ssl,
});

// Gracefully shut down the PostgreSQL pool on app exit
process.on('exit', () => {
  pgPool.end().then(() => console.log('PostgreSQL pool has ended'));
});

process.on('SIGINT', () => {
  pgPool.end().then(() => {
    console.log('PostgreSQL pool has ended');
    process.exit();
  });
});

process.on('SIGTERM', () => {
  pgPool.end().then(() => {
    console.log('PostgreSQL pool has ended');
    process.exit();
  });
});
