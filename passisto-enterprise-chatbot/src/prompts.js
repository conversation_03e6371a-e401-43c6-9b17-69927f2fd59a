import { coerceMessageLikeToMessage, HumanMessage, mergeMessageRuns, SystemMessage } from "@langchain/core/messages";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { MessagesPlaceholder } from "@langchain/core/prompts";

// Question-Answer template for Enterprise Data
const qa_template = `You are an AI Assistant for an enterprise environment.
Use the provided context from enterprise data sources to answer questions accurately and professionally.
Keep your response **concise** and focused on the enterprise data provided in the context.
If you don't know the answer based on the provided context, state that clearly instead of guessing.
Always answer in the **same language** as the question, regardless of the context language.

====== START CONTEXT ======

{context}.

====== END CONTEXT ======

====== Chat History ======
{chat_history}
====== End Chat History ======



Note: The context may be in different languages, but the answer should always be in the same language as the question below.
When referring to enterprise data, be precise and professional in your response.`;

export const qaPrompt2 = ChatPromptTemplate.fromMessages([
    ["system", qa_template],
    new MessagesPlaceholder("chat_history"),
    ["human", "Question: {input}"],
]);


// Question-Answer template
const question_generator1 = `You're are not a chatbot. You're a text processor. Process input by preserving non-questions exactly as given, while reformulating questions into standalone complete queries that maintain the original meaning without context dependencies or references to prior messages.

====== Chat History ======

{chat_history}

====== End Chat History ======

Note 1: The chat history may be in different languages, but the standalone question should always be in the same language as the input bellow.

Note 2: Return the bellow input as is if it is not a question.
`;


export const contextualizeQPrompt2 = ChatPromptTemplate.fromMessages([
    ["system", question_generator1],
    new MessagesPlaceholder("chat_history"),
    ["human", "Current Input: {input}"],
]);