import express from 'express';
import cors from 'cors';
import { config } from 'dotenv';
import morgan from 'morgan';
import { specs, swaggerUi } from './passisto-config/swagger.js';
// import { InvalidEnvironmentError } from './passisto-config/init.cjs';
import { configureRoutes } from './routes/index.js';

// Load environment variables
config();

// Initialize Express app
const app = express();
const port = process.env.APP_PORT;

// Initialize middleware
app.set('trust proxy', 1);

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());

// Request logging middleware
app.use(morgan('dev'));

// Check environment
if (!['TEST', 'PROD', 'DEV'].includes(process.env.ENVIRONMENT)) {
  throw new InvalidEnvironmentError(`Invalid environment: ${process.env.ENVIRONMENT}`);
}
// else if (process.env.ENVIRONMENT != 'DEV'){
//   app.use('/chatapi/chatbot', incrementalMiddleware, Push2DWMiddleware)
// }

// Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));



// Configure all routes
configureRoutes(app);

// 404 handler for unmatched routes
app.use((req, res) => {
  res.status(404).json({
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.ENVIRONMENT === 'DEV' ? err.message : {}
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Passisto Chatbot app listening on port ${port}`);
  console.log(`Environment: ${process.env.ENVIRONMENT || 'development'}`);
  console.log(`API Documentation: http://localhost:${port}/api-docs`);
});
