import chatbotRouter from './chatbot.js';
import historyRouter from './history.js';
import userSessionsRouter from './user-sessions.js';
import sessionMessagesRouter from './session-messages.js';
import searchRouter from './search.js';
import { authMiddleware } from '../middlewares/auth.js';
import { chatbotSchema, searchSchema, validateRequest } from '../middlewares/validation.js';

/**
 * Configure all routes for the application
 * @param {Express} app - Express application instance
 */
export const configureRoutes = (app) => {
  // Apply authentication middleware to all API routes
  // app.use(authMiddleware);
  app.use('/chatbot', validateRequest(chatbotSchema), chatbotRouter);
  app.use('/search', validateRequest(searchSchema), searchRouter);
  // app.use('/search', searchRouter);
  // app.use('/chatbot', chatbotRouter);
  app.use('/history', historyRouter);
  app.use('/user-sessions', userSessionsRouter);
  app.use('/session-messages', sessionMessagesRouter);

  // Public routes (if any) would go here without the authMiddleware
};
