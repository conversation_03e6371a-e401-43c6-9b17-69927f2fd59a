import express from 'express';
import { getMongoCollections } from '../messageHistory.js';

const router = express.Router();

// Middleware to validate UUID format
const validateUUID = (req, res, next) => {
  const { sessionId } = req.params;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  if (!uuidRegex.test(sessionId)) {
    return res.status(400).send({
      message: "Invalid session ID format. Must be a valid UUID.",
      session_id: sessionId
    });
  }

  next();
};

/**
 * @swagger
 * /history/{sessionId}:
 *   get:
 *     description: Retrieve chat history for a specific session ID
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         description: ID of the session to retrieve history for
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *         description: Optional user ID to verify session ownership
 *     responses:
 *       200:
 *         description: Chat history retrieved successfully
 *       404:
 *         description: History not found
 *       500:
 *         description: An internal error occurred
 */
router.get("/:sessionId", validateUUID, async (req, res) => {
  // Extract parameters outside try-catch so they're accessible in both blocks
  const { sessionId } = req.params;
  const { user_id: userId = null } = req.query;

  console.log("sessionId:", sessionId);
  console.log("userId:", userId);

  try {
    const [, historyCollection] = await getMongoCollections(); // Use empty first element to ignore mongoClient

    // Query the MongoDB collection directly to get history with user_id filter
    let query = { sessionId: sessionId };

    // If userId is provided, check only in the document root
    if (userId) {
      query = {
        $and: [
          { sessionId: sessionId },
          { userId: userId } // Only check userId in document root
        ]
      };
    }

    const historyData = await historyCollection.findOne(query);

    if (!historyData) {
      return res.status(404).send({
        message: "History not found",
      });
    }

    res.send({
      history: historyData,
      user_id: userId || null,
    });
  } catch (error) {
    console.error('History retrieval error:', error);
    // if (process.env.ENVIRONMENT && process.env.ENVIRONMENT.toLowerCase() !== 'dev') {
    //   apm.captureError(error);
    // }
    res.status(500).send({
      message: "Something went wrong, please try again later.",
    });
  }
});

/**
 * @swagger
 * /history/{sessionId}:
 *   delete:
 *     description: Delete a chat session history by ID
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         description: ID of the session to delete
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *         description: User ID to verify session ownership (required for security)
 *     responses:
 *       200:
 *         description: Session deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 session_id:
 *                   type: string
 *                   description: The ID of the deleted session
 *       400:
 *         description: Bad request - user_id is required
 *       403:
 *         description: Forbidden - user does not own this session
 *       404:
 *         description: Session not found
 *       500:
 *         description: An internal error occurred
 */
router.delete("/:sessionId", validateUUID, async (req, res) => {
  const { sessionId } = req.params;
  const { userId } = req.body;

  // For security, require a user_id to delete a session
  if (!userId) {
    return res.status(400).send({
      message: "user_id is required to delete a session",
      session_id: sessionId
    });
  }

  try {
    const [, historyCollection] = await getMongoCollections();

    // First check if the session exists and belongs to the user
    const session = await historyCollection.findOne({
      sessionId: sessionId,
      userId: userId
    });

    if (!session) {
      // Check if the session exists but doesn't belong to this user
      const sessionExists = await historyCollection.findOne({ sessionId: sessionId });

      if (sessionExists) {
        return res.status(403).send({
          message: "You don't have permission to delete this session",
          session_id: sessionId
        });
      } else {
        return res.status(404).send({
          message: "Session not found",
          session_id: sessionId
        });
      }
    }

    // Delete the session
    const result = await historyCollection.deleteOne({
      sessionId: sessionId,
      userId: userId
    });

    if (result.deletedCount === 1) {
      return res.status(200).send({
        message: "Session deleted successfully",
        session_id: sessionId
      });
    } else {
      return res.status(500).send({
        message: "Failed to delete session",
        session_id: sessionId
      });
    }
  } catch (error) {
    console.error('Session deletion error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later.",
      session_id: sessionId
    });
  }
});

/**
 * @swagger
 * /history/bulk-delete:
 *   post:
 *     description: Delete multiple chat sessions by their IDs
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - session_ids
 *             properties:
 *               user_id:
 *                 type: string
 *                 description: ID of the user who owns the sessions
 *               session_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: Array of session IDs to delete
 *     responses:
 *       200:
 *         description: Sessions deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 deleted_count:
 *                   type: integer
 *                   description: Number of sessions deleted
 *                 failed_sessions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       session_id:
 *                         type: string
 *                       reason:
 *                         type: string
 *       400:
 *         description: Bad request - missing required fields or invalid format
 *       500:
 *         description: An internal error occurred
 */
router.post("/bulk-delete", async (req, res) => {
  const { user_id: userId, session_ids: sessionIds } = req.body;

  // Validate required fields
  if (!userId) {
    return res.status(400).send({
      message: "user_id is required"
    });
  }

  if (!sessionIds || !Array.isArray(sessionIds) || sessionIds.length === 0) {
    return res.status(400).send({
      message: "session_ids must be a non-empty array"
    });
  }

  // Validate UUID format for all session IDs
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  const invalidIds = sessionIds.filter(id => !uuidRegex.test(id));

  if (invalidIds.length > 0) {
    return res.status(400).send({
      message: "Invalid session ID format. All IDs must be valid UUIDs.",
      invalid_ids: invalidIds
    });
  }

  try {
    const [, historyCollection] = await getMongoCollections();

    // Find all sessions that belong to this user
    const userSessions = await historyCollection.find({
      sessionId: { $in: sessionIds },
      userId: userId
    }).toArray();

    // Get the session IDs that belong to the user
    const userSessionIds = userSessions.map(session => session.sessionId);

    // Find sessions that exist but don't belong to this user
    const unauthorizedSessions = await historyCollection.find({
      sessionId: { $in: sessionIds },
      userId: { $ne: userId }
    }).toArray();

    const unauthorizedSessionIds = unauthorizedSessions.map(session => session.sessionId);

    // Calculate sessions that don't exist
    const nonExistentSessionIds = sessionIds.filter(id =>
      !userSessionIds.includes(id) && !unauthorizedSessionIds.includes(id)
    );

    // Delete the sessions that belong to the user
    const deleteResult = await historyCollection.deleteMany({
      sessionId: { $in: userSessionIds },
      userId: userId
    });

    // Prepare the failed sessions list
    const failedSessions = [
      ...unauthorizedSessionIds.map(id => ({
        session_id: id,
        reason: "You don't have permission to delete this session"
      })),
      ...nonExistentSessionIds.map(id => ({
        session_id: id,
        reason: "Session not found"
      }))
    ];

    return res.status(200).send({
      message: `${deleteResult.deletedCount} session(s) deleted successfully`,
      deleted_count: deleteResult.deletedCount,
      failed_sessions: failedSessions
    });
  } catch (error) {
    console.error('Bulk session deletion error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later."
    });
  }
});

export default router;
