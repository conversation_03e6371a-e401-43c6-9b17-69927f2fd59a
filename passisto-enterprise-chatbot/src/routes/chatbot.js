import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AzureChatOpenAI } from '@langchain/openai';
import { StateGraph, END } from '@langchain/langgraph';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import getVectorStore from '../getVectoreStore.js';
import { getMongoCollections, getMongoSessionHistory } from '../messageHistory.js';

const router = express.Router();

// Node functions for the graph
async function retrieveDocuments(state, config) {
  console.log("--- RETRIEVING DOCUMENTS ---");

  try {
    const { alias } = config.configurable;
    const vectorStore = await getVectorStore(alias);
    const retriever = vectorStore.asRetriever();

    // Use rephrased question if available, otherwise use original question
    const queryText = state.rephrased_question || state.question;
    const documents = await retriever.invoke(queryText);

    return {
      ...state,
      context: documents
    };
  } catch (error) {
    console.error("Error in retrieveDocuments:", error);
    return {
      ...state,
      error: `Failed to retrieve documents: ${error.message}`
    };
  }
}

async function rephraseQuestion(state, config) {
  console.log("--- REPHRASING QUESTION ---");

  try {
    // If no chat history, no need to rephrase
    if (!state.messages || state.messages.length === 0) {
      return {
        ...state,
        rephrased_question: state.question
      };
    }

    const { llm } = config.configurable;

    // Format chat history as a string for the system message
    const chatHistoryText = state.messages
      .map(msg => {
        const type = msg.type || (msg._getType ? msg._getType() : 'unknown');
        return `${type}: ${msg.content}`;
      })
      .join('\n');

    // Create a custom prompt template instead of using contextualizeQPrompt2
    const contextualizePrompt = await ChatPromptTemplate.fromMessages([
      ["system", `You're are not a chatbot. You're a text processor. Process input by preserving non-questions exactly as given, while reformulating questions into standalone complete queries that maintain the original meaning without context dependencies or references to prior messages.

====== Chat History ======
${chatHistoryText}
====== End Chat History ======

Note 1: The chat history may be in different languages, but the standalone question should always be in the same language as the input bellow.

Note 2: Return the bellow input as is if it is not a question.`],
      ["human", `Current Input: ${state.question}`]
    ]).format({});

    const response = await llm.invoke(contextualizePrompt);
    const rephrasedQuestion = response.content.trim();

    console.log(`Original: ${state.question}`);
    console.log(`Rephrased: ${rephrasedQuestion}`);

    return {
      ...state,
      rephrased_question: rephrasedQuestion
    };
  } catch (error) {
    console.error("Error in rephraseQuestion:", error);
    return {
      ...state,
      rephrased_question: state.question, // Fallback to original question
      error: `Failed to rephrase question: ${error.message}`
    };
  }
}

async function generateAnswer(state, config) {
  console.log("--- GENERATING ANSWER ---");

  try {
    if (state.error) {
      return state; // Skip if there's an error
    }

    const { llm } = config.configurable;

    // Format context documents
    const contextText = state.context
      .map(doc => doc.pageContent)
      .join('\n\n');

    // Format chat history as a string for the system message
    const chatHistoryText = state.messages.length > 0
      ? state.messages
          .map(msg => {
            const type = msg.type || (msg._getType ? msg._getType() : 'unknown');
            return `${type}: ${msg.content}`;
          })
          .join('\n')
      : "No previous conversation.";

    // Create the QA prompt without the MessagesPlaceholder
    const qaPromptFormatted = await ChatPromptTemplate.fromMessages([
      ["system", `You are an AI Assistant for an enterprise environment.
Use the provided context from enterprise data sources to answer questions accurately and professionally.
Keep your response **concise** and focused on the enterprise data provided in the context.
If you don't know the answer based on the provided context, state that clearly instead of guessing.
Always answer in the **same language** as the question, regardless of the context language.

====== START CONTEXT ======

${contextText}

====== END CONTEXT ======

====== Chat History ======
${chatHistoryText}
====== End Chat History ======

Note: The context may be in different languages, but the answer should always be in the same language as the question below.
When referring to enterprise data, be precise and professional in your response.`],
      ["human", `Question: ${state.question}`]
    ]).format({});

    const response = await llm.invoke(qaPromptFormatted);
    const answer = response.content.trim();

    // Extract unique sources
    const sources = [...new Set(state.context.map(doc => doc.metadata.source))];

    console.log(`Generated answer: ${answer}`);

    return {
      ...state,
      answer: answer,
      sources: sources
    };
  } catch (error) {
    console.error("Error in generateAnswer:", error);
    return {
      ...state,
      answer: "I apologize, but I encountered an error while generating the answer.",
      error: `Failed to generate answer: ${error.message}`
    };
  }
}

async function saveToHistory(state, config) {
  console.log("--- SAVING TO HISTORY ---");

  try {
    const { sessionHistory } = config.configurable;

    // Add the user question and bot answer to history using proper message classes
    await sessionHistory.addMessage(new HumanMessage(state.question));
    await sessionHistory.addMessage(new AIMessage(state.answer));

    console.log("Successfully saved to history");

    return state;
  } catch (error) {
    console.error("Error in saveToHistory:", error);
    return {
      ...state,
      error: `Failed to save to history: ${error.message}`
    };
  }
}

// Conditional function to decide whether to rephrase
function shouldRephrase(state) {
  // Rephrase if we have chat history
  return state.messages && state.messages.length > 0 ? "rephrase" : "retrieve";
}

// Create the graph
function createChatbotGraph() {
  // Define the state schema for StateGraph
  const stateSchema = {
    messages: { default: () => [] },
    question: { default: () => "" },
    context: { default: () => [] },
    rephrased_question: { default: () => "" },
    answer: { default: () => "" },
    sources: { default: () => [] },
    session_id: { default: () => "" },
    user_id: { default: () => "" },
    error: { default: () => null }
  };

  const workflow = new StateGraph({
    channels: stateSchema
  });

  // Add nodes
  workflow.addNode("rephrase", rephraseQuestion);
  workflow.addNode("retrieve", retrieveDocuments);
  workflow.addNode("generate", generateAnswer);
  workflow.addNode("save_history", saveToHistory);

  // Define the flow - use addEdge instead of deprecated setEntryPoint
  workflow.addEdge("__start__", "rephrase");

  // Conditional edge: rephrase or go straight to retrieve
  workflow.addConditionalEdges(
    "rephrase",
    shouldRephrase,
    {
      "rephrase": "retrieve",
      "retrieve": "retrieve"
    }
  );

  // Linear flow after retrieval
  workflow.addEdge("retrieve", "generate");
  workflow.addEdge("generate", "save_history");
  workflow.addEdge("save_history", END);

  return workflow.compile();
}

/**
 * @swagger
 * /chatbot-langgraph:
 *   post:
 *     description: LangGraph-based chatbot API for receiving a question and generating contextual responses using retrieval-augmented generation.
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *               - opensearch_alias
 *             properties:
 *               message:
 *                 type: string
 *                 description: The question asked by the user.
 *                 example: "What is the weather like?"
 *                 maxLength: 500
 *               opensearch_alias:
 *                 type: string
 *                 description: The chatbot index where answers are fetched from.
 *                 example: "general"
 *               session_id:
 *                 type: string
 *                 description: The ID of the conversation session.
 *                 example: "12345"
 *                 format: uuid
 *               user_id:
 *                 type: string
 *                 description: The ID of the user for tracking.
 *                 example: "user123"
 *                 maxLength: 100
 *     responses:
 *       200:
 *         description: Successful response with answer and sources.
 *         content:
 *          application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 answer:
 *                   type: string
 *                   description: The chatbot response.
 *                 sources:
 *                   type: array
 *                   description: Information sources used.
 *                   items:
 *                     type: string
 *                 session_id:
 *                   type: string
 *                   description: The conversation session ID.
 *                 user_id:
 *                   type: string
 *                   description: The user ID.
 *                 graph_state:
 *                   type: object
 *                   description: Final state of the graph execution.
 */
router.post("/", async (req, res) => {
  const {
    message: question = "",
    indices: alias = [],
    session_id: initialSessionId = "",
    user_id: userId = null,
  } = req.body;

  const sessionId = initialSessionId || uuidv4();

  try {
    console.log("Processing request with LangGraph:", req.body);

    // Initialize LLM
    const llm = new AzureChatOpenAI({
      model: "gpt-4o-mini",
      temperature: 0,
      max_tokens: 200,
      azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY,
      azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME,
      azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
      azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION,
      user: sessionId,
      callbacks: [
        {
          handleLLMStart: async (_, prompts) => {
            console.log("\n>>>> LLM START ====");
            console.log(prompts[0]);
            console.log("<<<< LLM START ====\n");
          },
          handleLLMEnd: async (output) => {
            console.log("\n>>>> LLM END ====");
            console.log("Response:", output.generations[0][0].text);
            console.log("Tokens:", output.llmOutput?.tokenUsage?.totalTokens);
            console.log("<<<< LLM END ====\n");
          },
          handleLLMError: async (err) => {
            console.error("LLM Error:", err);
          },
        },
      ],
    });

    // Get session history
    const [, historyCollection] = await getMongoCollections();
    const sessionHistory = await getMongoSessionHistory(
      historyCollection,
      sessionId,
      userId
    );

    // Get existing messages for context
    const existingMessages = await sessionHistory.getMessages();

    // Create initial state
    const initialState = {
      question: question,
      messages: existingMessages,
      session_id: sessionId,
      user_id: userId,
      context: [],
      rephrased_question: "",
      answer: "",
      sources: [],
      error: null
    };

    // Create and run the graph
    const chatbotGraph = createChatbotGraph();

    const config = {
      configurable: {
        llm: llm,
        alias: alias,
        sessionHistory: sessionHistory
      }
    };

    // Execute the graph
    console.log("--- STARTING GRAPH EXECUTION ---");
    const finalState = await chatbotGraph.invoke(initialState, config);
    console.log("--- GRAPH EXECUTION COMPLETED ---");

    // Update user ID in MongoDB if provided
    if (userId) {
      try {
        await historyCollection.updateOne(
          { sessionId: sessionId },
          { $set: { userId: userId } },
          { upsert: true }
        );
        console.log(`Updated userId ${userId} for session ${sessionId}`);
      } catch (error) {
        console.error('Error updating userId:', error);
      }
    }

    // Store response for middleware
    res.locals.response = finalState.answer;

    // Send response
    res.send({
      answer: finalState.answer,
      sources: finalState.sources,
      session_id: sessionId,
      user_id: userId || null,
      graph_state: {
        rephrased_question: finalState.rephrased_question,
        context_count: finalState.context.length,
        error: finalState.error
      }
    });

  } catch (error) {
    console.error('LangGraph chatbot error:', error);
    res.status(500).send({
      answer: "Something went wrong, please try again later.",
      session_id: sessionId,
      user_id: userId || null,
      error: error.message
    });
  }
});

export default router;