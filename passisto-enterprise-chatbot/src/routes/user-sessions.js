import express from 'express';
import { getMongoCollections } from '../messageHistory.js';

const router = express.Router();

/**
 * @swagger
 * /user-sessions/{userId}:
 *   get:
 *     description: Retrieve all chat sessions for a specific user
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: ID of the user to retrieve sessions for
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Maximum number of sessions to return
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of sessions to skip (for pagination)
 *     responses:
 *       200:
 *         description: List of chat sessions for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sessions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       session_id:
 *                         type: string
 *                         description: The ID of the chat session
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         description: When the session was created
 *                       last_message:
 *                         type: string
 *                         description: The last message in the conversation
 *                       message_count:
 *                         type: integer
 *                         description: Number of messages in the session
 *                 total:
 *                   type: integer
 *                   description: Total number of sessions available
 *       404:
 *         description: No sessions found for the user
 *       500:
 *         description: An internal error occurred
 */
router.get("/:userId", async (req, res) => {
  // Extract parameters outside try-catch so they're accessible in both blocks
  const { userId } = req.params;
  const limit = parseInt(req.query.limit) || 10;
  const skip = parseInt(req.query.skip) || 0;

  try {
    // Get MongoDB collections and ensure they're properly initialized
    const [, historyCollection] = await getMongoCollections(); // Use empty first element to ignore mongoClient

    if (!historyCollection) {
      console.error('History collection is undefined');
      return res.status(500).send({
        message: "Database connection error. Please try again later.",
      });
    }

    // Query MongoDB for all sessions with this user ID
    // Only check userId in the document root
    const query = { userId: userId };

    // Get total count for pagination
    const total = await historyCollection.countDocuments(query);

    if (total === 0) {
      return res.status(404).send({
        message: "No sessions found for this user",
        sessions: [],
        total: 0
      });
    }

    // Find all sessions and sort by most recent first
    const sessions = await historyCollection
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Process the sessions to extract relevant information
    const processedSessions = sessions.map(session => {
      // Get the last message if available
      const messages = session.messages || [];

      // Extract the last message content
      let lastMessage = "";
      if (messages.length > 0) {
        const lastMsg = messages[messages.length - 1];
        // Handle different message structures
        if (lastMsg?.data?.content) {
          lastMessage = lastMsg.data.content;
        } else if (lastMsg?.content) {
          lastMessage = lastMsg.content;
        } else if (typeof lastMsg === 'string') {
          lastMessage = lastMsg;
        }
      }

      // Extract creation date from ObjectId if not available directly
      const createdAt = session.createdAt ||
                        (session._id ? new Date(parseInt(session._id.toString().substring(0, 8), 16) * 1000) : new Date());

      return {
        session_id: session.sessionId,
        created_at: createdAt,
        last_message: lastMessage,
        message_count: messages.length,
      };
    });

    res.send({
      sessions: processedSessions,
      total,
      user_id: userId
    });
  } catch (error) {
    console.error('User sessions retrieval error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later.",
    });
  }
});

export default router;
