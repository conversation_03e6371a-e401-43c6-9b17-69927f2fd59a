import express from 'express';
import { getMongoCollections } from '../messageHistory.js';

const router = express.Router();

/**
 * @swagger
 * /session-messages/{sessionId}:
 *   get:
 *     description: Retrieve all user messages for a specific chat session
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         description: ID of the session to retrieve messages for
 *         schema:
 *           type: string
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *         description: Optional user ID to verify session ownership
 *       - in: query
 *         name: message_type
 *         schema:
 *           type: string
 *           enum: [user, ai, all]
 *           default: user
 *         description: Type of messages to retrieve (user, ai, or all)
 *     responses:
 *       200:
 *         description: List of messages from the session
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 messages:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       content:
 *                         type: string
 *                         description: The message content
 *                       type:
 *                         type: string
 *                         description: The message type (user or ai)
 *                       timestamp:
 *                         type: string
 *                         format: date-time
 *                         description: When the message was sent
 *                 session_id:
 *                   type: string
 *                   description: The ID of the session
 *       404:
 *         description: Session not found
 *       500:
 *         description: An internal error occurred
 */
router.get("/:sessionId", async (req, res) => {
  // Extract parameters
  const { sessionId } = req.params;
  const { user_id: userId = null, message_type: messageType = 'user' } = req.query;

  try {
    const [, historyCollection] = await getMongoCollections();

    // Build query to find the session
    let query = { sessionId: sessionId };

    // If userId is provided, check only in the document root
    if (userId) {
      query = {
        $and: [
          { sessionId: sessionId },
          { userId: userId } // Only check userId in document root
        ]
      };
    }

    // Find the session
    const session = await historyCollection.findOne(query);

    if (!session) {
      return res.status(404).send({
        message: "Session not found",
        session_id: sessionId
      });
    }

    // Extract messages from the session
    const messages = session.messages || [];

    // Process messages based on the requested type
    const processedMessages = messages.map(msg => {
      // Determine message type
      let type = 'unknown';

      // Check all possible ways the type might be stored
      if (msg.type === 'human' ||
          msg?.data?.type === 'human' ||
          msg.type === 'user' ||
          msg?.data?.type === 'user') {
        type = 'user';
      } else if (msg.type === 'ai' ||
                msg?.data?.type === 'ai' ||
                msg.type === 'assistant' ||
                msg?.data?.type === 'assistant') {
        type = 'ai';
      }

      // Extract content based on message structure
      let content = '';
      if (msg?.data?.content) {
        content = msg.data.content;
      } else if (msg?.content) {
        content = msg.content;
      } else if (typeof msg === 'string') {
        content = msg;
      } else if (msg?.text) {
        content = msg.text;
      }

      // Extract timestamp if available, or use ObjectId timestamp
      const timestamp = msg?.timestamp ||
                        (msg?._id ? new Date(parseInt(msg._id.toString().substring(0, 8), 16) * 1000) : new Date());

      return {
        content,
        type,
        timestamp
      };
    }).filter(msg => {
      // Filter messages based on requested type
      if (messageType === 'all') {
        return true;
      } else if (messageType === 'user') {
        return msg.type === 'user';
      } else if (messageType === 'ai') {
        return msg.type === 'ai';
      }
      return true;
    });

    // Return the processed messages
    res.send({
      messages: processedMessages,
      session_id: sessionId,
      user_id: userId || null
    });
  } catch (error) {
    console.error('Session messages retrieval error:', error);
    res.status(500).send({
      message: "Something went wrong, please try again later.",
      session_id: sessionId
    });
  }
});

export default router;
