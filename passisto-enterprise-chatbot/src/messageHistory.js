import { <PERSON>ufferWindowMemory } from "langchain/memory";
import { config as dotenvConfig } from 'dotenv';
import { MongoClient } from 'mongodb';
import { RedisChatMessageHistory } from "@langchain/redis";
import { MongoDBChatMessageHistory } from "@langchain/mongodb";
import { createClient } from "redis";

dotenvConfig();

let mongoclient;
let historyCollection;
let redisClient;

// MongoDB Singleton
export async function getMongoCollections() {
  try {
    if (!mongoclient) {
      console.log("Initializing MongoDB connection...");
      mongoclient = new MongoClient(process.env.MONGO_CHAT_HISTORY_URL, {
        maxPoolSize: 50, // Optimize connection pool size
        minPoolSize: 5,  // Minimum number of connections
      });

      await mongoclient.connect(); // Establish the connection
      console.log("Connected to MongoDB");

      // Get the collection once after connecting
      historyCollection = mongoclient.db('langchain').collection('memory');
    } else if (!historyCollection) {
      // If client exists but collection doesn't, initialize the collection
      console.log("Initializing MongoDB collection...");
      historyCollection = mongoclient.db('langchain').collection('memory');
    }

    // Verify connection is still alive with a ping
    await mongoclient.db("admin").command({ ping: 1 });

    return [mongoclient, historyCollection];
  } catch (error) {
    console.error("MongoDB connection error:", error);

    // Try to reconnect if there was an error
    try {
      if (mongoclient) {
        await mongoclient.close();
      }

      mongoclient = new MongoClient(process.env.MONGO_CHAT_HISTORY_URL, {
        maxPoolSize: 50,
        minPoolSize: 5,
      });

      await mongoclient.connect();
      console.log("Reconnected to MongoDB");

      historyCollection = mongoclient.db('langchain').collection('memory');
      return [mongoclient, historyCollection];
    } catch (reconnectError) {
      console.error("MongoDB reconnection failed:", reconnectError);
      throw new Error("Failed to connect to MongoDB");
    }
  }
}

export async function getMongoSessionHistory(historyCollection, sessionId, userId = null) {
  // First, check if a session already exists and update it with userId if needed
  if (userId) {
    try {
      // Try to update an existing session with the user ID only in the document root
      await historyCollection.updateOne(
        { sessionId: sessionId },
        {
          $set: {
            userId: userId  // Store userId directly in the document root only
          },
        },
        { upsert: false }
      );
      console.log(`Updated session ${sessionId} with userId ${userId} in document root`);
    } catch (error) {
      console.error('Error updating session with userId:', error);
    }
  }

  const chatHistory = new BufferWindowMemory({
    memoryKey: 'chat_history',
    k: 10,
    returnMessages: true,
    chatHistory: new MongoDBChatMessageHistory({
      collection: historyCollection,
      sessionId: sessionId
      // No longer using sessionMetadata or metadata for userId
    }),
  });

  return chatHistory.chatHistory;
}

// Redis Singleton
async function initializeRedisClient() {
  if (!redisClient) {
    redisClient = createClient({
      url: process.env.REDIS_URL || "redis://185.252.233.224:6179",
    });

    redisClient.on('error', (err) => console.error('Redis Client Error', err));

    await redisClient.connect();
    console.log("Connected to Redis");
  }
  return redisClient;
}

export async function getRedisSessionHistory(sessionId) {
  const client = await initializeRedisClient();

  const chatHistory = new RedisChatMessageHistory({
    sessionId: sessionId,
    sessionTTL: 300, // Time-to-live for the session in seconds
    client: client,
  });

  return chatHistory;
}

process.on('SIGINT', async () => {
  if (mongoclient) {
    await mongoclient.close();
    console.log("MongoDB client disconnected");
  }
  if (redisClient) {
    await redisClient.quit();
    console.log("Redis client disconnected");
  }
  process.exit(0);
});