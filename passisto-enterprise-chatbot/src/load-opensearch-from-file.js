import { Document } from "@langchain/core/documents";
import getVectorStore from "./getVectoreStore.js";
import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";
import path from "path";

dotenv.config();

/**
 * Load data from a JSON file into OpenSearch
 * @param {string} indexName - The name of the OpenSearch index (alias)
 * @param {string} filePath - Path to the JSON file containing documents
 */
async function loadDataFromFile(indexName, filePath) {
  try {
    // Read and parse the JSON file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const documents = JSON.parse(fileContent);
    
    if (!Array.isArray(documents)) {
      throw new Error("File content must be a JSON array of documents");
    }
    
    console.log(`Loading ${documents.length} documents from ${filePath} to OpenSearch index: ${indexName}`);
    
    // Get the vector store for the specified index
    const vectorStore = await getVectorStore(indexName);
    
    // Convert to LangChain Document objects
    const langchainDocs = documents.map(doc => {
      if (!doc.text && !doc.content && !doc.pageContent) {
        throw new Error("Each document must have a 'text', 'content', or 'pageContent' field");
      }
      
      return new Document({
        pageContent: doc.text || doc.content || doc.pageContent,
        metadata: doc.metadata || {}
      });
    });
    
    // Add documents to OpenSearch
    await vectorStore.addDocuments(langchainDocs);
    
    console.log(`Successfully loaded ${documents.length} documents to OpenSearch index: ${indexName}`);
    return indexName;
  } catch (error) {
    console.error("Error loading data to OpenSearch:", error);
    throw error;
  }
}

// Check command line arguments
if (process.argv.length < 3) {
  console.log("Usage: node load-opensearch-from-file.js <path-to-json-file> [index-name]");
  process.exit(1);
}

const filePath = process.argv[2];
const indexName = process.argv[3] || uuidv4();

// Execute the function
loadDataFromFile(indexName, filePath)
  .then((usedIndexName) => {
    console.log(`Data loaded successfully to index: ${usedIndexName}`);
    console.log("Use this index name in your application to query the loaded data.");
  })
  .catch(error => {
    console.error("Failed to load data:", error);
    process.exit(1);
  });
