import { Client } from "@opensearch-project/opensearch";
import { OpenSearchVectorStore } from "@langchain/community/vectorstores/opensearch";
import dotenv from "dotenv";

dotenv.config();

let rejectUnauthorized = true;
if (process.env.ENVIRONMENT === 'DEV') {
  rejectUnauthorized = false;
}

let openSearchClientInstance;

function createOpenSearchClient() {
  return new Client({
    node: process.env.OPENSEARCH_ENDPOINT,
    auth: {
      username: process.env.OPENSEARCH_USERNAME,
      password: process.env.OPENSEARCH_PASSWORD,
    },
    ssl: {
      rejectUnauthorized,
    },
  });
}

async function getOpenSearchClient() {
  if (!openSearchClientInstance) {
    openSearchClientInstance = createOpenSearchClient();
    try {
      const info = await openSearchClientInstance.info();
      // console.log("OpenSearch client connected:", info);
    } catch (error) {
      console.error("OpenSearch connection failed:", error.message);
      throw new Error("Failed to connect to OpenSearch");
    }
  }
  return openSearchClientInstance;
}

export default async function getVectorStore(alias) {
  const client = await getOpenSearchClient();

  // Import and initialize Azure OpenAI embeddings
  const { OpenAIEmbeddings } = await import("@langchain/openai");

  const embeddings = new OpenAIEmbeddings({
    modelName: process.env.EMBEDDING_MODEL,
    dimensions: parseInt(process.env.EMBEDDING_DIMENSION),
    azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY,
    azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION,
    azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME,
    azureOpenAIApiDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
    azureOpenAI: true, // Enable Azure OpenAI mode
  });

  const vectorStore = new OpenSearchVectorStore(embeddings, {
    client,
    indexName: alias,
    vectorFieldName: "vector_field",
  });

  return vectorStore;
}
