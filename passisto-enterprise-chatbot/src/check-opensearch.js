import { Client } from "@opensearch-project/opensearch";
import dotenv from "dotenv";

dotenv.config();

/**
 * Check OpenSearch connection and list indices
 */
async function checkOpenSearch() {
  let rejectUnauthorized = true;
  if (process.env.ENVIRONMENT === 'DEV') {
    rejectUnauthorized = false;
  }

  const client = new Client({
    node: process.env.OPENSEARCH_ENDPOINT,
    auth: {
      username: process.env.OPENSEARCH_USERNAME,
      password: process.env.OPENSEARCH_PASSWORD,
    },
    ssl: {
      rejectUnauthorized,
    },
  });

  try {
    // Check connection
    const info = await client.info();
    console.log("OpenSearch connection successful!");
    console.log("Cluster name:", info.body.cluster_name);
    console.log("OpenSearch version:", info.body.version.number);
    
    // List indices
    const indices = await client.cat.indices({ format: "json" });
    console.log("\nAvailable indices:");
    
    if (indices.body.length === 0) {
      console.log("No indices found.");
    } else {
      console.table(indices.body.map(index => ({
        index: index.index,
        health: index.health,
        status: index.status,
        docs: index["docs.count"],
        size: index["store.size"]
      })));
    }
    
    // If index name is provided as argument, check documents in that index
    if (process.argv.length > 2) {
      const indexName = process.argv[2];
      console.log(`\nChecking documents in index: ${indexName}`);
      
      try {
        // Check if index exists
        const indexExists = await client.indices.exists({ index: indexName });
        
        if (!indexExists.body) {
          console.log(`Index '${indexName}' does not exist.`);
          return;
        }
        
        // Count documents
        const count = await client.count({ index: indexName });
        console.log(`Total documents: ${count.body.count}`);
        
        // Get sample documents
        const search = await client.search({
          index: indexName,
          body: {
            query: { match_all: {} },
            size: 3
          }
        });
        
        console.log("\nSample documents:");
        search.body.hits.hits.forEach((hit, i) => {
          console.log(`\nDocument ${i+1}:`);
          console.log(`ID: ${hit._id}`);
          console.log(`Text: ${hit._source.text}`);
          console.log("Metadata:", hit._source.metadata);
        });
      } catch (error) {
        console.error(`Error checking index ${indexName}:`, error.message);
      }
    }
  } catch (error) {
    console.error("OpenSearch connection failed:", error.message);
    if (error.message.includes("connect ECONNREFUSED")) {
      console.log("\nTips:");
      console.log("1. Make sure OpenSearch is running");
      console.log("2. Check the OPENSEARCH_URL in your .env file");
      console.log("3. Verify network connectivity to the OpenSearch server");
    }
  }
}

// Execute the function
checkOpenSearch().catch(console.error);
