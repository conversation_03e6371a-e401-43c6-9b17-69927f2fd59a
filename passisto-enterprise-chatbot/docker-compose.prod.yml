services:
  chatbot:
    image: passisto/pe-chatbot:dev
    container_name: chatbot
    ports:
      - 127.0.0.1:5921:5921
      - 10.0.3.1:5921:5921
    environment:
      - EXPRESS_PORT=5921
    # depends_on:
    #   - redis-history

    command: ["bash", "./entry.prod.sh"]
    networks:
      - prod-net

  # redis-history:
  #   image: redis:latest
  #   container_name: redis-history
  #   ports:
  #     - 127.0.0.1:6379:6379
  #   networks:
  #     - prod-net

networks:
  prod-net:
    external: true
