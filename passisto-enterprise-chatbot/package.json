{"name": "passisto-js", "version": "1.0.0", "description": "", "main": "app.js", "type": "module", "scripts": {"test": "jest", "dev": "nodemon src/app.js", "start": "node src/app.js", "dev:debug": "nodemon --inspect src/app.js"}, "author": "", "license": "ISC", "dependencies": {"@langchain/community": "^0.3.10", "@langchain/core": "^0.3.56", "@langchain/google-genai": "^0.1.0", "@langchain/langgraph": "^0.2.72", "@langchain/mongodb": "^0.1.0", "@langchain/redis": "^0.1.0", "@opensearch-project/opensearch": "^2.6.0", "@xenova/transformers": "^2.16.1", "axios": "^1.6.8", "cors": "^2.8.5", "dotenv": "^16.4.5", "elastic-apm-node": "^4.7.0", "express": "^4.19.2", "express-rate-limit": "^7.3.1", "ioredis": "^5.4.1", "kafkajs": "^2.2.4", "langchain": "^0.3.15", "mongodb": "^6.12.0", "morgan": "^1.10.0", "passisto-js": "file:", "pg": "^8.13.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "zod": "^3.24.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.4"}}