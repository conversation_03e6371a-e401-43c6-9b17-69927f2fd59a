# Stage 1: Build
FROM node:22 AS build

# Create a non-root user
RUN groupadd -r myuser && useradd -r -g myuser myuser

# Set the working directory inside the container
WORKDIR /app
RUN chown -R myuser:myuser /app

# Install system dependencies for sharp
RUN apt-get update && apt-get install -y libvips-dev

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Upgrade npm
RUN npm install -g npm@latest

# Install dependencies with sharp binary host set
RUN npm install --ignore-scripts && npm rebuild sharp --unsafe-perm

# Copy the rest of the application code to the working directory
COPY . .

# Stage 2: Run
FROM node:22-slim

RUN apt-get update && apt-get install -y bash curl && curl -1sLf \
'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.deb.sh' | bash \
&& apt-get update && apt-get install -y infisical

# Create a non-root user
RUN groupadd -r myuser && useradd -r -g myuser myuser

# Set the working directory inside the container
WORKDIR /app
RUN chown -R myuser:myuser /app

# Install system dependencies for sharp in the slim image
RUN apt-get update && apt-get install -y libvips-dev

# Copy only the necessary files from the build stage
COPY --from=build /app /app

RUN chmod u+x /app/entry.test.sh

CMD ["./entry.test.sh"]

