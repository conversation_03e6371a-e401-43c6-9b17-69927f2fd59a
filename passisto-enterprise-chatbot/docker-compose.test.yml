services:
  chatbot:
    image: passisto/pe-chatbot:dev
    container_name: chatbot
    ports:
      - 127.0.0.1:5921:5921
      - 10.0.2.1:5921:5921
    environment:
      - EXPRESS_PORT=5921
    # depends_on:
    #   - redis-history

    command: ["bash", "./entry.test.sh"]
    networks:
      - test-net

  # redis-history:
  #   image: redis:latest
  #   container_name: redis-history
  #   ports:
  #     - 127.0.0.1:6379:6379
  #   networks:
  #     - test-net

networks:
  test-net:
    external: true
