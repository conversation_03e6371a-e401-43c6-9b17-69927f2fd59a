const { MongoClient } = require("mongodb");
const { MongoDBChatMessageHistory } = require("@langchain/mongodb");
const dotenv = require("dotenv");
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

dotenv.config();

describe("Bulk Delete Sessions API", () => {
  let client;
  let collection;
  const testUserId = "test-user-" + Date.now();
  const otherUserId = "other-user-" + Date.now();
  const sessionIds = [];
  const otherSessionIds = [];
  const baseUrl = "http://localhost:" + (process.env.APP_PORT || 8753);
  const nonExistentSessionId = uuidv4();

  beforeAll(async () => {
    // Connect to MongoDB
    const uri = process.env.MONGO_CHAT_HISTORY_URL;
    client = new MongoClient(uri);
    await client.connect();
    collection = client.db("langchain").collection("memory");

    // Create 3 test sessions for our test user
    for (let i = 0; i < 3; i++) {
      const sessionId = uuidv4();
      sessionIds.push(sessionId);

      const chatHistory = new MongoDBChatMessageHistory({
        collection,
        sessionId
      });

      // Update the document directly to add userId in the root
      await collection.updateOne(
        { sessionId },
        { $set: { userId: testUserId } },
        { upsert: true }
      );

      // Add some test messages
      await chatHistory.addUserMessage(`Test user message ${i+1}`);
      await chatHistory.addAIMessage(`Test AI response ${i+1}`);
    }

    // Create 2 test sessions for another user
    for (let i = 0; i < 2; i++) {
      const sessionId = uuidv4();
      otherSessionIds.push(sessionId);

      const chatHistory = new MongoDBChatMessageHistory({
        collection,
        sessionId
      });

      // Update the document directly to add userId in the root
      await collection.updateOne(
        { sessionId },
        { $set: { userId: otherUserId } },
        { upsert: true }
      );

      // Add some test messages
      await chatHistory.addUserMessage(`Other user message ${i+1}`);
      await chatHistory.addAIMessage(`Other AI response ${i+1}`);
    }

    console.log(`Created ${sessionIds.length} test sessions for user ${testUserId}`);
    console.log(`Created ${otherSessionIds.length} test sessions for user ${otherUserId}`);
  });

  afterAll(async () => {
    // Clean up any remaining test data
    if (collection) {
      await collection.deleteMany({ 
        $or: [
          { userId: testUserId },
          { userId: otherUserId }
        ]
      });
    }
    if (client) {
      await client.close();
    }
  });

  test("should require user_id to bulk delete sessions", async () => {
    try {
      await axios.post(`${baseUrl}/history/bulk-delete`, {
        session_ids: sessionIds
      });
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(400);
      expect(error.response.data.message).toContain("user_id is required");
    }
  });

  test("should require session_ids to bulk delete sessions", async () => {
    try {
      await axios.post(`${baseUrl}/history/bulk-delete`, {
        user_id: testUserId
      });
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(400);
      expect(error.response.data.message).toContain("session_ids must be a non-empty array");
    }
  });

  test("should validate UUID format for session IDs", async () => {
    try {
      await axios.post(`${baseUrl}/history/bulk-delete`, {
        user_id: testUserId,
        session_ids: ["invalid-uuid", ...sessionIds]
      });
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(400);
      expect(error.response.data.message).toContain("Invalid session ID format");
      expect(error.response.data.invalid_ids).toContain("invalid-uuid");
    }
  });

  test("should delete multiple sessions when the user owns them", async () => {
    try {
      const response = await axios.post(`${baseUrl}/history/bulk-delete`, {
        user_id: testUserId,
        session_ids: sessionIds
      });
      
      expect(response.status).toBe(200);
      expect(response.data.message).toContain("session(s) deleted successfully");
      expect(response.data.deleted_count).toBe(sessionIds.length);
      expect(response.data.failed_sessions).toHaveLength(0);

      // Verify the sessions are actually deleted
      for (const sessionId of sessionIds) {
        const deletedSession = await collection.findOne({ sessionId });
        expect(deletedSession).toBeNull();
      }
    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should handle mixed case of owned, unauthorized, and non-existent sessions", async () => {
    try {
      // Create one more session for our test user
      const newSessionId = uuidv4();
      const chatHistory = new MongoDBChatMessageHistory({
        collection,
        sessionId: newSessionId
      });
      await collection.updateOne(
        { sessionId: newSessionId },
        { $set: { userId: testUserId } },
        { upsert: true }
      );
      await chatHistory.addUserMessage("New test message");

      const response = await axios.post(`${baseUrl}/history/bulk-delete`, {
        user_id: testUserId,
        session_ids: [
          newSessionId,                // Should succeed (owned by user)
          otherSessionIds[0],          // Should fail (unauthorized)
          nonExistentSessionId         // Should fail (doesn't exist)
        ]
      });
      
      expect(response.status).toBe(200);
      expect(response.data.deleted_count).toBe(1);
      expect(response.data.failed_sessions).toHaveLength(2);
      
      // Check that the failed sessions have the correct reasons
      const unauthorizedSession = response.data.failed_sessions.find(
        s => s.session_id === otherSessionIds[0]
      );
      expect(unauthorizedSession.reason).toContain("don't have permission");
      
      const nonExistentSession = response.data.failed_sessions.find(
        s => s.session_id === nonExistentSessionId
      );
      expect(nonExistentSession.reason).toBe("Session not found");

      // Verify the owned session was deleted
      const deletedSession = await collection.findOne({ sessionId: newSessionId });
      expect(deletedSession).toBeNull();
      
      // Verify the unauthorized session still exists
      const unauthorizedSessionDoc = await collection.findOne({ sessionId: otherSessionIds[0] });
      expect(unauthorizedSessionDoc).not.toBeNull();
    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });
});
