const { createClient } = require('redis');
const { RedisChatMessageHistory } = require('@langchain/redis');
const Redis = require('ioredis');
const dotenv = require('dotenv');

dotenv.config();


describe('Chat Message History', () => {
  test('should store and manage chat history in Redis', async () => {
    const sessionId = 'aab71a92-fe71-4537-8c44-521a1eee3971';
    const client = createClient({
      url: `redis://185.252.233.224:6179`,
    });

    client.on('error', (err) => console.log('Redis Client Error', err));

    await client.connect();

    const chatHistory = new RedisChatMessageHistory({
      sessionId: sessionId,
      sessionTTL: 300,
      client:client
    });

    chatHistory.addAIMessage("Hi! I'm <PERSON>.");
    chatHistory.addUserMessage("Hi! I'm <PERSON>.");

    const messages = await chatHistory.getMessages();
    console.log(messages);
  });
});
