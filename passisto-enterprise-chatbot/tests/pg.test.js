const pg = require('pg');
const dotenv = require('dotenv');

dotenv.config();

describe('Database Integration Tests', () => {
    let pgPool;

    beforeAll(() => {
        const connectionString = `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_DATABASE}?options=-c%20search_path%3D${process.env.DB_SCHEMA}`;
        pgPool = new pg.Pool({
            connectionString,
            ssl: process.env.ENVIRONMENT === 'PROD' ? {
                rejectUnauthorized: false,
                ca: process.env.DB_SSL_CERT_PATH ? fs.readFileSync(process.env.DB_SSL_CERT_PATH).toString() : undefined,
            } : false,
        });
    });

    afterAll(async () => {
        await pgPool.end();
    });

    test('show current schema', async () => {
        const client = await pgPool.connect();

        try {
            // Query to get the current schema and its tables
            const result = await client.query(`
            select * from "Chatbot";
          `);
        //     const result = await client.query(`
        //     select current_schema;
        //   `);

            console.log('Current schema and tables:', result.rows);

            // Ensure the result is an array
            expect(Array.isArray(result.rows)).toBe(true);
        } finally {
            client.release();
        }
    });
});
