const { z } = require('zod');

const schema = z.object({
    message: z.string().max(500),
    opensearch_alias: z.string().uuid(),
    historyId: z.union([z.string().uuid(), z.literal('')]).nullable().optional(),
    chatbot_name: z.string().max(100),
    user_location: z.object({
      extra_id: z.string().max(50).optional(),
      _extra_ci: z.string().max(50).optional(),
      _extra_rg: z.string().max(50).optional(),
      _extra_co: z.string().max(50).optional(),
    }).optional(),
  });

describe('Schema validation', () => {
    test('should validate a correct object', () => {
        const validData = {
            "message": "What is the weather like?",
            "opensearch_alias": "0fe7936d-d8b1-4814-89d0-de889ca930e4",
            "historyId": "aae7936d-d8b1-4814-89d0-de889ca930e4",
            "chatbot_name": "moroccan chatbot",
            "user_location": {
                "extra_id": "192.168.1.1",
                "_extra_ci": "Casablanca",
                "_extra_rg": "Grand Casablanca",
                "_extra_co": "Morocco"
            }
        };
        expect(() => schema.parse(validData)).not.toThrow();
    });

    test('should fail if message exceeds 500 characters', () => {
        const invalidData = {
            message: 'a'.repeat(501),
            opensearch_alias: '123e4567-e89b-12d3-a456-************',
            chatbot_name: 'TestBot',
        };
        expect(() => schema.parse(invalidData)).toThrow();
    });

    test('should fail if opensearch_alias is not a valid UUID', () => {
        const invalidData = {
            message: 'Hello, this is a test message',
            opensearch_alias: 'invalid-uuid',
            chatbot_name: 'TestBot',
        };
        expect(() => schema.parse(invalidData)).toThrow();
    });

    test('should fail if chatbot_name exceeds 100 characters', () => {
        const invalidData = {
            message: 'Hello, this is a test message',
            opensearch_alias: '123e4567-e89b-12d3-a456-************',
            chatbot_name: 'a'.repeat(101),
        };
        expect(() => schema.parse(invalidData)).toThrow();
    });

    test('should validate without optional fields', () => {
        const validData = {
            message: 'Hello, this is a test message',
            opensearch_alias: '123e4567-e89b-12d3-a456-************',
            chatbot_name: 'TestBot',
        };
        expect(() => schema.parse(validData)).not.toThrow();
    });

    test('should fail if user_location.extra_id exceeds 50 characters', () => {
        const invalidData = {
            message: 'Hello, this is a test message',
            opensearch_alias: '123e4567-e89b-12d3-a456-************',
            chatbot_name: 'TestBot',
            user_location: {
                extra_id: 'a'.repeat(51),
            },
        };
        expect(() => schema.parse(invalidData)).toThrow();
    });
});