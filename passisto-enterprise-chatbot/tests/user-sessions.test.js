const { MongoClient } = require("mongodb");
const { MongoDBChatMessageHistory } = require("@langchain/mongodb");
const dotenv = require("dotenv");
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

dotenv.config();

describe("User Sessions API", () => {
  let client;
  let collection;
  const testUserId = "test-user-" + Date.now();
  const sessionIds = [];
  const baseUrl = "http://localhost:" + (process.env.APP_PORT || 8753);

  beforeAll(async () => {
    // Connect to MongoDB
    const uri = process.env.MONGO_CHAT_HISTORY_URL;
    client = new MongoClient(uri);
    await client.connect();
    collection = client.db("langchain").collection("memory");

    // Create 3 test sessions for our test user
    for (let i = 0; i < 3; i++) {
      const sessionId = uuidv4();
      sessionIds.push(sessionId);

      // Create a chat history with the user ID in both possible locations
      // to ensure our endpoint can find sessions regardless of which field is used
      const chatHistory = new MongoDBChatMessageHistory({
        collection,
        sessionId,
        sessionMetadata: { userId: testUserId },
        metadata: { userId: testUserId }
      });

      // Add some test messages
      await chatHistory.addUserMessage(`Test user message ${i+1}`);
      await chatHistory.addAIMessage(`Test AI response ${i+1}`);
    }

    console.log(`Created ${sessionIds.length} test sessions for user ${testUserId}`);
  });

  afterAll(async () => {
    // Clean up test data
    if (collection) {
      await collection.deleteMany({ 'sessionMetadata.userId': testUserId });
    }
    if (client) {
      await client.close();
    }
  });

  test("should retrieve all sessions for a user", async () => {
    try {
      const response = await axios.get(`${baseUrl}/user-sessions/${testUserId}`);

      expect(response.status).toBe(200);
      expect(response.data.sessions).toBeDefined();
      expect(response.data.sessions.length).toBe(3);
      expect(response.data.total).toBe(3);
      expect(response.data.user_id).toBe(testUserId);

      // Verify session IDs match what we created
      const returnedSessionIds = response.data.sessions.map(s => s.session_id);
      // Sort both arrays for comparison
      const sortedReturnedIds = [...returnedSessionIds].sort((a, b) => a.localeCompare(b));
      const sortedSessionIds = [...sessionIds].sort((a, b) => a.localeCompare(b));
      expect(sortedReturnedIds).toEqual(sortedSessionIds);

      // Check that each session has the expected properties
      response.data.sessions.forEach(session => {
        expect(session).toHaveProperty('session_id');
        expect(session).toHaveProperty('created_at');
        expect(session).toHaveProperty('last_message');
        expect(session).toHaveProperty('message_count');
        expect(session.message_count).toBe(2); // We added 2 messages per session
      });

    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should handle pagination correctly", async () => {
    try {
      // Test with limit=2
      const response1 = await axios.get(`${baseUrl}/user-sessions/${testUserId}?limit=2`);
      expect(response1.status).toBe(200);
      expect(response1.data.sessions.length).toBe(2);
      expect(response1.data.total).toBe(3);

      // Get the remaining session with skip=2
      const response2 = await axios.get(`${baseUrl}/user-sessions/${testUserId}?limit=2&skip=2`);
      expect(response2.status).toBe(200);
      expect(response2.data.sessions.length).toBe(1);
      expect(response2.data.total).toBe(3);

      // Combine both responses and verify we have all sessions
      const allSessionIds = [
        ...response1.data.sessions.map(s => s.session_id),
        ...response2.data.sessions.map(s => s.session_id)
      ];

      // Sort both arrays for comparison
      const sortedAllIds = [...allSessionIds].sort((a, b) => a.localeCompare(b));
      const sortedSessionIds = [...sessionIds].sort((a, b) => a.localeCompare(b));
      expect(sortedAllIds).toEqual(sortedSessionIds);

    } catch (error) {
      console.error("Pagination test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should return 404 for non-existent user", async () => {
    try {
      const nonExistentUserId = "non-existent-user-" + Date.now();
      const response = await axios.get(`${baseUrl}/user-sessions/${nonExistentUserId}`);

      // This should fail with 404
      expect(response.status).toBe(404);

    } catch (error) {
      // We expect a 404 error
      expect(error.response.status).toBe(404);
      expect(error.response.data.message).toBe("No sessions found for this user");
      expect(error.response.data.sessions).toEqual([]);
      expect(error.response.data.total).toBe(0);
    }
  });
});
