const { MongoClient } = require("mongodb");
const { MongoDBChatMessageHistory } = require("@langchain/mongodb");
const dotenv = require("dotenv");
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

dotenv.config();

describe("Session Messages API", () => {
  let client;
  let collection;
  const testUserId = "test-user-" + Date.now();
  const sessionId = uuidv4();
  const baseUrl = "http://localhost:" + (process.env.APP_PORT || 8753);

  beforeAll(async () => {
    // Connect to MongoDB
    const uri = process.env.MONGO_CHAT_HISTORY_URL;
    client = new MongoClient(uri);
    await client.connect();
    collection = client.db("langchain").collection("memory");

    // Create a test session with messages
    const chatHistory = new MongoDBChatMessageHistory({
      collection,
      sessionId
    });

    // Update the document directly to add userId in the root
    await collection.updateOne(
      { sessionId },
      { $set: { userId: testUserId } },
      { upsert: true }
    );

    // Add some test messages
    await chatHistory.addUserMessage("Hello, this is a user message 1");
    await chatHistory.addAIMessage("Hi there! This is an AI response 1");
    await chatHistory.addUserMessage("Can you help me with something?");
    await chatHistory.addAIMessage("Of course! What do you need help with?");
    await chatHistory.addUserMessage("I need information about the weather");
    await chatHistory.addAIMessage("The weather is currently sunny with a temperature of 25°C");

    // No need for additional updates as userId is already set in the root

    console.log(`Created test session ${sessionId} for user ${testUserId} with 6 messages`);
  });

  afterAll(async () => {
    // Clean up test data
    if (collection) {
      await collection.deleteOne({ sessionId });
    }
    if (client) {
      await client.close();
    }
  });

  test("should retrieve all user messages from a session", async () => {
    try {
      const response = await axios.get(`${baseUrl}/session-messages/${sessionId}?message_type=user`);

      expect(response.status).toBe(200);
      expect(response.data.messages).toBeDefined();
      expect(response.data.session_id).toBe(sessionId);

      // Should have 3 user messages
      expect(response.data.messages.length).toBe(3);

      // All messages should be of type 'user'
      response.data.messages.forEach(msg => {
        expect(msg.type).toBe('user');
        expect(msg.content).toBeDefined();
        expect(msg.timestamp).toBeDefined();
      });

      // Check content of messages
      expect(response.data.messages[0].content).toBe("Hello, this is a user message 1");
      expect(response.data.messages[1].content).toBe("Can you help me with something?");
      expect(response.data.messages[2].content).toBe("I need information about the weather");

    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should retrieve all AI messages from a session", async () => {
    try {
      const response = await axios.get(`${baseUrl}/session-messages/${sessionId}?message_type=ai`);

      expect(response.status).toBe(200);
      expect(response.data.messages).toBeDefined();

      // Should have 3 AI messages
      expect(response.data.messages.length).toBe(3);

      // All messages should be of type 'ai'
      response.data.messages.forEach(msg => {
        expect(msg.type).toBe('ai');
      });

    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should retrieve all messages from a session", async () => {
    try {
      const response = await axios.get(`${baseUrl}/session-messages/${sessionId}?message_type=all`);

      expect(response.status).toBe(200);
      expect(response.data.messages).toBeDefined();

      // Should have 6 messages total
      expect(response.data.messages.length).toBe(6);

      // Should have alternating user and AI messages
      expect(response.data.messages[0].type).toBe('user');
      expect(response.data.messages[1].type).toBe('ai');
      expect(response.data.messages[2].type).toBe('user');
      expect(response.data.messages[3].type).toBe('ai');

    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should filter by user ID", async () => {
    try {
      const response = await axios.get(`${baseUrl}/session-messages/${sessionId}?user_id=${testUserId}`);

      expect(response.status).toBe(200);
      expect(response.data.user_id).toBe(testUserId);

      // Should return 404 for wrong user ID
      try {
        await axios.get(`${baseUrl}/session-messages/${sessionId}?user_id=wrong-user-id`);
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect(error.response.status).toBe(404);
      }

    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should return 404 for non-existent session", async () => {
    try {
      const nonExistentSessionId = "non-existent-session-" + Date.now();
      await axios.get(`${baseUrl}/session-messages/${nonExistentSessionId}`);
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(404);
      expect(error.response.data.message).toBe("Session not found");
    }
  });
});
