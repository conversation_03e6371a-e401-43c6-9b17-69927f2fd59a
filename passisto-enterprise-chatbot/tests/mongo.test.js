const { MongoClient, ObjectId } = require("mongodb");
const { BufferMemory } = require("langchain/memory");
const { MongoDBChatMessageHistory } = require("@langchain/mongodb");
const dotenv = require("dotenv");

const { v4 } =  require('uuid');

dotenv.config();

describe("MongoDB Chat Message History", () => {
  let client;
  let collection;
  const sessionId = "aab71a92-fe71-4537-8c44-521a1eee3971";

  beforeAll(async () => {
    const uri = process.env.MONGO_CHAT_HISTORY;
    client = new MongoClient(uri, {
      driverInfo: { name: "langchainjs" },
    });
    await client.connect();
    collection = client.db("langchain").collection("memory");
  });

  afterAll(async () => {
    if (client) {
      await client.close();
    }
  });

  test("should store and manage chat history in MongoDB", async () => {
    const memory = new BufferMemory({
      chatHistory: new MongoDBChatMessageHistory({
        collection,
        sessionId,
      }),
    });

    // Simulate user input and chatbot responses
    const userMessage1 = "Hi! I'm Jim.";
    const botResponse1 = "Hello Jim! It's nice to meet you. How can I assist you?";

    const userMessage2 = "What did I just say my name was?";
    const botResponse2 = "You said your name was Jim.";

    // Simulate adding messages to the chat history
    await memory.chatHistory.addUserMessage(userMessage1);
    await memory.chatHistory.addAIMessage(botResponse1);

    await memory.chatHistory.addUserMessage(userMessage2);
    await memory.chatHistory.addAIMessage(botResponse2);

    // Retrieve messages from the chat history
    const messages = await memory.chatHistory.getMessages();

    console.log(messages);

    // Verify the stored chat history
    expect(messages.length).toBe(4);
    expect(messages[0].content).toBe(userMessage1);
    expect(messages[1].content).toBe(botResponse1);
    expect(messages[2].content).toBe(userMessage2);
    expect(messages[3].content).toBe(botResponse2);

    // Clear chat history
    await memory.chatHistory.clear();
    const clearedMessages = await memory.chatHistory.getMessages();

    // Verify chat history is cleared
    expect(clearedMessages.length).toBe(0);
  });
});
