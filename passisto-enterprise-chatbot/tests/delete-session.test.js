const { MongoClient } = require("mongodb");
const { MongoDBChatMessageHistory } = require("@langchain/mongodb");
const dotenv = require("dotenv");
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

dotenv.config();

describe("Delete Session API", () => {
  let client;
  let collection;
  const testUserId = "test-user-" + Date.now();
  const sessionId = uuidv4();
  const otherUserId = "other-user-" + Date.now();
  const otherSessionId = uuidv4();
  const baseUrl = "http://localhost:" + (process.env.APP_PORT || 8753);

  beforeAll(async () => {
    // Connect to MongoDB
    const uri = process.env.MONGO_CHAT_HISTORY_URL;
    client = new MongoClient(uri);
    await client.connect();
    collection = client.db("langchain").collection("memory");

    // Create a test session with messages for our test user
    const chatHistory = new MongoDBChatMessageHistory({
      collection,
      sessionId
    });

    // Update the document directly to add userId in the root
    await collection.updateOne(
      { sessionId },
      { $set: { userId: testUserId } },
      { upsert: true }
    );

    // Add some test messages
    await chatHistory.addUserMessage("Test user message for deletion");
    await chatHistory.addAIMessage("Test AI response for deletion");

    // Create another session owned by a different user
    const otherChatHistory = new MongoDBChatMessageHistory({
      collection,
      sessionId: otherSessionId
    });

    // Update the document directly to add userId in the root
    await collection.updateOne(
      { sessionId: otherSessionId },
      { $set: { userId: otherUserId } },
      { upsert: true }
    );

    // Add some test messages
    await otherChatHistory.addUserMessage("Other user message");
    await otherChatHistory.addAIMessage("Other AI response");

    console.log(`Created test sessions for deletion tests`);
  });

  afterAll(async () => {
    // Clean up any remaining test data
    if (collection) {
      await collection.deleteMany({ 
        $or: [
          { userId: testUserId },
          { userId: otherUserId }
        ]
      });
    }
    if (client) {
      await client.close();
    }
  });

  test("should require user_id to delete a session", async () => {
    try {
      await axios.delete(`${baseUrl}/history/${sessionId}`);
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(400);
      expect(error.response.data.message).toContain("user_id is required");
    }
  });

  test("should delete a session when the user owns it", async () => {
    try {
      const response = await axios.delete(`${baseUrl}/history/${sessionId}?user_id=${testUserId}`);
      
      expect(response.status).toBe(200);
      expect(response.data.message).toBe("Session deleted successfully");
      expect(response.data.session_id).toBe(sessionId);

      // Verify the session is actually deleted
      const deletedSession = await collection.findOne({ sessionId });
      expect(deletedSession).toBeNull();
    } catch (error) {
      console.error("Test failed:", error.response ? error.response.data : error.message);
      throw error;
    }
  });

  test("should return 404 when trying to delete a non-existent session", async () => {
    try {
      const nonExistentSessionId = uuidv4();
      await axios.delete(`${baseUrl}/history/${nonExistentSessionId}?user_id=${testUserId}`);
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(404);
      expect(error.response.data.message).toBe("Session not found");
    }
  });

  test("should return 403 when user tries to delete another user's session", async () => {
    try {
      await axios.delete(`${baseUrl}/history/${otherSessionId}?user_id=${testUserId}`);
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error.response.status).toBe(403);
      expect(error.response.data.message).toContain("don't have permission");
    }
  });
});
