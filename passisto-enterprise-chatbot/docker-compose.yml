services:
  chatbot:
    container_name: chatbot
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - 127.0.0.1:5921:5921
      - 10.0.4.1:5921:5921
    environment:
      - EXPRESS_PORT=5921
    env_file:
      - .env
    depends_on:
      # - redis-history
      - mongo
    networks:
      - pe-net

  # redis-history:
  #   image: redis:latest
  #   container_name: redis-history
  #   ports:
  #     - 127.0.0.1:6380:6379
  #   networks:
  #     - pe-net

  mongo:
    image: mongo:latest
    container_name: mongo
    ports:
      - 127.0.0.1:27018:27017
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=adminpassword
    networks:
      - pe-net

  mongo-express:
    image: mongo-express:latest
    container_name: mongo-express
    ports:
      - 127.0.0.1:8081:8081
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=adminpassword
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_BASICAUTH_USERNAME=username
      - ME_CONFIG_BASICAUTH_PASSWORD=password
    networks:
      - pe-net

networks:
  pe-net:
    external: true
