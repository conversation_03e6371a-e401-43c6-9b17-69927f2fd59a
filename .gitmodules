[submodule "nginx"]
	path = nginx
	url = **************:passisto-enterprise/nginx.git
	branch = pre-dev
[submodule "passisto-enterprise-workers/jira-worker"]
	path = passisto-enterprise-workers/jira-worker
	url = **************:passisto-enterprise/jira-worker.git
	branch = pre-dev
[submodule "passisto-enterprise-workers/web-worker"]
	path = passisto-enterprise-workers/web-worker
	url = **************:passisto-enterprise/web-worker.git
	branch = pre-dev
[submodule "passisto-enterprise-workers/ftp-worker"]
	path = passisto-enterprise-workers/ftp-worker
	url = **************:passisto-enterprise/ftp-worker.git
	branch = pre-dev
# [submodule "passisto-enterprise-chatbot"]
# 	path = passisto-enterprise-chatbot
# 	url = **************:passisto-enterprise/passisto-enterprise-chatbot.git
# 	branch = pre-dev

[submodule "passisto-enterprise-backend-v2"]
	path = passisto-enterprise-backend-v2
	url = **************:passisto-enterprise/passisto-enterprise-backend-v2.git
	branch = pre-dev
[submodule "passisto-enterprise-frontend-v2"]
	path = passisto-enterprise-frontend-v2
	url = **************:passisto-enterprise/passisto-enterprise-frontend-v2.git
	branch = pre-dev

[submodule "passisto-enterprise-workers/passisto-scrapyd-server"]
	path = passisto-enterprise-workers/passisto-scrapyd-server
	url = **************:passisto-enterprise/pe-scrapyd-server.git
