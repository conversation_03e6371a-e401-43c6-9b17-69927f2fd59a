import requests
from dotenv import load_dotenv
from os import getenv
import time
import logging

load_dotenv()

class ScrapydError(Exception):
    def __init__(self, message=None):
        self.message = message
        super().__init__(message)
        
        
SCRAPYD_SERVER = getenv("SCRAPYD_SERVER")
SCRAPYD_USERNAME = getenv("SCRAPYD_USERNAME")
SCRAPYD_PASSWORD = getenv("SCRAPYD_PASSWORD")
SCRAPYD_PROJECT = getenv("SCRAPYD_PROJECT")
SCRAPYD_VERSION = getenv("SCRAPYD_VERSION")

EGG_FILE_PATH = getenv("EGG_FILE_PATH")

url = f"{SCRAPYD_SERVER}/addversion.json"

data = {
    "project": SCRAPYD_PROJECT,
    "version": SCRAPYD_VERSION
}

files = {
    "egg": open(EGG_FILE_PATH, "rb")
}

try:
    response = requests.post(url, data=data, files=files, auth=(SCRAPYD_USERNAME, SCRAPYD_PASSWORD))
    response_json = response.json()
    status = response_json.get('status')

    if response.status_code == 200 and status == "ok":
        print("Egg deployed successfully!")
        print("Response:", response.json())
    else:
        print(f"""
                    Failed to deploy egg. 
                    Status code: {response.status_code}, 
                    Response, {response.text}"""
                    )
        raise ScrapydError("Scrapyd server failed to build scraper project.")
except Exception as e:
    logging.error(f"Scrapyd server is not available: {e}",exc_info=True)
finally:
    time.sleep(300)  



"""
scrapy crawl generalspider -a website_url=https://www.passisto.com -a allowed_domains=passisto.com -a index_name="teeezt-paaas-123" -a max_data_size=1
"""