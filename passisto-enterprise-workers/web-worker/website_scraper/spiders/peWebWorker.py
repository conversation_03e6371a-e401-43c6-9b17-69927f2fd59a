from scrapy.linkextractors import LinkExtractor
from scrapy.spiders import Crawl<PERSON><PERSON><PERSON>, Rule
from urllib.parse import urlparse


class PECrawlerSpider(CrawlSpider):
    name = "pe-web-worker"

    def __init__(self, website_url=None, allowed_domains=None, index_name='', *args, **kwargs):
        super(PECrawlerSpider, self).__init__(*args, **kwargs)
        self.start_urls = [website_url] if website_url else []
        self.allowed_domains = [urlparse(website_url).netloc]
        self.index_name = index_name if index_name != '' else urlparse(website_url).netloc


    rules = (Rule(LinkExtractor(), callback="parse_page", follow=True),)
    

    def parse_page(self, response):        
        item = {}
        item["type"] = response.headers.get('Content-Type', b'').decode('utf-8')
        item["source"] = response.url
        
        if 'text/html' in item["type"] :
            item["timestamp"] = response.xpath('//meta[@property="og:modified_time"]/@content').get() \
                or response.xpath('//meta[@property="og:updated_time"]/@content').get() \
                or response.xpath('//meta[@property="article:modified_time"]/@content').get() \
                or response.xpath('//meta[@property="article:updated_time"]/@content').get() \
                or response.xpath('//meta[@property="og:published_time"]/@content').get() \
                or response.xpath('//meta[@property="article:published_time"]/@content').get()
            
            item["image"] = response.xpath('//meta[@property="og:image"]/@content').get()
            item["content"] = ' '.join(response.xpath('//body//text()[not(parent::script)]').extract()).strip()
            item["title"] = response.xpath('//title//text()').get()
        
        elif "application/pdf" in item["type"]:
            item['content'] = response.body
        
        return item


