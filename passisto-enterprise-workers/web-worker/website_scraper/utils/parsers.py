import PyPDF2
from langchain.docstore.document import Document
from wimpy import chunks
from os import getenv
import io
from dotenv import load_dotenv
import logging

load_dotenv()
CHUNKS_SIZE = int(getenv("TEXT_CHUNK_SIZE"))
OVERLAP = int(getenv("TEXT_OVERLAP"))


def parse_pdf(PDFstream,source):
    try:
        reader = PyPDF2.PdfReader(io.BytesIO(PDFstream))
        title = reader.metadata['/Title']
    except KeyError:
        title = ''    
    except Exception as e:
        logging.error(f"fail to load file {source}")
        return None

    docs = []
    for page in range(len(reader.pages)):
        page_content = reader.pages[page].extract_text()
        clean_content = ' '.join(page_content.split()) 
        split_content = clean_content.split(' ')
        chuncked_content = list(chunks(split_content,CHUNKS_SIZE,OVERLAP))
        num_page = page + 1
        for chunk in chuncked_content:
            docs.append(
            Document(
                page_content=' '.join(chunk),
                metadata={
                    'source': source,
                    'page': num_page,
                    'title':title
                }
            )
        ) 
    del reader
    return docs
    
        
        

