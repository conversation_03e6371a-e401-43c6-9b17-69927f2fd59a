from langchain_openai import AzureOpenAIEmbeddings
import logging
import os
from langchain_community.vectorstores import OpenSearchVectorSearch
import requests
from dotenv import load_dotenv

load_dotenv()
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
OPENSEARCH_HOST = os.getenv("OPENSEARCH_HOST")
OPENSEARCH_USERNAME = os.getenv("OPENSEARCH_USERNAME")
OPENSEARCH_PASSWORD = os.getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_SSL = os.getenv("OPENSEARCH_USE_SSL")
OPENSEARCH_VERIFT_CERT = os.getenv("OPENSEARCH_VERIFT_CERT")
BATCH_SIZE = int(os.getenv("BATCH_SIZE"))
MAX_BULK_SIZE = int(os.getenv("MAX_BULK_SIZE"))
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION"))
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")


class Load2Database:
    
    def __init__(self,index_name):

        self.embeddings = AzureOpenAIEmbeddings(
            model= EMBEDDING_MODEL,
            dimensions= EMBEDDING_DIMENSION,
            azure_endpoint = AZURE_OPENAI_ENDPOINT,
            api_key = AZURE_OPENAI_API_KEY,
            openai_api_version = AZURE_OPENAI_API_VERSION
        )
        logging.info(f'Loaded embeddings model {EMBEDDING_MODEL}')
        self.index_name = index_name
        try:
            self.db = OpenSearchVectorSearch(
            index_name= index_name,
                embedding_function = self.embeddings,
                opensearch_url = OPENSEARCH_HOST,
                http_auth = (OPENSEARCH_USERNAME, OPENSEARCH_PASSWORD),
                use_ssl = OPENSEARCH_USE_SSL=="true",
                verify_certs = OPENSEARCH_VERIFT_CERT=="true",
                ssl_assert_hostname = False,
                ssl_show_warn = False,
                timeout=30, 
                max_retries=10, 
                retry_on_timeout=True
            )
            logging.info(f'Connected to OpenSearch index {index_name}')
        except Exception as e:
            logging.error(e)

    
    def load_document(self,docs):    
        if len(docs) > MAX_BULK_SIZE:
            batched_docs = batching_content(docs)
            for doc in batched_docs:
                self.db.add_documents(
                    doc, 
                    embedding=self.embeddings
                )        
        else:
            self.db.add_documents(
                docs,   
                embedding = self.embeddings
            )
        logging.info(f'{len(docs)} documents added to the index')
        

def batching_content(docs):
        """
        this function has as role to batch the huge number of docs to store in opensearch to smaller entities instead
        of sending them once because of the limited bulk_size in opensearch
        """
        batch_size = BATCH_SIZE
        chunked_list =  [docs[i:i + batch_size] for i in range(0, len(docs), batch_size)]
        return chunked_list
