import opensearchpy as op
import logging
import os
from dotenv import load_dotenv

load_dotenv()
OPENSEARCH_HOST = os.getenv("OPENSEARCH_HOST")
OPENSEARCH_USERNAME = os.getenv("OPENSEARCH_USERNAME")
OPENSEARCH_PASSWORD = os.getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_SSL = os.getenv("OPENSEARCH_USE_SSL")
OPENSEARCH_VERIFT_CERT = os.getenv("OPENSEARCH_VERIFT_CERT")
REPLICAS_NUMBER = os.getenv("REPLICAS_NUMBER")
SHARDS_NUMBER = os.getenv("SHARDS_NUMBER")
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION"))

class Index():
    
    def __init__(self):
        self.opensearch_db = op.OpenSearch(
            hosts=[OPENSEARCH_HOST], 
            http_auth=(OPENSEARCH_USERNAME, OPENSEARCH_PASSWORD),
            use_ssl =  OPENSEARCH_USE_SSL=="true",
            verify_certs = OPENSEARCH_VERIFT_CERT=="true"
        )
        logging.info("Connected to OpenSearch")


    def verify_index_existance(self,index_name):

        body_request = {
            "settings":{
                "number_of_shards":SHARDS_NUMBER,
                "number_of_replicas":REPLICAS_NUMBER,
                "index":{
                    "knn":True
                }
            },
            "mappings":{
                "properties":{
                    "vector_field": {
                        "type":"knn_vector",
                        "dimension": EMBEDDING_DIMENSION
                    },
                    "text" : {
                        "type" : "text"
                    }
                }
            }
        }
        
        if not self.opensearch_db.indices.exists(index = index_name):
            self.opensearch_db.indices.create(index = index_name ,body = body_request)


    def truncate_source_documents(self,source,index_name):
        """
        this method takes the source that all docs belong to will be trancated.
            - args :
            source : the source which will be trancated
            index_name : the target index which will be updated  
        """
        #the query that return all document of the target source 
        body_request = {
            "query":
            {
                "match":{
                    "metadata.source.keyword":source
                }
            }
        }
        #deleting the docs based on the query above    
        try:
            self.opensearch_db.delete_by_query(index_name,body_request)
        except:
            logging.error("new file to load after update")        
