# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html


# # useful for handling different item types with a single interface
from scrapy.exceptions import DropItem
import website_scraper.utils.loader  as loader
import website_scraper.utils.index  as index
import website_scraper.utils.parsers as parser
from langchain_core.documents import Document
from wimpy import chunks
import logging
from os import getenv
from dotenv import load_dotenv

load_dotenv()
TEXT_CHUNK_SIZE = int(getenv("TEXT_CHUNK_SIZE"))
TEXT_OVERLAP = int(getenv("TEXT_OVERLAP"))

    
class PEWebpagePipeline:

    def process_item(self, item, spider):
        content_type = item['type']
        url = item['source']
        docs = []
        
        if 'text/html' in content_type:
            page_content = item['content']
            logging.info(f"{url} ################ web page #############")
            clean_content = ' '.join(page_content.split()) 
            split_content = clean_content.split(' ')
            chuncked_content = list(chunks(split_content,TEXT_CHUNK_SIZE,TEXT_OVERLAP))     #split text to chunks  
            for chunk in chuncked_content:
                docs.append(
                    Document(
                        page_content=' '.join(chunk),
                        metadata={
                            'source': url,
                            'title': item["title"],
                            'time': item["timestamp"],
                            'image': item["image"]
                        }
                    )
                )
        else:
            logging.info(f"{url} ################ file ################")
            source = item["source"]  #holds the url of the file
            if "application/pdf" in content_type:
                logging.info("################ pdf ################")
                docs = parser.parse_pdf(item["content"], source)
            else:
                logging.warning("format not supported")
                raise DropItem("format not supported")
            
        return {"data":docs}
    
    
    
class PEDataFetchingPipeline:

    def __init__(self):
        self.loader = None
        self.index = None
        self.index_name = ''


    def process_item(self, item, spider):
    
        if not self.index:    
            self.index = index.Index()
            self.index_name = spider.index_name
            self.index.verify_index_existance(self.index_name)
                
        if not self.loader:
            self.loader = loader.Load2Database(self.index_name)
        try:
            self.index.truncate_source_documents(item["data"][0].metadata["source"], self.index_name)
            self.loader.load_document(item["data"])
            logging.info("document is loaded to opensearch index.")
        except Exception as e:
            logging.error(f"no data to load: {e}")
            raise DropItem(e)
        
        return item