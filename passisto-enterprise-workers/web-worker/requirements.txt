--extra-index-url https://download.pytorch.org/whl/cpu
aiohttp==3.9.0
aiosignal==1.3.1
annotated-types==0.6.0
anyio==3.7.1
APScheduler==3.10.4
attrs==23.1.0
Automat==22.10.0
blinker==1.7.0
Brotli==1.1.0
certifi==2023.11.17
cffi==1.16.0
charset-normalizer==3.3.2
click==8.1.7
constantly==23.10.4
cryptography==41.0.5
cssselect==1.2.0
dataclasses-json==0.6.2
filelock==3.13.1
Flask==3.0.0
Flask-Compress==1.14
Flask-SQLAlchemy==3.1.1
frozenlist==1.4.0
fsspec==2023.10.0
greenlet==3.0.1
huggingface-hub==0.19.4
hyperlink==21.0.0
idna==3.4
incremental==22.10.0
itemadapter==0.8.0
itemloaders==1.1.0
itsdangerous==2.1.2
Jinja2==3.1.2
jmespath==1.0.1
joblib==1.3.2
jsonpatch==1.33
jsonpointer==2.4
langchain==0.0.340
langsmith==0.0.66
logparser==0.8.2
lxml==4.9.3
MarkupSafe==2.1.3
marshmallow==3.20.1
mpmath==1.3.0
multidict==6.0.4
mypy-extensions==1.0.0
networkx==3.2.1
# nltk==3.8.1
numpy==1.26.2
opensearch-py==2.4.2
packaging==23.2
parsel==1.8.1
pexpect==4.9.0
# Pillow==10.1.0
Protego==0.3.0
ptyprocess==0.7.0
pyasn1==0.5.1
pyasn1-modules==0.3.0
pycparser==2.21
pydantic==2.5.2
pydantic_core==2.14.5
PyDispatcher==2.0.7
pyOpenSSL==23.3.0
PyPDF2==3.0.1
python-dateutil==2.8.2
pytz==2023.3.post1
PyYAML==6.0.1
queuelib==1.6.2
regex==2023.10.3
requests==2.31.0
requests-file==1.5.1
# safetensors==0.4.0
# scikit-learn==1.3.2
# scipy==1.11.4
Scrapy==2.11.0
scrapy-user-agents==0.1.1
scrapyd==1.4.3
scrapyd-client==1.2.3
# sentence-transformers==2.2.2
# sentencepiece==0.1.99
service-identity==23.1.0
six==1.16.0
sniffio==1.3.0
SQLAlchemy==2.0.23
sympy==1.12
tenacity==8.2.3
threadpoolctl==3.2.0
tldextract==5.1.1
# tokenizers==0.15.0
# torch==2.1.1
# torchvision==0.16.1
tqdm==4.66.1
# transformers==4.35.2
# triton==2.1.0
Twisted==22.10.0
typing-inspect==0.9.0
typing_extensions==4.8.0
tzlocal==5.2
ua-parser==0.18.0
uberegg==0.1.1
urllib3==2.1.0
user-agents==2.2.0
w3lib==2.1.2
Werkzeug==3.0.1
wimpy==0.6
yarl==1.9.3
zope.interface==6.1
python-dotenv==1.0.0
psycopg2-binary==2.9.9
pypdf==3.15.0