Build an egg file of scrapy project than upload it to scrapyd server. 

1- add a setup.py file which will eggify your scrapy project.
my_scrapy_project/
├── my_scrapy_project/
│   ├── __init__.py
│   ├── items.py
│   ├── middlewares.py
│   ├── pipelines.py
│   ├── settings.py
│   └── spiders/
│       ├── __init__.py
│       └── my_spider.py
├── scrapy.cfg
└── setup.py

2 run this command to build the egg
    python3 setup.py bdist_egg

3 To upload scrapy project to scarapyd server (project name = website_scraper)
curl -u <srapyd_username>:<scrapyd_password> http://<scrapyd_host>:6800/addversion.json -F project=<project_name> -F version=r1 -F egg=<egg file path>

SCRAPYD_USERNAME="yS2RsEFBrnDiG4CUvoX7b"
SCRAPYD_PASSWORD="S32vNKQlv8hgKiDD2Vxq0qbOeUVfvLdKNsHCnbrK3xLTzgWjPV"
SCRAPYD_API = "http://scrapyd-server:6800"
