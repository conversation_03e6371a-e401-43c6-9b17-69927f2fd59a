name: scrapyd server dependencies CI CD
on:
  push:
    branches: [dev]
  pull_request:
    branches: [dev]
  workflow_dispatch:
    
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}
      - name: Build and push Docker image
        run: |
          docker build -t ${{ secrets.DOCKER_USERNAME }}/scrapyd-server:dev .
          docker push ${{ secrets.DOCKER_USERNAME }}/scrapyd-server:dev
  
  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Ubuntu server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_TEST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-test
            touch /pe-test/scrapyd-server/logs.txt
            docker pull ${{ secrets.DOCKER_USERNAME }}/scrapyd-server:dev
            
            docker compose up --build -d pe-scrapyd-server