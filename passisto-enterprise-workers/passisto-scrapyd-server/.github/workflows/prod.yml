name: Test FastAPI Server CI CD

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:

  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKER_USERNAME }}/scrapyd-server:prod
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Ubuntu server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SERVER_HOST_PROD }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          passphrase: ''
          port: ${{ secrets.PORT }}
          script: |
            cd /pe-prod
            mkdir -p /pe-prod/passisto-enterprise-workers/scrapyd-server/logs.txt
            docker pull ${{ secrets.DOCKER_USERNAME }}/scrapyd-server:prod

            infisical export --env=prod --path="/scrapyd-server" > /pe-prod/passisto-enterprise-workers/passisto-scrapyd-server/.env

            
            docker compose up -d scrapyd-server --force-recreate
  
    
