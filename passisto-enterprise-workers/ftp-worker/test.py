from ftplib import FTP

def traverse_ftp_directory(ftp, directory="/", indent=0):
    try:
        files = ftp.nlst(directory)
        for filename in files:
            print(" " * indent + filename)
            if ftp.nlst(filename) != [filename]:  # Check if it's a directory
                traverse_ftp_directory(ftp, filename, indent + 4)
    except Exception as e:
        print("Error:", e)

def main():
    ftp_host = "*************"  # Change this to your FTP server hostname or IP address
    ftp_user = "demo"  # Change this to your FTP username
    ftp_password = "demo"  # Change this to your FTP password

    with FTP() as ftp:
        ftp.connect(host=ftp_host,port=21000)
        ftp.login(user=ftp_user, passwd=ftp_password)
        print("Logged in")

        traverse_ftp_directory(ftp)

if __name__ == "__main__":
    main()
