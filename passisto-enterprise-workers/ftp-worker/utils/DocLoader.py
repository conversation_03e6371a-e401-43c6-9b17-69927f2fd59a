import os
import warnings
from .setLogger import logger
from langchain_community.vectorstores import OpenSearchVectorSearch
from langchain_openai import AzureOpenAIEmbeddings

AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
EMBEDDING_DIMENSION = os.getenv("EMBEDDING_DIMENSION")

OPENSEARCH_URL = os.getenv('OPENSEARCH_URL')
OPENSEACH_USERNAME = os.getenv("OPENSEARCH_USERNAME")
OPENSEACH_PASSWORD = os.getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_SSL = True if os.getenv("OPENSEARCH_USE_SSL")=='true' else False
OPENSEARCH_VERIFY_CERT = True if os.getenv("OPENSEARCH_VERIFY_CERT")=='true' else False

MAX_BULK_SIZE = int(os.getenv("MAX_BULK_SIZE"))
BATCH_SIZE = int(os.getenv("BATCH_SIZE"))

warnings.filterwarnings('ignore')


class Load2Database:
    
    def __init__(self, opensearch_index):
        self._initialize_embeddings()
        self._establish_connection(opensearch_index)
        
    
    def _initialize_embeddings(self):
        logger.log.info('Loading vector embedding model...')
        self.embeddings = AzureOpenAIEmbeddings(
            model= EMBEDDING_MODEL,
            dimensions= EMBEDDING_DIMENSION,
            azure_endpoint = AZURE_OPENAI_ENDPOINT,
            api_key = AZURE_OPENAI_API_KEY,
            openai_api_version = AZURE_OPENAI_API_VERSION
        )
    
    
    def _establish_connection(self, opensearch_index):
        logger.log.info('Establishing connection to OpenSearch...')
        self.db = OpenSearchVectorSearch(
            index_name=opensearch_index,
            embedding_function=self.embeddings,
            opensearch_url=OPENSEARCH_URL,
            http_auth=(OPENSEACH_USERNAME, OPENSEACH_PASSWORD),
            use_ssl=OPENSEARCH_USE_SSL=="true",
            verify_certs=OPENSEARCH_VERIFY_CERT=="true",
            timeout=30,
            max_retries=10,
            retry_on_timeout=True
        )
        logger.log.info('Successfully established connection')

    
    def load_document(self, docs):
        if len(docs) > MAX_BULK_SIZE:
            logger.log.debug('Too many documents, batching...')
            batched_docs = self._batch_documents(docs)
            for i, batch in enumerate(batched_docs):
                self._add_documents(batch, i)
        else:
            self._add_documents(docs)
    
    
    def _batch_documents(self, docs):
        return [docs[i:i + BATCH_SIZE] for i in range(0, len(docs), BATCH_SIZE)]
    
    
    def _add_documents(self, docs, batch_index=None):
        self.db.add_documents(docs, embedding=self.embeddings)
        if batch_index is not None:
            logger.log.debug(f'Batch {batch_index} was loaded successfully')
        else:
            logger.log.info('Documents were loaded successfully')
