import os
import json
import logging
from typing import List, Dict, Optional
from pathlib import Path
from pptx import Presentation
import pandas as pd
import xml.etree.ElementTree as ET
from langchain_community.document_loaders import PyPDFLoader, CSVLoader, TextLoader, Docx2txtLoader
from langchain_community.docstore.document import Document
from langchain.text_splitter import CharacterTextSplitter

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    format="%(asctime)s - %(levelname)s - %(message)s",  # Log format
    handlers=[
        logging.FileHandler("document_processing.log"),  # Log to a file
        logging.StreamHandler()  # Log to the console
    ]
)
logger = logging.getLogger(__name__)

# Load configuration from environment variables

CHUNKS_SIZE = int(os.getenv('TEXT_CHUNK_SIZE', '1000'))
OVERLAP = int(os.getenv('TEXT_OVERLAP', '200'))
ENCODER = os.getenv('ENCODER', 'utf-8')

# Define the text splitter
text_splitter = CharacterTextSplitter(chunk_size=CHUNKS_SIZE, chunk_overlap=OVERLAP)

def extract_slide_content(pptx_path: str) -> Optional[List[Dict[str, str]]]:
    """Extract content from PowerPoint slides."""
    try:
        presentation = Presentation(pptx_path)
        slides = []
        for slide_number, slide in enumerate(presentation.slides, start=1):
            content = [shape.text for shape in slide.shapes if hasattr(shape, "text")]
            slides.append({"slide_number": slide_number, "content": "\n".join(content)})
        logger.info(f"Successfully extracted slides from {pptx_path}.")
        return slides
    except Exception as e:
        logger.error(f"Failed to open {pptx_path}: {e}")
        return None

def parse_pptx(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse PPTX files and return a list of Document objects."""
    extracted_slides = extract_slide_content(filename)
    if not extracted_slides:
        return None

    docs = [
        Document(
            page_content=slide['content'],
            metadata={
                'source': remote_source,
                'title': filename,
                'page': slide['slide_number']
            }
        )
        for slide in extracted_slides
    ]
    logger.info(f"Successfully processed PPTX file: {filename}.")
    return docs

def parse_excel(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse Excel files and return a list of Document objects."""
    try:
        excel_file = pd.ExcelFile(filename)
        docs = []
        for sheet in excel_file.sheet_names:
            df = pd.read_excel(filename, sheet_name=sheet)
            for row in df.iterrows():
                docs.append(Document(
                    page_content=str(row[1]),
                    metadata={
                        'source': remote_source,
                        'title': filename,
                        'row': row[0],
                        'sheet': sheet
                    }
                ))
        logger.info(f"Successfully processed Excel file: {filename}.")
        return docs
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None

def parse_doc(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse DOCX files and return a list of Document objects."""
    try:
        loader = Docx2txtLoader(filename)
        docs = loader.load_and_split(text_splitter)
        for doc in docs:
            doc.metadata = {'source': remote_source, 'title': filename}
        logger.info(f"Successfully processed DOCX file: {filename}.")
        return docs
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None

def parse_word(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse DOCX files and return a list of Document objects."""
    try:
        # Check if file exists
        if not os.path.exists(filename):
            logger.error(f"File not found: {filename}")
            return None
            
        # Use absolute path to avoid issues with spaces in filenames
        abs_path = os.path.abspath(filename)
        loader = Docx2txtLoader(abs_path)
        docs = loader.load_and_split(text_splitter)
        for doc in docs:
            doc.metadata = {'source': remote_source, 'title': os.path.basename(filename)}
        logger.info(f"Successfully processed DOCX file: {filename}.")
        return docs
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None



def retrieve_data(tag: ET.Element) -> str:
    """Recursively retrieve data from XML tags."""
    sub_tags = []
    for sub_element in tag:
        if len(sub_element) > 0:
            sub_tags.append(retrieve_data(sub_element))
        else:
            sub_tags.append(f'{sub_element.tag}:{sub_element.text}\t')
    return ' '.join(sub_tags)

def parse_xml(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse XML files and return a list of Document objects."""
    try:
        tree = ET.parse(filename)
        root = tree.getroot()
        docs = []
        for element in root:
            tag_info = [f'tag_name:{element.tag}'] + [f'{key}:{value}' for key, value in element.attrib.items()]
            tag_text = ' '.join(tag_info)
            sub_tags_text = retrieve_data(element)
            docs.append(Document(
                page_content=sub_tags_text,
                metadata={
                    'source': remote_source,
                    'metadata_tag': tag_text,
                    'title': filename
                }
            ))
        logger.info(f"Successfully processed XML file: {filename}.")
        return docs
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None

def parse_pdf(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse PDF files and return a list of Document objects."""
    try:
        loader = PyPDFLoader(filename)
        documents = loader.load()
        docs = text_splitter.split_documents(documents)
        logger.info(f"Successfully processed {len(docs)} docs in PDF file: {filename}.")
        return [process_document(doc, filename, source, remote_source) for doc in docs]
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None

def parse_csv(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse CSV files and return a list of Document objects."""
    try:
        loader = CSVLoader(filename, encoding=ENCODER)
        documents = loader.load()
        docs = text_splitter.split_documents(documents)
        logger.info(f"Successfully processed CSV file: {filename}.")
        return [process_document(doc, filename, source, remote_source) for doc in docs]
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None

def parse_text(filename: str, source: str, remote_source: str) -> Optional[List[Document]]:
    """Parse text files and return a list of Document objects."""
    try:
        loader = TextLoader(filename)
        documents = loader.load()
        docs = text_splitter.split_documents(documents)
        logger.info(f"Successfully processed text file: {filename}.")
        return [process_document(doc, filename, source, remote_source) for doc in docs]
    except Exception as e:
        logger.error(f"Failed to open {filename}: {e}")
        return None

def process_document(document: Document, filename: str, source: str, remote_source: str) -> Document:
    """Process and format Document objects for PDF, CSV, and TXT files."""
    metadata = {'source': remote_source, 'title': filename}
    if filename.endswith('.pdf'):
        metadata['page'] = document.metadata['page']
    elif filename.endswith('.csv'):
        metadata['row'] = document.metadata['row']
    return Document(
        page_content=document.page_content,
        metadata=metadata
    )
