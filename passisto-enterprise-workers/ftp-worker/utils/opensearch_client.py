import os
from opensearchpy import OpenSearch

# OpenSearch Configuration from Django settings
OPENSEARCH_ENDPOINT = os.getenv("OPENSEARCH_URL", "localhost:9200")
POOL_SIZE = int(os.getenv("OPENSEARCH_POOL_SIZE", 10))

# Authentication using environment variables
OPENSEARCH_USERNAME = os.getenv("OPENSEARCH_USERNAME")
OPENSEARCH_PASSWORD = os.getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_SSL = True if os.getenv("OPENSEARCH_USE_SSL")=='true' else False
OPENSEARCH_VERIFY_CERT = True if os.getenv("OPENSEARCH_VERIFY_CERT")=='true' else False

class OpenSearchClient:
    _instance = None
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OpenSearchClient, cls).__new__(cls)
            cls._instance.client = OpenSearch(
                hosts=OPENSEARCH_ENDPOINT,
                http_auth=(OPENSEARCH_USERNAME, OPENSEARCH_PASSWORD),
                http_compress=True,
                use_ssl=OPENSEARCH_USE_SSL,
                verify_certs=OPENSEARCH_VERIFY_CERT,
                pool_maxsize=POOL_SIZE,
            )
        return cls._instance


def get_opensearch_client():
    return OpenSearchClient().client
