import logging

class Logger:
    def __init__(self):
        self.log = logging.getLogger("passisto-ftp-worker")
        self.log.setLevel(logging.DEBUG)
        self.log.propagate = False  # Prevent double logging

        # Remove any existing handlers
        if self.log.hasHandlers():
            self.log.handlers.clear()

        formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s - %(asctime)s')

        # Console Handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(formatter)
        self.log.addHandler(console_handler)

        # File Handler
        file_handler = logging.FileHandler('jira.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.log.addHandler(file_handler)

logger = Logger()
