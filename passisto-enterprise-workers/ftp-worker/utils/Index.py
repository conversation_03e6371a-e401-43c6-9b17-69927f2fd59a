from .setLogger import logger
from .opensearch_client import get_opensearch_client
from os import getenv

OPENSEARCH_NUM_SHARDS = int(getenv("OPENSEARCH_NUM_SHARDS"))
OPENSEARCH_NUM_REPLICAS = int(getenv("OPENSEARCH_NUM_REPLICAS"))
EMBEDDING_DIMENSION = int(getenv("EMBEDDING_DIMENSION")) 

class Index():

    def __init__(self) -> None:
        self.opensearch_db = get_opensearch_client()
        
        
    def create_index(self,opensearch_index):
        body_request = {
            "settings":{
                "number_of_shards":OPENSEARCH_NUM_SHARDS,
                "number_of_replicas":OPEN<PERSON>ARCH_NUM_REPLICAS,
                "index":{
                    "knn":True
                }
            },
            "mappings":{
                "properties":{
                    "vector_field": {
                        "type":"knn_vector",
                        "dimension":EMBEDDING_DIMENSION
                    }
                }
            }
        }
        if not self.opensearch_db.indices.exists(index = opensearch_index):
            self.opensearch_db.indices.create(index = opensearch_index ,body = body_request)
            logger.log.info('index not existing, an index was created successefully')
        else:
            logger.log.info('the index is already existing')

    
    def truncate_source_documents(self,source,index_name):
        """
        this method takes the source that all docs belong to will be trancated.
            - args :
              source : the source which will be trancated
              index_name : the target index which will be updated  
        """
        #the query that return all document of the target source 
        body_request = {
            "query":
            {
                "match":{
                    "metadata.source.keyword":source
                }
            }
        }
        #deleting the docs based on the query above    
        try:
            self.opensearch_db.delete_by_query(index_name,body_request)
        except:
            logger.log.error("new file to load after update")
        
        logger.log.debug("related docs are trancated")