FROM python:3.11-slim

WORKDIR /app

RUN apt-get update && apt-get install -y bash curl && curl -1sLf \
'https://dl.cloudsmith.io/public/infisical/infisical-cli/setup.deb.sh' | bash \
&& apt-get update && apt-get install -y infisical

RUN apt-get update && apt-get install -y bash

COPY ./requirements.txt /app

RUN pip install --no-cache -r requirements.txt

COPY . .

RUN chmod +x entry.test.sh

# CMD celery -A tasks worker -l info -Q jira -n jira_worker@%h
