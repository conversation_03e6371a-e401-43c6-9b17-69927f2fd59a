from jira import JIRA
from dotenv import load_dotenv
from os import getenv,environ
from langchain.docstore.document import Document
from utils.index import Index
from utils.DocLoader import Load2Database
from utils.setLogger import logger

load_dotenv()
ENBEDDING_MODEL = getenv('EMBEDDING_MODEL')
OPENSEARCH_URL = getenv('OPENSEARCH_URL')

class LoadingJiraError(Exception):
    pass

class ProjecNotExistError(Exception):
    pass


class JiraProject:
    def __init__(self,server_ip,email,api_key,opensearch_index):
        '''
        This function load jira data from jira cloud using this args:
        server: takes the url of the jira platform.
        email:  the email of the user
        api_key: takes the api key that allowed the user to access the jira data from the api.
        '''
        #launching the api
        logger.log.debug('load text embedding...')
        self.opensearch_db = Index()
        logger.log.debug('getting connection to jira api')
        
        if not server_ip.startswith("https://"):
            server_ip = f"https://{server_ip}"

        self.server = JIRA(  
            basic_auth=(email, api_key),  # os.environ('api_key')
            server=server_ip
        )
        
        logger.log.info('connection succesfully made')
        #get the different boards of jira will will aloows as to get in to the backlogger project
        #for getng this ressource user must authentificate  
        self.boards = self.server.boards()  #get boards of all projects
        self.server_ip = server_ip  #  get the adress ip of the all jira Sprojects
        self.opensearch_index = opensearch_index
          
        
    def verfiy_project_existance(self,project_key):
        """
        from the name of the project we verify neither the project is exists or not if yes a project instance
        it is retured
        """
        # projects = self.server.projects()
        # for project in projects:
        #     if project.key == project_key:
        #         return project
        return self.server.project(project_key)

    
    def get_board(self,project_key):
        """
        this function return the board id of the project from the project key
        """
        for board in self.boards:
            if board.name.startswith(project_key):
                logger.log.info(f'getting the board id of the project which is {board.id}')
                return board.id

        
    def loadJiraProject(self,project_name):
        """
        This function load jira issues of the project as langchain doc format
        """
        logger.log.debug(f'get access to {project_name}')
        project = self.verfiy_project_existance(project_name)
        if not project:
            logger.log.error("project doesn't exist")
            raise ProjecNotExistError("project {project_name} doesn't, loader will stop here... ")
            
        board_id = self.get_board(project.key)
        self.opensearch_db.verify_index_existance(self.opensearch_index)     #if the name of the project has an espace remplace it with the underscore

        logger.log.debug('establish connection to opensearch')
        doc2opensearch = Load2Database(self.opensearch_index)
        
        Docs =[]
        logger.log.debug('starting the loader...')
        for issue in self.server.search_issues(jql_str = f"project = {project}"):  
            try:
                content = f'''
                    Issue ID: {issue.key}
                    Title: {issue.fields.summary}
                    Description: {issue.fields.description}
                    Type: {issue.fields.issuetype.name if hasattr(issue.fields, 'issuetype') else None}  
                    Priority: {issue.fields.priority.name if hasattr(issue.fields, 'priority') else None}  
                    Status: {issue.fields.status.name if hasattr(issue.fields, 'status') else None}  
                    Votes: {issue.fields.votes if hasattr(issue.fields, 'votes') else None}  
                    Created: {issue.fields.created if hasattr(issue.fields, 'created') else None}  
                    Project: {issue.fields.project.name if hasattr(issue.fields, 'project') else None} 
                    Reporter: {issue.fields.reporter.displayName if hasattr(issue.fields, 'reporter') else None} 
                    Assignee: {issue.fields.assignee.displayName if hasattr(issue.fields, 'assignee') and issue.fields.assignee else None}  
                '''
                source = f'{self.server_ip}/jira/software/projects/{issue.fields.project}/boards/{board_id}?selectedIssue={issue.key}'
                self.opensearch_db.truncate_source_documents(source,self.opensearch_index)

                Docs.append(
                    Document(
                        page_content=content,
                        metadata={
                            'source': source,
                            'title': issue.fields.summary,
                            'reporter': issue.fields.reporter.displayName,
                            'type': issue.fields.issuetype.name,
                            'statut': issue.fields.status.name   
                        }
                    )
                )
            except Exception as e:
                logger.log.error(f"failed to load jira ticket {issue.key} : {e}",exc_info=True)
                continue    
            
        logger.log.debug('loading data....')
        
        if len(Docs) > 0:
            doc2opensearch.load_document(Docs)
        else:
            raise LoadingJiraError('no ticket were loaded')
        logger.log.info("loading project is finished")
