from celery import Celery
from JiraLoader import JiraProject
from utils.setLogger import logger
from utils.decrypt import decrypt
import os

CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND')

app = Celery('jira-loader', broker=CELERY_BROKER_URL,backend=CELERY_RESULT_BACKEND)
app.config_from_object('celeryConfig')

@app.task(name='celery.jira_loader')
def jiraLoader(server_ip, email, encrypted_api_key, project, opensearch_index):
    logger.log.info(f"jiraLoader worker receives a task to load {project} jira project")
    api_key = decrypt(encrypted_api_key)
    jiraP = JiraProject(server_ip, email, api_key, opensearch_index)
    jiraP.loadJiraProject(project)    
    logger.log.info("ji<PERSON><PERSON><PERSON><PERSON> finishes its task")

