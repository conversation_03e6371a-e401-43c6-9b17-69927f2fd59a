from opensearchpy import OpenSearch
from os import getenv
from dotenv import load_dotenv
from .setLogger import logger
#Firstly connecting to opensearch database in order

load_dotenv()
OPENSEARCH_URL = getenv("OPENSEARCH_URL")
OPENSEACH_USERNAME = getenv("OPENSEARCH_USERNAME")
OPENSEACH_PASSWORD = getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_SSL = True if getenv("OPENSEARCH_USE_SSL")=='true' else False
OPENSEARCH_VERIFY_CERT = True if getenv("OPENSEARCH_VERIFY_CERT")=='true' else False
OPENSEARCH_NUM_SHARDS = int(getenv("OPENSEARCH_NUM_SHARDS"))
OPENSEARCH_NUM_REPLICAS = int(getenv("OPENSEARCH_NUM_REPLICAS"))
OPENSEARCH_VECTOR_DIM = int(getenv("OPENSEARCH_VECTOR_DIM"))

class Index():
    
    def __init__(self) -> None:
        self.opensearch_db = OpenSearch(
            hosts=[OPENSEARCH_URL],
            http_auth=(OPENSEACH_USERNAME, OPENSEACH_PASSWORD),
            use_ssl=OPENSEARCH_USE_SSL,
            verify_certs=OPENSEARCH_VERIFY_CERT,
            timeout=30  
        )
        
        
    def verify_index_existance(self,opensearch_index):
        body_request = {
            "settings":{
                "number_of_shards":OPENSEARCH_NUM_SHARDS,
                "number_of_replicas":OPENSEARCH_NUM_REPLICAS,
                "index":{
                    "knn":True
                }
            },
            "mappings":{
                "properties":{
                    "vector_field": {
                        "type":"knn_vector",
                        "dimension":int(OPENSEARCH_VECTOR_DIM)
                    }
                }
            }
        }
        if not self.opensearch_db.indices.exists(index = opensearch_index):
            self.opensearch_db.indices.create(index = opensearch_index ,body = body_request)
            logger.log.info('index not existing, an index was created successefully')
        else:
            logger.log.info('the index is already existing')

    
    def truncate_source_documents(self,source,opensearch_index):
        """
        this method takes the source that all docs belong to will be trancated.
            - args :
              source : the source which will be trancated
              index_name : the target index which will be updated  
        """
        #the query that return all document of the target source 
        body_request = {
            "query":
            {
                "match":{
                    "metadata.source.keyword":source
                }
            }
        }
        #deleting the docs based on the query above    
        try:
            self.opensearch_db.delete_by_query(opensearch_index,body_request)
        except:
            logger.log.error("doc doesn't seem to be alreqady in index")
            return
        
        logger.log.info("related docs are trancated")