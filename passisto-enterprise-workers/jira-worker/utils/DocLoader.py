from langchain.vectorstores import OpenSearchVectorSearch
from dotenv import load_dotenv
from os import getenv
from .setLogger import logger
import os
from langchain_openai import AzureOpenAIEmbeddings


load_dotenv()
ENBEDDING_MODEL = getenv('EMBEDDING_MODEL')
OPENSEARCH_URL = getenv('OPENSEARCH_URL')
BATCH_SIZE = getenv('BATCH_SIZE')
MAX_BULK_SIZE = getenv('MAX_BULK_SIZE')
OPENSEACH_USERNAME = getenv("OPENSEARCH_USERNAME")
OPENSEACH_PASSWORD = getenv("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_SSL = True if getenv("OPENSEARCH_USE_SSL")=='true' else False
OPENSEARCH_VERIFY_CERT = True if getenv("OPENSEARCH_VERIFY_CERT")=='true' else False

AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
EMBEDDING_DIMENSION = os.getenv("EMBEDDING_DIMENSION")


class Load2Database:
    
    def __init__(self,opensearch_index,auth=(OPENSEACH_USERNAME,OPENSEACH_PASSWORD)):
        logger.log.debug('loader vector embbedding...')
        #embedding model for transforming docs to vectors
        self.embeddings = AzureOpenAIEmbeddings(
            model= EMBEDDING_MODEL,
            dimensions= EMBEDDING_DIMENSION,
            azure_endpoint = AZURE_OPENAI_ENDPOINT,
            api_key = AZURE_OPENAI_API_KEY,
            openai_api_version = AZURE_OPENAI_API_VERSION
        )
        logger.log.debug('establish connection')
        self.db = OpenSearchVectorSearch(
            index_name= opensearch_index,
            embedding_function = self.embeddings,
            opensearch_url = OPENSEARCH_URL,
            http_auth = auth,
            use_ssl = OPENSEARCH_USE_SSL,
            verify_certs = OPENSEARCH_VERIFY_CERT,
        )
        logger.log.info('successfully establishing connection')

   
    
    def load_document(self,docs):
        if len(docs) > int(MAX_BULK_SIZE):
            logger.log.debug('too much docs')
            batched_docs = batching_content(docs)
            for i,doc in enumerate(batched_docs):
                self.db.add_documents(                   #add docs of file to the database
                    doc, 
                    embedding=self.embeddings
                )
                logger.log.info(f'batch {i} was loaded successfully')
        else:
            self.db.add_documents(                   #add docs of file to the database
                docs,   
                embedding = self.embeddings
            )


def batching_content(docs):
        """
        this function has as role to batch the huge number of docs to store in opensearch to smaller entities instead
        of sending them once because of the limited bulk_size in opensearch
        """
        batch_size = BATCH_SIZE
        chunked_list =  [docs[i:i + batch_size] for i in range(0, len(docs), batch_size)]
        return chunked_list