from Crypto.Cipher import AES
import os
import os
from utils.setLogger import logger


ivLength = 16
ENCRYPTION_SECRET = os.getenv("ENCRYPTION_SECRET")
ENCRYPTION_ALGORITHM = os.getenv("ENCRYPTION_ALGORITHM")

def decrypt(encrypted_text: str) -> str:
    try:
        iv_hex, encrypted_data_hex = encrypted_text.split(':')
        iv = bytes.fromhex(iv_hex)
        encrypted_data = bytes.fromhex(encrypted_data_hex)
        key = bytes.fromhex(ENCRYPTION_SECRET)

        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_data = cipher.decrypt(encrypted_data)

        # Remove PKCS7 padding
        padding_length = decrypted_data[-1]
        plaintext = decrypted_data[:-padding_length].decode('utf-8')
        return plaintext
    
    except Exception as e:
        logger.log.error(f"Decryption failed: {e}",exc_info=True)
        raise e
